server {
    listen       80;
    server_name  localhost;
    return 301 https://$host$request_uri;
}

server {
    listen 443 ssl;
    server_name www.axsiem.local;
    server_tokens off;
   
    client_max_body_size 200m;
    ssl_certificate     /usr/local/nginx/certs/server.crt;
    ssl_certificate_key /usr/local/nginx/certs/server.key;
    ssl_session_timeout 5m;
    ssl_protocols TLSv1.1 TLSv1.2 TLSv1.3;
    ssl_prefer_server_ciphers on;
    ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:ECDHE:ECDH:AES:HIGH:!NULL:!aNULL:!MD5:!ADH:!RC4;

    add_header X-Frame-Options "SAMEORIGIN";
    add_header Content-Security-Policy "default-src 'self';";
    add_header X-XSS-Protection "1; mode=block";
    add_header Referrer-Policy "no-referrer";
    add_header Strict-Transport-Security 'max-age=31536000; includeSubDomains; preload';

    location / {
        if ($request_filename ~* .*\.(?:htm|html)$) {
            add_header Cache-Control "private, no-store, no-cache, must-revalidate, proxy-revalidate";
            add_header Expires 0;
            access_log on;
        }
      
        root   /usr/local/nginx/html/html;
        index  index.html index.htm;
        try_files $uri $uri/ /index.html;
    }

    # 接口代理，用于解决跨域问题
    location /anxinsec-siem {
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        # 后台接口地址
        resolver 127.0.0.11 valid=5s;
        proxy_pass http://axsiem-web:28100/anxinsec-siem;
        proxy_redirect default;
        add_header Access-Control-Allow-Origin *;
        add_header Access-Control-Allow-Headers X-Requested-With;
        add_header Access-Control-Allow-Methods GET,POST,OPTIONS;
        proxy_http_version 1.1;
        proxy_connect_timeout 5s;
        proxy_read_timeout 10s;
        proxy_send_timeout 10s;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "$connection_upgrade";
    }
} 
