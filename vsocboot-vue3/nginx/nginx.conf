worker_processes  auto;

#pid        logs/nginx.pid;
error_log  /var/log/nginx/error.log notice;
error_log  /dev/stdout notice;
pid        /var/run/nginx.pid;


events {
    worker_connections  10240;
    use epoll;    
}


http {
    include       mime.types;
    default_type  application/octet-stream;

    log_format  main  '$remote_addr - $remote_user [$time_local] "$request" '
                      '$status $body_bytes_sent "$http_referer" '
                      '"$http_user_agent" "$http_x_forwarded_for"';
    # 定义一个以IP为限制请求的方式，名字为req_limit_zone，开辟10M的共享内存区域，每秒处理的速率为10个请求
              #    limit_req_zone $binary_remote_addr zone=req_limit_zone:10m rate=1000r/s;
    # 定义一个共享内存区域，用于存储连接计数
              #limit_conn_zone $binary_remote_addr zone=conn_limit_zone:10m;


    #keepalive_timeout  0
    access_log  /var/log/nginx/access.log  main;
    access_log  /dev/stdout  main;
    sendfile        on;
    #tcp_nopush     on;

    map $http_upgrade $connection_upgrade {
    default upgrade;
    ''      close;
    }

    more_clear_headers '';

    gzip on;
    gzip_types text/plain application/javascript text/css application/xml text/javascript image/jpeg image/gif image/png;
    gzip_comp_level 5;
    gzip_vary on;
    gzip_buffers 16 8k;
    gzip_disable "MSIE [1-6]\.";
    gzip_http_version 1.1;
    gzip_min_length 2k;
    gzip_proxied off;

    keepalive_timeout 15s;
    client_body_timeout 10s;
    client_header_timeout 10s;

    # Include server configurations
    include /usr/local/nginx/conf.d/*.conf;
}
