# 是否打开mock
VITE_USE_MOCK = true

# 发布路径
VITE_PUBLIC_PATH = /

# 跨域代理，您可以配置多个 ,请注意，没有换行符
VITE_PROXY = [["/anxinsec-siem","http://localhost:8081/anxinsec-siem"],["/upload","http://localhost:3300/upload"]]

# 控制台不输出
VITE_DROP_CONSOLE = false

#后台接口父地址(必填)
VITE_GLOB_API_URL=/anxinsec-siem
#后台接口全路径地址(必填)
VITE_GLOB_DOMAIN_URL=http://localhost:8081/anxinsec-siem

# 接口前缀
VITE_GLOB_API_URL_PREFIX=

#微前端qiankun应用,命名必须以VITE_APP_SUB_开头,jeecg-app-1为子应用的项目名称,也是子应用的路由父路径
VITE_APP_SUB_jeecg-app-1 = '//*************:8092'

#默认语言 zh_CN,en
VITE_GLOB_LANGUAGE = en

#使用平台
VITE_GLOB_PLATFORM = ump

#copyright
VITE_GLOB_COPYRIGHT = Anxinsec Technology Co., Ltd.


