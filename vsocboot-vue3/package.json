{"name": "jeecgboot-vue3", "version": "3.5.2", "author": {"name": "jeecg", "email": "jeec<PERSON>@163.com", "url": "https://github.com/jeecgboot/jeecgboot-vue3"}, "scripts": {"bootstrap": "pnpm install", "serve": "npm run dev", "dev": "vite", "clean:cache": "rimraf node_modules/.cache/ && rimraf node_modules/.vite", "clean:lib": "rimraf node_modules", "prebuild": "pnpm clean", "build": "cross-env NODE_ENV=production NODE_OPTIONS=--max-old-space-size=8192 vite build && esno ./build/script/postBuild.ts", "build-bjys": "cross-env NODE_ENV=production NODE_OPTIONS=--max-old-space-size=8192 vite build --mode bjys && esno ./build/script/postBuild.ts", "build-dev254": "cross-env NODE_ENV=production NODE_OPTIONS=--max-old-space-size=8192 vite build --mode dev254 && esno ./build/script/postBuild.ts", "build-zh": "cross-env NODE_ENV=production NODE_OPTIONS=--max-old-space-size=8192 vite build --mode prod_zh && esno ./build/script/postBuild.ts", "build-en": "cross-env NODE_ENV=production NODE_OPTIONS=--max-old-space-size=8192 vite build --mode prod_en && esno ./build/script/postBuild.ts", "build:no-cache": "pnpm clean:cache && npm run build", "report": "cross-env REPORT=true npm run build", "type:check": "vue-tsc --noEmit --skipLib<PERSON><PERSON><PERSON>", "preview": "npm run build && vite preview", "preview:dist": "vite preview", "log": "conventional-changelog -p angular -i CHANGELOG.md -s", "lint:eslint": "eslint --cache --max-warnings 0  \"{src,mock}/**/*.{vue,ts,tsx}\" --fix", "lint:prettier": "prettier --write  \"src/**/*.{js,json,tsx,css,less,scss,vue,html,md}\"", "lint:stylelint": "stylelint --cache --fix \"**/*.{vue,less,postcss,css,scss}\" --cache --cache-location node_modules/.cache/stylelint/", "lint:pretty": "pretty-quick --staged", "test:unit": "jest", "test:unit-coverage": "jest --coverage", "test:gzip": "http-server dist --cors --gzip -c-1", "test:br": "http-server dist --cors --brotli -c-1", "reinstall": "rimraf pnpm-lock.yaml && yarn.lock && rimraf package.lock.json && rimraf node_modules && npm run bootstrap", "gen:icon": "esno ./build/generate/icon/index.ts"}, "dependencies": {"@ant-design/colors": "^6.0.0", "@ant-design/icons-vue": "^6.1.0", "@iconify/iconify": "^2.2.1", "@jsplumb/browser-ui": "^6.2.10", "@vue/runtime-core": "^3.2.33", "@vue/shared": "^3.2.33", "@vueuse/core": "^8.3.0", "@vueuse/shared": "^8.3.0", "@zxcvbn-ts/core": "^3.0.4", "ace-builds": "^1.36.2", "ant-design-vue": "^3.2.12", "axios": "^0.26.1", "bpmn-js": "^14.0.0", "camunda-bpmn-js-behaviors": "^1.0.0", "camunda-bpmn-moddle": "^7.0.1", "codemirror": "^5.65.3", "cron-parser": "^3.5.0", "cropperjs": "^1.5.12", "crypto-js": "^4.1.1", "dayjs": "^1.11.1", "diagram-js": "^12.8.0", "dom-align": "^1.12.2", "echarts": "^5.5.0", "echarts-gl": "^2.0.9", "echarts-wordcloud": "2.1.0", "emoji-mart-vue-fast": "^11.1.1", "enquire.js": "^2.1.6", "html2canvas": "^1.4.1", "inherits": "^2.0.4", "intro.js": "^5.1.0", "js-sql-parser": "^1.6.0", "jshashes": "^1.0.8", "lodash-es": "^4.17.21", "lodash.get": "^4.4.2", "md5": "^2.3.0", "min-dash": "^4.1.1", "mockjs": "^1.1.0", "monaco-editor": "^0.44.0", "nprogress": "^0.2.0", "path-to-regexp": "^6.2.0", "pinia": "2.0.12", "pinyin-pro": "^3.11.0", "print-js": "^1.6.0", "qrcode": "^1.5.0", "qrcodejs2": "0.0.2", "qs": "^6.10.3", "resize-observer-polyfill": "^1.5.1", "screenfull": "^6.0.2", "showdown": "^2.1.0", "sortablejs": "^1.15.2", "spark-md5": "^3.0.2", "tiny-svg": "^3.0.1", "tinymce": "^5.10.3", "vditor": "^3.8.13", "vite-plugin-monaco-editor": "^1.1.0", "vue": "~3.3.0", "vue-clipboard3": "^1.0.1", "vue-cropper": "^0.5.6", "vue-cropperjs": "^5.0.0", "vue-draggable-plus": "^0.5.6", "vue-i18n": "^9.1.9", "vue-infinite-scroll": "^2.0.2", "vue-json-pretty": "^2.0.6", "vue-print-nb-jeecg": "^1.0.12", "vue-router": "^4.0.14", "vue-simple-uploader": "^1.0.0-beta.5", "vue-types": "^4.1.1", "vue3-colorpicker": "^2.3.0", "vue3-draggable-resizable": "^1.6.5", "vue3-scale-box": "^0.1.9", "vue3-seamless-scroll": "^2.0.1", "vue3-sketch-ruler": "^1.3.15", "vuedraggable": "^4.1.0", "vxe-table": "4.1.0", "vxe-table-plugin-antd": "3.0.5", "wujie-vue3": "^1.0.18", "xe-utils": "^3.3.1", "xss": "^1.0.13"}, "devDependencies": {"@commitlint/cli": "^16.2.3", "@commitlint/config-conventional": "^16.2.1", "@iconify/json": "^2.1.30", "@purge-icons/generated": "^0.8.1", "@rys-fe/vite-plugin-theme": "^0.8.6", "@types/crypto-js": "^4.1.1", "@types/fs-extra": "^9.0.13", "@types/inquirer": "^8.2.1", "@types/intro.js": "^3.0.2", "@types/jest": "^27.0.2", "@types/lodash-es": "^4.17.6", "@types/mockjs": "^1.0.6", "@types/node": "^17.0.25", "@types/nprogress": "^0.2.0", "@types/pinyin": "^2.10.0", "@types/qrcode": "^1.4.2", "@types/qs": "^6.9.7", "@types/showdown": "^1.9.4", "@types/sortablejs": "^1.15.8", "@typescript-eslint/eslint-plugin": "^5.20.0", "@typescript-eslint/parser": "^5.20.0", "@vitejs/plugin-legacy": "^2.0.0", "@vitejs/plugin-vue": "^3.0.1", "@vitejs/plugin-vue-jsx": "^1.3.10", "@vue/compiler-sfc": "^3.2.33", "@vue/test-utils": "^2.0.0-rc.21", "autoprefixer": "^10.4.4", "commitizen": "^4.2.4", "conventional-changelog-cli": "^2.2.2", "cross-env": "^7.0.3", "cz-git": "^1.3.9", "czg": "^1.3.9", "dotenv": "^16.0.0", "eslint": "^8.22.0", "eslint-config-prettier": "^8.6.0", "eslint-define-config": "^1.14.0", "eslint-plugin-jest": "^27.2.1", "eslint-plugin-prettier": "^4.2.1", "eslint-plugin-vue": "^9.9.0", "esno": "^0.14.1", "fs-extra": "^10.1.0", "http-server": "^14.0.0", "inquirer": "^8.2.2", "is-ci": "^3.0.0", "less": "^4.1.2", "lint-staged": "12.3.7", "npm-run-all": "^4.1.5", "picocolors": "^1.0.0", "postcss": "^8.4.12", "postcss-html": "^1.4.1", "postcss-less": "^6.0.0", "prettier": "^2.6.2", "pretty-quick": "^3.1.1", "rimraf": "^3.0.2", "rollup": "^2.70.2", "rollup-plugin-visualizer": "^5.6.0", "stylelint": "^14.7.1", "stylelint-config-prettier": "^9.0.3", "stylelint-config-recommended": "^7.0.0", "stylelint-config-recommended-vue": "^1.4.0", "stylelint-config-standard": "^25.0.0", "stylelint-order": "^5.0.0", "ts-jest": "^27.0.7", "ts-node": "^10.7.0", "typescript": "^4.6.3", "vite": "^3.0.2", "vite-plugin-compression": "^0.5.1", "vite-plugin-html": "^3.2.0", "vite-plugin-imagemin": "^0.6.1", "vite-plugin-mkcert": "^1.10.1", "vite-plugin-mock": "^2.9.6", "vite-plugin-optimize-persist": "^0.1.2", "vite-plugin-package-config": "^0.1.1", "vite-plugin-purge-icons": "^0.8.2", "vite-plugin-pwa": "^0.12.3", "vite-plugin-style-import": "^2.0.0", "vite-plugin-svg-icons": "^2.0.1", "vite-plugin-vue-setup-extend-plus": "^0.1.0", "vite-plugin-windicss": "^1.8.7", "vue-eslint-parser": "^8.3.0", "vue-tsc": "^0.33.9", "windicss": "^3.5.6"}, "resolutions": {"bin-wrapper": "npm:bin-wrapper-china", "rollup": "^2.72.0"}, "repository": {"type": "git", "url": "git+https://github.com/jeecgboot/jeecgboot-vue3.git"}, "license": "MIT", "bugs": {"url": "https://github.com/jeecgboot/jeecgboot-vue3/issues"}, "homepage": "https://github.com/jeecgboot/jeecgboot-vue3", "engines": {"node": "^12 || >=14"}, "vite": {"optimizeDeps": {"include": ["@ant-design/colors", "@ant-design/icons-vue", "@vueuse/core", "@vueuse/shared", "ant-design-vue", "axios", "clipboard", "codemirror", "codemirror/addon/fold/brace-fold.js", "codemirror/addon/fold/comment-fold.js", "codemirror/addon/fold/foldcode.js", "codemirror/addon/fold/foldgutter.js", "codemirror/addon/fold/indent-fold.js", "codemirror/addon/hint/anyword-hint.js", "codemirror/addon/hint/show-hint.js", "codemirror/addon/selection/active-line.js", "codemirror/mode/clike/clike.js", "codemirror/mode/css/css.js", "codemirror/mode/javascript/javascript.js", "codemirror/mode/markdown/markdown.js", "codemirror/mode/python/python.js", "codemirror/mode/r/r.js", "codemirror/mode/shell/shell.js", "codemirror/mode/sql/sql.js", "codemirror/mode/swift/swift.js", "codemirror/mode/vue/vue.js", "codemirror/mode/xml/xml.js", "cron-parser", "cropperjs", "crypto-js/aes", "crypto-js/enc-base64", "crypto-js/enc-utf8", "crypto-js/md5", "crypto-js/mode-ecb", "crypto-js/pad-pkcs7", "dom-align", "echarts", "echarts/charts", "echarts/components", "echarts/core", "echarts/renderers", "emoji-mart-vue-fast/src", "intro.js", "lodash-es", "md5", "nprogress", "path-to-regexp", "pinia", "print-js", "qrcode", "qs", "resize-observer-polyfill", "showdown", "sortablejs", "vditor", "vue", "vue-i18n", "vue-print-nb-jeecg/src/printarea", "vue-router", "vue-types", "vxe-table", "vxe-table-plugin-antd", "xe-utils", "xss"]}}}