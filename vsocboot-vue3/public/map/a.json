{"log_fields_mapping": [{"key": "$['a']", "bind": [{"socLogFieldName": "srcAssetId", "socLogFieldType": "String", "defaultValue": "", "valueArray": [{"old": "(\\d+)", "new": "value$1", "type": 4, "start": "", "end": ""}], "condition": [{"exp": "noaction([group:0])", "name": "variate:name"}, {"exp": "if(variate:name=='aaa'){return noaction('high')}else{return 'low'}", "name": "variate:asd"}]}]}, {"key": "$['b']", "bind": [{"socLogFieldName": "srcAssetName", "socLogFieldType": "String", "defaultValue": "", "valueArray": [{"old": "2", "new": "发", "type": 1, "start": "", "end": ""}]}]}]}