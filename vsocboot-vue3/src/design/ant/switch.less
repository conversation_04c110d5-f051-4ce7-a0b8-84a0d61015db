// 定义开关组件的宽度，使用变量方便后续修改和维护
@switch-width: 32;
// 计算开关组件宽度的一半，用于后续计算开关手柄的位置
@half-switch-width: @switch-width / 2;


// 定义 antd 开关组件的样式
button.ant-switch {
  // 设置开关组件的宽度，将变量转换为像素单位
  width: @switch-width * 1px;
  // 设置开关组件的最小宽度，防止在某些情况下宽度过小
  min-width: @switch-width * 1px;
  // 设置开关组件的高度
  height: 10px;
  // 设置行高，确保内容垂直居中
  line-height: 10px;
  // 垂直对齐方式，使开关组件与其他元素在垂直方向上对齐
  vertical-align: top;
  // 设置开关组件的圆角，使其具有圆角外观
  border-radius: 5px;
  // 设置开关组件的背景颜色，使用半透明的白色
  background: @input-bg-disabled;
  // 设置开关组件的顶部位置
  top: 0;

  // 当开关处于选中状态时的样式
  &.ant-switch-checked {
    // 设置选中状态下开关的背景颜色，使用半透明的蓝色
    background: rgba(64, 148, 255, 0.4);

    // 选中状态下开关手柄的样式
    .ant-switch-handle {
      // 计算并设置选中状态下开关手柄的左侧位置，使其移动到右侧
      left: calc(100% - (@half-switch-width * 1px));
      &:before {
        // 设置开关手柄的背景颜色为蓝色
        background: @primary-color;
      }
    }
  }

  // 当开关处于禁用状态时的样式
  &.ant-switch-disabled{
    // 设置禁用状态下的透明度为 1，即不透明
    opacity: 1;

    // 未选中状态下禁用开关手柄的样式
    .ant-switch-handle:before{
      // 设置未选中禁用状态下开关手柄的背景颜色为灰色
      background: #888888;
    }
    // 选中状态下禁用开关的样式
    &.ant-switch-checked{
      // 设置选中禁用状态下开关的背景颜色，使用半透明的蓝色
      background: rgba(64, 148, 255, 0.2);
      .ant-switch-handle:before{
        // 设置选中禁用状态下开关手柄的背景颜色为深蓝色
        background: #133866;
      }
    }
  }

  // 开关手柄的样式
  .ant-switch-handle {
    // 设置开关手柄的宽度
    width: 16px;
    // 设置开关手柄的高度
    height: 16px;
    // 初始状态下开关手柄的左侧位置，使其位于开关组件的左侧
    left: 0;
    // 设置开关手柄的顶部位置，使其垂直居中
    top: -3px;
    // 设置开关手柄的圆角，使其呈圆形
    border-radius: 50%;

    &:before {
      // 设置开关手柄的背景颜色为白色
      background: #FFFFFF;
      // 设置开关手柄的阴影效果，增加立体感
      box-shadow: 0 0 4px 0 rgba(0, 0, 0, 0.1);
    }
  }

  // 开关内部文本的样式
  .ant-switch-inner {
    // 设置开关内部文本的上外边距
    margin: -3px 0 0 0;
    // 设置开关内部文本的对齐方式为左对齐
    text-align: left;
    // 设置开关内部文本的左内边距，根据开关宽度计算
    padding-left: calc((@switch-width * 1px) + 8px);
    // 设置开关内部文本的最小宽度
    min-width: 100px;
    // 设置开关内部文本的宽度为自动，根据内容自适应
    width: auto;
    // 设置开关内部文本的字体大小
    font-size: @font-size-base;
    // 设置开关内部文本的行高
    line-height: @line-height-base;
  }
}
