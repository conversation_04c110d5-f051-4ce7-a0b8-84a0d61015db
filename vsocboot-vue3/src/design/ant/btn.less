.ant-btn {
  font-size: 14px!important;
  line-height: 24px!important;
  padding: 0 16px;
  border-radius: 6px;
  min-width: 80px;
  text-align: center;
  box-sizing: border-box;
  gap: 4px;
  display: flex!important;
  align-items: center;
  justify-content: center;
  .anticon{
    font-size: 16px!important;
  }
  &.ax-btn-pre{
    padding-left: 15px;
  }
  &.ax-btn-suf{
    padding-right: 15px;
  }
  //小号button
  &.ant-btn-sm{
    min-width: 64px;
    font-size: 12px!important;
    line-height: 16px!important;
    border-radius: 4px;
    gap: 4px;
    padding: 0 8px;
    .ax-icon{
      font-size: 16px!important;
    }
    &.ax-btn-pre{
      padding-left: 7px;
    }
    &.ax-btn-suf{
      padding-right: 7px;
    }
  }
  &-icon-only,&.ant-dropdown-trigger{
    min-width: 20px!important;
    justify-content: center;
  }
  //只显示文字和图标
  &.ant-btn-text{
    font-size: 13px!important;
    line-height: 16px!important;
  }
  &[preicon]{
    min-width: 88px;
    padding: 0 16px 0 15px ;
    &.ant-btn-sm{
      min-width: 64px;
      padding: 0 8px 0 7px;
      span{
        font-size: 12px;
      }
    }

  }


  &-link:hover,
  &-link:focus,
  &-link:active {
    border-color: transparent !important;
  }

  & > span + span,& > .anticon + span, & > span + .anticon ,& > span + .soc {
    //margin-left: 4px!important;
  }
}

html[data-theme='dark']{
  .ant-btn{
    /* 默认按钮 */
    color: #FFFFFF;
    border: 1px solid @button-default-border;
    &:focus{
      border: 1px solid @button-default-border;
      color: #FFFFFF;
    }
    &:active,&:hover{
      /* 字体色/白0.2 */
      background-color: @button-default-hover!important;
      /* 字体色/白0.2 */
      border: 1px solid @button-default-border;
      color: #FFFFFF;
    }
    &.is-disabled{
      border: 1px solid @border-disabled;
      color: @font-color-disabled;
      background: transparent;
      cursor: not-allowed;
    }

    &-primary{
      color: @white;
      background-color: @primary-color;
      border: 0!important;
      box-shadow: none!important;
      display: flex;
      align-items: center;
      &:hover,&:active {
        color: @white;
        background-color: @primary-bg-disabled!important;
        border: 0!important;
      }
      &:focus{
        border: 0!important;
        background-color: @primary-color!important;
        color: inherit!important;
      }
      &.is-disabled {
        border: 0!important;
        background: @primary-bg-disabled!important;
        color:@font-color-disabled!important;
      }
      &.ant-btn-background-ghost{
        border: 1px solid @primary-bg-disabled!important;
        background: transparent!important;
        color: @primary-color!important;
        &:hover ,&:active{
          border: 1px solid @primary-bg-disabled!important;
          background: @primary-bg-hover!important;
        }
        &:focus{
          border: 1px solid @primary-bg-disabled!important;
          color:@primary-color;
        }
      }
    }
    &-text{
      &:focus{
        border: inherit!important;
        background-color: inherit!important;
        color: inherit!important;
      }
      &:hover, &:active{
        background-color: @button-default-hover!important;
        color: #FFFFFF;
        border: 0!important;
      }
      &.is-disabled{
        color: @button-default-disable;
        background: transparent;
      }
    }
    &[disabled]{
      &:hover{
        background: none!important;
        color: @font-color-disabled;
        border-color: @border-disabled!important;
      }
    }
  }




}

//--------------图标按钮 start---------------------------------
.ax-icon-button{
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  border-radius: 4px;
  color: @font-color-default;
  width: 32px;
  height: 32px;
  font-size: 20px;
  .ax-icon{
    height: 20px;
    width: 20px;
    font-size: 20px!important;
    line-height: 20px !important
  }
  &:hover{
    background: rgba(255, 255, 255, 0.1);
  }
  &.is_del{
    width: auto;
    &:hover{
      color: #F85058;
      background: transparent;
    }
  }
  &.is-disabled{
    color: @button-default-disable;
    pointer-events: none;
    cursor: not-allowed;
  }
  &.primary-button{
    color: @primary-color;
    &:hover{
      background: @button-primary-hover-color!important;
    }
    &.is-disabled{
      color: @button-primary-disable;
    }
  }
  &.ax-icon-large{
    width: 40px;
    height: 40px;
    font-size: 24px;
    .ax-icon{
      height: 24px;
      width: 24px;
      font-size: 24px!important;
      line-height: 24px !important
    }
  }

  &.ax-icon-small{
    width: 24px;
    height: 24px;
    font-size: 16px;
    .ax-icon{
      width: 16px;
      height: 16px;
      font-size: 16px!important;
      line-height: 16px !important
    }
  }

  &.ax-icon-smallest{
    width: 16px;
    height: 16px;
    font-size: 14px;
    .ax-icon{
      width: 14px;
      height: 14px;
      font-size: 14px!important;
      line-height: 14px !important
    }
  }
}
//--------自定义--------------
.ax-btn{
  display: flex;
  align-items: center;
  justify-content: center;
  color:@font-color-default;
  &.ax-btn-border{
    border: 1px solid @border-color-01;
    border-radius: 8px;
  }
  &:hover{
    background: rgba(255, 255, 255, 0.1);
  }
}

//--------------图标按钮 end---------------------------------
//--------------label start---------------------------------
.ax-label{
  padding: 4px 8px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 4px;
  display: flex;
  align-items: center;
  gap: 4px;
  color: rgba(255, 255, 255, 0.8);
  font-size: 12px;
  font-weight: normal;
  line-height: 16px;
  .soc,.ax-icon{
    font-size: 14px;
    line-height: 14px !important
  }
  &.ax-color-icon{
    padding: 4px;
    .soc{
      font-size: 14px;
      line-height: 14px !important
    }
  }
}
.ax-label-large{
  padding: 8px;
}
.ax-label-primary{
  color: @color-primary!important;
  background:@color-primary-bg!important;
}
.ax-label-purple{
  color: @color-purple!important;
  background: @color-purple-bg!important;
}
.ax-label-red{
  color: @color-red!important;
  background: @color-red-bg!important;
}
.ax-label-cyan{
  color: @color-cyan!important;
  background:@color-cyan-bg!important;
}
.ax-label-green{
  color: @color-green!important;
  background:@color-green-bg!important;
}
.ax-label-yellow{
  color: @color-yellow!important;
  background:@color-yellow-bg!important;
}
.ax-label-orange{
  color: @color-orange!important;
  background:@color-orange-bg!important;
}
.ax-label-default{
  background: @color-default-bg!important;
  color: @color-default!important;
}

//--------------label end---------------------------------
//--------------badge start---------------------------------
.badge-red{
  .ant-badge-count{
    background: @color-red!important;
  }
}
.ant-badge-count{
  min-width: 16px!important;
  height: 16px!important;
  line-height: 16px!important;
  border-radius: 8px!important;
}
//--------------badge end---------------------------------

//------------color start------------------
.ax-color-default{
  color: @color-default!important;
}
.ax-color-primary{
  color: @color-primary!important;
}
.ax-color-purple{
  color: @color-purple!important;
}
.ax-color-red{
  color: @color-red!important;
}
.ax-color-cyan{
  color: @color-cyan!important;
}
.ax-color-green{
  color: @color-green!important;
}
.ax-color-yellow{
  color: @color-yellow!important;
}
.ax-color-orange{
  color: @color-orange!important;
}
.ax-color-red-orange{
  color: @color-red-orange!important;
}
//选中渐变字色
.ax-color-active{
  background: @font-active-color;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-fill-color: transparent;
  font-weight: 600;
}
//---------------color end--------------
.ax-bg-primary{
  background: @primary-color!important;
  border-radius: 4px;
}
.ax-bg-active{
  background:@menu-active-bg;
  border-radius: 4px;
}
.ax-bg-default{
  background: @border-color-01;
}
.ax-bg-dark1{
  background: @dark-bg1;
  border-radius: 8px;
}
.ax-bg-dark2{
  background: @dark-bg2;
  border-radius: 8px;
}
/*
  类用于实现文本单行显示，超出部分以省略号表示
  white-space: nowrap; 确保文本不会换行，保持在一行显示
  overflow: hidden; 隐藏超出容器宽度的文本
  text-overflow: ellipsis; 当文本超出容器宽度时，用省略号表示被截断的部分
  width: 100%; 让元素宽度占满其父容器
  word-break 属性移除，因为它与 white-space: nowrap 冲突
*/
.ax-text-ellipsis {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  word-break: keep-all;
  width: 100%;
}


