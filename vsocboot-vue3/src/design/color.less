html {
  // header
  --header-bg-color: rgba(255, 255, 255, 0.08);
  --header-bg-hover-color: rgba(255, 255, 255, 0.08);
  --header-active-menu-bg-color: rgba(255, 255, 255, 0.04);

  // sider
  --sider-dark-bg-color: #273352;
  --sider-dark-darken-bg-color: #273352;
  --sider-dark-lighten-bg-color: #273352;
}

@white: #fff;

@content-bg: #f4f7f9;

// :export {
//   name: "less";
//   mainColor: @mainColor;
//   fontSize: @fontSize;
// }
@iconify-bg-color: #5551;

// =================================
// ==============border-color=======
// =================================

// Dark-dark
@border-color-dark: #b6b7b9;

// Dark-light
@border-color-shallow-dark: #cececd;

// Light-dark
@border-color-light: @border-color-base;

// =================================
// ==============message==============
// =================================

// success-bg-color
@success-background-color: #f1f9ec;
// info-bg-color
@info-background-color: #e8eff8;
// warn-bg-color
@warning-background-color: #fdf6ed;
// danger-bg-color
@danger-background-color: #fef0f0;

// =================================
// ==============Header=============
// =================================

@header-dark-bg-color: rgba(255, 255, 255, 0.08);
@header-dark-bg-hover-color: rgba(255, 255, 255, 0.08);
@header-light-bg-hover-color: #f6f6f6;
@header-light-desc-color: #7c8087;
@header-light-bottom-border-color: #eee;
// top-menu
@top-menu-active-bg-color: var(--header-active-menu-bg-color);

// =================================
// ==============Menu============
// =================================

// let -menu
@sider-dark-bg-color: var(--sider-dark-bg-color);
@sider-dark-darken-bg-color: var(--sider-dark-darken-bg-color);
@sider-dark-lighten-bg-color: var(--sider-dark-lighten-bg-color);

// trigger
@trigger-dark-hover-bg-color: rgba(255, 255, 255, 0.2);
@trigger-dark-bg-color: rgba(255, 255, 255, 0.1);

// =================================
// ==============tree============
// =================================
// tree item hover background
@tree-hover-background-color: #f5f7fa;
// tree item hover font color
@tree-hover-font-color: #f5f7fa;

// =================================
// ==============link============
// =================================
@link-hover-color: @primary-color;
@link-active-color: darken(@primary-color, 10%);

// =================================
// ==============Text color-=============
// =================================

// Main text color
@text-color-base: @white;

// Label color
@text-color-call-out: #606266;

// Auxiliary information color-dark
@text-color-help-dark: #909399;

// =================================
// ==============breadcrumb=========
// =================================
@breadcrumb-item-normal-color: #999;
// =================================
// ==============button=============
// =================================
@button-primary-disable: rgba(64, 148, 255, 0.4);
@button-default-hover: rgba(255, 255, 255, 0.2);
@button-default-border: rgba(255, 255, 255, 0.2);
@button-default-disable: rgba(255, 255, 255, 0.4);

@button-primary-color: @primary-color;
@button-primary-hover-color: rgba(@primary-color, 0.2);
@button-primary-active-color: darken(@primary-color, 20%);

@button-ghost-color: @white;
@button-ghost-hover-color: lighten(@white, 10%);
@button-ghost-hover-bg-color: #e1ebf6;
@button-ghost-active-color: darken(@white, 10%);

@button-success-color: @success-color;
@button-success-hover-color: lighten(@success-color, 10%);
@button-success-active-color: darken(@success-color, 10%);

@button-warn-color: @warning-color;
@button-warn-hover-color: lighten(@warning-color, 10%);
@button-warn-active-color: darken(@warning-color, 10%);

@button-error-color: @error-color;
@button-error-hover-color: lighten(@error-color, 10%);
@button-error-active-color: darken(@error-color, 10%);

@button-cancel-color: @text-color-call-out;
@button-cancel-bg-color: @white;
@button-cancel-border-color: @border-color-shallow-dark;

// Mouse over
@button-cancel-hover-color: @primary-color;
@button-cancel-hover-bg-color: @white;
@button-cancel-hover-border-color: @primary-color;
@arrow-color: rgba(255, 255, 255, 0.4); //下拉选箭头颜色
@icon-color: rgba(255, 255, 255, 0.6); //图标颜色
@page-background: transparent;
@font-color-white: #ffffff;
@font-color-default: rgba(255, 255, 255, 0.8);
@font-color-1: rgba(255, 255, 255, 0.6);
@font-color-2: rgba(255, 255, 255, 0.5);
@font-color-4: rgba(255, 255, 255, 0.4);
@font-color-disabled: rgba(255, 255, 255, 0.4); // 禁用字体颜色
@primary-bg-disabled: rgba(@primary-color, 0.4); // 选中 + 禁用背景色
@primary-bg-hover: rgba(@primary-color, 0.2); // 选中 + 鼠标悬停背景色
@primary-border-disabled: rgba(@primary-color, 0.4); // 选中 + 禁用边框颜色
@primary-bg-01:rgba(@primary-color, 0.1); // 选中 颜色
@border-disabled: rgba(255, 255, 255, 0.2); // 禁用边框颜色
@bg-disabled: rgba(255, 255, 255, 0.1); // 禁用背景色
@input-bg-disabled: rgba(255, 255, 255, 0.2); // 禁用背景色
@dark-bg3: #28282C;
@dark-bg2: #18191D;
@dark-bg1: #030306;
@dark-table-hover: rgba(255, 255, 255, 0.04);
@border-color: rgba(255, 255, 255, 0.08);
@border-color-01: rgba(255, 255, 255, 0.1);
@border-node-color: rgba(255, 255, 255, 0.5);
@bg-color: rgba(255, 255, 255, 0.08);
@red-color: #ff0000;
@green-color: #00ff00;

@critical-color: #F85058;
@high-color: #FA7650;
@medium-color: #F6A058;
@low-color: #F4C44E;
@information-color: #439ef6;
@closed-color: #888888;
@close-color2: #2ECF99;

@m-text-bg: rgba(48, 140, 255, 0.1);
@m-text-bg2: rgba(48, 140, 255, 0.2);
@m-text-color: #4094FF;
@m-color: #4094FF;
@label-primary-bg: rgba(@primary-color, 0.2); // label 背景色

@tag-bg-color:rgba(64, 148, 255, 0.2);
@tag-font-color:#4094FF;

@fz12: 12px;
@fz13: 13px;
@fz14: 14px;
@fz15: 15px;
@fz16: 16px;
@fz18: 18px;
@fz20: 20px;
@fz22: 22px;
@fz24: 24px;
@fz30: 30px;

@wf-green: #308cff;
@wf-red: #ff0000;
@new-bg-color: #030306;
@new-soc-input-bg-color: rgba(255, 255, 255, 0.1);
@new-soc-hover-bg-color: #343535;
//-----------------menu tab 等 start ----------------
//高亮字色
@font-active-color: linear-gradient(90deg, #4094FF 0%, #F6A058 100%);
//选中
@menu-active-bg: linear-gradient(90deg, rgba(64, 148, 255, 0.25) 0%, rgba(246, 160, 88, 0.25) 100%);
//悬浮
@menu-hover-bg: linear-gradient(90deg, rgba(64, 148, 255, 0.25) 0%, rgba(246, 160, 88, 0.25) 100%);
//-----------------menu tab 等 end ----------------
// -----------------color type start -----------------------------------
@color-red: #F85058;//红
@color-red-bg: rgba(248, 80, 88, 0.1);
@color-red-orange: #FA7650;//红橙
@color-red-orange-bg: rgba(250, 118, 80, 0.2);
@color-orange: #F6A058;//橙,告警
@color-orange-bg: rgba(246, 160, 88, 0.2);
@color-yellow: #F4C44E;//黄
@color-yellow-bg: rgba(244, 196, 78, 0.2);
@color-primary:@primary-color;//青蓝
@color-primary-bg:rgba(64, 148, 255, 0.2);
@color-cyan: #6CE082;//绿
@color-cyan-bg: rgba(48, 220, 168, 0.2);
@color-green: #30DCA8;//绿青
@color-green-bg: rgba(108, 224, 130, 0.2);
@color-purple: #8C5CFA;
@color-purple-bg: rgba(140, 92, 250, 0.2);
@color-default: rgba(255, 255, 255, 0.8);
@color-default-bg: rgba(255, 255, 255, 0.2);

// -----------------color type end -----------------------------------
