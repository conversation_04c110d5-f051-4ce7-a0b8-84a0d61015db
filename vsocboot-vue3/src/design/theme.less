.bg-white {
  background-color: @component-background !important;
}


html[data-theme='light'] {
  .text-secondary {
    color: rgba(0, 0, 0, 0.45);
  }

  /*【美化】自定义table字体颜色*/

  .ant-table {
    color: rgba(0, 0, 0, 0.65);
  }

  /*【美化】自定义table字体颜色*/
  /*【美化】自定义form字体颜色*/

  .ant-select-multiple .ant-select-selection-item-content {
    color: rgba(0, 0, 0, 0.65);
  }

  .ant-input-affix-wrapper > input.ant-input {
    color: rgba(0, 0, 0, 0.65);
    background: transparent !important;
  }

  .ant-select-single.ant-select-show-arrow .ant-select-selection-item, .ant-select-single.ant-select-show-arrow .ant-select-selection-placeholder {
    color: rgba(0, 0, 0, 0.65);
  }

  /*【美化】自定义form字体颜色*/

  .ant-alert-success {
    background-color: #f6ffed;
    border: 1px solid #b7eb8f;
  }

  .ant-alert-error {
    background-color: #fff2f0;
    border: 1px solid #ffccc7;
  }

  .ant-alert-warning {
    background-color: #fffbe6;
    border: 1px solid #ffe58f;
  }

  :not(:root):fullscreen::backdrop {
    background-color: @layout-body-background !important;
  }
}


html[data-theme='dark'] {
  body, .ant-layout {
    background: @dark-bg1 !important;
    color: @white;
  }
  a {
    text-decoration: none;
    color: @primary-color!important;
  }
  .text-secondary {
    color: #8b949e;
  }
  .ant-select-disabled .ant-select-selector {
    background: @bg-color!important;
  }
  .ant-card-grid-hoverable:hover {
    box-shadow: 0 3px 6px -4px rgb(0 0 0 / 48%), 0 6px 16px 0 rgb(0 0 0 / 32%), 0 9px 28px 8px rgb(0 0 0 / 20%);
  }
  .ant-picker-ranges .ant-picker-preset > .ant-tag-blue {
    border-color: @border-color!important;
  }
  .ant-card-grid {
    box-shadow: 1px 0 0 0 #434343, 0 1px 0 0 #434343, 1px 1px 0 0 #434343, 1px 0 0 0 #434343 inset, 0 1px 0 0 #434343 inset;
  }

  .ant-calendar-selected-day .ant-calendar-date {
    color: rgba(0, 0, 0, 0.8);
  }

  .ant-select-tree li .ant-select-tree-node-content-wrapper.ant-select-tree-node-selected {
    color: rgba(0, 0, 0, 0.9);
  }
  .ant-select-dropdown{
    background-color: @dark-bg3!important;
    line-height: @line-height-base;
    box-shadow: 0px 0px 10px 0px rgba(0, 0, 0, 0.5);
    border-radius: 8px;
    .ant-select-item-option-selected{
      background: rgba(255, 255, 255, 0.1);
    }
  }

  .ant-picker-panel-container{
    background: @dark-bg3!important;
  }
  .ant-picker-input > input {
    border: 0px!important;
    &:focus{
      border: 0px!important;
      box-shadow: none!important;
    }
  }
  /*--------------------------sub menu----------------------------------------*/

  .ant-menu-sub {
    background: @dark-bg3 !important;


    &.ant-menu-sub {
      .ant-menu-item {
        &.ant-menu-item-active {
          background-color: @primary-color !important;
        }
      }


    }
  }

  .ant-menu-dark:not(.ant-menu-horizontal) .ant-menu-item-selected {
    background-color: @dark-bg2 !important;
  }
  .ant-menu-dark.ant-menu-horizontal {
    border-bottom: 0!important;
  }
  /*--------------------------table----------------------------------------*/

  .ant-table {
    color: @font-color-default !important;
    background: @page-background !important;
    .ant-checkbox-inner{
      border: 1.6px solid rgba(255, 255, 255, 0.2)!important;
    }

  }
  .ant-table-bordered .ant-table-thead > tr > th {
    border-top: 0px solid @border-color !important;
  }
  .thead-border-bottom .ant-table-thead > tr > th {
    border-bottom: 1px solid @border-color !important;
  }
  .ant-table-thead > tr > th {
    // background-color: @dark-bg2 !important;
    // background: @dark-bg2 !important;
    background-color: transparent !important;
    background: transparent !important;
    border-bottom: 0px solid @border-color !important;
    border-top: 1px solid @border-color !important;
    color: @font-color-1 !important;
  }
  .ant-table-body {

    &::-webkit-scrollbar-corner {
      background: @border-color-01;
    }
  }
  .ant-table-tbody > tr > td ,.vxe-table--body tbody > tr > td{
    color: @font-color-default!important;
    border-bottom: 1px solid @border-color !important;
    .action-border {
      height: 24px;
      width: auto;
      line-height: 16px;
      border: 1px solid @border-color;
      border-radius: 6px;
      cursor: pointer;
      font-size: 12px;
      padding: 0;
      &.space-action{
        padding: 0 8px!important;
      }
      .ant-space-item{
        .action-divider{
          display: none;
        }
        &:not(:last-child) {
          .ant-btn {
            &:after {
              right: 0;
              content: '';
              height: 8px;
              width: 1px;
              position: absolute;
              top: 4px;
              background: @border-color;
            }

          }
        }
        .ant-btn{
          position: relative;
          padding:4px 10px!important;
          height: 16px!important;
          line-height: 16px;
          display: flex;
          align-items: center;
          &:not(:hover) {
            border: none;
          }
          &:hover{
            border: 0!important;
          }
        }
        span{
          margin: 0px!important;
        }

      }
    }
  }

  .ant-table-tbody > tr.ant-table-row:hover > td:not(.ant-table-cell-fix-right), .ant-table-tbody > tr > td.ant-table-cell-row-hover:not(.ant-table-cell-fix-right) {
    background: @dark-table-hover!important;
  }
  .ant-table-cell-fix-right, .ant-table-tbody > tr.ant-table-row:hover > td.ant-table-cell-fix-right {
    background: @new-bg-color !important;
  }
  .ant-table-thead tr .ant-table-cell-fix-right {
    background: @new-bg-color !important;
  }
  tr.ant-table-expanded-row > td, tr.ant-table-expanded-row:hover > td {
    background: @dark-bg1!important;
  }
  .ant-pagination{
    &.mini{

      .ant-pagination-jump-prev .ant-pagination-item-container .ant-pagination-item-ellipsis,
      .ant-pagination-jump-next .ant-pagination-item-container .ant-pagination-item-ellipsis{
        color:  @font-color-default!important;
      }
      li {
        color: @font-color-default!important;
        a {
          color: @font-color-default!important;
          font-weight: normal;
        }
      }
      .ant-pagination-item-active {
        background-color: rgba(255, 255, 255, 0.1) !important;
        border-color: rgba(255, 255, 255, 0.08)!important;
        a {
          color: @white !important;
          font-weight: bold;
        }
      }
    }
  }
  /*--------------------------drawer----------------------------------------*/

  .ant-drawer-header {
    color: @font-color-white !important;
    border-bottom: 0 !important;
    background-color: @dark-bg2 !important;
  }

  .ant-modal-content {
    color: @font-color-white !important;
    background-color: @dark-bg2 !important;
  }

  .ant-modal-header {
    color: @font-color-white !important;
    background-color: @dark-bg2 !important;
    background: @dark-bg2 !important;
    border-bottom: 1px solid @border-color !important;
  }

  .ant-modal-title{
    color: @font-color-white !important;
  }
  .ant-modal-body {
    color: @font-color-white !important;
    background-color: @dark-bg2 !important;
  }

  .table-cell-text-primary {
    color: @primary-color;
  }

  .table-cell-text-red {
    color: @red-color;
  }
  /*--------------------------form 组件 start----------------------------------------*/
  //form label颜色
  .ant-form-item-label,.ant-radio-group,.ant-checkbox-group{
    color:@font-color-default!important;
    label{
      color:@font-color-default!important;
    }
  }
  // 修改默认样式
  .ant-select-selector  {
    border-color: transparent !important;
    background-color: rgba(255, 255, 255, 0.1) !important;
    // box-shadow: none !important;
  }
  .ant-input-affix-wrapper{
    // box-shadow: none !important;
    border-color: transparent !important;
    background-color: rgba(255, 255, 255, 0.1) !important;
  }
  .ant-select:not(.ant-select-disabled):hover .ant-select-selector  {
    border-color: transparent !important;
    background-color: @new-soc-hover-bg-color !important;

  }
  .ant-input-affix-wrapper > input.ant-input {
    background: transparent !important;
  }
  .ant-input-affix-wrapper:not(.ant-input-affix-wrapper-disabled):hover {
    border-color: transparent !important;
    background-color: @new-soc-hover-bg-color !important;
  }

  .ant-input-search-button {
    background-color: rgba(255, 255, 255, 0.1) !important;
     border-color: transparent !important;
  }
  .ant-select-clear {
    background: @new-soc-hover-bg-color !important; 
    top: 40% !important;
    font-size: 14px;
    height: 14px;
    width: 14px;
    color: @font-color-default!important;
  }

  //下拉选后边的三角符号
  .ant-select-arrow {
    color: @arrow-color !important;
  }

  .ant-radio{
    &-disabled{
      .ant-radio-inner{
        border: 1.6px solid @border-disabled!important;
        background: @bg-disabled!important;
        color: @font-color-disabled;
      }
    }
    &.ant-radio-checked{
      &.ant-radio-disabled{
        .ant-radio-inner{
          border: 1.6px solid @primary-border-disabled!important;
          background: transparent!important;
          &:after{
            background: @primary-bg-disabled!important;
          }
        }

      }
    }
  }
  .ant-checkbox{
    &-disabled{
      .ant-checkbox-inner{
        border: 1.6px solid @border-disabled!important;
        background: @bg-disabled!important;
        color: @font-color-disabled;
      }
    }
    &.ant-checkbox-checked{
      &.ant-checkbox-disabled{
        .ant-checkbox-inner{
          border: 1.6px solid @primary-border-disabled!important;
          background: @primary-bg-disabled!important;
        }

      }
    }
  }
/*--------------------------form 组件 end----------------------------------------*/
/*--------------------------tabs----------------------------------------*/

  .ant-tabs {
    .ant-tabs-nav::before {
      border-bottom: 0 !important;
    }
    .ant-tabs-tab {
      color: rgba(255, 255, 255, 0.6);
      &:hover {
        color: inherit;
      }
      .ant-tabs-tab-btn {
        color: inherit;
        transition: transform 0.3s ease;
        &:active {
          transform: scale(0.95);
        }
      }
    }
    .ant-tabs-ink-bar {
      display: none;
    }
    .ant-tabs-tab-active .ant-tabs-tab-btn {
      color: transparent !important;
      @apply siem-gradient-text;
      font-weight: bolder;
    }
  }
/*----------------------------table----------------------------------------*/
  .ant-table-column-sorter-up, .ant-table-column-sorter-down {
    font-size: 8px;
    color:rgba(255, 255, 255, 0.2);
  }

  .ant-table-column-title {
    flex: none;
  }
  .ant-table-column-sorters {
    justify-content: flex-start;
  }
  /*--------------------------ant-picker----------------------------------------*/
  .ant-picker{
    background: @new-soc-input-bg-color !important;
    border-color: transparent !important;
  }
  /*--------------------------ant-picker end----------------------------------------*/
  /*--------------------------input----------------------------------------*/
  .ant-input {
    border-color: transparent !important;
    background: @new-soc-input-bg-color !important;
    &:hover {
      border-color: transparent !important;
      background: @new-soc-hover-bg-color !important;
    }
    &-disabled{
      background: @input-bg-disabled!important;
      color: @font-color-disabled!important;
    }
  }
  .ant-input-number-input {
    background: @new-soc-input-bg-color !important;
    border-color: transparent!important;
    &:hover {
      border-color: transparent !important;
      background: @new-soc-hover-bg-color !important;
    }
    &-disabled{
      background: @input-bg-disabled !important;
      color: rgba(255, 255, 255, 0.4)!important;
    }
  }
  .ant-input-number:hover {
    border-color: transparent !important;
  }
  /*--------------------------input----------------------------------------*/
  .div-custom-bg {
    background: @new-soc-input-bg-color;
    border: 1px solid transparent;
    &:focus {
      border-color: @primary-color !important;
    }
    &:hover {
      background: @new-soc-hover-bg-color;
    }
  }
}
/*--------------------------modal table----------------------------------------*/
.ant-modal-body {
  .ant-table-wrapper {
    padding:0px!important;
    background-color: transparent !important;
  }

  .ant-table {
    background: transparent !important;
    background-color: transparent !important;
  }

  .ant-table-thead > tr > th {
    background: transparent !important;
  }

  table > tr > td {
    border-right: 0px solid #303030 !important;
  }

  .ant-table-thead > tr > th {
    border-top: 0px!important;
    border-bottom: 0px !important;
  }

  .ant-table.ant-table-bordered .ant-table-container {
    border: 0px  !important;
  }

  .soc-basic-table-row__striped td {
    background-color: transparent !important;
  }

  .ant-table.ant-table-bordered > .ant-table-container > .ant-table-content > table > thead > tr > th,
  .ant-table.ant-table-bordered > .ant-table-container > .ant-table-header > table > thead > tr > th,
  .ant-table.ant-table-bordered > .ant-table-container > .ant-table-body > table > thead > tr > th, .ant-table.ant-table-bordered > .ant-table-container > .ant-table-summary > table > thead > tr > th,
  .ant-table.ant-table-bordered > .ant-table-container > .ant-table-content > table > tbody > tr > td, .ant-table.ant-table-bordered > .ant-table-container > .ant-table-header > table > tbody > tr > td,
  .ant-table.ant-table-bordered > .ant-table-container > .ant-table-body > table > tbody > tr > td,
  .ant-table.ant-table-bordered > .ant-table-container > .ant-table-summary > table > tbody > tr > td, .ant-table.ant-table-bordered > .ant-table-container > .ant-table-content > table > tfoot > tr > th, .ant-table.ant-table-bordered > .ant-table-container > .ant-table-header > table > tfoot > tr > th,
  .ant-table.ant-table-bordered > .ant-table-container > .ant-table-body > table > tfoot > tr > th, .ant-table.ant-table-bordered > .ant-table-container > .ant-table-summary > table > tfoot > tr > th, .ant-table.ant-table-bordered > .ant-table-container > .ant-table-content > table > tfoot > tr > td,
  .ant-table.ant-table-bordered > .ant-table-container > .ant-table-header > table > tfoot > tr > td, .ant-table.ant-table-bordered > .ant-table-container > .ant-table-body > table > tfoot > tr > td,
  .ant-table.ant-table-bordered > .ant-table-container > .ant-table-summary > table > tfoot > tr > td {
    border-right: 0px solid #303030 !important;
  }
  .soc-basic-table-form-container{
    padding-top : 0px!important;

  }

}
