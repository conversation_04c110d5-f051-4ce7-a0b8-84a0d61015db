import {useI18n} from "/@/hooks/web/useI18n";
import {typeOptions} from "/@/views/integrationManagement/IntegrationManagement.data";
import {HostLog, NetworkLog, OperationLog, SecurityLog} from "/@/utils/ckTable";

const {t} = useI18n();
const tp = (name) => {
  return t('common.' + name);
}
/**
 * 列表配置展示列
 */
export const TABLE_CACHE_KEY = {
  'risk': 'riskEvent',
  'correlation': 'correlationEvent',
  'ml': 'mlEvent',
  'unaddedAsset': 'unaddedAsset',
  'assetScan': 'assetScan',
  'assetport': 'assetport',
  'sendConfiguration': 'sendConfiguration',
  'recipient': 'recipient',
  'suspiciousProcessesCollect': 'suspiciousProcessesCollect',
  'badactors': 'badactors',
  'badactors2': 'badactors2',
  'badactorsHistory': 'badactorsHistory',
  'investigation': 'investigation',
  'reportView': 'reportView',
  'reportTemplate': 'reportTemplate',
  'reportTask': 'reportTask',
  'reportInfo': 'reportInfo',
  'assetBase': 'assetBase',
  'workflowHistory': 'workflowHistory',
  'mlRule': 'mlRule',
  'correlationRule': 'correlationRule',
  'detectionRule': 'detectionRule',
  'whitelist': 'whitelist',
  'parserule': 'parserule',
  'relatedRule': 'relatedRule',
  'logSourceManager': 'logSourceManager',
  'systemtenant': 'systemtenant',
  'systemrole': 'systemrole',
  'systemuser': 'systemuser',
  'mDPSManagement': 'mDPSManagement',
  'agentManagementLinux': 'agentManagementLinux',
  'agentManagementWindow': 'agentManagementWindow',
  'quartzindex': 'quartzindex',
  'loginlog': 'loginlog',
  'operationlog': 'operationlog',
  'workflowset': 'workflowset',
  'threatHunting': 'threatHunting',
  'field': 'field',
  'logTable': 'logTable',
  'flowLogTable1': 'flowLogTable1',
  'flowLogTable2': 'flowLogTable2',
  'flowLogTable3': 'flowLogTable3',
  'flowLogTable4': 'flowLogTable4',
  'correlationML1': 'correlationML1',
  'correlationML2': 'correlationML2',
  'correlationML3': 'correlationML3',
  'correlationML4': 'correlationML4',
  'workflowExecution': 'workflowExecution',
  'riskEventLog': 'riskEventLog',
  'riskEventLog2': 'riskEventLog2',
  'plugin': 'plugin',
  'suspiciousProcesses': 'suspiciousProcesses',
  'plugin2': 'plugin2',
  'integrationAsset': 'integrationAsset',
  'integrationApiAsset': 'integrationApiAsset',
  ruleLogSource: 'ruleLogSource',
  unparselog: 'unparselog',
  riskDetail: 'riskDetail',
  proposalList: 'proposalList',
  userSelect: 'userSelect',
  httpWafEventList: 'httpWafEventList',
}

export const RULE_SCOPE = {
  1: tp('ExclusiveRule'),
  2: tp('ShareableRule')
}
export const SEVERITY = {
  "1": tp('Critical'),
  '2': tp('High'),
  '3': tp('Middle'),
  '4': tp('Low'),
  '5': tp('Information'),
}
/**
 * 等级，数字类型转文字，为了和risk显示保持一致做转换
 */
export const SEVERITY_NUM_STR = {
  "1": 'Critical',
  '2': 'High',
  '3': 'Middle',
  '4': 'Low',
  '5': 'Information',
}


export const LOG_TABLE = {
  [SecurityLog]: tp('SecurityLog'),
  [HostLog]: tp('HostLog'),
  [NetworkLog]: tp('NetworkLog'),
  [OperationLog]: tp('OperationLog')
}

export const TIME_SELECT = [
  {label: tp('Last1hour'), value: 1},
  {label: tp('Last12hours'), value: 2},
  {label: tp('Last1day'), value: 3},
  {label: tp('Last1week'), value: 4},
  {label: tp('Last1month'), value: 5},
]

export const TIME_SELECT2 = [
  {label: tp('Last1hour'), value: 1},
  {label: tp('Last3hour'), value: 2},
  {label: tp('Last12hours'), value: 3},
  {label: tp('Last1day'), value: 4},
  {label: tp('Last1week'), value: 5},
  {label: tp('Last1month'), value: 6},
]

export const RISK_TYPE_SELECT = [
  {label: tp('RiskSecurity'), text: tp('RiskSecurity'), value: '1'},
  {label: tp('RiskEndpoint'), text: tp('RiskEndpoint'), value: '2'},
]

/**
 * 状态，1开启，2关闭
 */
export const RISK_STATUS_SELECT = [
  {label: tp('Closed'), text: tp('Closed'), value: '2'},
  {label: tp('Unclosed'), text: tp('Unclosed'), value: '1'},
]

export const CORRELATION_STATUS_SELECT = [
  {label: tp('Closed'), text: tp('Closed'), value: '1'},
  {label: tp('Unclosed'), text: tp('Unclosed'), value: '0'},
]

export const CLOSE_STATUS_SELECT = [
  {label: tp('Closed'), text: tp('Closed'), value: '1'},
  {label: tp('Unclosed'), text: tp('Unclosed'), value: '0'},
]

/**
 * 等级下拉选值
 */
export const SEVERITY_SELECT = [
  {label: tp('Low'), text: tp('Low'), value: 'Low'},
  {label: tp('Medium'), text: tp('Medium'), value: 'Medium'},
  {label: tp('High'), text: tp('High'), value: 'High'},
  {label: tp('Critical'), text: tp('Critical'), value: 'Critical'},
  {label: tp('Information'), text: tp('Information'), value: 'Informational'},
]

export const SEVERITY3_SELECT = [
  {label: tp('Low'), text: tp('Low'), value: 'Low'},
  {label: tp('Medium'), text: tp('Medium'), value: 'Medium'},
  {label: tp('High'), text: tp('High'), value: 'High'},
  {label: tp('Critical'), text: tp('Critical'), value: 'Critical'},
]

export const SEVERITY2_SELECT = [
  {label: tp('Critical'), text: tp('Critical'), value: '1'},
  {label: tp('High'), text: tp('High'), value: '2'},
  {label: tp('Medium'), text: tp('Medium'), value: '3'},
  {label: tp('Low'), text: tp('Low'), value: '4'},
  {label: tp('Information'), text: tp('Information'), value: '5'},
]

export const INVEST_SEVERITY_SELECT = [
  {label: tp('Critical'), text: tp('Critical'), value: '1'},
  {label: tp('High'), text: tp('High'), value: '2'},
  {label: tp('Medium'), text: tp('Medium'), value: '3'},
  {label: tp('Low'), text: tp('Low'), value: '4'},
  {label: tp('Safe'), text: tp('Safe'), value: '5'},
]

export const CORRELATION_SEVERITY_SELECT = [
  {label: tp('Low'), text: tp('Low'), value: '1'},
  {label: tp('Medium'), text: tp('Medium'), value: '2'},
  {label: tp('High'), text: tp('High'), value: '3'},
  {label: tp('Critical'), text: tp('Critical'), value: '4'},
  {label: tp('Information'), text: tp('Information'), value: '5'},
]
/**
 * 事件验证状态
 */
export const TRIAGE_STATUS_SELECT = [
  {label: tp('Untriaged'), text: tp('Untriaged'), value: '0'},
  {label: t('routes.RiskEventLogView.true'), text: t('routes.RiskEventLogView.true'), value: '1'},
  {label: t('routes.RiskEventLogView.false'), text: t('routes.RiskEventLogView.false'), value: '2'},
  {label: t('routes.RiskEventLogView.other'), text: t('routes.RiskEventLogView.other'), value: '3'},
]
/**
 * 时间类型
 */
export const TIME_TYPE_SELECT = [
  {label: tp('min'), text: tp('min'), value: 1},
  {label: tp('hour'), text: tp('hour'), value: 2},
  {label: tp('day'), text: tp('day'), value: 3},
]

// 1:ml,2:order ml,3:content ml

export const ML_RULE_TYPE_SELECT = [
  {label: tp('Statistic'), text: tp('Statistic'), value: '1'},
  {label: tp('Order'), text: tp('Order'), value: '2'},
  {label: tp('Content'), text: tp('Content'), value: '3'},
]

export const ML_RULE_TYPE_NAME = {
  1: tp('Statistic'),
  2: tp('Order'),
  3: tp('Content'),
}


/**
 * 资产安全状态
 */
export const ASSET_SAFE_SELECT = [
  {label: tp('Safe'), text: tp('Safe'), value: '0'},
  {label: tp('Attacked'), text: tp('Attacked'), value: '1'},
]
/**
 * 资产等级
 */
export const ASSET_LEVEL_SELECT = [
  {label: tp('Core'), text: tp('Core'), value: '3'},
  {label: tp('Important'), text: tp('Important'), value: '2'},
  {label: tp('Normal'), text: tp('Normal'), value: '1'},
]
/**
 * 资产状态
 */
export const ASSET_STATE_SELECT = [
  {label: tp('Online'), text: tp('Online'), value: '1',},
  {label: tp('Offline'), text: tp('Offline'), value: '2',},
  {label: tp('Unknown'), text: tp('Unknown'), value: '3',}
];

export const ASSET_MAIN_TYPE_SELECT = typeOptions;


export const ASSET_SOURCE_SELECT = [
  {
    label: t('routes.assetBase.Manually'),
    text: t('routes.assetBase.Manually'),
    value: '1',
  },
  {
    label: t('routes.assetBase.MDPS'),
    text: t('routes.assetBase.MDPS'),
    value: '2',
  },
  {
    label: t('routes.assetBase.Assetdiscovery'),
    text: t('routes.assetBase.Assetdiscovery'),
    value: '3',
  },
  {
    label: t('routes.assetBase.Import'),
    text: t('routes.assetBase.Import'),
    value: '4',
  },
];

export const ASSET_LOG_SOURCE_SELECT = [
  {
    label: t('common.yes'),
    text: t('common.yes'),
    value: '1',
  },
  {
    label: t('common.no'),
    text: t('common.no'),
    value: '2',
  },
];

export const ASSET_SOURCE_STATUS_SELECT = [
  {
    label: t('routes.assetBase.normal'),
    text: t('routes.assetBase.normal'),
    value: '1',
  },
  {
    label: t('routes.assetBase.abnormal'),
    text: t('routes.assetBase.abnormal'),
    value: '2',
  },
];


export const TICKET_TYPE_SELECT = [
  {
    label: tp('adminTnternalTicket'),
    text: tp('adminTnternalTicket'),
    value: '1'
  },
  {
    label: tp('ticketIssuedToTenant'),
    text: tp('ticketIssuedToTenant'),
    value: '2'
  },
  {
    label: tp('ticketSubmittedByTenant'),
    text: tp('ticketSubmittedByTenant'),
    value: '3'
  },
  {
    label: tp('tenantInternalTicket'),
    text: tp('tenantInternalTicket'),
    value: '4'
  },
];

export const WORKFLOW_STATUS_SELECT = [
  {
    label: tp('Closed'),
    text: tp('Closed'),
    value: '1'
  },
  {
    label: tp('Processing'),
    text: tp('Processing'),
    value: '2'
  }
];

export const PARSE_RULE_LOG_TYPE_SELECT = [
  {
    label: t('routes.parserule.security'),
    text: t('routes.parserule.security'),
    value: 'Security Log',
  },
  {
    label: t('routes.parserule.host'),
    text: t('routes.parserule.host'),
    value: 'Host Log',
  },
  {
    label: t('routes.parserule.network'),
    text: t('routes.parserule.network'),
    value: 'Network Log',
  },
  {
    label: t('routes.parserule.operation'),
    text: t('routes.parserule.operation'),
    value: 'Operation Log',
  },
];

export const PARSE_RULE_METHOD_SELECT = [
  {
    label: 'Json',
    text: 'Json',
    value: '1',
  },
  {
    label: t('routes.parserule.RegularExpressions'),
    text: t('routes.parserule.RegularExpressions'),
    value: '2',
  },
  {
    label: t('routes.parserule.Separator'),
    text: t('routes.parserule.Separator'),
    value: '3',
  },
];


export const getEventLevelClass = (value, status) => {
  if (!value) {
    return '';
  }
  //已关闭，绿色
  if (status == 2) {
    return 'event_close';
  }
  const colorMap = {
    'critical': 'severity1',
    'high': 'severity2',
    'middle': 'severity3',
    'low': 'severity4',
    'info': 'severity5',
    '1': 'severity1',
    '2': 'severity2',
    '3': 'severity3',
    '4': 'severity4',
    '5': 'severity5',
  };
  let className = colorMap[value.toLowerCase()];
  if (!className) {
    className = 'severity5';
  }
  return className;
}

export const getEventLevelClass2 = (value, status) => {
  if (!value) {
    return '';
  }
  //已关闭，绿色
  if (status == 2) {
    return 'event_close';
  }
  const colorMap = {
    '1': 'severity1',
    '2': 'severity2',
    '3': 'severity3',
    '4': 'severity4',
    '5': 'severity5',
  };
  let className = colorMap[value.toString()];
  if (!className) {
    className = 'severity5';
  }
  return className;
}

export const AUTHORITY_SELECT = [
  {label: tp('viewOnly'), text: tp('viewOnly'), value: '1'},
  {label: tp('editable'), text: tp('editable'), value: '2'},
];

