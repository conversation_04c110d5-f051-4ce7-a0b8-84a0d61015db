import { Persistent, BasicKeys } from '/@/utils/cache/persistent';
import { CacheTypeEnum, FLOW_MODE, UEBA_MODE, USER_INFO_KEY } from '/@/enums/cacheEnum';
import projectSetting from '/@/settings/projectSetting';
import { TOKEN_KEY, TENANT_ID, SYSTEM_MODE } from '/@/enums/cacheEnum';
import { usePermissionStore } from '/@/store/modules/permission';
import { toRaw } from 'vue';

const { permissionCacheType } = projectSetting;
const isLocal = permissionCacheType === CacheTypeEnum.LOCAL;

/**
 * 获取token
 */
export function getToken() {
  return getAuthCache<string>(TOKEN_KEY);
}

/**
 * 获取登录信息
 */
export function getLoginBackInfo() {
  return getAuthCache(USER_INFO_KEY);
}

/**
 * 获取模式
 */
export function getTenantMode() {
  return getAuthCache<boolean>(SYSTEM_MODE);
}

export function getFlowMode() {
  return getAuthCache<boolean>(FLOW_MODE);
}
export function getUebaMode() {
  return getAuthCache<boolean>(UEBA_MODE);
}

/**
 * true表示当前登录人是运维管理员，不是租户
 */
export function isAdministrator() {
  const loginUser: any = getLoginBackInfo();
  if (loginUser?.tenantType) {
    const tenantType = loginUser.tenantType;
    return getTenantMode() && tenantType == 1;
  } else {
    //登录超时
    return false;
  }
}

/**
 * true表示当前登录的是租户
 */
export function isTenant() {
  const loginUser: any = getLoginBackInfo();
  if (loginUser?.tenantType) {
    const tenantType = loginUser.tenantType;
    const flag = getTenantMode() && tenantType == 2;
    return flag;
  } else {
    //登录超时
    return false;
  }
}

/**
 * 获取租户id
 */
export function getTenantId() {
  return getAuthCache<string>(TENANT_ID);
}

export function getAuthCache<T>(key: BasicKeys) {
  const fn = isLocal ? Persistent.getLocal : Persistent.getSession;
  return fn(key) as T;
}

export function setAuthCache(key: BasicKeys, value) {
  const fn = isLocal ? Persistent.setLocal : Persistent.setSession;
  return fn(key, value, true);
}

/**
 * 设置动态key
 * @param key
 * @param value
 */
export function setCacheByDynKey(key, value) {
  const fn = isLocal ? Persistent.setLocal : Persistent.setSession;
  return fn(key, value, true);
}

/**
 * 获取动态key
 * @param key
 */
export function getCacheByDynKey<T>(key) {
  const fn = isLocal ? Persistent.getLocal : Persistent.getSession;
  return fn(key) as T;
}

/**
 * 移除动态key
 * @param key
 */
export function removeCacheByDynKey<T>(key) {
  const fn = isLocal ? Persistent.removeLocal : Persistent.removeSession;
  return fn(key) as T;
}

/**
 * 移除缓存中的某个属性
 * @param key
 * @update:移除缓存中的某个属性
 * @updateBy:lsq
 * @updateDate:2021-09-07
 */
export function removeAuthCache<T>(key: BasicKeys) {
  const fn = isLocal ? Persistent.removeLocal : Persistent.removeSession;
  return fn(key) as T;
}

export function clearAuthCache(immediate = true) {
  const fn = isLocal ? Persistent.clearLocal : Persistent.clearSession;
  return fn(immediate);
}

/**
 * 登录用户是否有菜单,控制跳转菜单的按钮显示
 */
export function hasHunting(menuPath) {
  const permissionStore = usePermissionStore();
  const menuMap = toRaw(permissionStore.getBackMenuMap);
  return menuMap[menuPath];
}
