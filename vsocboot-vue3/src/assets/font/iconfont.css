@font-face {
  font-family: "SOC"; /* Project id 4211246 */
  src: url('iconfont.eot?t=1749728073211'); /* IE9 */
  src: url('iconfont.eot?t=1749728073211#iefix') format('embedded-opentype'), /* IE6-IE8 */
       url('iconfont.woff2?t=1749728073211') format('woff2'),
       url('iconfont.woff?t=1749728073211') format('woff'),
       url('iconfont.ttf?t=1749728073211') format('truetype'),
       url('iconfont.svg?t=1749728073211#SOC') format('svg');
}

.SOC,.soc {
  font-family: "SOC" !important;
  font-size: 16px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.ax-com-Finish:before {
  content: "\e727";
}

.ax-com-Send:before {
  content: "\e728";
}

.ax-com-Withdraw:before {
  content: "\e726";
}

.ax-com-Loading:before {
  content: "\e725";
}

.ax-ai-Action:before {
  content: "\e724";
}

.ax-com-Drag:before {
  content: "\e722";
}

.ax-com-Twoview:before {
  content: "\e723";
}

.ax-com-Topup:before {
  content: "\e71d";
}

.ax-com-Help:before {
  content: "\e721";
}

.ax-com-Delete:before {
  content: "\e71c";
}

.ax-com-Processing:before {
  content: "\e720";
}

.ax-com-Message:before {
  content: "\e71b";
}

.ax-com-TeleMessage:before {
  content: "\e71e";
}

.ax-com_Telephone:before {
  content: "\e71f";
}

.ax-com-Authorize:before {
  content: "\e71a";
}

.ax-com-Time:before {
  content: "\e719";
}

.ax-com-Submit:before {
  content: "\e718";
}

.ax-com-Association:before {
  content: "\e717";
}

.ax-com-unSee:before {
  content: "\e713";
}

.ax-com-See:before {
  content: "\e715";
}

.ax-com-Datetime:before {
  content: "\e712";
}

.ax-com-Increase:before {
  content: "\e710";
}

.ax-com-Decrease:before {
  content: "\e711";
}

.ax-com-Pause:before {
  content: "\e706";
}

.ax-com-Discontinue:before {
  content: "\e709";
}

.ax-com-Wait:before {
  content: "\e70d";
}

.ax-com-Play:before {
  content: "\e70e";
}

.ax-com-Quit:before {
  content: "\e70f";
}

.ax-com-Score:before {
  content: "\e704";
}

.ax-com-Score-f:before {
  content: "\e705";
}

.ax-com-Compositefilters:before {
  content: "\e703";
}

.ax-com-Cardview:before {
  content: "\e702";
}

.ax-com-Formview:before {
  content: "\e701";
}

.ax-com-Barview:before {
  content: "\e700";
}

.ax-asset-Networkdevice:before {
  content: "\e6ff";
}

.ax-nav-Posture:before {
  content: "\e6fd";
}

.ax-nav-Posture-f:before {
  content: "\e6fe";
}

.ax-setting-Security:before {
  content: "\e6f9";
}

.ax-setting-Network:before {
  content: "\e6fa";
}

.ax-setting-Host:before {
  content: "\e6fb";
}

.ax-setting-Audit:before {
  content: "\e6fc";
}

.ax-ticket-Application:before {
  content: "\e6f3";
}

.ax-ticket-Processed:before {
  content: "\e6f4";
}

.ax-ticket-Viewonly:before {
  content: "\e6f5";
}

.ax-ticket-Carboncopy:before {
  content: "\e6f6";
}

.ax-ticket-Submitted:before {
  content: "\e6f7";
}

.ax-ticket-Pending:before {
  content: "\e6f8";
}

.ax-com-Menu:before {
  content: "\e6f2";
}

.ax-cmo-Attachment:before {
  content: "\e6f1";
}

.ax-com-Enlarge:before {
  content: "\e649";
}

.ax-com-Fullscreen:before {
  content: "\e64a";
}

.ax-com-Reduce:before {
  content: "\e64b";
}

.ax-com-Nofullscreen:before {
  content: "\e64c";
}

.ax-com-Danger:before {
  content: "\e69a";
}

.ax-com-Warning:before {
  content: "\e69d";
}

.ax-com-Success:before {
  content: "\e699";
}

.ax-com-Info:before {
  content: "\e69b";
}

.ax-com-Fault:before {
  content: "\e69c";
}

.ax-com-Upload:before {
  content: "\e70b";
}

.ax-com-Download:before {
  content: "\e714";
}

.ax-Export:before {
  content: "\e708";
}

.ax-Import:before {
  content: "\e70a";
}

.ax-com-Forward:before {
  content: "\e707";
}

.ax-com-Configure:before {
  content: "\e687";
}

.ax-com-Email:before {
  content: "\e716";
}

.ax-com-Add:before {
  content: "\e670";
}

.ax-com-Record:before {
  content: "\e672";
}

.ax-com-Edit:before {
  content: "\e675";
}

.ax-com-Copy:before {
  content: "\e67c";
}

.ax-com-Update:before {
  content: "\e682";
}

.ax-com-Tips:before {
  content: "\e683";
}

.ax-com-More:before {
  content: "\e684";
}

.ax-com-Notice:before {
  content: "\e692";
}

.ax-wh-label:before {
  content: "\e68c";
}

.ax-com-Batch:before {
  content: "\e69e";
}

.ax-com-Setting:before {
  content: "\e70c";
}

.ax-com-Filters:before {
  content: "\e680";
}

.ax-com-Search:before {
  content: "\e679";
}

.ax-com-Close:before {
  content: "\e673";
}

.ax-com-Arrow-right:before {
  content: "\e678";
}

.ax-com-Arrow-left:before {
  content: "\e67a";
}

.ax-com-Arrow-up:before {
  content: "\e67b";
}

.ax-com-Arrow-down:before {
  content: "\e67e";
}

.ax-com-Retract:before {
  content: "\e6ef";
}

.ax-com-Expand:before {
  content: "\e6f0";
}

.ax-nav-AssetManagement-f:before {
  content: "\e632";
}

.ax-nav-Configuration-f:before {
  content: "\e633";
}

.ax-Branch:before {
  content: "\e634";
}

.ax-nav-AssetManagement:before {
  content: "\e635";
}

.ax-nav-Configuration:before {
  content: "\e636";
}

.ax-logrule:before {
  content: "\e637";
}

.ax-nav-Reports-f:before {
  content: "\e638";
}

.ax-nav-Reports:before {
  content: "\e639";
}

.ax-nav-Investigation-f:before {
  content: "\e63a";
}

.ax-nav-RiskCentre:before {
  content: "\e63b";
}

.ax-nav-Dashboard-f:before {
  content: "\e63c";
}

.ax-nav-SOAR:before {
  content: "\e63d";
}

.ax-nav-Investigation:before {
  content: "\e63e";
}

.ax-nav-SOAR-f:before {
  content: "\e63f";
}

.ax-nav-RiskCentre-f:before {
  content: "\e640";
}

.ax-nav-ThreatHunting-f:before {
  content: "\e641";
}

.ax-system:before {
  content: "\e642";
}

.ax-Save:before {
  content: "\e644";
}

.ax-nav-Tickets-f:before {
  content: "\e645";
}

.ax-nav-Tickets:before {
  content: "\e646";
}

.ax-nav-Dashboard:before {
  content: "\e647";
}

.ax-nav-ThreatHunting:before {
  content: "\e648";
}

.ax-a-icon25_Investigation-attck:before {
  content: "\e6e6";
}

.ax-a-icon25_Investigation-event:before {
  content: "\e6e7";
}

.ax-a-icon25_Investigation-attacker:before {
  content: "\e6e8";
}

.ax-a-icon25_Investigation-attribute:before {
  content: "\e6e9";
}

.ax-a-icon25_Investigation-task:before {
  content: "\e6ea";
}

.ax-a-icon25_Investigation-repair:before {
  content: "\e6eb";
}

.ax-a-icon25_Investigation-file:before {
  content: "\e6ec";
}

.ax-a-icon25_Investigation-index:before {
  content: "\e6ed";
}

.ax-a-icon25_Investigation-malware:before {
  content: "\e6ee";
}

.ax-zc-Network:before {
  content: "\e62b";
}

.ax-ac-Application:before {
  content: "\e62c";
}

.ax-system-Ubuntu:before {
  content: "\e62d";
}

.ax-system-SUSE:before {
  content: "\e62e";
}

.ax-zc-Others:before {
  content: "\e62f";
}

.ax-system-CentOS:before {
  content: "\e630";
}

.ax-system-Windows:before {
  content: "\e631";
}

.ax-zc-InternetofThings:before {
  content: "\e628";
}

.ax-zc-Cybersecurity:before {
  content: "\e629";
}

.ax-system-RHEL:before {
  content: "\e62a";
}

.ax-aqcp-EDR:before {
  content: "\e621";
}

.ax-aqcp-firewall:before {
  content: "\e622";
}

.ax-aqcp-IDS:before {
  content: "\e623";
}

.ax-aqcp-GAV:before {
  content: "\e624";
}

.ax-aqcp-SDS:before {
  content: "\e625";
}

.ax-aqcp-IPS:before {
  content: "\e626";
}

.ax-aqcp-XDR:before {
  content: "\e627";
}

.ax-zc-exchangeboard:before {
  content: "\e61c";
}

.ax-zc-cloudserver:before {
  content: "\e61d";
}

.ax-zc-databaseserver:before {
  content: "\e61e";
}

.ax-zc-router:before {
  content: "\e61f";
}

.ax-zc-server:before {
  content: "\e620";
}

.ax-zc-safetyequipment:before {
  content: "\e61b";
}

.ax-nav-riskevents:before {
  content: "\e616";
}

.ax-nav-badactors:before {
  content: "\e617";
}

.ax-nav-correlationevents:before {
  content: "\e618";
}

.ax-nav-suspiciousprocess:before {
  content: "\e619";
}

.ax-nav-mlevents:before {
  content: "\e61a";
}

.ax-bjq-start:before {
  content: "\e60f";
}

.ax-bjq-task:before {
  content: "\e610";
}

.ax-bjq-note:before {
  content: "\e611";
}

.ax-bjq-end:before {
  content: "\e612";
}

.ax-bjq-grab:before {
  content: "\e613";
}

.ax-bjq-connection:before {
  content: "\e614";
}

.ax-bbq-judgement:before {
  content: "\e615";
}

.ax-bjq-list:before {
  content: "\e813";
}

.ax-bjq-investgation:before {
  content: "\e666";
}

.ax-bjq-radiobox:before {
  content: "\e601";
}

.ax-bjq-textfield:before {
  content: "\e602";
}

.ax-bjq-text:before {
  content: "\e603";
}

.ax-bjq-referencebutton:before {
  content: "\e604";
}

.ax-bjq-textbox:before {
  content: "\e605";
}

.ax-bjq-uploadfile:before {
  content: "\e606";
}

.ax-bjq-dropdownbox:before {
  content: "\e607";
}

.ax-bjq-checkbox:before {
  content: "\e608";
}

.ax-bjq-separator:before {
  content: "\e609";
}

.ax-bjq-grid:before {
  content: "\e60a";
}

.ax-bjq-numberbox:before {
  content: "\e60b";
}

.ax-bjq-switch:before {
  content: "\e60c";
}

.ax-bjq-datebox:before {
  content: "\e60d";
}

.ax-bjq-timebox:before {
  content: "\e60e";
}

