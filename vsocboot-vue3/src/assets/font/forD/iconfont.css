@font-face {
  font-family: "ForDashboard"; /* Project id 4602414 */
  src: url('iconfont.eot?t=1727228638506'); /* IE9 */
  src: url('iconfont.eot?t=1727228638506#iefix') format('embedded-opentype'), /* IE6-IE8 */
       url('iconfont.woff2?t=1727228638506') format('woff2'),
       url('iconfont.woff?t=1727228638506') format('woff'),
       url('iconfont.ttf?t=1727228638506') format('truetype'),
       url('iconfont.svg?t=1727228638506#ForDashboard') format('svg');
}

.ForDashboard {
  font-family: "ForDashboard" !important;
  font-size: 16px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.forD-vulnerabilities:before {
  content: "\e6ce";
}

.forD-Box:before {
  content: "\e607";
}

.forD-Configure:before {
  content: "\e620";
}

.forD-Memory:before {
  content: "\e6ca";
}

.forD-Code:before {
  content: "\e6cf";
}

.forD-Endpoint:before {
  content: "\e628";
}

.forD-Event:before {
  content: "\e6e7";
}

.forD-Attacker:before {
  content: "\e6e8";
}

.forD-Pin:before {
  content: "\e6ed";
}

.forD-Setting:before {
  content: "\e633";
}

.forD-Data:before {
  content: "\e638";
}

.forD-linkage:before {
  content: "\e63f";
}

.forD-Security:before {
  content: "\e61b";
}

.forD-Firewall:before {
  content: "\e622";
}

.forD-Internet:before {
  content: "\e62b";
}

.forD-Ticket:before {
  content: "\e6ea";
}

.forD-Server:before {
  content: "\e632";
}

.forD-Asset:before {
  content: "\e61e";
}

.forD-BadIP:before {
  content: "\e61f";
}

.forD-Virus:before {
  content: "\e6ae";
}

.forD-yingyong:before {
  content: "\e62c";
}

.forD-Investigation:before {
  content: "\e63a";
}

.forD-Dashboard:before {
  content: "\e63c";
}

.forD-Report:before {
  content: "\e645";
}

.forD-Protection:before {
  content: "\e6f9";
}

.forD-Risk:before {
  content: "\e6fd";
}

