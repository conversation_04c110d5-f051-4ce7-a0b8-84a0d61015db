export default {
  app: {
    searchNotData: "Résultat introuvable",
    toSearch: "Confirmez",
    toNavigate: "Basculez",
  },
  countdown: {
    normalText: "Percevez le code de vérification",
    sendText: "Dans {0}seconde, veuillez réessayez",
  },
  cropper: {
    selectImage: "Sélectionnez l'image",
    uploadSuccess: "Importation réussie",
    modalTitle: "Importation de la photo de profil",
    okText: "Confirmez et faites importer la photo",
    btn_reset: "Réinitialisation",
    btn_rotate_left: "Rotation dans le sens inverse des aiguilles d'une montre",
    btn_rotate_right: "Rotation dans le sens des aiguilles d'une montre",
    btn_scale_x: "Rotation horizontale",
    btn_scale_y: "Rotation perpendiculaire",
    btn_zoom_in: "élargez",
    btn_zoom_out: "Diminuez",
    preview: "Prévisualisation",
  },
  drawer: {
    loadingText: "Chargement en cours",
    cancelText: "Fermez",
    okText: "Confirmez",
  },
  excel: {
    exportModalTitle: "Exportez des données",
    fileType: "Type du fichier",
    fileName: "Nom du fichier",
  },
  form: {
    putAway: "Repliez",
    unfold: "Déployez",
    maxTip: "Le nombre de caractères doit être inférieur à {0} chiffres",
    apiSelectNotFound: "Chargement de données en cours ,veuillez patienter",
  },
  icon: {
    placeholder: "Appuyez sur le bouton pour choisir l'icône",
    search: "Recherchez l'icône",
    copy: "Copie de l'icône réussie",
  },
  menu: {
    search: "Recherchez le menu",
  },
  modal: {
    cancelText: "Fermez",
    okText: "Confirmez",
    close: "Fermez",
    maximize: "Maximalisez",
    restore: "Restaurez",
  },
  table: {
    settingDens: "Densité",
    settingDensDefault: "Consentement tacite",
    settingDensMiddle: "Moyen",
    settingDensSmall: "Compact",
    settingColumn: "Disposition de colonne",
    settingColumnShow: "Exhibition  de colonne",
    settingIndexColumnShow: "Colonne du nombre d'ordre",
    settingSelectColumnShow: "Cochez la colonne",
    settingFixedLeft: "Fixez à gauche",
    settingFixedRight: "Fixez à droite",
    settingFullScreen: "En plein écran",
    index: "Numéro d'ordre",
    total: "Au total",
    lastElTips: "Il ne peut y avoir moins d'un champ de liste",
  },
  time: {
    before: "Avant",
    after: "Après",
    just: "Tout à l'heure",
    seconds: "Seconde",
    minutes: "Minute",
    hours: "Huere",
    days: "Jour",
  },
  tree: {
    selectAll: "Tout sélectionner",
    unSelectAll: "Annulation de sélection",
    expandAll: "Tout déployer",
    unExpandAll: "Tout plier",
    checkStrictly: "Corrélation de niveaux",
    checkUnStrictly: "Indépendance de niveaux",
  },
  upload: {
    save: "Sauvegardez",
    upload: "Importez",
    imgUpload: "Importation des images",
    uploaded: "Bien importé",
    operating: "Opération",
    del: "Supprimez",
    download: "Téléchargez",
    saveWarn: "Téléchargment en cours, veuillez patienter et sauvegardez.",
    saveError: "le fichier n'étant pas importé avec succès ne peut pas être sauvegardé.",
    preview: "Prévisualisez",
    choose: "Sélectionnez le fichier, s'il vous plaît",
    accept: "Ce qui est compatible avec le format {0}",
    acceptUpload: "Seul le fichier au format {0}peut être importé",
    maxSize: "Fichier unique ne dépasse pas {0}MB",
    maxSizeMultiple: "Seul le ficher moins de {0}MB peut  être importé",
    maxNumber: "Vous pouvez importer tout au plus le fichier au format {0}",
    legend: "Miniature",
    fileName: "Nom de fichier",
    fileSize: "Taille de fichier",
    fileStatue: "Status",
    startUpload: "Commencez  à  importer",
    uploadSuccess: "Importation réussie",
    uploadError: "échec de l'importation",
    uploading: "Importation en cours",
    uploadWait: "à peine le téléchargement du fichier est-il terminé,que l'opération s'effectue",
    reUploadFailed: "Réessayez faire importer le fichier qui a connu échec de l'importation.",
  },
  verify: {
    error: "échec de vérification",
    time: "Vérification réussie,en{time}seconde.",
    redoTip: "Cliquez sur l'image pour réactualiser",
    dragText: "Faites glisser en appuyant sur le bouton",
    successText: "Vérification réussie",
    passwordNull: "Mot de passe ne peut pas être vide",
    passwordError: "Mot de passe que vous avez entré n'est pas identique",
    confirmPasswordNull: "Confirmez le mot de passe n'étant pas vide",
  },
};
