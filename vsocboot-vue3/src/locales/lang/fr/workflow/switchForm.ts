export default {
  mode:"Mode",
  modeTooltip:"Il va falloir déterminer le mode du routage",
  dataType:"Type de données",
  dataTypeTooltip:"Il va falloir le type de données sur le routage",
  value1:"Valeur1",
  value1Tooltip:"Valeur1 comparée avec valeur2",
  value2: 'Valeur2',
  value2Tooltip:"Valeur comparée avec valeur1",
  operation:"Opération",
  operationTooltip:"Opération qui décide où des données se font transporter",
  defaultOutput:"Exportation par défaut",
  defaultOutputTooltip:"Les données qui ne respectent aucune règle se font exporter vers le chemin par défaut",
  output:"Exportez",
  outputTooltip:"Si la règle y correspond, l'index de sortie des données lui est envoyé.",
  outputTooltip2:"Chemin de sortie auquel on envoie les données",
  formatter:"Formatage",
  formatterTooltip:"Remplissez l'expression de format de temps et utilisez-la pour convertir la chaîne de temps que vous avez remplie ci-dessus en un objet de temps pour la comparaison de taille 2024(yyyy)-08(MM)-05(dd)T(\'T\')10(HH):19(mm):24(ss).449(SSS)+08:00(XXX)[Asia/Shanghai](\'[\'VV\']\')。par example :2024-08-05T10:28:44.378+08:00[Asia/Shanghai] le format relatif est ：yyyy-MM-dd HH:mm:ss.SSSXXX'['VV']'",
  Rules:"Règles",
  Expression:"Expression",
  RoutingRules:"Règles de routage",
}
