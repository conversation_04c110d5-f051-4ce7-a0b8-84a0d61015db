export default {
  conditions: "Condition de filtre",
  conditionsTooltip: "Type de valeur ",
  value1: "Valeur 1",
  value1Tooltip: "Valeur en comparaison avec la valeur2",
  operation: "Condition",
  operationTooltip: "Opération de comparaison entre valeur1 et valeur2",
  value2: "Valeur2",
  value2Tooltip: "Valeur en comparaison avec la valeur 1  ",
  auth: "Relation",
  authTooltip: "Si plusieurs règles sont définies, ce paramètre détermine si elle est vraie dès que l’une des conditions y correspond ou uniquement lorsque toutes les conditions sont remplies.",
  formatter: "Format",
  formatterTooltip: "Remplissez une expression de format de temps et utilisez-la pour convertir la chaîne de temps que vous avez renseignée ci-dessus en un objet de temps pour la comparaison de taille 2024(yyyy)-08(MM)-05(dd)T(\'T\')10(HH):19(mm):24(ss).449(SSS)+08:00(XXX)[Asia/Shanghai](\'[\'VV\']\') Par example, 2024-08-05T10:28:44.378+08:00[Asia/Shanghai] le format correspondant：yyyy-MM-dd HH:mm:ss.SSSXXX'['VV']'",
  example: "Example",
}
