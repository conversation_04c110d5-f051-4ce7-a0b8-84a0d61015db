export default {
  name: "Nom de certificat",
  type: "Type de certificat",
  user: "Identifiant ",
  pwd: "Mot de passe",


  passwd: "Mot de passe",
  host: "<PERSON>ô<PERSON>",
  port: "Port",
  encrypt: "SSL/TLS",

  trust: "Certificat auto-signé ",
  Credential: "Justificatif ",
  lastUpdate: "Dernière mise à jour ",
  created: "Créez",

  timeout: "Délai d'expiration de connexion réseau ",
  database: "Bibliothèque de bases de données",
  connectusing: "Connexion ",
  privatekey: "Clé privée",
  passphrase: "Mot de passe",
}
