export default {

  method: "Méthode",
  methodTooltip: "Méthode à utiliser",
  url: "URL",
  urlTooltip: "URL",
  authentication: "Vérification d'identité",
  sendQuery: "Envoyez le paramètre d'enquête",
  sendQueryTooltip: "Demandez s'il existe le paramètre d'enquête",
  specifyQuery: "Spécifiez le paramètre d'enquête",
  queryParameters: "Paramètre d'enquête",
  addParameter: "Ajoutez le paramètre",
  sendHeaders: "Envoyez un en-tête de requête",
  sendHeadersTooltip: "Demandez s'il exsite un en-tête de requête",
  specifyHeaders: "Spécifiez un en-tête de requête",
  headerParameters: "Demandez un en-tête de requête",
  sendBody: "Envoyez le corps du texte",
  sendBodyTooltip: "Demandez s'il existe le corps du texte",

  contentType: "Type de contenu du corps",
  contentTypeTooltip: "Type de contenu destiné à  envoyer le paramètre du corps",
  specifyBody: "Spécifiez le corps",
  bodyParameters: "Paramètre du corps",
  rawContentType: "Type de contenu",
  rawBody: "Contenu de demande",
  name: "Nom",
  value: "Valeur",
  json: "JSON",
  timeout: "Dépassement du délai",
  timeoutTooltip: "Temps (ms) nécessaire pour que le serveur envoie un en-tête de réponse (et démarre le corps de la réponse) avant d'abandonner la demande",
  maxRedirects: "Fréquence maximale de redirection",
  maxRedirectsTooltip: "Fréquence maximale de redirection à suivre",
  genericAuthType: "Type de vérification pour l'identité générale",
  credential: "Justificatif de vérification d'identité",


}
