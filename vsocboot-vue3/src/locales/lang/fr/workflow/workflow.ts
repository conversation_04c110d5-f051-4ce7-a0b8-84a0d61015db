export default {
  input_no_data: "Aucune de donnée importée",
  execute_previous_nodes: "Exécutez le node précédent",
  execute_previous_nodes_desc: "Commencez par le noeud précédent dénué de données exportées",
  inputTitle: "Importez",
  outputTitle: "Exportez",
  executeNodes: "Exécutez les noeuds",
  name: "Nom",
  value: "Valeur",
  executeWorkflow: "Exécutez le flux de travail",
  workflowName: "Nom du processus de travail",
  executingLoading: "Exécution de noeuds en cours",
  waitingLoading: "Chargement en cours",
  checkNodeError: "Infos de configuration des noeuds sont érronées",
  stopExecuteWorkflow: "Arrêtez",
  startedat: "Date du début",
  mode: "Mode",
  dragInputPlaceholder: "Vous pouvez faire glisser des données importées",
  editor: "Modifiez",
  executions: "Enregistrement d'exécution",
  defaultName: "Mon flux de travail",
  addNodeTitle: "Choisissez le noeud",
  addFirstTip: "Ajoutez le noeud déclencheur",
  nodeFormTitle: "Info de noeuds",
  Add: "Ajoutez",
  CreateCredential: "Créez un justificatif",
  SearchNodes: "Recherchez les noeuds",
  operation: "Manière",
  query: "Consultez",
  credential: "Justificatif de l'adresse d'accès à la connexion",
  table: "Tableau",
  datamode: "Mode SQL",
  auto: "Dans ce mode, assurez-vous que le nom du champ de données entrant est le même que celui de la colonne de tableau. Si nécessaire, utilisez le nœud Set pour modifier le nom du champ avant ce nœud.",
  valuestosend: "Valeur transmise",
  column: "Colonne",
  addValue: "Valeur ajoutée",
  allCols: "Retour",
  distinct: "Déduplication",
  limit: "Limite",
  cols: "Colonne",
  where: "Choisissez le rang",
  operator: "Signe algébrique",
  whereType: "Combinaison de conditions",
  order: "Mise en ordre",
  orderType: "Manière de mettre en ordre",
  addsort: "Ajoutez les règles de mise en ordre",
  addcondition: "Ajoutez une condition",
  matchCol: "Colonne à associer",
  matchVal: "Valeur de colonne à associer",
  requiresql: "Paramètre 'Query' est nécessaire",
  requireLimit: "Paramètre 'Limite' est nécessaire",
  enterTableName: "Choisissez le tableau que vous allez traiter, s'il vous plaît.",
  requireTableName: "Paramètre 'Limite'est nécessaire",
  command: "Commandez",
  directory: "Catalogue",
  key: "Clé",
  type: "Type",
  url: "URL",
  resource: "Ressource",
  pleaseSelectColumns: "Choisissez la colonne,s'il vous plaît",
  basicUsage: "Usage basique",
  deleteItem: "Supprimez",
  example: "Example",
  usingJson: "Employez JSON",
  usingFieldsBelow: "Employez les champs ci-dessous",
  any: "N'importe qeulle condition",
  all: "Tout",
  usingSingleField: "Employez le champ unique",
  both: "Les deux",
  html: "HTML",
  text: "Texte",
  mySubjectLine: "Ma ligne du thème",
  basicAuth: "Certificat élémentaire",
  none: "Nul",
  genericCredentialType: "Type de justificatif général",
  false: "Faux",
  true: "Vrai",
  pleaseSelectType: "Choisissez le type",
  pleaseEnterTheUrl: "Entrez URL",
  networkConnectivityTest: "Test de connexion de résaux",
  umpIpAddress: "Adresse UMP IP",
  umpConnectionManagement: "Management de connexion UMP",
  ticketname: "Nom du ticket de travail",
  tenant: "Unité de services",
  ticketType: "Type du ticket de travail",
  Description: "Description",
  Creator: "Créateur",
  Creatortype: "Type du créateur",
  Creationtime: "Temps consacré à la création",
  MSSPTickets: "Ticket de travail MSSP",
  ServiceTickets: "Ticket de services",
  NotificationTickets: "Ticket de notification",
  TenantTickets: "Ticket de l'unité de services",
  GoBack: "Retour",
  Back: "Retour",
  Cancel: "Annulez",
  OKandNext: "D'accord et l'étape suivante",
  Public: "En public",
  Save: "Sauvegardez",
  Basicinfo: "Info de base",
  Formcustomization: "Personnalisation de forme",
  Flowcustomization: "Personnalisation de procédure",
  uploadSuccess: "Importation réussie",
  uploadFailed: "échec de l'exportation",
  Nodename: "Nom de noeud",
  Nodetype: "Type de noeud",
  askNodeType: 'type de tâche',
  Radiobox: "Radiobox",
  Checkbox: "Case à cocher",
  Dropdownbox: "Liste déroulante",
  Pleaseselect: "Choisissez",
  Time: "Temps",
  Date: "Date",
  Layout: "Mise en page",
  Genericcomponents: "Composant général",
  Text: "Texte",
  Textbox: "Zone de texte",
  Textfield: "Champ de texte",
  Number: "Numéro",
  Switch: "Interrupteur",
  Uploadfiles: "Importez des fichiers",
  Boundary: "Frontière",
  Riskcomponents: "Composants de risques",
  Alerts: "Alertes",
  Area: "Zone",
  thisiscontent: "C'est du contenu",
  Pleaseentervalue: "Veuillez entrer la valeur,s'il vous plaît",
  Ticketname1: "Nom du ticket de travail.",
  LabelName: "Nom de l'étiquette",
  Width: "Largeur",
  Placeholder: "Espace réservé",
  DefaultValue: "Valeur par défaut",
  Layoutmethod: "Méthode de mise en page",
  Options: "Options",
  MultipleChoice: "à choix multiples",
  Searchable: "Consultable",
  staticdata: "Données statiques",
  dictionarydata: "Données de dictionnaire",
  AddOptions: "Ajoutez de options",
  Cleardefaultvalues: "Enlevez la valeur par défaut",
  User: "Utilisateur",
  Format: "Format",
  Whetherchooseforrange: "Choisissez le champ?",
  Starttimeplaceholdercontent: "Contenu d'espace réservé à la date du début",
  Endtime: "Contenu d'espace réservé de à la date de fin",
  Placeholdercontent: "Contenu d'espace réservé.",
  Displaytype: "Type d'exhibition",
  Valuewhenenabled: "Valeur activée",
  Valuewhenclosed: "Valeur désactivée",
  Margin: "Marge",
  Textcontent: "Contenu du texte",
  Rowheight: "Hauteur de rang",
  Alignment: "Alignement",
  Left: "Alignement à gauche",
  Center: "Au milieu",
  Right: "Alignement à droite",
  Bold: "En caractères gras",
  Italic: "En italique",
  Underline: "Sous ligne",
  Strikethrough: "Ligne barrée",
  DisplayCAPTCHA: "Code de vérification s'affiche",
  Draganddrop: "Fiates glisser-déplacer",
  Maximumuploads: "Nombre maximal des importations",
  Bulkupload: "Importez par lots",
  Uploadbuttontext: "Importez le text de bouton",
  BindKey: "Clé de liaison",
  BindTable: "Tableau de liaison",
  Columns: "Colonne",
  Fontsize: "Taille de police",
  Fontstyle: "Style de police",
  WorkFlowTitle: "Titre de flux de travail",
  WorkFlowNumber: "Nombre du flux de travail",
  NO_START: "Sans noeud du début",
  MORE_START: "Beaucoup plus de noeuds du début",
  NO_END: "Sans noeud de fin",
  MORE_END: "Beaucoup plus de noeuds de fin",
  NO_TASK: "Sans aucune tâche d'utilisateurs",
  MISS_LINE: "Erreur du nombre de lignes de connexion",
  MISS_TASK_LINE: "Tâche sans connexion",
  MISS_GATEWAY_LINE: "ExclusiveGateway sans connexion',",
  MISS_TASK_PERMISSION: "Tâche dont le répartiteur n'est pas choisi",
  MISS_GATEWAY_PERMISSION: "ExclusiveGateway manque de condition configurée",
  MORE_TASK_LINE: "Point de noeud ne doit pas être mis à la disposition deux sorties",
  IN_START_LINE: "Noeud du début n'accepte pas de ligne transmise",
  GATEWAY_CONFIG: "Condition incomplète",
  ERROR_1: "Sans noeud du début",
  ERROR_2: "Beaucoup plus de noeuds du début",
  ERROR_3: "Sans noeud de fin",
  ERROR_4: "Beaucoup plus de noeuds de fin",
  ERROR_5: "Sans tâche de l'utilisateur",
  ERROR_6: "Erreur du nombre des lignes de liaison",
  ERROR_7: "Tâche sans connexion",
  ERROR_8: "Dépourvu de connexion de portail",
  ERROR_9: "Tâche dont le répartiteur n'est pas choisi",
  ERROR_10: "Dépourvu de configuration du portail",
  ERROR_11: "Noeud du début ne peut pas accéder directement au portail",
  ERROR_12: "Noeud ne doit pas être mis à la disposition deux sorties",
  ERROR_13: "Noeud du début n'accepte pas de ligne transmise",
  ERROR_14: "Connexion mutuelle n'est pas autorisée",
  ERROR_15: "Condition incomplète",
  ERROR_16: 'Erreur de configuration de la tâche initiale',
  ERROR_17: 'Le nœud initial ne peut avoir qu’une seule sortie',
  OUT_START_LINE: 'Le nœud initial ne peut avoir qu’une seule sortie',
  CONFIG: "Configurez",
  ADD_TASK: "Tâche",
  ADD_GATEWAY: "Jugez",
  ADD_END: "Terminez",
  ADD_NOTE: "Ajoutez des notes",
  COPY: "Copiez",
  DELETE: "Supprimez",
  EDIT_NAME: "Renommez",
  requiredcondistion: "Composant de tâches est acquis avant la condition",
  requiredaftercondistion: "Composant de tâches est acquis après la condition",
  Earlierthan: "Plus tôt que",
  Laterthan: "Plus tard que",
  Includeoneof: "Un d'entre eux est compris",
  Includeall: "Tout est compris",
  Noneof: "Nul est compris",
  Greaterthan: "Plus nombreux que",
  Lessthan: "Moins nombreux que",
  Nodepermission: "Accès de noeuds",
  Formpermission: "Accès du formulaire",
  "Applicationnoconfigured": "Aucune application configurée !",
  "Nopresetassignee": "Nœud non traité préalablement",
  "Presetassignee": "Nœud traité préalablement",
  "Readytosubmit": "Prêt à soumettre",
  "Nonsubmit": "Non soumissible",
  "tenantTask": "Tâches du locataire",
  "msspTask": "Tâches de l'administrateur",
  "Available": "Personne responsable de la gestion",
  "permissionTip": "Seuls les utilisateurs ou rôles configurés ici ont le droit d'utiliser les autorisations",
  "Unread": "Non lu",
  "applyTip": "Demande de l'utilisateur pour visualiser le ticket",
  "currentProcessor": "Personne en charge actuelle",
  "ProcessorUser": "Utilisateurs ayant le droit de visualisation",
  "refuse": "Refuser",
  "agree": "Accepter",
  "detail": "Informations détaillées"
}
