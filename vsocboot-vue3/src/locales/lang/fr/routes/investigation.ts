export default {
  investigation: "Nom d'enquête",
  severity: "Gravité",
  status: "Status d'enquête",
  tickets: "Tickets de travail",
  creator: "<PERSON><PERSON><PERSON>ur",
  creationTime: "Temps consacré à la création",
  riskEvents: "événement des risques",
  ml: "événement  ML",
  badActor: "IP des acteurs de la menace",
  suspiciousProcesses: "Processus suspect",
  huntingResult: "Résultat de chasse",
  tenant: "Unité de services",
  members: "Membres d'enquête",
  priority: "Priorité",
  discription: "Description d'enquête",
  category: "Catégorie",
  addText: "Ajoutez",
  open: "Consultez",
  ticketsBtn: "Tickets de travail",
  query: {
    time: "Temps",
    search: "Recherche",
    isMe: "Y compris moi",
  },
  continueAdd: "Continuez d'ajouter",
  addRule: "Règles d'ajout",
  ignore: {
    title: "Ignorez",
    content: "Vous êtes sûr que vous alliez ignorer cet enregistrement?",
  },
  close: {
    title: "Fermez",
    title2: "Vous êtes sûr que vous alliez le réouvrir?",
    content: "Vous êtes sûr que vous alliez le fermer.",
  },
  pause: {
    title: "Arrêt",
    content: "Vous êtes sûr que vous alliez l'arrêter?",
  },
  InProcess: {
    title: "Récupération",
    content: "Vous êtes sûr que vous alliez récupérer du contenu?",
  },
  history: {
    operator: "Opérateur",
    content: "Contenu d'opération",
    operateType: "Type d'opération",
    createTime: "Temps consacré à l'opération",
  },
  saveAsTracking: "Suavegardez comme une trace",
  tenantConfirm: {
    title: "Confirmez",
    content: "Modifiez l'unité de services va entraîner une élimination de données choisies!",
  },
  'ProportionSeverity': "Selon une proportion de gravité",
  'QuantityTrend': "Tendance de quantité",
  'MeanTime': "Durée moyenne",
  'MeanTimeTip': "Durée moyenne pour la gestion d'enquête de la dernière 10 fois.",
  'KeyIndicators': "Indice majeur",
  'BaseInformation': "Information élémentaire",
  'AddRiskEvent': "Ajoutez l'événement des risques",
  'AddML': "Ajoutez ML",
  'AddBadActor': "Ajoutez des facteurs de la menace",
  'AddSuspiciousProcess': "Ajoutez un processus suspect",
  'Conclusion': "Conclusion",
  'Urgency': "Niveau",
  'AlarmTime': "Temps d'alerte",
  'Duration': "Durée",
  'AttackTarget': "Cible attaquée",
  'AttackNumber': "Nombre d'attaque",
  'AttackMethodsNumber': "Nombre de méthods d'attaque",
  'AttackMethod': "Méthode d'attaque",
  'Score': "Score",
  'LastAlertTime': "Temps de la dernière alerte",
  'HuntingName': "Nom de chasse aux menaces",
  'TargetLog': "Journal ciblé",
  'HuntingTimeRange': "Fourcette horaire pour la chasse aux menaces",
  'InvestigationTime': "Temps pour l'enquête",
  'Overview': "Abrégé",
  'RiskObject': "IP sur l'objet de risque",
  'RiskSourceIP': "IP sur la source du risque",
  'AddTicket': "Ajoutez le ticket de travail",
  'Comments': "Commentaire",
  'Send': "Envoyez",
  'UploadFile': "Importez un fichier",
  "createCategory": "Créez une catégorie",
  baseInformation: "Information de base",
};
