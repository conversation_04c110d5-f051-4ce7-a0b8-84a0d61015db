export default {
  templateNameRule: "Veu<PERSON>z remplir le nom de modèle,s'il vous plaît",
  design: "Design",
  startTime: "Heure du début",
  endTime: "Heure de fin",
  soarview: "Revue du processus",
  execution: "Enregistrement d'exécution",
  All: "Application du ticket de travail",
  Pending: "à traiter",
  Processed: "Traitement en cours",
  Copy: "Le ticket de travail copié qui m'a été adressé",
  Review: "Seul visible",
  'Cc to me': "Le ticket de travail copié qui m'a été adressé",
  Submitted: "Soumis",
  received: "Reçu",
  ticket: "Ticket de travail",
  ticketname: "Nom du ticket de travail",
  tickettype: "Type du ticket de travail",
  status: "Status",
  read: "Lu",
  currentprocessor: "Opérateur actuel",
  applicant: "Postulant",
  starttime: "Heure du début",
  desc: "Description",
  applicanttime: "Temps pour la candidature",
  attention: "Attention",
  next: "étape à suivre",
  changeAssignee: "Changez de gérant",
  userApplicant: "Postulant",
  tenantApplicant: "Unité de services qui postule",
  events: "événement",
  process: "Gérez le ticket de travail",
  message: "Message",
  record: "Enregistrez",
  relevantUser: "Utilisateur relatif",
  wait: "Veuillez patienter, s'il vous plaît",
  end: "Fin",
  Processing: "Traitement en cours",
  Close: "Terminez",
  Commitable: "Soumettable",
  Noncommitable: "Non soumettable",
  riskCenterTitle: "Centre de risques",
  lastAlertTime: "Temps de la dernière alerte",
  "tenantTask": "Tâches du locataire",
  "msspTask": "Tâches de l'administrateur",
  "apply": "Gérer",
  "mismatch": "Locataire non correspondant",
  "checkNextUser": "Veuillez choisir la personne responsable de la gestion",
  "nextError": "Aucune étape suivante trouvée, veuillez vérifier les données ou la configuration du processus",
  "noPermission": "Vous n'avez actuellement pas les droits pour voir cela. Vous devez demander la permission à la personne qui a soumis la demande ou à la personne qui la traite actuellement",
  "assignAllEvents": "Attribuer tous les événements",
  "triageAllEvents": "Gérer tous les événements",
  "closeAllEvents": "Fermer tous les événements",
  "removeAllEvents": "Supprimer tous les événements",
  "assignTip": "Êtes-vous sûr de vouloir attribuer tous les événements?",
  "assignTip2": "Veuillez noter qu'il y a {0} événements déjà fermés et qui ne peuvent pas être réaffectés.",
  "assignTip3": "Il n'y a pas d'événement à attribuer.",
  "triageTip": "Êtes-vous sûr de vouloir gérer tous les événements qui vous ont été attribués?",
  "triageTip2": "Veuillez noter qu'il y a {0} événements déjà fermés et qui ne peuvent pas être régérés.",
  "triageTip3": "Il n'y a pas d'événement à gérer.",
  "closeTip": "Êtes-vous sûr de vouloir fermer tous les événements gérés qui vous ont été attribués?",
  "closeTip2": "Veuillez noter qu'il y a {0} événements déjà fermés et qui ne peuvent pas être fermés à nouveau.",
  "closeTip3": "Il n'y a pas d'événement à fermer.",
  "removeTip": "Êtes-vous sûr de vouloir supprimer tous les événements?"

}
