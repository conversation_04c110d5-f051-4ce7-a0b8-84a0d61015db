export default {
  'name': "Nom de liste blanche",
  'createBy': "Créateur",
  'createTime': "Temps consacré à la création",
  'riskModule': "Modèle à risque",
  'rule': "Règles",
  'comment': "Commentaire",
  'ruleScope': "Portée de règles",
  'tenantType': "Type de l'unité de services",
  'status': "Status",
  'tip': "Sélectionnez la règle de liste blanche à laquelle le module de risque s'applique",
  'Risk_Event': "Événements à risque",
  'ML_View': "Événements ML",
  'tip2': "Sélectionnez la règle de détection à laquelle s’applique la liste d’autorisation. Celle-ci peut être laissé vide. Lorsqu’elle est vide, cette liste blanche s’appliquera à toutes les règles de détection",
  'DetectionRule': "Règles de détection",
  'tip3': "Sélectionnez la règle ML à laquelle la liste blanche s’applique. Celle-ci peut être laissé vide. Lorsqu’elle est vide, cette liste blanche s’applique à toutes les règles de ML",
  'ML_Rule': "Règles ML",
  'field_matching': "Assortiment des champs",
  'tip4': "Les journaux qui répondent aux critères ne deviendront pas des alertes générées",
  'addWhitelist': "Ajoutez la liste blanche",
  RiskByEndpoint: "Risques de terminaux",
  RiskBySecurityDevice: "Des risques du matériel de sécurité",
  NetworkLog: "Journal d'internet",
  OperationLog: "Journal opératoire",
  Conclusion: "Conclusion",
};
