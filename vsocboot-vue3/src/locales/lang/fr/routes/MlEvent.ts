export default {
  'ruleName': "Nom de risques",
  'urgency': "Niveau",
  'riskStatus': "Status de risques",
  'alarmTime': "Temps d'alarme",
  'triage': "Résultat de traitement",
  'owner': "Répartiteur",
  'ruleType': "Type de règles",
  'assignmentTime': "Temps consacré à la répartition",
  'triageTime': "Temps de traitement",
  'closeTime': "Temps de fermeture",
  'totalTime': "Temps total",
  'time': "Temps pour la dernière détection",
  Summary: "Résumé",
  Statistic: "Statistique",
  Dataset: "Série de données",
  Rule: "Règle",
  Triggers: "Bascule",
  Step: "étape",
  HideChart: "Graphique caché",
  DisplayChart: "Graphique affiché",
  Setting: "Réglages",
  new: "Tout neuf",
  investigating: "Enquête",
  close: "<PERSON>rm<PERSON>",
  list: "Liste",
  bar: "Histogramme",
  line: "Graphique linéraire",
  pie: "Graphique circulaire",
  countLog: "Nombre du journal",
  Urgency: "Niveau",
  Timeshold: "Limite du temps",
  'RuleScope': "Portée de règles",
  'LogType': "Type du journal",
  DataSource: "Source de données",
  'PrimaryKey': "Champs de regroupement",
  'StatisticalFields': "Champs de statistiques",
  'CompareCondition': "Condition de comparaison",
  'triggers': "Bascule sur condition de séries de données",
  "conclusion": "Conclusion",
  showField: "Champs affichés",
  Order: "Ordre chronologique",
  Content: "Contenu",
  "pleasechoose": "Veuillez sélectionner des incidents dont l'état n'est pas fermé",
  "pleasechoosesame": "Veuillez sélectionner des incidents pour le même locataire",
  "pleasechooseown": "Veuillez sélectionner des incidents attribués à l'utilisateur actuel pour traitement",
  "pleasechoosedisposal": "Veuillez sélectionner des incidents ayant un résultat de traitement",
  "pleasechoosestatus": "Veuillez sélectionner des incidents avec le même état",
  "pleasechooseclose": "Veuillez sélectionner des incidents dont l'état est fermé",
  "pleasechooseopen": "Veuillez sélectionner des incidents dont l'état est ouvert"

};
