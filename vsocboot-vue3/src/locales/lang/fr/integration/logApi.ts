export default {
  'apiSearch': "Nom de l'API",
  'updateTime': "Temps consacré à la mise à jour",
  'number': "Numéro tacite",
  'ConfigurationVariables': "Variables de configuration",
  'tip1': "les variables de configuration ci-desous sont demandées pour la collection des journaux de cet intégration .Si vous voulez collecter des journaux par l'API, veuillez déterminer ces invariables,si il vous plaît.",
  'OutputLogExample': "Example de journaux sorties",
  'RelatedAssets': "Actifs  corrélatives",
  'tip2': "les actifs ci-dessous sont destinées à l'emploi de l'API.",
  'tip2_mssp': "les actifs ci-dessous sont destinées à l'emploi de l'API. sous condition tacite,des actifs de l'unité de service se pésentent. si vous voulez rechercher des actifs issues d'autres unités de service, veuillez cliquer sur la bouton \"autres unités de service\" s'il vous plaît.",
  columns: {
    variable: "Nom des variables",
    required: "Champ obligatoire",
    type: "Type de données",
    description: "Description",
  },
  assetColumns: {
    assetName: "Nom de l'actif",
    assetIp: "IP de l'actif",
    tenant: "Unité de service",
    apiStatus: "état de l'API",
  },
  test: "Test",
  close: "Fermez",
  closeConfirmText: "Vous êtes sûr que vous alliez le fermer?",
  asset: "l'actif",
  otherTenants: "Autres unités de services",
  apiColumns: {
    name: "Nom de l'API",
    publishDate: "Temps de publication",
  }

}
