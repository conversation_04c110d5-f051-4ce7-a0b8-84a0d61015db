export default {
  actionsSearch: "Nom d'actions",
  number: "Numéro tacite",
  updateTime: "Temps consacré à la mise à jour",
  description: "Description",
  configuration: "Variables de configuration",
  output: "Sortie",
  columns: {
    variable: "Variable",
    Required: "Champ obligatoire",
    type: "Type de données",
    description: "Description",
  },
  tip1: "les variables de configuration ci-dessous sont demandées lors des mises en marche d'actions intégrées .Celles-là sont désignées lors d'une configuration d'actifs dans UMP.",
  actionColumns: {
    name: "Nom d'actions",
    updateTime: "Temps de publication",
    type: "Type",
    description: "Description",
  },
}
