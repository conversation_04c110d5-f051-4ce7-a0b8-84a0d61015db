export default {
  ruleName: '规则名称',
  ruleType: '风险类型',
  ruleObject: '风险对象',
  riskCount: '风险计数',
  alarmTime: '闹钟时间',
  disposition: '处置',
  ruleUrgency: '规则等级',
  status: '状态',
  owner: '所有者',
  pause: '暂停',
  delConfirmText: '您确定要删除风险事件吗？',
  RiskEvents: '风险事件',
  Correlated: '关联风险事件',
  MLStatistic: 'ML统计风险',
  MLOrder: 'ML时序风险',
  MLContent: 'ML内容风险',
  Processes: '可疑进程',
  Bad: '可疑行为',

  whitelist: '白名单',
  threatHunting: '威胁狩猎',
  investigation: '调查',
  ticket: '工单',
  summary: '摘要',
  detail: '详情',
  rule: '规则',
  proposal: '建议',
  eventName: '事件名称',
  eventType: '事件类型',
  severity: '等级',
  firstAlertTime: '首次警报时间',
  lastAlertTime: '最后一次警报时间',
  reportIP: '报告IP',
  assignee: '分配人',
  triage: '处置结果',
  closeOrNot: '是否关闭',

  proposalName: '建议名称',
  category: '分类',
  urgency: '紧急程度',
  impact: '影响程度',
  incidentAnalysis: '事件分析',
  mitigation: '建议措施',
  searchTemplate: '搜索模板',
  otherInfo: '其他信息',
  referenceFile: '参考文件',
  summaryField: '概要字段设置',

  ownerTime: '分配时间',
  triageTime: '处置时间',
  closeTime: '关闭时间',
  assignmentConsume: '分配耗时',
  triageConsume: '处置耗时',
  closeConsume: '关闭耗时',
  totalConsume: '总耗时',
  conclusion: '结论',
  srcLat: '源IP位置的纬度',
  srcLng: '源IP位置的经度',
  srcIpCountry: '源IP国家',
  dstLat: '目的IP位置的纬度',
  dstLng: '目的IP位置的经度',
  dstIpCountry: '目的IP国家',
  AddEvent: '添加事件',
  anomalyRecord: '异常记录',
};
