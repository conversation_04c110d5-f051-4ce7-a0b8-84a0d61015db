export default {
  entityType: '特征主体类型',
  entityName: '特征主体名称',
  statDate: '统计日期',
  featureValue: '异常特征值',
  handler: '处置人',
  handlerTime: '处置时间',
  isRealAnomaly: '是否真异常',
  alertDesc: '告警描述',
  featureFullName: '异常特征全名称',
  alertLevel: '告警等级',
  owner: '分配人',
  triage: '处置结果',
  eventStatus: '事件状态',
  true: '真',
  false: '假',
  unknown: '未知',
  assignmentTime: '分配时间',
  triageTime: '处置时间',
  closeTime: '关闭时间',
  totalTime: '总时间',
  time: '时间',
  ticket: '工单',
  noTicket: '未关联工单',
  hasTicket: '关联工单',
  severity: '严重程度',
};
