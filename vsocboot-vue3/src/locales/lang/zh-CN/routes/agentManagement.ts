export default {
  wgetInstallationCommand: 'wget安装命令',
  commandInstallation: '命令安装',
  rollback: '回滚',
  releaseTime: '发布时间',
  version: '版本',
  linux: 'Linux',
  umpAgent: 'UMP代理',
  linuxAgent: 'Linux代理',
  windowsAgent: 'Windows代理',
  windows: 'Windows',
  upgradePackage: '升级包',
  generateInstallationCommands: '生成安装命令',
  linuxVersion: 'Linux版本',
  windowsVersion: 'Windows版本',
  curlInstallationCommand: 'curl安装命令',
}
