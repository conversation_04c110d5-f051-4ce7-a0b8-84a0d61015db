export default {
  receiversetting: '接收者设置',
  sendemailconf: '发送邮件配置',
  accountname: '账户名',
  emailaccount: '邮箱账户',
  tenantmssp: '租户/MSSP',
  creator: '创建者',
  creatortype: '创建者类型',
  createtime: '创建时间',
  tenant: '租户',
  sendemail: '发送邮件',
  mailserver: '邮件服务器',
  port: '端口',
  defaultmail: '默认邮件服务器',
  name: '名称',
  detectionrule: '检测规则',
  mlrule: '机器学习规则',
  riskevent: '风险事件',
  searchtext: '输入搜索文本',
  search: '搜索',
  rulename: '规则名称',
  Severity: '严重性',
  ruletype: '规则类型',
  communication: '通信协议',
  emailserversetting: '电子邮件服务设置',
  skipauthentication: '跳过证书认证',
  yes: '是',
  no: '否',
  defaultemailcomm: '默认电子邮件配置',
  sendemailaddress: '发送电子邮件地址',
  password: '发送电子邮件密码',
  emailver: '电子邮件验证',
  sendemailname: '发送电子邮件名称',
  none: '无',
  StartTLS: '启动TLS',
};
