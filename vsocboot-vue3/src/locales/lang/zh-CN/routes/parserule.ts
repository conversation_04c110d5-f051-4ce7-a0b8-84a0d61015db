export default {
  "LogSource": "日志来源",
  "DeviceName": "设备名称",
  "ParsedFields": "已解析的字段",
  "RuleName": "规则名称",
  "RuleType": "规则类型",
  "SuspectedHaltAfter": "疑似停止后:",
  "ParseMethod": "解析方法",
  "Creator": "创建者",
  "CreatorType": "创建者类型",
  "Editor": "修改者",
  "UpdateTime": "修改时间",
  "AvailableTenant": "可用租户",
  "CreationTime": "创建时间",
  "ParseFields": "解析字段",
  "Step": "步骤",
  "tip1": "创建一个过滤器，只有原始日志匹配该过滤器才会应用此规则。过滤器不是必需的。",
  "AddLogSource": "添加日志源",
  "tip2": "选择一个您想要使用新的解析规则构建为 JSON 格式的日志",
  "ChangeLogSource": "更改日志源",
  "ParsingRules": "解析规则",
  "tip3": "选择一个时间并创建一个过滤器，从您选择的日志中提取样本日志行，以便在下一步中您可以预览并验证以 JSON 格式所做的更改。",
  "AddLocalFiles": "添加本地文件",
  "tip4": "输入前参考标记和后参考标记。只有标记范围内的日志内容才会应用此规则。标记不是必需的。",
  "Rollback": "返回",
  "tip5": "请选择日志类型，正确的日志类型将帮助您编写其他规则或进行日志搜索",
  "security": "安全日志",
  "host": "主机日志",
  "network": "网络日志",
  "operation": "操作日志",
  "Name": "名称",
  "SelectSampleLog": "选择样本记录",
  "LogClipping": "日志剪辑",
  "SelectParseMethod": "选择解析方法",
  "ExtractFields": "提取字段",
  "BatchPublic": "选择租户",
  "BulkApply": "批量应用",
  "UploadParsingFile": "上传解析文件",
  "FieldsList": "字段列表",
  "Fields": "字段",
  "Separator": "分隔符",
  "Empty": "清空",
  "ApplyUnparsedLogs": "应用于未解析的日志",
  "CreateFilter": "创建一个过滤器",
  "tip6": "选择一个您想要使用新的解析规则构建为 JSON 格式的日志",
  "PleaseEnterNameOrIp": "请输入名称或IP地址",
  "ParseRule": "解析规则",
  "FrontReferenceMark": "前置参考标记",
  "PostReferenceMark": "后置参考标记",
  "JsonFormat": "JSON格式",
  "jsonTip": "系统会自动将Json格式的原始日志转换为键值对列表，您可以将解析后的值配置到系统管理的字段中",
  "RegularExpressions": "正则表达式",
  "RegularExpressionsTip": "您可以编写一个正则表达式，系统将根据您的正则表达式将原始日志解析为值列表，您可以将解析后的值配置到系统管理的字段中",
  "SeparatorTip": "您可以输入日志字段之间的分隔符，系统将根据您的分隔符将原始日志拆分为字段值列表，并且您可以将解析后的值配置为系统管理的字段",
  "NginxLog": "Nginx日志",
  "NginxLogTip": "您可以输入Nginx日志的日志格式，系统将根据您输入的内容将原始日志还原为字段值列表，并且您可以将解析后的值配置为系统管理的字段",
  "ApacheLog": "Apache日志",
  "ApacheLogTip": "您可以输入Apache日志的日志格式，系统将根据您输入的内容将原始日志还原为字段值列表，并且您可以将解析后的值配置为系统管理的字段",
  "UsingParsingFile": "使用解析文件",
  "UsingParsingFileTip": "您可以使用解析文件来解析原始日志。您所要做的就是上传解析文件，解析文件将把原始日志值与系统字段进行匹配",
  "JsonParseTable": "JSON解析表格",
  "RegularExpressionsTable": "正则表达式表格",
  "SeparatorTable": "分隔符表格",
  "NginxTable": "Nginx表格",
  "ApacheTable": "Apache表格",
  "UploadFileTable": "上传文件表格",
  "OriginalFieldName": "原始字段名称",
  "SampleOriginalValue": "原始值",
  "SystemFieldName": "系统字段名称",
  "SampleNewValue": "新值",
  "Field": "字段",
  "CorrespondingField": "设置解析字段",
  "usingRegularBtn": "通过正则表达式修正值",
  "enumeratingBtn": "通过枚举对应值来修正值",
  "enumeratingBtn2": "通过枚举对应范围值来修正值",
  "enumeratingBtn3": "通过包含字符来修正值",
  "enumeratingBtn4": "通过正则表达式修正值",
  "enumeratingBtn5": "Base64解码",
  "convertJsonBtn":"转化JSON",
  "NotUsed": "未使用",
  "Used": "已使用",
  "TimeFormat": "时间格式",
  "Test": "测试",
  "FixOriginalTitle": "修复原始值",
  "OriginalValue": "原始值",
  "NewValue": "新值",
  "confirm": "解析",
  "tip": "请至少选择一条记录！",
  "tip7": "最多可以选择 10 个选项！",
  "tip8": "请上传解析文件！",
  "tip9": "请输入规则名称！",
  "tip10": "Json 解析错误，请检查您的日志！",
  "tip11": "请首先选择相应的字段！",
  "SampleLogs": "样本日志",
  "formattingRule": "格式化规则",
  "detail": "详细信息",
  "clipper": "裁剪器",
  "fieldType": "字段类型",
  "filter": "过滤器",
  "fieldDesc": "字段描述",
  "ruleType": "规则类型",
  "fieldName": "字段名称",
  "back": "返回",
  "fieldList": "字段列表",
  "addNewField": "新增字段",
  "applicationLogSource": "应用日志源",
  "logSourceIp": "日志发生源IP",
  "logSourceName": "日志发生源",
  "tip12": "打开此按钮后，规则应用程序将尝试解析应用该规则的所有未解析日志，这可能需要很长时间，因此请耐心等待",
  "tip13": "忽略大小写，打开此开关后，解析日志的效率会有所降低，请确保您真的需要打开它。",
  "logAggregation": "日志聚合",
  "tip14": "日志聚合功能可以帮助您聚合接收到警报中的安全日志，它可以减少来自安全设备的警报量，帮助您专注于高价值警报。请选择要分组的字段和聚合时间。",
  "aggregationExpire": "聚合时间",
  "groupByField": "分组字段",
  "alertSummary": "告警摘要",
  "tip15": "为了便于关联和调查告警，您可以选择一些字段作为摘要字段。系统将显示这些字段的去重数据，作为告警摘要。",
  "summaryField": "摘要字段",
  "logtype": "日志类型",
  "AddLocalText": "添加本地文本",
  "tiptext": "输入一个您想要使用新的解析规则构建为 JSON 格式的日志",
  "tipfilter": "当前选中数据与筛选不匹配",
  "isUpgrade": "关联的内置规则",
  "tenantRuleUpgrade": "关联的租户规则",
  "ruleUpgrade": "关联的规则",
  "integration": "集成",
  "newVersion": "最新版本",
  "upgradeable": "可升级",
  "relatedMSSPRules": "关联的MSSP规则",
  "status": "状态",
  "modification": "是否已修改",
  "modified": "已修改",
  "unmodified": "未修改",
  "regularExpression": "正则表达式",
  "sampleLog": "示例日志",
  "ruleDetail": "规则详情",
  "relatedTenant": "关联租户规则",
  "relatedBuilt-inRule": "关联内置规则",
  "upgrade": "升级",
  "tip16": "该规则修改过，升级后将被覆盖，是否继续升级？",
  "tip17": "以下规则之前已经修改过，可能这些租户有一些特定的解析需求，请检查一下。如果您认为它们可以升级，请单击[确认]按钮，所有这些规则都将被覆盖，或者请选择[仅升级未修改的规则]，它只会升级未经修改的规则。",
  "tip18": "确认升级规则吗？",
  "upgradeRulesBtn": "只升级未修改的规则",
  "tip19": "同时将删除关联的集成规则",
  "tip20": "其中{0}个规则已被使用。",
  "tip21": "我们不建议删除正在使用的规则，请小心删除。",
  "tip22": "我已经知道风险并继续删除。",
  "tip23": "没有需要升级的数据。",
  "relatedRule": "关联规则",
  "MSSPRule": "MSSP规则",
  "PluginRule": "插件规则",
  "integrationPlugins": "集成插件",
  "logSource": "日志发生源",
  "vendorProduct": "供应商/产品",
  "vendorCategory": "类型",
  "vendorService": "服务",
  "tip24": "请选择租户！",
  "displayField": "只显示配置字段",
  "tip25": "当前正则表达式无法解析样本日志。请检查日志或正则表达式。",
  "Case-Sensitive": "区分大小写",
  "Case-Insensitive": "不区分大小写",
  "tip26": "规则来自不同的租户。请选择来自单一租户的规则，并将其应用于该租户的日志源。",

}
