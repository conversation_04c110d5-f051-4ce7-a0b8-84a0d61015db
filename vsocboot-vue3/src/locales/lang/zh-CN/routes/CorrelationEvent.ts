export default {
  tactic: '策略',
  eventName: '威胁名称',
  techniques: '技术',
  subTechniques: '子技术',
  updateTime: '更新时间',
  createTime: '创建时间',
  severity: '等级',
  eventStatus: '状态',
  owner: '分配人',
  triage: '处置结果',
  'assignmentTime': '分配时间',
  'triageTime': '处置时间',
  'closeTime': '关闭时间',
  'totalTime': '总耗时',
  'time': '最新检测时间',
  'ruleName': '规则名称',
  MITRETags: 'MITRE标签',
  ruleScope: '规则范围',
  'createBy': '创建人',
  creatorType: '创建人类型',
  creationTime: '创建时间',
  host: '主机',
  repeats: '重复',
  timespan: '时间跨度',
  event: '事件',
  joinBy: '关联',
  'CorrelationEvents': '关联事件',
  'CorrelationFields': '关联字段',
  asc: '时间升序',
  desc: '时间降序',
  'SrcIP': '源IP',
  'DstIP': '目的IP',
  type: '类型',
  technique: '技术',
  Unhit: '未命中',
  HideUnhit: '隐藏未命中技术'
};
