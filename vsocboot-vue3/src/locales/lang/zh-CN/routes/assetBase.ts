export default {
  tenant: '租户',
  hostName: '主机名称',
  ipv4: 'IPv4',
  ipv6: 'IPv6',
  safeStatus: '安全状态',
  onlineStatus: '在线状态',
  assetType: '资产类型',
  assetSubtype: '资产子类型',
  assetLevel: '资产等级',
  tag: '标签',
  assetGroup: '资产组',
  source: '来源',
  deviceManufacturer: '设备制造商',
  deviceVersion: '设备版本',
  deviceType: '设备类型',
  whetherVm: '是否虚拟机',
  location: '地点',
  cpuArchitecture: 'CPU架构',
  memery: '内存',
  hardDisk: '硬盘',
  graphicsCard: '显卡',
  os: '操作系统',
  Os: '操作系统',
  ov: '操作系统版本',
  cpu: 'CPU 规格',
  memorysp: '存储规格',
  hs: '硬件规格',
  kernelVersion: '内核版本',
  mac: 'MAC',
  personInCharge: '负责人',
  VMName: 'VM 名称',
  VMIP: 'VM IP',
  VMos: 'VM 操作系统',
  AssetLevel: '资产等级',
  logSource: '是否是日志来源',
  IPAddress: 'IP地址',
  CloudInfo: '云服务信息',
  CloudID: '云服务ID',
  zone: '可用区域',
  ExpirationTime: '到期时间',
  domainurl: '可使用 domain/url',
  allAsset: '所有资产',
  baseInfo: '基础信息',
  deviceInfo: '设备信息',
  systemInfo: '系统信息',
  networkInfo: '网络信息',
  primaryIPAddress: '主IP地址',
  otherIPAddress: '其他IP地址',
  assetName: '资产名称',
  assetNumber: '资产编号',
  whetherVM: '是否虚拟机',
  phoneNumber: '电话号码',
  email: '邮箱',
  memory: '内存',
  assetGroupPlaceholder: '请选中资产组',
  updateAsset: '资产修改',
  agentInfo: '代理商信息',
  iDNumber: 'ID编号',
  lastOnlineTime: '最新联机时间',
  lastOfflineTime: '最新离线时间',
  agentVersion: '代理版本',
  installationPath: '安装路径',
  configurePath: '配置路径',
  logPath: '日志路径',
  application: '应用信息',
  appversion: '应用版本',
  appvendor: '应用厂商',
  deviceVendor: '设备制造商',
  deviceModal: '设备型号',
  deviceId: '设备ID',
  publicCloud: '公有云',
  privateCloud: '私有云',
  cloudType: '公有云/私有云',
  cloudprovider: '云厂商',
  credentialname: '凭证名称',
  status: '状态',
  permission: '权限',
  publisher: '发布者',
  publishtime: '发布时间',
  lastupdatetime: '最后更新时间',
  addcredential: '添加凭证',
  api: '接口',
  actions: '动作',
  editable: '可编辑',
  usable: '可用',
  method: '方法',
  rulename: '规则名称',
  defaultnumber: '默认数量',
  parsingmethod: '解析方法',
  addingway: '添加方式',
  action: '操作',
  logsourceswitch: '日志源开关',
  logsourcetext1: '资产是否配置为日志源，只有日志源发送到系统的日志才会被系统接收并解析。',
  receive: '接收系统日志',
  logsourcetext2: '当使用“日志 API”时，系统将自动调用该 API 以获取资产的日志。',
  logapi: '日志API',
  logsourcetext3: '当选中“接收系统日志”时，日志源应主动将日志发送到系统代理。',
  logsourcetext4:
    '只有配置好的IP发送的日志才会被系统接收。默认情况下，主IP被用作资产的日志源IP。如果资产不使用主IP的网卡作为日志发送IP，请选择其他IP。',
  logsourceip: '日志源IP',
  logencodeformat: '日志编码格式',
  timeThreshold: '时间阈值',
  iflogapi: '是否启用日志api。',
  hostip: '主机IP',
  logport: '日志端口',
  username: '用户名',
  password: '密码',
  addasset: '添加资产',
  associateasset: '关联已存在资产',
  logsource: '日志源',
  asssetName: '资产名称',
  ipaddress: 'IP地址',
  integration: '集成',
  assetlevel: '资产级别',
  onlinestatus: '在线状态',
  safestatus: '安全状态',
  logsourcesetting: '日志源设置',
  integrationsetting: '集成插件设置',
  tab1: '附加信息',
  tab2: '日志源',
  tab3: '凭证',
  tab4: '关联资产',
  tab5: '日志源关联集成',
  ip: 'IP地址',
  osversion: 'OS版本',
  cloudservername: '云服务名称',
  cloudProvider: '云厂商',
  des: '描述',
  cpuSpecification: 'CPU规格',
  accessibleUrl: '域名/URL',
  applicationName: '应用名称',
  proxyconfigured: '代理配置',
  searchtext: '搜索资产名称、IP地址',
  changeintegration: '变更集成插件',
  deleteintegration: '删除集成插件',
  addintegration: '添加集成插件',
  loadintegration: '加载集成插件',
  unparsedLogs: '未解析日志',
  parsedLogs: '已解析日志',
  Host: '主机服务器',
  Cloud: '云服务器',
  Virtual: '虚拟机',
  Network: '网络设备',
  Cybersecurity: '安全设备',
  IOT: '物联网设备',
  Application: '应用程序',
  addingmethod: '添加方式',
  Manually: '手动',
  MDPS: 'MDPS',
  Assetdiscovery: '资产发现',
  Import: '导入',
  lossourcestatus: '日志源状态',
  normal: '正常',
  abnormal: '异常',
  integrationPlugin: '集成插件',
  pleaseselectproxy: '请选择代理服务器',
  pleaseinputIp: '请输入IP地址',
  pleaseinputIpIn: '请输入Log Source页的IP地址',
  pleaseSelectSameTenant: '只能导出同一租户下的资产',
  pleaseSelectData: '请选择数据',
};
