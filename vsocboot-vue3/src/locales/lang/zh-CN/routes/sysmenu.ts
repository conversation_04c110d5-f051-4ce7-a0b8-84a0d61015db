export default {
  configure: {
    text: '配置',
    parsing_files_storage: {
      text: '解析文件存储',
      button: {
        button_add: '添加',
      },
      fields: {
        update_time: '更新时间',
        file_type: '字段配型',
        file_type_placeholder: '请选择字段类型',
        update_file: '更新文件',
        update_file_placeholder: '请选择文件',
        file_name: '文件名称',
        file_name_placeholder: '请填写文件名称',
      },
    },
    log_handlers: {
      text: '日志处理',
      app_server: {
        app_server_name: '名称',
        app_server_name_placeholder: '请填写名称',
        app_server_ip: 'IP',
        app_server_ip_placeholder: '请填写IP',
        app_server_authorization_code: '授权代码',
        app_server_authorization_code_placeholder: '请填写授权代码',
        app_server_desc: '描述',
        app_server_desc_placeholder: '请填写描述',
        empty_field_msg: '请填写值',
      },
      proxy_server: {
        text: '日志收集器',
        proxy_name: '收集器名称',
        proxy_name_placeholder: '请填写收集器名称',
        proxy_ip: 'IP',
        proxy_ip_placeholder: '请填写IP',
        proxy_port: '端口',
        proxy_port_placeholder: '请填写端口',
        internet_proxy_ip: '互联网IP',
        internet_proxy_ip_placeholder: '请填写互联网IP地址或域名',
        internet_proxy_syslog_port: '互联网系统日志端口',
        internet_proxy_server_port: '互联网服务端端口',
        internet_proxy_port_placeholder: '请填写端口',
        proxy_server_authorization_code: '授权代码',
        proxy_server_authorization_code_placeholder: '请填写授权代码',
      },
    },
    log_source_manager: {
      text: '日志源管理',
      device_name: '设备名称',
      device_name_placeholder: '设备名称',
      log_source_ip: '发生源IP',
      unparsed_logs: "未解析日志",
      parsing_rules: "解析规则",
      parsed_fields: "解析字段",
      log_source_ip_placeholder: '日志发生源IP',
      file_for_parsing: '解析文件',
      parsing_rule_manage: '解析规则',
      file_for_parsing_placeholder: '解析文件',
      log_collector: '日志收集器',
      log_collector_placeholder: '日志收集器',
      log_charset: '日志字符集',
      log_charset_placeholder: '日志字符集',
      ip_placeholder: 'IP',
      dropPolicy: '丢弃策略'
    },
  },
  common: {
    rule: {
      ip: '请填写IP地址',
    }
  }
};
