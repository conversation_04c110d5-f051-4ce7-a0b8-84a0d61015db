export default {

  method: '方法',
  methodTooltip: '要使用的请求方法',
  url: 'URL',
  urlTooltip: '向其发出请求的URL',
  authentication: '身份验证',
  sendQuery: '发送查询参数',
  sendQueryTooltip: '请求是否有查询参数',
  specifyQuery: '指定查询参数',
  queryParameters: '查询参数',
  addParameter: '添加参数',
  sendHeaders: '发送请求头',
  sendHeadersTooltip:'请求是否有请求头',
  specifyHeaders: '指定请求头',
  headerParameters: '请求头参数',
  sendBody: '发送正文',
  sendBodyTooltip: '请求是否有正文',

  contentType: '正文内容类型',
  contentTypeTooltip: '用于发送正文参数的内容类型',
  specifyBody: '指定正文',
  bodyParameters: '正文参数',
  rawContentType: "内容类型",
  rawBody: "请求内容",
  name: '名称',
  value: '值',
  json: 'JSON',
  timeout: '超时时间',
  timeoutTooltip: '在中止请求之前等待服务器发送响应标头（并启动响应正文）的时间（毫秒）',
  maxRedirects: '最大重定向',
  maxRedirectsTooltip: '要遵循的最大重定向次数',
  genericAuthType: '通用身份验证类型',
  credential: '基本身份验证凭据',


}
