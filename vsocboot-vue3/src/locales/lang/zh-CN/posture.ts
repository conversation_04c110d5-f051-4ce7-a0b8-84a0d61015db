export default {
  Unassigned: '未分配',
  Untriged: '未处置',
  Unclosed: '未关闭',
  Unprocessed: '未处理',
  Processing: '处理中',
  Resolved: '已解决',
  Risk_security: '风险日志',
  Risk_endpoint: '主机日志',
  normal: '正常',
  freeze: '冻结',
  peakValue: '接收峰值',
  logPosture: {
    'hostLogs': '主机日志',
    'securityLogs': '安全日志',
    'networkLogs': '网络日志',
    'peakValue': '接收峰值',
    'normal': '正常',
    'freeze': '冻结',
    'Risk_Events': '检测到风险事件',
    'ML_events': '检测到ML事件',
    'Correlation_events': '检测到关联事件',
    'Bad_actors': '恶意行为事件',
    'suspicious_process': '具有可疑进程的主机',
    'Collected_Logs': '已收集的日志',
    'log_types': '日志类型的百分比',
    'Top10_log_sources': 'Top10日志源',
    'IP_address': 'IP地址',
    'Log_type': '日志类型',
    'Count': '数量'
  },
  risk: {
    'Risk_Event': '风险事件',
    'Unassign': '未分配',
    'Unclosed': '未关闭',
    'Correlation_Event': '关联事件',
    'ML_Event': 'ML事件',
    'peak_value': '接收峰值',
    'normal': '正常',
    'freeze': '冻结',
    'Risk_Events': '检测到风险事件',
    'ML_events': '检测到ML事件',
    'Correlation_events': '检测到关联事件',
    'Bad_actors': '恶意行为事件',
    'suspicious_process': '具有可疑进程的主机',
    'Risk': '风险事件',
    'Risk_endpoint': '终端设备统计',
    'Risk_security': '安全设备统计',
    'Risk_level': '等级统计',
    'Percentage_severity': '等级百分比',
    'Top10_rule': 'Top10 检测规则',
    'Top10_SourceIP': 'Top10 源IP',
    'ML_View': 'ML展示',
    'Top10_ML_rule': 'Top10 ML规则',
    'ML_rules_radar': 'ML规则的雷达图',
    'Rule_name': '规则名称',
    'Severity': '等级',
    'Count': '数量',
    'Sparkline': '线图',
    'IP_address': 'IP地址',
    'Country': '国家',
  },
  analysis: {
    'Bad_actor': '恶意行为事件',
    'Suspicious_process': '可疑进程',
    'Risk_Events': '检测到风险事件',
    'ML_events': '检测到ML事件',
    'Correlation_events': '检测到关联事件',
    'Bad_actors': '恶意行为事件',
    'suspicious_process': '具有可疑进程的主机',
    'badActor': '恶意行为事件',
    'Top20_Bad_actor': 'Top20恶意行为',
    'Bad_actor_topology': '恶意行为拓扑',
    'Suspicious_host': '可疑主机',
    'Top20_Suspicious_hosts': 'Top20可疑主机',
    'Word_cloud_processes': '进程词云',
    'Correlation_event': '关联事件',
    'latest10_Correlation_events': '最新10个关联事件',
    'Event_correlation_topology': '事件关联拓扑',
    'Percentage_severity': '等级百分比',
    'IP_address': 'IP地址',
    'Severity': '等级',
    'Status': '状态',
    'Score': '分数',
    'suspiciousProcess': '可疑进程',
    'Name': '名称',
    'New': '新的',
    'Pending': '待处理',
    'Closed': '已关闭',
    'Investigating': '调查'
  },
  statistic: {
    'title': '风险事件态势',
    'Attacking': '攻击中',
    'Attacking_sources': '攻击来源',
    'Attacking_targets': '攻击目标',
    'Attacking_Type': '攻击类型',
    'Attacking_List': '攻击列表',
    'Time': '时间',
    'Source_IP': '源IP',
    'Target_IP': '目标IP',
    'Type': '类型',
    'Severity': '等级',
    'Risk_Logs_Trend': '风险日志趋势',
    'Per': '前',
    'statistics': '统计',
    'Risk_Rules': '风险规则',
    'False_positive_rate': '误报率',
    'False_positive_count': '误报数量',
    'Risk_rules': '正在使用的风险规则',
    'Investigations_Situation': '调查情况',
    'category': '类别',
    'Pending': '待处理',
    'Processing': '处理中',
    'Closed': '已关闭',
  },
  attack: {
    'title': '攻击态势',
    'Security_Device': '安全设备',
    'Router': '路由器',
    'Switch': '交换机',
    'Server': '服务器',
    'Cloud_Server': '云服务器',
    'Database_Server': '数据库服务器',
    'Attack_Detected': '攻击检测',
    'Firewall': '防火墙',
    'IDS': '入侵检测系统',
    'IPS': '入侵防御系统',
    'GAV': '防病毒网关',
    'XDR': '扩展检测和响应',
    'EDR': '终端防护中心',
    'SDS': '垃圾邮件检测系统',
    'Attacking_Situation': '攻击情况',
    'Attackings': '攻击',
    'Victims': '受害者',
    'Attacking_Type': '攻击类型',
    'Collected_Logs': '收集的日志',
    'Access_Risk_IP': '访问风险IP',
    'IP': 'IP地址',
    'Counts': '数量',
    'Network_Risk_IP': '网络风险IP',
    'Threat_Risk_IP': '威胁风险IP',
    'Audi_Risk_IP': '审计风险IP',
  }

}
