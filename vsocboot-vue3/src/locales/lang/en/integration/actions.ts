export default {
  actionsSearch: 'Action name',
  number: 'Default Number',
  updateTime: 'Last update time',
  description: 'Description',
  configuration: 'Configuration Variables',
  output: 'Action Output',
  columns: {
    variable: 'Variable',
    Required: 'Required',
    type: 'Type',
    description: 'Description'
  },
  tip1: 'The following configuration variables are necessary for running on this integrated action. These are specified when configuring assets in UMP.',
  actionColumns: {
    name: 'Action name',
    updateTime: 'Publish date',
    type: 'Type',
    description: 'Description'
  },
}
