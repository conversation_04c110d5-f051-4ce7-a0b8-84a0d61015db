export default {
  'integrationName': 'Integration Name',
  'versions': 'Version Supported',
  'manufacturer': 'Brand',
  'integrationDesc': 'Description',
  'publishDate': 'Publish Date',
  'ruleNum': 'Parsing Rule',
  'apiNum': 'Log API',
  'actionNum': 'Supported Actions',
  'assetNum': 'Configured Asset',
  'load': 'Load',
  'serialNumber': 'Serial Number',
  'parsingRule': 'Parsing rule',
  'logAPI': 'Log API',
  'supportedAction': 'Supported Action',
  'changePlugin': 'Change Plugin',
  'deletePlugin': 'Delete Plugin',
  'actions': 'Actions',
  'delPluginTip': 'Are you sure to delete the plugin?',
  'plugin': 'Plugin',
  'searchTip': 'Integration Name/Brand Name',
  'saveTip': 'Please choose to install plugins',
  'saveTip2': 'Please select tenant',
  'saveTip3': 'Please select type',
  'install': 'Install',
  'searchTip1': 'All Configured Asset',
  'searchTip2': 'All Log API',
  'searchTip3': 'All Supported  Action',
  'searchTip4': 'Search plugin name',
  'installTip': 'Are you sure to install this plugin?'
};
