export default {
  'investigation': 'Investigation',
  'investName': 'Investigation Name',
  'tenant': 'Tenant',
  'permission': 'Authority',
  'template': 'Template',
  'createBy': 'Creator',
  'severity': 'Severity',
  'state': 'Status',
  'normalInvestigation': 'Normal Investigation',
  "stateTip1": "Are you sure you want to close?",
  "stateTip2": "Are you sure you want to open?",
  'severityTip1': 'Please select a severity!',
  "createInvestigation": "Create Investigation",
  "investigationPermission": "Investigation permission",
  "public": "Public",
  "limited": "limited",
  "basicInfo": "Basic info",
  "selectTemplate": "Select template",
  "usersAuthorized": "Users authorized",
  "node": "Node",
  "searchTemplate": "Search template",
  "proposal": "Proposal",
  "investigateImmediately": "Investigate immediately",
  "cursor": "Cursor",
  "relate": "Relate",
  "newNode": "New node",
  "tip1": "Please note that the page data has been modified. Refreshing will result in the loss of modified data. Are you sure you want to continue refreshing?",
  "runReports": "Run Reports",
  "conclusion": "Conclusion",
  "ipAddress": "IP Address",
  "assets": "Assets",
  "processes": "Processes",
  "users": "Users",
  "logs": "Logs",
  "fileName": "File Name",
  "tip2": "Oops, there is no data yet.",
  "tip3": "Please click the Run Reports button to run report of related IP address ",
  "findWay": "Select find way",
  "assetOrNot": "Select asset or not",
  "processesName": "Processes Name",
  "processesId": "Processes ID",
  "userName": "User Name",
  "filter": "Filter",
  "lastUpdateTime": "Last update time",
};
