export default {
  "LogSource": "Log Source",
  "DeviceName": "Device Name",
  "ParsedFields": "Parsed Fields",
  "RuleName": "Rule Name",
  "RuleType": "Rule Type",
  "SuspectedHaltAfter": "Suspected Halt After:",
  "ParseMethod": "Parse Method",
  "Creator": "Creator",
  "CreatorType": "Creator type",
  "Editor": "Editor",
  "UpdateTime": "Update Time",
  "AvailableTenant": "Available Tenant",
  "CreationTime": "Creation Time",
  "ParseFields": "Parse Fields",
  "Step": "Step",
  "tip1": "Create a filter, and only the raw logs match the filter will have this rule applied. Filter is not required.",
  "AddLogSource": "Add Log Source",
  "tip2": "Choose a log that you want to structure into JSON with a new Parsing Rule",
  "ChangeLogSource": "Change Log Source",
  "ParsingRules": "Parsing Rules",
  "tip3": "Choose a time and create a filter to pull sample log lines from your chosen logs so that in the next step you can preview and validate the changes in JSON format as you make them.",
  "AddLocalFiles": "Add Local Files",
  "tip4": "Enter front reference mark and post reference mark . Only the log content within the scope of marks will apply this rule. Marks is not required .",
  "Rollback": "Rollback",
  "tip5": "Please select the log type, the correct log type will help you write other rules or do log search",
  "security": "Security",
  "host": "Host",
  "network": "Network",
  "operation": "Operation",
  "BasicInformation":"Basic Information",
  "Name": "Name",
  "SelectSampleLog": "Select Sample Log",
  "LogClipping": "Log clipping",
  "SelectParseMethod": "Select Parse Method",
  "ExtractFields": "Extract Fields",
  "BatchPublic": "Select Tenant",
  "BulkApply": "Bulk Apply",
  "UploadParsingFile": "Upload Parsing File",
  "FieldsList": "Fields List",
  "Fields": "Fields",
  "Separator": "Separator",
  "Empty": "Empty",
  "ApplyUnparsedLogs": "Apply to unparsed logs",
  "CreateFilter": "Create a filter",
  "tip6": "Choose a log that you want to structure into JSON with a new Parsing Rule",
  "PleaseEnterNameOrIp": "Please enter Name or IP",
  "ParseRule": "Parse Rule",
  "FrontReferenceMark": "Front reference mark",
  "PostReferenceMark": "Post reference mark",
  "JsonFormat": "Json",
  "jsonTip": "The system will automatically convert the raw log in Json format into a list of key-values, you can configure the parsed values to the system-managed fields",
  "RegularExpressions": "Regex",
  "RegularExpressionsTip": "You can write a regular expression and the system will parse the raw log into a list of values based on your regular expression,you can configure the parsed values to the system-managed fields",
  "SeparatorTip": "You can enter a separator between log fields, and the system will separate raw log into a list of field values based on your separator, and you can configure the parsed values to be system-managed fields",
  "NginxLog": "Nginx Log",
  "NginxLogTip": "You can enter the log format of Nginx log, and the system will restore raw log to a list of field values based on what you enter, and you can configure the parsed values to be system-managed fields",
  "ApacheLog": "Apache Log",
  "ApacheLogTip": "You can enter the log format of Apache log, and the system will restore raw log to a list of field values based on what you enter, and you can configure the parsed values to system-managed fields",
  "UsingParsingFile": "Using Parsing File",
  "UsingParsingFileTip": "You can use the parsing file to parse raw log. All you need to do is upload the parsing file, and the parsing file will match the raw log values with the system fields",
  "JsonParseTable": "Json Parse Table",
  "RegularExpressionsTable": "Regular Expressions Table",
  "SeparatorTable": "Separator Table",
  "NginxTable": "Nginx Table",
  "ApacheTable": "Apache Table",
  "UploadFileTable": "Upload File Table",
  "OriginalFieldName": "Original Field Name",
  "SampleOriginalValue": "Sample Original Value",
  "SystemFieldName": "System Field Name",
  "SampleNewValue": "Sample New Value",
  "Field": "Field",
  "CorrespondingField": "Corresponding Field",
  "usingRegularBtn": "Fix value by using regular expression",
  "enumeratingBtn": "Fix value by enumerating corresponding values",
  "enumeratingBtn2": "Fix value by enumerating corresponding range values",
  "enumeratingBtn3": "Fix value by including them",
  "enumeratingBtn4": "Fix value by using regular expression",
  "enumeratingBtn5": "Base64 Decode",
  "convertJsonBtn":"Convert JSON",
  "NotUsed": "Not Used",
  "Used": "Used",
  "TimeFormat": "Time Format",
  "Test": "Test",
  "FixOriginalTitle1": "Fix Value By Enumerating Values",
  "FixOriginalTitle2": "Fix Value By Enumerating Range Values",
  "FixOriginalTitle3": "Fix Value By Including Value",
  "FixOriginalTitle4": "Fix Value By Regular expression",
  "FixOriginalTitle5": "Fix Value By Base64 Decode",
  "OriginalValue": "Original value",
  "NewValue": "New value",
  "confirm": "Confirm",
  "tip": "Please select at least one record!",
  "tip7": "Up to 10 options can be selected!",
  "tip8": "Please upload Parsing File!",
  "tip9": "Please enter Rule Name!",
  "tip10": "Json Parsing Error,Please check your log!",
  "tip11": "Please select first corresponding field!",
  "SampleLogs": "Sample Logs",
  "formattingRule": "Formatting Rule",
  "detail": "Detail",
  "clipper": "Clipper",
  "fieldType": "Field Type",
  "filter": "Filter",
  "fieldDesc": "Field Desc",
  "ruleType": "Rule Type",
  "fieldName": "Field Name",
  "back": "Back",
  "fieldList": "Field List",
  "addNewField": "Add New Field",
  "applicationLogSource": "Application log source",
  "logSourceIp": "Log Source IP",
  "logSourceName": "Log Source Name",
  "tip12": "When this button is turned on, the rule application will attempt to parse all unparsed logs to which the rule is applied, which may take a long time, so please be patient",
  "tip13": "Ignore text capitalization,the efficiency of ingesting log will discount after opening this switch, please ensure that you really need to open it.",
  "logAggregation": "Log Aggregation",
  "tip14": "Log aggregation feature can help you aggregate security logs ingested into alerts，it can reduce the volume of alerts from security device, assist you focus on high-value alerts. Please select the fields you would like to group by and the aggregation time.",
  "aggregationExpire": "Aggregation Time",
  "groupByField": "Group by field",
  "alertSummary": "Alert Summary",
  "tip15": "To facilitate correlation and investigation of your  alerts, you can select some fields as the summary fields. The system will show the deduplicated data of these fields as the summary of alerts.",
  "summaryField": "Summary Field",
  "logtype": "Rule Type",
  "AddLocalText": "Add Local Text",
  "tiptext": "Input a log that you want to structure into JSON with a new Parsing Rule",
  "tipfilter": "The currently selected data does not match the filter",
  "isUpgrade": "Related built-in rules",
  "tenantRuleUpgrade": "Related tenant rules",
  "ruleUpgrade": "Related rules",
  "integration": "Integration",
  "newVersion": "New version",
  "upgradeable": "Upgradeable",
  "relatedMSSPRules": "Related MSSP rules",
  "status": "Status",
  "modification": "Modification",
  "modified": "Modified",
  "unmodified": "Unmodified",
  "regularExpression": "Regular Expression",
  "sampleLog": "Sample Log",
  "ruleDetail": "Rule detail",
  "relatedTenant": "Related tenant",
  "relatedBuilt-inRule": "Related built-in rule",
  "upgrade": "Upgrade",
  "tip16": "This rule has been modified and will be overwritten after upgrading. Do you want to upgrade?",
  "tip17": "The following rules are modified before, it's possible that there are some specific parse needs of these tenants, please check it. If you consider that they can be upgraded, please click the [Confirm] button, and all of them will be covered, or please choose the [Just upgrade unmodified rules], it will only upgrade rules unmodified.",
  "tip18": "Are you sure about the upgrade rules?",
  "upgradeRulesBtn": "Just upgrade unmodified rules",
  "tip19": "At the same time, the associated integration rules will be deleted",
  "tip20": "{0} of them have been used.",
  "tip21": "We don't suggest to delete rules under using, please carefully deleting.",
  "tip22": "I already know the risk and continue deleting.",
  "tip23": "There is no data that needs to be upgraded.",
  "relatedRule": "Related rule",
  "MSSPRule": "MSSP Rule",
  "PluginRule": "Plugin Rule",
  "integrationPlugins": "Integration plugins",
  "logSource": "Log Sources",
  "vendorProduct": "Vendor/Product",
  "vendorCategory": "Category",
  "vendorService": "Service",
  "tip24": "Please select the tenant!",
  "displayField": "Display only configuration fields",
  "tip25": "The current regular expression cannot parse the Sample Log. Please check the log or the regular expression.",
  "Case-Sensitive": "Case-Sensitive",
  "Case-Insensitive": "Case-Insensitive",
  "tip26": "The rules come from different tenants. Please select the rules from a single tenant and apply them to the log sources of that tenant.",
  "tip27": "Fill in duplicate values!",
  "tip28": "Original value Cannot be empty!",
  "tip29": "The interval range cannot have intersection",
  "ruleSort": "Sort"

}
