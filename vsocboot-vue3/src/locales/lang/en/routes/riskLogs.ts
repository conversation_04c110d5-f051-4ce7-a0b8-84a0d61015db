export default {
  add: 'New Investigation',
  addMore: 'Add to More Investigation',
  save: 'Add to Investigations',

  confirm: {
    title: 'Confirm Add',
    content: 'Are you sure to add to this survey?'
  },

  include: 'Include',
  exclude: 'Exclude',
  groupBy: 'GroupBy',
  copy: 'Copy',
  rise: 'Rise',
  down: 'Down',
  goBack: 'GO Back',
  hunting: 'Hunting',
  add<PERSON><PERSON>elist: 'Add whitelist',
  addInvestigationPrompt: 'Are you sure to add to the Investigation ?',
  LogSource: 'Log source',
  Severity: 'Severity',
  SourceIP: 'Source IP',
  DestinationIP: 'Destination IP',
  tip: 'Filter logs and save it as whitelist. Logs that meet the whitelist criteria will not contribute to generation of Risk Event.',
  DetectedBy: 'Detected By',
  Description: 'Description',
  Tags: 'Tags',
  conclusion: 'Conclusion',
  "lessThan":"less than",
  "greaterThan":"greater than"
};
