export default {
  'ruleName': 'Risk Name',
  'urgency': 'Severity',
  'riskStatus': 'Status',
  'alarmTime': 'Alert time',
  'triage': 'Triage',
  'owner': 'Assigner',
  'ruleType': "Rule Type",
  'assignmentTime': 'Assignment Time',
  'triageTime': 'Triage Time',
  'closeTime': 'Close Time',
  'totalTime': 'Total Time',
  'time': 'Last Detection Time',
  Summary: 'Summary',
  Statistic: 'Statistic',
  Dataset: 'Dataset',
  Rule: 'Rule',
  Triggers: 'Triggers',
  Step: 'Step',
  HideChart: 'Hide Chart',
  DisplayChart: 'Display Chart',
  Setting: 'Setting',
  new: 'New',
  investigating: 'Investigating',
  close: 'Close',
  list: 'List',
  bar: 'Bar chart',
  line: 'Line chart',
  pie: 'Pie chart',
  countLog: 'Count of logs',
  Urgency: 'Urgency',
  Timeshold: 'Timeshold',
  'RuleScope': 'Rule Scope',
  'LogType': 'Log Type',
  DataSource: 'Data source',
  'PrimaryKey': 'Primary key',
  'StatisticalFields': 'Statistical Fields',
  'CompareCondition': 'Compare Condition',
  'triggers': 'Dataset comparison triggers',
  "conclusion": "Conclusion",
  showField: 'Show Field',
  Order: 'Order',
  Content: 'Content',
'pleasechoose':'Please select the events that are not closed',
  'pleasechoosesame':'Please select the events under the same tenant',
  'pleasechooseown':'Please select the events assigned to the current user for disposal',
  'pleasechoosedisposal':'Please select the events with disposal results',
  'pleasechoosestatus':'Please select events with the same status',
  'pleasechooseclose':'Please select events that are closed',
  'pleasechooseopen':'Please select events that are not closed',
  'ruleNames':'Rule Name',
  'eventType':'Event Type',
  'Executionfrequency':'Execution frequency',
  'Creator':'Creator',
  'Creationtime':'Creation time',
  'updatetime':'Last modification time',
  'periodoffocus':'Period of focus',
  'Searchcondition':'Search condition',
  'Summaryfields':'Summary fields',
};
