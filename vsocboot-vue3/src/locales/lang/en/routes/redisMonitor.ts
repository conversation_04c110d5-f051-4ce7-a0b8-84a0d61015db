export default{
    redisClusterInformation: 'Redis Cluster Information',
    deploymentMode: 'Deployment Mode',
    instanceNumber: 'Instance Number',
    selectedInstanceId: 'Selected Instance ID',
    noInstanceData: 'No Instance Data',
    cluster: 'Cluster',
    singleNode: 'Single Node',
    clientConnectionNumber: 'Client Connection Number',
    perSecondOperationNumber: 'Per Second Operation Number',
    keyHitRate: 'Key Hit Rate',
    memoryFragmentationRatio: 'Memory Fragmentation Ratio',
    memoryUsage: 'Memory Usage',
    aofStatus: 'AOF Status',
    lastRdbSave: 'Last RDB Save',
    slaveNumber: 'Slave Number',
    loadRedisMetricsFailed: 'Load Redis Metrics Failed',
    loadRedisInstanceListFailed: 'Load Redis Instance List Failed',
    enabled: 'Enabled',
    disabled: 'Disabled',
    selectedInstance: 'Selected Instance Host',
}