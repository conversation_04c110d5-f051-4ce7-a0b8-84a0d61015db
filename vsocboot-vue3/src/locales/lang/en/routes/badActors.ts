export default {
  tenant: 'Tenant Name',
  members: 'Duration',
  attackLatestTime: 'Last Alert Time',
  targetNum: 'Attack IP',
  attackNum: 'Attack Times',
  attackWayNum: 'Attack Technique',
  assign: 'Assign',
  threatScore: 'Score',
  owner: 'Assigner',
  ownScore: 'Own Score',
  'badActor': 'Bad Actor',
  'myScore': 'My Score',
  'totalScore': 'Total Score',
  'severity': 'Severity',
  'tenantsAffected': 'Tenants Affected',
  'firstDetectionTime': 'First Detection Time',
  'lastDetectionTime': 'Last Detection Time',
  'duration': 'Duration',
  'numberOfIPAffected': 'Number Of IP Affected',
  'attackTimes': 'Attack Times',
  'numberOfTechnique': 'Number Of Technique',
  'assigner': 'Assigner',
  'triage': 'Triage',
  'status': 'Status',
  'assignmentTime': 'Assignment Time',
  'triageTime': 'Triage Time',
  'closeTime': 'Close Time',
  'totalTime': 'Total Time',
  'score': 'Score',
  'numberOfTenant': 'Number Of Tenant',
  'StatisticsSeverity': 'Statistics by severity',
  'StatisticsDuration': 'Statistics by duration',
  'StatisticsCount': 'Statistics by count',
  'StatisticsAttackName': 'Statistics by Attack name',
  'Overview': 'Overview',
  'ViewTenant': 'View by tenant',
  'BadActor': 'Bad Actor',
  'HistoryBadActor': 'History bad actor',
  'all': 'All',
  'LogSource': 'Log Source',
  'DestinationIP': 'Destination IP',
  'AttackName': 'Attack Name',
  'AttackCount': 'Attack Count',
  'Score': 'Score',
  chooseTenant: 'Choose Tenant',
  conclusion: 'Conclusion',
  chooseAssigner: 'Choose Assigner',
  chooseTriage: 'Choose Triage',
  'pleasechoose':'Please select the events that are not closed',
  'pleasechoosesame':'Please select the events under the same tenant',
  'pleasechooseown':'Please select the events assigned to the current user for disposal',
  'pleasechoosedisposal':'Please select the events with disposal results',
  'pleasechoosestatus':'Please select events with the same status',
  'pleasechooseclose':'Please select events that are closed',
  'pleasechooseopen':'Please select events that are not closed',
}
