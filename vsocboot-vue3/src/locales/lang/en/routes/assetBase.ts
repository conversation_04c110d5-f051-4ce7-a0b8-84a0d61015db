export default {
  tenant: 'Tenant',
  hostName: 'Host Name',
  ipv4: 'IPv4',
  ipv6: 'IPv6',
  safeStatus: 'Safe Status',
  onlineStatus: 'Online Status',
  assetType: 'Asset Type',
  assetSubtype: 'Asset Sub-Type',
  assetLevel: 'Asset Level',
  tag: 'Tag',
  assetGroup: 'Asset Group',
  source: 'Source',
  deviceManufacturer: 'Device Manufacturer',
  deviceVersion: 'Device Version',
  deviceType: 'Device Type',
  whetherVm: 'Whether VM',
  location: 'Location',
  cpuArchitecture: 'CPU Architecture',
  memery: 'Memery',
  hardDisk: 'Hard Disk',
  graphicsCard: 'Graphics Card',
  os: 'OS',
  Os: 'Operation system',
  ov: 'OS version',
  cpu: 'CPU Specification',
  memorysp: 'Memory Specification',
  hs: 'Hard Specification',
  kernelVersion: 'Kernel Version',
  mac: 'Mac',
  personInCharge: 'Person In Charge',
  VMName: 'VM name',
  VMIP: 'VM IP',
  VMos: 'VM os',
  AssetLevel: 'Asset Level',
  logSource: 'Whether log source',
  IPAddress: 'IP Address',
  CloudInfo: 'Cloud Service Info',
  CloudID: 'Cloud Server ID',
  zone: 'Availability Zone',
  ExpirationTime: 'Expiration time',
  domainurl: 'Accessible domain/url',
  allAsset: 'All Asset',
  baseInfo: 'Base info',
  deviceInfo: 'Device info',
  systemInfo: 'System info',
  networkInfo: 'Network info',
  primaryIPAddress: 'Primary IP Address',
  otherIPAddress: 'Other IP Address',
  assetName: 'Asset name',
  assetNumber: 'Asset number',
  whetherVM: 'Whether VM',
  phoneNumber: 'Phone number',
  email: 'Email',
  memory: 'Memory',
  assetGroupPlaceholder: 'Please select asset group',
  updateAsset: 'Update asset',
  agentInfo: 'Agent info',
  iDNumber: 'ID Number',
  lastOnlineTime: 'Last Online Time',
  lastOfflineTime: 'Last Offline Time',
  agentVersion: 'Agent Version',
  installationPath: 'Installation Path',
  configurePath: 'Configure Path',
  logPath: 'Log Path',
  application: 'Application info',
  appversion: 'Application version',
  appvendor: 'Application vendor',
  deviceVendor: 'Device Vendor',
  deviceModal: 'Device Modal',
  deviceId: 'Device ID',
  publicCloud: 'Public Cloud',
  privateCloud: 'Private Cloud',
  cloudType: 'Public/Private Cloud',
  cloudprovider: 'Cloud Service Provider',
  credentialname: 'Credential Name',
  status: 'Status',
  permission: 'Permission',
  publisher: 'Publisher',
  publishtime: 'Publish Time',
  lastupdatetime: 'Last Update Time',
  addcredential: 'Add a credential',
  api: 'API',
  actions: 'Actions',
  editable: 'Editable',
  usable: 'Usable',
  method: 'Method',
  rulename: 'Rule Name',
  defaultnumber: 'Default Number',
  parsingmethod: 'Parsing Method',
  addingway: 'Adding Way',
  action: 'Action',
  logsourceswitch: 'Log source switch',
  logsourcetext1:
    'Whether the asset is configured as a log source, only logs sent to the system by the log source will be received and parsed by the system',
  receive: 'Receive Syslog',
  logsourcetext2: 'When the "Log API" is used, the system will automatically invoke the API to obtain logs of the asset.',
  logapi: 'Log API',
  logsourcetext3: 'When "Receive Syslog" is selected, the log source should actively send logs to the system Proxy.',
  logsourcetext4:
    'Only logs sent by the configured lP will be received bythe system. By default, the Primary lP is used as the logsource lP of the asset.lf the asset does not use thenetwork card of the Primary lP as the log sending lP,please select another lP',
  logsourceip: 'Log source IP',
  logencodeformat: 'Log encode format',
  timeThreshold: 'Time threshold',
  iflogapi: 'Whether to enable the log api',
  hostip: 'Host IP',
  logport: 'Log port',
  username: 'User name',
  password: 'Password',
  addasset: 'Add an asset',
  associateasset: 'Associate existing asset',
  logsource: 'Log source',
  asssetName: 'Asset Name',
  ipaddress: 'IP Address',
  integration: 'Integration',
  assetlevel: 'Asset Level',
  onlinestatus: 'Online Status',
  safestatus: 'Safe Status',
  logsourcesetting: 'Log source setting',
  integrationsetting: 'Plugin setting',
  tab1: 'Additional Information',
  tab2: 'Log source',
  tab3: 'Credentials',
  tab4: 'Related assets',
  tab5: 'Integration',
  ip: 'IP Address',
  osversion: 'OS version',
  cloudservername: 'Cloud Server Name',
  cloudProvider: 'Cloud Service Provider',
  des: 'Description',
  cpuSpecification: 'CPU Specification',
  accessibleUrl: 'Domain/URL',
  applicationName: 'Application Name',
  proxyconfigured: 'Proxy Configured',
  searchtext: 'Search Asset name、IP Address',
  changeintegration: 'Change Plugin',
  deleteintegration: 'Delete Plugin',
  addintegration: 'Add Plugin',
  loadintegration: 'Load an plugin',
  unparsedLogs: 'Unparsed logs',
  parsedLogs: 'Parsed logs',
  Host: 'Host',
  Cloud: 'Cloud',
  Virtual: 'Virtual',
  Network: 'Network',
  Cybersecurity: 'Cybersecurity',
  IOT: 'IOT',
  Application: 'Application',
  addingmethod: 'Adding Method',
  Manually: 'Manually',
  MDPS: 'MDPS',
  Assetdiscovery: 'Asset discovery',
  Import: 'Import',
  lossourcestatus: 'Log source status',
  normal: 'Normal',
  abnormal: 'Abnormal',
  integrationPlugin: 'Integration Plugin',
  pleaseselectproxy: 'Please select proxy',
  pleaseinputIp: 'Please input IP',
  pleaseinputIpIn: 'Please input IP In Log Source',
  pleaseSelectSameTenant: 'Please select same tenant asset',
  pleaseSelectData: 'Please select data',
};
