export default {
  ruleName: 'Rule Name',
  urgency: 'Urgency',
  timeVal: 'Timeshold',
  datasetNum: 'Dataset num',
  statisticNum: 'Statistic num',
  datasetComparisonTriggers: 'Dataset comparison triggers',
  statisticTriggers: 'Statistic triggers',
  createBy: 'Creator',
  createTime: 'Creation time',
  status: 'Status',
  ruleScope: 'Rule Scope',
  tenant: 'Tenant',
  tenantType: 'Creator Type',
  timeOfOccurrence: 'Time range',
  securityLog: 'Security log',
  hostLog: 'Host log',
  networkLog: 'Network log',
  operationLog: 'Operation log',
  workingHours: 'Working Hours',
  nonWorkingHours: 'Non Working Hours',
  Finish: 'Finish',
  Step: 'Step',
  Start: 'Start',
  End: 'End',
  Relationship: 'Relationship',
  TimeInterval: 'Time interval',
  Dataset: 'Dataset',
  tip1: 'At least two nodes are required',
  name: 'name',
  switch: 'switch',
  viewAll: 'View all',
  continueToView: 'Continue to view',
  showNode: 'Show Node',
  conclusion: 'Conclusion',
  lineTip: 'There are no configuration conditions at the red line!',
  nodeTip: 'The {0} node condition cannot be empty!',
  fieldTypeMismatch: 'Field Type is not matched ,please choose the same type',
  filter: "Filter",
  statistic: "Statistic",
  groupField: "Group Field",
  statisticField: "Statistic Field",
  distinct:"Distinct"
};
