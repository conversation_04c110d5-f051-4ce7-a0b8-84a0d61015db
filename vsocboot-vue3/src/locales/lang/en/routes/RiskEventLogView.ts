export default {
  'eventType': 'Event Type',
  'eventName': 'Event Name',
  'deviceName': 'Log Source',
  'fromIp': 'Log Source IP',
  'updateTime': 'Last Alert Time',
  'logCount': 'Log Count',
  'severity': 'Severity',
  'srcIp': 'Source IP',
  'dstIp': 'Destination IP',
  'type': 'Type',
  'riskType': 'Risk Type',
  'tag': 'Tag',
  'owner': 'Assigner',
  'triage': 'Triage',
  'eventStatus': 'Status',
  'true': 'True Positive',
  'false': 'False Positive',
  'other': 'Pending Verification',
  'unknown': 'Unknown',
  'assignmentTime': 'Assignment Time',
  'triageTime': 'Triage Time',
  'closeTime': 'Close Time',
  'totalTime': 'Total Time',
  'time': 'Time',
  'ticket': 'Ticket',
  'noTicket':'No Ticket',
  'hasTicket':'Ticket',
  'pleasechoose':'Please select the events that are not closed',
  'pleasechoosesame':'Please select the events under the same tenant',
  'pleasechooseown':'Please select the events assigned to the current user for disposal',
  'pleasechoosedisposal':'Please select the events with disposal results',
  'pleasechoosestatus':'Please select events with the same status',
  'pleasechooseclose':'Please select events that are closed',
  'pleasechooseopen':'Please select events that are not closed',
  "vendorProduct":"Vendor/Product",
  "switchTable":'Switch table view',
  "expend":'Expand all bars',
  "fold":'Folding all bars',
  "whitelist":'Add Whitelist',
  "eventSource": "Event Source",
  "riskEvent": "Security Event",
  "statisticEvent": "Statistic Event",
  "orderEvent": "Order Event",
  "contentEvent": "Content Event",

};
