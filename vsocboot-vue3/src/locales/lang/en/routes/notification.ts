export default {
  receiversetting: 'Receiver settings',
  sendemailconf: 'Send email configuration',
  accountname: 'Account name',
  emailaccount: 'Email account',
  tenantmssp: 'Tenant/MSSP',
  creator: 'Creator',
  creatortype: 'Creator type',
  createtime: 'Creation time',
  tenant: 'Tenant',
  sendemail: 'Sender email',
  mailserver: 'Mail server',
  port: 'Port',
  defaultmail: 'Default mail server',
  name: 'Name',
  detectionrule: 'Detection Rules',
  mlrule: 'ML Rule',
  riskevent: 'Risk Event',
  searchtext: 'input search text',
  search: 'Search',
  rulename: 'Rule name',
  Severity: 'Severity',
  ruletype: 'Rule type',
  communication: 'Communication protocol',
  emailserversetting: 'Email service settings',
  skipauthentication: 'Skip certificate authentication',
  yes: 'YES',
  no: 'No',
  defaultemailcomm: 'Default email configuration',
  sendemailaddress: 'Send email address',
  password: 'Send email password',
  emailver: 'Email verification',
  sendemailname: 'Send email name',
  none: 'None',
  StartTLS: 'StartTLS',
};
