export default {
  new : 'New Rules',
  rule : 'Rule',
  aggregation : 'Aggregation Rule',
  ruleName: 'Rule Name',
  ruleNameRule: 'Please enter Rule Name',
  riskType: 'Risk Type',
  urgency: 'Urgency',
  riskGroupby: 'Group By',
  limits: 'Aggregation limits',
  minLimits: 'Minimum aggregation limits',
  maxLimits: 'Max aggregation limits',
  timeThreshold: 'Time Threshold',
  timeThresholdType: 'Time Threshold Type',
  advancedRules: 'Advanced Rules',
  ruleStatus: 'Status',
  riskDesc: 'Description',
  createBy: 'Creator',
  createTime: 'Creation Time',
  updateBy: 'Modifier',
  updateTime: 'Modify Time',
  falseRate: 'False Position Rate',
  falseCount: 'False Position Count',
};
