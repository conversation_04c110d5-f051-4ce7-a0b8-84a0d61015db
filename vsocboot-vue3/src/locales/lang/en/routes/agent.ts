export default {
  startCol: 'Start collection',
  download: 'Download',
  pIptPwdAd: 'Please enter your login password',
  drvStoTi: 'Driver Stop Time',
  pasingmd5: 'Verifying MD5...',
  upgraFail: 'Failed',
  prepupgra: 'Upgradable',
  upgrading: 'Upgrading',
  Upgradeable: 'Upgradable',
  lastVer: 'Latest Version',
  cretver: 'Current Version',
  abnresn: 'Abnormal Cause',
  abRest: 'Abnormal Reboot',
  driStu: 'Driver State',
  clearertit: 'Are you sure you want to clear the abnormal cause record ?',
  unisagenttit: 'Are you sure you want to uninstall the Agent ?',
  cfmpwdtitle: 'Are you sure you want to revert to the previous version ?',
  cfmupgagt: 'Are you sure you want to upgrade the main program ?',
  suruinsrst: 'Are you sure you want to restart the Agent ?',
  cfmColD: 'Are you sure you want to collect the downloaded files ?',
  agtRunLog: 'Agent Run Log',
  agtDumpLog: 'Agent Dump Log',
  sysDumpLog: 'System Dump Log',
  reCol: 'Recollect',
  colting: 'Collecting…',
  colct: 'Collect',
  copClid: 'Copied to Pasteboard',
  upgraagt: 'Upgrade',
  restartAgent: 'Restart Agent',
  unisagent: 'Uninstall Agent',
  clearer: 'Clear rebooted records',
  deteNor: 'Normal',
  deteErr: 'Abnormal',
  collectionPointStatus: 'Acquisition Point Status',
  suspend: 'Pause',
  errorCause: 'Failure Reason',
  driverLoadError: 'Failed to Load Driver',
  plfjgns: 'Please upload the correct Upgrade Package',
  shyfhs: 'Time remaining:',
  updPkg: 'Upload Package',
  lpbz: 'The system is in the state of "downtime obstacle avoidance"‘ The security drive is not loaded under the "downtime obstacle avoidance" status, and the security protection ability is weak. After troubleshooting, the "restart agent" can return to normal.',
  plfjwgns: 'The size of the server upgrade package cannot be more than 2G',
  mergeing: 'The file is being merged, please wait a moment',
  checking: 'Checking',
  agt: 'Agent Status',
  plfjgna: 'Please upload the correct installation package',
  dstns: 'Agent Operational Status Details',
  collection: 'collect',
  acpno: 'Agent driver, collection, and plug-in are all working properly.',
  acperr:
    "There are anomalies, security protection is weak, after troubleshooting the problem 'reboot Agent' can be restored to normal.",
  plgsta: 'Plugin Status',
  cpumsnfr:
    'Agent reboots frequently due to resource limitation issues, it is recommended to work with the service staff to investigate and raise the resource thresholds',
  cleare: 'The host has no abnormal records for the time being, no cleanup is required',
  ukgsips:
    'The uninstallation of software that uses the same hook points as the Agent on {0} and other {1} hosts may cause some of its functionality to fail if there are flaws in the processing logic of the software.',
  ukgsip:
    'The presence of software on host {0} that uses the same hook points as the Agent and whose processing logic is flawed may cause some of its functionality to fail when uninstalled',
  agttips:
    'The system will uninstall the Agent on the host computer and erase the data, the host computer will lose the ability to protect the security, please operate with caution',
  unsgtips:
    'If the host has software that uses the same hook points as the Agent, and its processing logic is flawed, uninstallation may cause some of its functions to fail',
  bfzt: 'Partial suspension',
  plfjwgnss: 'The size of the Agent installation package should not exceed 1.5G.',
  dlz: 'Queueing',
  error_msg:'The network connection between the UMP and the MDPS Server appears to be faulty. Please check and try again.',
  latestUpdateHost:'Latest update host',
  updatableHost:'Updatable host',
  abnormalHost:'Abnormal host'
};
