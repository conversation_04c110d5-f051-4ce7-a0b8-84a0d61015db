export default {
  entityType: 'Entity Type',
  entityName: 'Entity Name',
  statDate: 'State Date',
  featureValue: 'Feature Value',
  handler: 'Handler',
  handlerTime: 'Handler Time',
  isRealAnomaly: 'Is Real Anomaly',
  alertDesc: 'Alert Desc',
  featureFullName: 'Feature Full Name',
  alertLevel: 'Alert Level',
  owner: 'Assigner',
  triage: 'Triage',
  eventStatus: 'Status',
  true: 'True Positive',
  false: 'False Positive',
  other: 'Pending Verification',
  unknown: 'Unknown',
  assignmentTime: 'Assignment Time',
  triageTime: 'Triage Time',
  closeTime: 'Close Time',
  totalTime: 'Total Time',
  time: 'Time',
  ticket: 'Ticket',
  noTicket: 'No Ticket',
  hasTicket: 'Ticket',
  severity: 'Severity',
};
