export default {
  name: 'Credential Name',
  type: 'Credential Type',
  user: 'User',
  pwd: 'Password',


  passwd: 'Password',
  host: 'Host',
  port: 'Port',
  encrypt: 'SSL/TLS',

  trust: 'Allow Self-Signed Certificates',
  Credential: 'Credential',
  lastUpdate: 'Last Update',
  created: 'Created',

  timeout:'Connect Timeout',
  database:'Database',
  connectusing:'Connect Using',
  privatekey:'Private Key',
  passphrase:'Passphrase',

  url:'URL',
  authorization:'Authorization',
  
}
