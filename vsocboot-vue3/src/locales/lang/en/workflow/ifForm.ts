export default {
  conditions: 'Conditions',
  conditionsTooltip: 'The type of values to compare',
  value1: 'Value1',
  value1Tooltip: 'The value to compare with the second one',
  operation: 'Operation',
  operationTooltip: 'Operation to decide where the the data should be mapped to',
  value2: 'Value2',
  value2Tooltip: 'The value to compare with the first one',
  auth: 'Combine',
  authTooltip: 'If multiple rules got set this settings decides if it is true as soon as ANY condition matches or only if ALL get meet',
  formatter: 'Formatter',
  formatterTooltip: 'Fill in the time format expression and use this format to convert the time string filled in above into a time object for size comparison.' +
    '2024(yyyy)-08(MM)-05(dd)T(\'T\')10(HH):19(mm):24(ss).449(SSS)+08:00(XXX)[Asia/Shanghai](\'[\'VV\']\').' +
    'For example:2024-08-05T10:28:44.378+08:00[Asia/Shanghai] corresponds to the' +
    ' format：yyyy-MM-dd\'T\'HH:mm:ss.SSSXXX\'[\'VV\']\'',
}
