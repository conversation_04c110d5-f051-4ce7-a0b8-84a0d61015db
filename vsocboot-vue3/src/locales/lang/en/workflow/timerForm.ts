export default {
  nodeName: 'Schedule Trigger',
  desc: 'This workflow will run on the schedule you define here once you activate it.\n\t' +
    'For testing, you can also trigger it manually: by going back to the canvas and clicking‘execute workflow’',
  ruleTitle: 'Trigger Rules',
  timeType: 'Trigger Interval',
  timeValue: ' Between Triggers',
  minute: 'Trigger at Minute',
  hour: 'Trigger at Hour',
  weekdays: 'Trigger on Weekdays',
  day: 'Trigger at Day of Month',
  cron: 'Expression',
  Sunday: 'Sunday',
  Monday: 'Monday',
  Tuesday: 'Tuesday',
  Wednesday: 'Wednesday',
  Thursday: 'Thursday',
  Friday: 'Friday',
  Saturday: 'Saturday',
  Seconds: 'Seconds',
  Minutes: 'Minutes',
  Hours: 'Hours',
  Days: 'Days',
  Weeks: 'Weeks',
  Months: 'Months',
  Custom: 'Custom(Cron)',
  cronTip: 'Format: [Minute] [Hour] [Day of Month] [Month] [Day of Week]'
}
