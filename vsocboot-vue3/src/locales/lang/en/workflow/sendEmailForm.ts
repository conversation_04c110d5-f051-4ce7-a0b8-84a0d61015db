export default {
  credential:'Credential to connect with',
  from: 'From Email',
  fromTooltip: 'Email address of the sender. You can also specify a name: <PERSON> .',
  to: 'To Email',
  toTooltip: 'Email address of the recipient. You can also specify a name: <PERSON> .',
  toAddTooltip:'Add Email',
  subject: 'Subject',
  subjectTooltip: 'Subject line of the email',
  contentType: 'Email Format',
  contentTypeTooltip: 'Email Subject',
  contentHtml: 'HTML',
  contentHtmlTooltip: 'HTML text message of email',
  contentText: 'Text',
  contentTextTooltip: 'Plain text message of email',
  cc: 'CC Emai',
  ccTooltip: 'Email address of CC recipient',
  ccAddTooltip:'Add Email',
  bcc: 'BCC Email',
  bccTooltip: 'Email address of BCC recipient',
  bccAddTooltip:'Add Email',
  ignore: 'Ignore SSL Issues',
  ignoreTooltip: 'Whether to connect even if SSL certificate validation is not possible',



}
