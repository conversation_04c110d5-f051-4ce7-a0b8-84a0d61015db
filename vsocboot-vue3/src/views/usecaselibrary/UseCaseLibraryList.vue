<template>
  <div class="page_title">Use Case Library</div>
  <div  class="search_transparent">
    <!--引用表格-->
   <BasicTable @register="registerTable" :rowSelection="rowSelection" :isSearch="isSearch" >
     <!--插槽:table标题-->
          <template #form-formFooter>
<!--            <a-button  :class="{'btn-checked': isSearch == true}" type="text" @click="isSearch=!isSearch" preIcon="ant-design:filter-outlined"> {{ t('common.filter') }}-->
<!--            </a-button>-->
             <a-button type="primary" @click="handleAdd"  >   <span class="soc ax-com-Add ax-icon"></span>{{t('common.add')}}</a-button>
<!--             <a-button  type="primary" preIcon="ant-design:export-outlined" @click="onExportXls"> 导出</a-button>
             <j-upload-button  type="primary" preIcon="ant-design:import-outlined" @click="onImportXls">导入</j-upload-button>-->
             <a-dropdown v-if="selectedRowKeys.length > 0">
                 <template #overlay>
                    <a-menu>
                      <a-menu-item key="1" @click="batchHandleDelete">
                        <Icon icon="ant-design:delete-outlined"></Icon>
                        {{t('common.delText')}}
                      </a-menu-item>
                    </a-menu>
                  </template>
                  <a-button> {{t('common.batchDelText')}}
                     <span class="soc ax-com-Arrow-down"></span>
                  </a-button>
            </a-dropdown>
          </template>
       <!--操作栏-->
      <template #action="{ record }">
        <TableAction :actions="getTableAction(record)"  />
      </template>
    </BasicTable>

    <!-- 表单区域 -->
    <UseCaseLibraryModal @register="registerModal" @success="handleSuccess"></UseCaseLibraryModal>
  </div>
</template>

<script lang="ts" name="usecaselibrary-useCaseLibrary" setup>
  import {ref, computed, unref} from 'vue';
  import {BasicTable, useTable, TableAction} from '/@/components/Table';
  import {useModal} from '/@/components/Modal';
  import { useListPage } from '/@/hooks/system/useListPage'
  import UseCaseLibraryModal from './modules/UseCaseLibraryModal.vue'
  import {columns, searchFormSchema} from './UseCaseLibrary.data';
  import {list, deleteOne, batchDelete, getImportUrl,getExportUrl} from './UseCaseLibrary.api';
  import {useI18n} from "/@/hooks/web/useI18n";
  const { t } = useI18n();
  //search
  const isSearch = ref(true);
  //注册model
  const [registerModal, {openModal}] = useModal();
  //注册table数据
  const { prefixCls,tableContext,onExportXls,onImportXls } = useListPage({
       tableProps:{
            title: 'use case library',
            api: list,
            columns,
            canResize:false,
            formConfig: {
               labelWidth: 120,
               schemas: searchFormSchema,
               autoSubmitOnEnter:true,
               showAdvancedButton:true,
             },
         defSort: {
           column: 'createTime',
           order: 'desc',
         },
            actionColumn: {
                width: 140,
             },
        },
        exportConfig: {
             name:"use case library",
             url: getExportUrl,
           },
           importConfig: {
             url: getImportUrl
           },
   })

   const [registerTable, {reload},{ rowSelection, selectedRowKeys }] = tableContext

   /**
    * 新增事件
    */
   function handleAdd() {
     openModal(true, {
       isUpdate: false,
       showFooter: true,
     });
   }
   /**
    * 编辑事件
    */
  function handleEdit(record: Recordable) {
     openModal(true, {
       record,
       isUpdate: true,
       showFooter: true,
     });
   }
   /**
    * 详情
   */
  function handleDetail(record: Recordable) {
     openModal(true, {
       record,
       isUpdate: true,
       showFooter: false,
     });
   }
   /**
    * 删除事件
    */
   async function handleDelete(record) {
     await deleteOne({id: record.id}, reload);
   }
   /**
    * 批量删除事件
    */
   async function batchHandleDelete() {
     await batchDelete({ids: selectedRowKeys.value}, reload);
   }
   /**
    * 成功回调
    */
   function handleSuccess({isUpdate, values}) {
      reload();
   }
   /**
      * 操作栏
      */
   function getTableAction(record){
       return [
         {
           label: t('common.editText'),
           onClick: handleEdit.bind(null, record),
         },{
           label: t('common.delText'),
           popConfirm: {
             title: t('common.delConfirmText'),
             confirm: handleDelete.bind(null, record),
           }
         }
       ]
     }


</script>
<style scoped>

</style>
