<template>
<!--  返回-->
  <div  class="px-16px py-12px ax-border-bottom">
    <div class="flex flex-row items-center gap-8px">
      <div class="ax-icon-button ax-icon-small" @click="goBack">
        <span class="soc ax-com-Arrow-left ax-icon"></span>
      </div>
      <div class="font16 fcolor">
        {{ t('common.goBack') }}
      </div>
    </div>
  </div>
  <div class="flex flex-col px-16px pt-16px gap-8px h-[calc(100%-49px)]">
    <!--  基础信息-->
    <div class="ax-bg-dark2 py-12px px-16px font13 fcolor1">
    <div> {{ dataInfo?.rule_name }}</div>
    <div>
      {{ t('routes.FlowmlRule.urgency') }} : {{ urgencyMap[dataInfo?.urgency] }}
    </div>
    <div>
      {{ t('routes.FlowmlRule.timeVal') }} : {{ dataInfo?.time_val }}
      {{ timeTypeMap[dataInfo?.time_type] }}
    </div>
    <div v-if="isAdministrator()">
      {{ t('routes.FlowmlRule.ruleScope') }} : {{ RULE_SCOPE[dataInfo?.ruleScope] }}
    </div>
    <div v-if="isAdministrator()">
      {{ t('common.tenant') }} : {{ dataInfo?.tenantName }}
    </div>
  </div>
    <div class="flex-1 relative">
      <FlowmlRuleWriting ref="flowmlRule" :nodeTree="nodeMap" :id="mlRuleId"
                         :isShow="true"
                         :nodeTreeMap="nodeDataMap"/>
    </div>

  </div>
</template>

<script setup lang="ts">
import {ref} from 'vue'
import {useRouter} from "vue-router";
import {queryById} from "/@/views/mlRule/MlRuleVO.api";
import FlowmlRuleWriting from "/@/views/flowmlRule/modules/FlowmlRuleWriting.vue";
import {isAdministrator} from "/@/utils/auth";
import {useI18n} from "/@/hooks/web/useI18n";
import {RULE_SCOPE} from "/@/utils/valueEnum";

const {t} = useI18n();
const router = useRouter()

const urgencyMap = {
  1: t("common.Critical"),
  2: t("common.High"),
  3: t("common.Middle"),
  4: t("common.Low"),
  5: t("common.Information")
}

const timeTypeMap = {
  1: t("common.min"),
  2: t("common.hour")
}

const mlRuleId = ref("")
let id = sessionStorage.getItem("MlRuleVOModal_id")
if (id) {
  mlRuleId.value = id
}
const dataInfo = ref<any>({})
const nodeMap = ref({})
const nodeDataMap = ref({})
const flowmlRule = ref()

loadRuleInfo()

function loadRuleInfo() {
  queryById({id: mlRuleId.value}).then(data => {
    console.log('data', data)
    dataInfo.value.rule_name = data.ruleName
    dataInfo.value.urgency = data.urgency
    dataInfo.value.time_val = data.timeVal
    dataInfo.value.time_type = data.timeType
    dataInfo.value.ruleScope = data.ruleScope
    dataInfo.value.tenant = data.tenant
    dataInfo.value.tenantName = data.tenantName

    nodeMap.value = JSON.parse(data.nodeMap)
    nodeDataMap.value = JSON.parse(data.nodeDataMap)
    flowmlRule.value.viewInit(nodeMap.value, nodeDataMap.value)

  })
}

function goBack() {
  router.go(-1)
}
</script>

<style scoped lang="less">
</style>

