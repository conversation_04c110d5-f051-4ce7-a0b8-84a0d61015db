import { BasicColumn, FormSchema } from '/@/components/Table';
import { useI18n } from '/@/hooks/web/useI18n';
import { FieldTypeOptions } from '/@/utils/ckTable';
import { createVNode } from 'vue';
import { useUserStore } from '/@/store/modules/user';

const { t } = useI18n();
const userStore = useUserStore();

//该属性是国际化中英文配置，需要在/src/locales/lang/en/routes中创建TblField.ts文件，把下方属性复制到文件中
/*
export default {
     'fieldName': '名称',
     'fieldLabel': 'label显示名称',
     'fieldValue': '字段',
     'fieldSource': '1:SECURITY,2:HOST,3:network,4:operation,5:riskEvent,6:CorrelationEvent,7:mlevent,8:asset,9:ticketHistory,10:bad actors',
     'fieldType': 'string,number,ipv4',
     'fieldSort': '字段排序',
     'fieldState': '1显示',
     'quickSort': '快捷方式排序',
     'quickState': '快捷方式 1显示',
     'fieldStatis': '日志查询，是否允许统计1可以',
     'fieldNumType': '用于统计单位：1数字类型2流量类型',
     'classFieldName': '类对应的名称',
     'classFieldType': '类对应的类型',
};
*/

export const columns: BasicColumn[] = [
  {
    title: t('routes.TblField.fieldType'),
    dataIndex: 'fieldType',
  },
  {
    title: t('routes.TblField.fieldValue'),
    dataIndex: 'fieldValue',
  },
  {
    title: t('routes.TblField.fieldTag'),
    dataIndex: 'tagName',
    customRender(opt) {
      if (opt.value) {
        const list = opt.value.split(',');
        const names: any = [];
        for (const i in list) {
          names.push(createVNode('span', { class: 'tags' }, [list[i]]));
        }
        return createVNode('div', { class: 'tags_div' }, names);
      }
    },
  },
  {
    title: t('routes.TblField.description'),
    dataIndex: 'description',
  },
  {
    title: t('routes.TblField.example'),
    dataIndex: 'example',
  },
];

export const searchFormSchema: FormSchema[] = [
  {
    label: '',
    field: 'search',
    component: 'JInput',
    componentProps: {
      search:true,
      type:'',
      placeholder: t('routes.TblField.search'),
    },
  },
  {
    label: '',
    field: 'fieldType',
    component: 'Select',
    componentProps: {
      options: FieldTypeOptions,
      placeholder: t('routes.TblField.fieldType'),
    },
  },
  {
    label: '',
    field: 'tag',
    component: 'JSearchSelect',
    componentProps: {
      dict: "fieldTagDict,create_by='" + userStore.getUserInfo.username + "'",
      mode: 'multiple',
      placeholder: t('routes.TblField.fieldTag'),
    },
  },
];

export const formSchema: FormSchema[] = [
  { label: '', field: 'id', component: 'Input', show: false },
  {
    label: t('routes.TblField.fieldValue'),
    field: 'fieldValue',
    component: 'Text',
  },
  {
    label: t('routes.TblField.fieldType'),
    field: 'fieldType',
    component: 'Text',
    componentProps: {
      valueMap: {
        string: 'String',
        time: 'Time',
        number: 'Int',
        ipv4: 'IPV4',
      },
    },
  },
  {
    label: createVNode('div', { style: 'margin-top:15px' }, [t('routes.TblField.fieldTag') + ':']),
    itemProps: {
      colon: false,
    },
    field: '-',
    component: 'Text',
    slot: 'tag',
  },
  {
    label: t('routes.TblField.description'),
    field: 'description',
    component: 'InputTextArea',
    componentProps: {
      autoSize: {
        minRows: 5,
      },
    },
  },
];

export const FIELDTYPE = [
  { label: 'String', value: 'string' },
  { label: 'Time', value: 'time' },
  { label: 'Int', value: 'number' },
  { label: 'IPV4', value: 'ipv4' },
];
