<template>
  <!--引用表格-->
  <div style="padding: 16px" class="table_search flex flex-col gap-8px h-[100%]" id="search_model">
    <!--查询group start-->
    <div class="flex flex-col gap-8px">
      <div class="div_or" v-for="(item, index) in whereModel.whereList" :key="index">
        <div v-if="index > 0" class="mb-8px fcolor1 font-600">OR</div>
        <div class="div_and flex flex-col gap-8px">
          <!--    删除按钮-->
          <div class="soc ax-com-Fault del_img" @click="delGroup(whereModel.whereList, index)"></div>
          <!--    查询内容-->
          <div v-for="(item2, index2) in item" :key="index + '_' + index2" class="flex flex-row gap-8px">
            <!--    删除按钮-->
            <div class="ax-icon-button is_del" @click="delField(item, index2)">
              <span class="soc ax-com-Decrease ax-icon" />
            </div>
            <div class="w-400px">
              <a-select
                v-model:value="item2.field"
                :options="fieldList"
                @blur="checkedElRuleRequired"
                :id="'s_' + index + '_' + index2"
                show-search
                optionFilterProp="label"
                @change="fieldChange('s_' + index + '_' + index2, item2)"
                class="rule_required"
              />
            </div>

            <div class="w-160px">
              <a-select v-model:value="item2.rule" :options="getRuleOptions(item2)" />
            </div>

            <div class="flex flex-row w-540px">
              <div class="flex-1">
                <div v-if="showDictSelect(item2)">
                  <template v-if="item2.field === 'owner'">
                    <JSearchSelect
                      :appendFirstData="ownerFirstOption"
                      :dict="getValOption(item2)"
                      v-model:options="dictOptionsMap[item2.field]"
                      className="rule_required"
                      @blur="checkedElRuleRequired"
                      :id="'val_' + index + '_' + index2"
                      @change="checkSelectChange('val_' + index + '_' + index2)"
                      v-model:value="item2.val"
                    />
                  </template>
                  <template v-else>
                    <JSearchSelect
                      :dict="getValOption(item2)"
                      v-model:options="dictOptionsMap[item2.field]"
                      className="rule_required"
                      @blur="checkedElRuleRequired"
                      :id="'val_' + index + '_' + index2"
                      @change="checkSelectChange('val_' + index + '_' + index2)"
                      v-model:value="item2.val"
                    />
                  </template>
                </div>
                <div v-else-if="showTreeSelect(item2)">
                  <a-tree-select
                    v-model:value="item2.val"
                    style="width: 100%"
                    :dropdown-style="{ maxHeight: '400px', overflow: 'auto' }"
                    :placeholder="t('common.PleaseSelect')"
                    allow-clear
                    showSearch
                    class="rule_required"
                    @blur="checkedElRuleRequired"
                    :id="'val_' + index + '_' + index2"
                    @change="checkSelectChange('val_' + index + '_' + index2)"
                    :tree-data="getValOption(item2)"
                    tree-node-filter-prop="label"
                  />
                </div>
                <div v-else-if="showSelect(item2)">
                  <JSearchSelect
                    :dict-options="getValOption(item2)"
                    className="rule_required"
                    @blur="checkedElRuleRequired"
                    :id="'val_' + index + '_' + index2"
                    @change="checkSelectChange('val_' + index + '_' + index2)"
                    v-model:value="item2.val"
                  />
                </div>
                <!-- showTimeType类型的数据存到另一个属性中，在查询的是后转换成数据库需要的数据 -->
                <div v-else-if="showTimeType(item2)">
                  <a-input v-model:value="item2.timeVal" class="rule_number input-default" @input="checkedElRuleNumber($event)" />
                </div>
                <div v-else-if="fieldTypeMap[item2.field] == 'number'">
                  <a-input v-model:value="item2.val" class="rule_number input-default" @input="checkedElRuleNumber($event)" />
                </div>
                <div v-else>
                  <a-input v-model:value="item2.val" @input="checkedElRuleRequired($event)" />
                </div>
              </div>
              <div style="max-width: 85px; min-width: 85px; padding-left: 5px" v-if="showTimeType(item2)">
                <a-select
                  :options="getValOption(item2)"
                  @blur="checkedElRuleRequired"
                  :id="'time_type_' + index + '_' + index2"
                  @change="checkSelectChange('time_type_' + index + '_' + index2)"
                  class="rule_required"
                  show-search
                  optionFilterProp="label"
                  v-model:value="item2.timeValType"
                />
              </div>
            </div>
          </div>
          <!--    添加按钮-->
          <div>
            <a-button type="primary" ghost @click="add(item)" class="ant-btn-sm">
              <span class="soc ax-com-Add"></span>
              {{ t('common.add') }}
            </a-button>
          </div>
        </div>
      </div>
    </div>
    <!--查询group end-->
    <div>
      <a-button type="primary" ghost @click="addGroup" class="ant-btn-sm">
        <span class="soc ax-com-Add"></span>
        {{ t('common.AddGroup') }}
      </a-button>
    </div>
  </div>
</template>

<script lang="ts" name="notification-detectionRule" setup>
  import { defineEmits, defineProps, ref, watch } from 'vue';
  import { useI18n } from '/@/hooks/web/useI18n';
  import { RISK_STATUS_SELECT, RISK_TYPE_SELECT, TIME_TYPE_SELECT, TRIAGE_STATUS_SELECT } from '/@/utils/valueEnum';
  import { checkedElRuleNumber, checkedElRuleRequired, checkedRule, checkSelectChange } from '/@/utils/checkedRule';
  import { searchTemplateList } from '/@/views/tableSearch/TableSearchData.api';
  import { E_ADVANCED_JUDGE, E_ADVANCED_QUERY, E_ADVANCED_REL, judgeOption, relOption, systemValueOption } from '/@/views/reportChart/ts/systemData';
  import JSearchSelect from '/@/components/Form/src/jeecg/components/JSearchSelect.vue';
  const { t } = useI18n();
  const emits = defineEmits(['update:value']);
  const props = defineProps({
    value: Array,
    systemFilter: Object,
  });

  const checkedList = ref<unknown[]>(props.value || []);
  const searchValue = ref('');
  // 使用ref对象保存搜索条件，确保不会被清空
  const searchInfoRef = ref({ title: '' });

  const sourceTypeMap = {
    risk: {
      showDictSelect: ['socTenantId', 'owner'],
      showOptionSelect: ['type', 'triageStatus', 'eventStatus'],
      showTimeType: ['assignmentConsume', 'triageConsume', 'closeConsume', 'totalConsume'],
      getRuleOptions: ['socTenantId', 'owner', 'type', 'triageStatus', 'eventStatus'],
      type: RISK_TYPE_SELECT,
      triageStatus: TRIAGE_STATUS_SELECT,
      eventStatus: RISK_STATUS_SELECT,
      assignmentConsume: TIME_TYPE_SELECT,
      triageConsume: TIME_TYPE_SELECT,
      closeConsume: TIME_TYPE_SELECT,
      totalConsume: TIME_TYPE_SELECT,
      socTenantId: 'tenantActiveDict',
      owner: 'sysUserActiveDict',
    },
  };

  watch(
    () => props.value,
    (n) => {
      checkedList.value = n || [];
      console.log('checkedList', checkedList.value);
    },
    { deep: true, immediate: true }
  );

  const whereModel = ref<any>({
    whereList: [],
    advanceSystemData: {},
  });
  const ownerFirstOption = ref([{ text: t('common.Unassigned'), value: 'unassign' }]);
  //记录每个字段下拉选的值
  const dictOptionsMap = ref<any>({});
  const advanceSystemData = ref({
    rel: E_ADVANCED_REL.AND,
    data: [
      {
        label: 'Source IP',
        field: E_ADVANCED_QUERY.SOURCE_IP,
        rel: E_ADVANCED_JUDGE.EQ,
        value: E_ADVANCED_QUERY.ASSET_IP,
      },
      {
        label: 'Destination IP',
        field: E_ADVANCED_QUERY.DESTINATION_IP,
        rel: E_ADVANCED_JUDGE.EQ,
        value: E_ADVANCED_QUERY.ASSET_IP,
      },
    ],
  });

  const fieldList = ref<any[]>([
    { label: t('routes.RiskEventLogView.eventName'), value: 'eventName' },
    { label: t('routes.RiskEventLogView.srcIp'), value: 'srcIp' },
    { label: t('routes.RiskEventLogView.dstIp'), value: 'dstIp' },
    { label: t('routes.RiskEventLogView.fromIp'), value: 'fromIp' },
    { label: t('routes.RiskEventLogView.eventType'), value: 'eventType' },
    { label: t('routes.RiskEventLogView.severity'), value: 'eventLevel' },
  ]);
  const compareList = ref<any[]>([
    { label: t('common.compare.equal'), value: 'eq' },
    { label: t('common.compare.notEqual'), value: 'ne' },
    { label: t('common.compare.contains'), value: 'like' },
    { label: t('common.compare.notContains'), value: 'not_like' },
  ]);

  const compareDictList = ref<any[]>([
    { label: t('common.compare.equal'), value: 'eq' },
    { label: t('common.compare.notEqual'), value: 'ne' },
  ]);

  const compareNumList = ref<any[]>([
    { label: t('common.compare.equal'), value: 'eq' },
    { label: t('common.compare.notEqual'), value: 'ne' },
    { label: t('common.compare.larger'), value: 'gt' },
    { label: t('common.compare.smaller'), value: 'lt' },
  ]);

  const ruleMap = {
    eq: ' == ',
    ne: ' != ',
    gt: ' > ',
    lt: ' < ',
    like: ' contain ',
    not_like: ' not contain ',
  };

  const fieldTypeMap = ref({});

  function init(fields: any, defaultData: any, riskParam: any) {
    dictOptionsMap.value = {};
    fieldTypeMap.value = {};
    for (let i in fields) {
      if (fields[i].fieldType == 'time') {
        continue;
      }
      fieldTypeMap.value[fields[i].classFieldName] = fields[i].fieldType;
      console.log('fieldTypeMap.value', fieldList.value);
    }
    whereModel.value.whereList = [];
    if (defaultData) {
      setDefaultData(defaultData);
    } else {
      console.log('init', riskParam);

      // if (riskParam) {
      //   whereModel.value.whereList = riskParam;
      // } else {
      //   whereModel.value.whereList.push([{ field: undefined, rule: 'eq', val: undefined, dbType: '' }]);
      // }
      if (riskParam) {
        whereModel.value.whereList = riskParam;
      }
    }
  }

  function setDefaultData(defaultData) {
    whereModel.value = JSON.parse(defaultData);
    if (whereModel.value.advanceSystemData && props.systemFilter) {
      advanceSystemData.value = whereModel.value.advanceSystemData;
    }
  }

  /**
   * 获取比较条件下拉选数据
   * @param item
   */
  function getRuleOptions(item) {
    const array = sourceTypeMap['risk']['getRuleOptions'];
    if (array.indexOf(item.field) > -1) {
      //下拉选条件，只有等于和不等于
      return compareDictList.value;
    } else if (item.field && fieldTypeMap.value[item.field] == 'number') {
      //数字类型没有模糊查询
      return compareNumList.value;
    }
    return compareList.value;
  }

  /**
   * 判断是否为非查询数据库的下拉选值
   * @param item
   */
  function showSelect(item) {
    const array = sourceTypeMap['risk']['showOptionSelect'] ?? [];
    if (array.indexOf(item.field) > -1) {
      return true;
    }
    return false;
  }

  /**
   * 是否为级联下拉选
   */
  function showTreeSelect(item) {
    const array = sourceTypeMap['risk']['showTreeSelect'] ?? [];
    if (array.indexOf(item.field) > -1) {
      return true;
    }
    return false;
  }

  /**
   * 判断是否为查询数据库的下拉选值
   * @param item
   */
  function showDictSelect(item) {
    const array = sourceTypeMap['risk']['showDictSelect'] ?? [];
    if (array.indexOf(item.field) > -1) {
      return true;
    }
    return false;
  }

  /**
   * 选择耗时时长字段
   * @param item
   */
  function showTimeType(item) {
    const timeType = sourceTypeMap['risk']['showTimeType'] ?? [];
    if (timeType.indexOf(item.field) > -1) {
      return true;
    }
    return false;
  }

  /**
   * 根据字段获取值的下拉选数据
   * @param item
   */
  function getValOption(item: any) {
    if (sourceTypeMap['risk']) {
      return sourceTypeMap['risk'][item.field] ?? [];
    } else {
      return [];
    }
  }

  function fieldChange(id, item) {
    item.val = '';
    item.timeVal = '';
    item.timeValType = '';
    item.dbType = '';
    item.rule = 'eq';
    if (fieldTypeMap.value[item.field] == 'number') {
      item.dbType = 'long';
    }
    if (document.getElementById(id)) {
      document.getElementById(id)?.blur();
    }
  }

  /**
   * 校验并处理数据
   */
  function handleData() {
    let flag = checkedRule('rule_required,rule_number');
    if (!flag) {
      return flag;
    }

    let list = whereModel.value.whereList;
    for (let i in list) {
      let list2 = list[i];
      for (let j in list2) {
        if (list2[j].timeVal && list2[j].timeValType) {
          let timeVal = list2[j].timeVal;
          let timeValType = list2[j].timeValType;
          let field = list2[j].field;
          let num = 1000;
          let m = { 1: 60 * num, 2: 60 * 60 * num, 3: 60 * 60 * 24 * num };
          list2[j].val = timeVal * m[timeValType];
        }
      }
    }
    return true;
  }

  const templateNameRef = ref();
  const templateId = ref('');
  let templateName = '';

  /**
   * 保存高级查询条件
   */
  function saveSearch() {
    let flag = handleData();
    if (!flag) {
      return;
    }
    if (templateId.value) {
      templateName = templateDataMap[templateId.value].name;
    } else {
      templateName = '';
    }

    templateNameRef.value.init({
      id: templateId.value,
      templateName: templateName,
      dataJson: JSON.stringify(whereModel.value),
      source: 'risk',
    });
  }

  /**
   * 查询
   */
  function getData() {
    let flag = handleData();
    if (!flag) {
      return flag;
    }
    let data = JSON.parse(JSON.stringify(whereModel.value));
    if (!data.whereList || data.whereList.length == 0) {
      // if (props.systemFilter) {
      //   data.advanceSystemData = JSON.parse(JSON.stringify(advanceSystemData.value));
      //   emits('search', data, '');
      // } else {
      //   emits('search', '', '');
      // }
    } else {
      const view = handleSearchView(data);

      console.log('view111', view);
      // let params = {
      //   data: data,
      //   view: view,
      // };
      console.log('paramsson', data);

      emits('update:value', data);
    }
  }

  function handleSearchView(data) {
    let view: any = [];
    if (data && data?.whereList?.length > 0) {
      for (let i = 0; i < data?.whereList.length; i++) {
        let view2: any = [];
        const li = data?.whereList[i];
        for (let j = 0; j < li.length; j++) {
          let d = li[j];
          view2.push(handleSelectData(d));
        }
        const str = view2.join(' AND ');
        if (data?.whereList?.length > 1) {
          view.push('(' + str + ')');
        } else {
          view.push(str);
        }
      }
    }
    return view.join(' OR ');
  }

  function handleSelectData(d) {
    const fieldData: any = fieldList.value.filter((item) => item.value === d.field);

    let value = d.val;
    if (sourceTypeMap['risk'].showDictSelect.includes(d.field)) {
      value = dictOptionsMap.value[d.field].filter((item) => item.value === d.val)[0].label;
    } else if (sourceTypeMap['risk'].showOptionSelect.includes(d.field)) {
      console.log(sourceTypeMap['risk'][d.field]);
      value = sourceTypeMap['risk'][d.field].filter((item) => item.value === d.val)[0].label;
    }

    return (fieldData[0].label ?? d.field) + ruleMap[d.rule] + (!!value ? '"' + value + '"' : '""');
  }

  function addGroup() {
    if (!whereModel.value.whereList) {
      whereModel.value.whereList = [];
    }
    whereModel.value.whereList.push([{ field: undefined, rule: 'eq', val: undefined, dbType: '' }]);
  }

  function add(item) {
    item.push({ field: undefined, rule: 'eq', val: undefined, dbType: '' });
  }

  /**
   * 删除查询字段
   * @param item
   * @param index
   */
  function delField(item, index) {
    if (item.length !== 1) {
      item.splice(index, 1);
    }
  }

  /**
   * 删除查询块
   * @param item
   * @param index
   */
  function delGroup(item, index) {
    item.splice(index, 1);
  }

  const templateOption = ref<any[]>([]);
  let templateDataMap = {};

  loadTemplateOption();

  /**
   * 加载高级查询模板
   */
  function loadTemplateOption() {
    searchTemplateList({ source: 'risk' }).then((data) => {
      console.log(data);
      templateDataMap = {};
      let list: any = [];
      for (let i in data) {
        list.push({
          label: data[i].name,
          text: data[i].name,
          value: data[i].id,
        });
        templateDataMap[data[i].id] = data[i];
      }
      templateOption.value = list;
    });
  }

  function refTemplateData() {
    loadTemplateOption();
  }
  defineExpose({
    init,
    getData,
    refTemplateData,
  });
</script>
<style scoped lang="less">
  /deep/.ant-picker {
    min-width: auto !important;
  }
  .table_search {
    /deep/.ant-input {
      padding: 8px 12px !important;
      line-height: 16px !important;
      border: 0 !important;
    }

    .border_bottom {
      border-bottom: 1px solid @border-color;
    }

    .search_flex {
      display: flex;
      padding-bottom: 16px;
      margin-bottom: 8px;
      gap: 8px;
      .search_flex_1 {
        flex: 1;
        display: flex;
        gap: 8px;
      }

      .search_flex_2 {
        flex: 2;
        display: flex;
      }
    }

    .div_and {
      padding: 8px 8px 8px 12px;
      border: 1px solid @border-color;
      border-left: 4px solid @m-color;
      position: relative;
      border-radius: 4px;

      .del_img {
        position: absolute;
        top: -8px;
        right: -8px;
        cursor: pointer;
        color: rgba(255, 255, 255, 0.8);
      }
    }

    .div_or {
      position: relative;
    }
  }
</style>
