import { BasicColumn } from '/@/components/Table';
import { useI18n } from '/@/hooks/web/useI18n';

const { t } = useI18n();
export const detectionColumn: BasicColumn[] = [
  {
    title: t('routes.notification.rulename'),
    dataIndex: 'title',
  },
  {
    title: t('routes.notification.Severity'),
    width: '120px',
    dataIndex: 'ruleLevel',
  },
];
export const mlColumn: BasicColumn[] = [
  {
    title: t('routes.notification.rulename'),
    dataIndex: 'ruleName',
  },
  {
    title: t('routes.notification.Severity'),
    dataIndex: 'urgency',
    customRender: ({ value }) => {
      const map = { 1: 'Critical', 2: 'High', 3: 'Middle', 4: 'Low', 5: 'Information' };
      return t('common.' + map[value]);
    },
  },
  {
    title: t('routes.notification.ruletype'),
    width: '120px',
    dataIndex: 'ruleType',
    customRender: ({ value }) => {
      const map = { 1: 'MLStatistic', 2: 'MLOrder', 3: 'MLContent' };
      return t('common.' + map[value]);
    },
  },
];
