import {defHttp} from '/@/utils/http/axios';
import {message, Modal} from 'ant-design-vue';
import {useI18n} from "/@/hooks/web/useI18n";

const {t} = useI18n();

enum Api {
  list = '/threatHunting/threatHuntingVO/list',
  save = '/threatHunting/threatHuntingVO/add',
  saveInve = '/threatHunting/threatHuntingVO/addInve',
  addHistory = '/threatHunting/threatHuntingHistory/add',
  updateHistory = '/threatHunting/threatHuntingHistory/edit',
  historyList = '/threatHunting/threatHuntingHistory/list',
  edit = '/threatHunting/threatHuntingVO/edit',
  deleteOne = '/threatHunting/threatHuntingVO/delete',
  deleteBatch = '/threatHunting/threatHuntingVO/deleteBatch',
  importExcel = '/threatHunting/threatHuntingVO/importExcel',
  exportXls = '/threatHunting/threatHuntingVO/exportXls',
  threatHuntingList = '/clickHouse/threatHuntingList',
  invHuntinglist = '/threatHunting/threatHuntingInve/invHuntinglist',
  saveInveFilter = '/threatHunting/threatHuntingInve/add',
  invHuntingInfo = '/threatHunting/threatHuntingInve/queryById',
  invlist = '/threatHunting/threatHuntingInve/list',
  loadHuntingTenantList = "/threatHunting/threatHuntingVO/loadHuntingTenantList",
  downloadLog = '/clickHouse/downloadLog',
  downloadLogExcel = '/clickHouse/downloadLogExcel',
  downloadLogCsv = '/clickHouse/downloadLogCsv',
  downloadTableExcel = '/clickHouse/downloadTableExcel'
}

/**
 * 导出api
 * @param params
 */
export const getExportUrl = Api.exportXls;
/**
 * 导入api
 */
export const getImportUrl = Api.importExcel;

export const downloadLog = async (params,fileFormat) => {
  const data = await defHttp.post({
    url: fileFormat == 'json' ? Api.downloadLog : fileFormat == 'xlsx' ? Api.downloadLogExcel : Api.downloadLogCsv,
    params: params,
    responseType: 'blob'
  }, {isTransformResponse: false});
  if (!data) {
    message.warning(t('common.downloadFailed'));
    return;
  }
  const name = "log";
  const blobOptions = { type: 'application/vnd.ms-excel' };
  const fileSuffix = '.' + fileFormat;
  const navigator:any = window.navigator;
  if (navigator && typeof navigator?.msSaveBlob !== 'undefined') {
    navigator.msSaveBlob(new Blob([data], blobOptions), name + fileSuffix);
  } else {
    const url = window.URL.createObjectURL(new Blob([data], blobOptions));
    const link = document.createElement('a');
    link.style.display = 'none';
    link.href = url;
    link.setAttribute('download', name + fileSuffix);
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link); //下载完成移除元素
    window.URL.revokeObjectURL(url); //释放掉blob对象
  }
}

export const downloadTableExcel = async (params,fileFormat) => {
  const data = await defHttp.post({
    url: fileFormat == 'xlsx' ? Api.downloadTableExcel : Api.downloadTableExcel,
    params: params,
    responseType: 'blob'
  }, {isTransformResponse: false});
  if (!data) {
    message.warning(t('common.downloadFailed'));
    return;
  }
  const name = "log";
  const blobOptions = { type: 'application/vnd.ms-excel' };
  const fileSuffix = '.' + fileFormat;
  const navigator:any = window.navigator;
  if (navigator && typeof navigator?.msSaveBlob !== 'undefined') {
    navigator.msSaveBlob(new Blob([data], blobOptions), name + fileSuffix);
  } else {
    const url = window.URL.createObjectURL(new Blob([data], blobOptions));
    const link = document.createElement('a');
    link.style.display = 'none';
    link.href = url;
    link.setAttribute('download', name + fileSuffix);
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link); //下载完成移除元素
    window.URL.revokeObjectURL(url); //释放掉blob对象
  }
}


/**
 * 列表接口
 * @param params
 */
export const getHistoryList = (params?) =>
  defHttp.get({url: Api.historyList, params});


/**
 * 列表接口
 * @param params
 */
export const saveHistory = (params, isUpdate) => {
  const url = isUpdate ? Api.updateHistory : Api.addHistory;
  return defHttp.post({url: url, params}, {errorMessageMode: 'none', successMessageMode: 'none'});
}

/**
 * 列表接口
 * @param params
 */
export const list = (params?) =>
  defHttp.get({url: Api.list, params});
/**
 * 查询符合条件的租户id
 * @param params
 */
export const loadHuntingTenantList = (params?) =>
  defHttp.get({url: Api.loadHuntingTenantList, params});


export const invHuntinglist = (params?) =>
  defHttp.get({url: Api.invHuntinglist, params});

export const invlist = (params?) =>
  defHttp.get({url: Api.invlist, params});


/**
 * 删除单个
 * @param params
 * @param handleSuccess
 */
export const deleteOne = (params, handleSuccess) => {
  return defHttp.delete({url: Api.deleteOne, params}, {joinParamsToUrl: true}).then(() => {
    handleSuccess();
  });
}
/**
 * 批量删除
 * @param params
 * @param handleSuccess
 */
export const batchDelete = (params, handleSuccess) => {
  Modal.confirm({
    title: '确认删除',
    content: '是否删除选中数据',
    okText: '确认',
    cancelText: '取消',
    onOk: () => {
      return defHttp.delete({
        url: Api.deleteBatch,
        data: params
      }, {joinParamsToUrl: true}).then(() => {
        handleSuccess();
      });
    }
  });
}
/**
 * 保存或者更新
 * @param params
 * @param isUpdate 是否是更新数据
 */
export const saveOrUpdate = (params, isUpdate) => {
  const url = isUpdate ? Api.edit : Api.save;
  return defHttp.post({url: url, params});
}

export const saveInveFilterRequest = (params) => {
  return defHttp.post({url: Api.saveInveFilter, params});
}

export const saveInve = (params) => {
  return defHttp.post({url: Api.saveInve, params});
}


export const threatHuntingList = (params) => defHttp.post({url: Api.threatHuntingList, params});

export const invHuntingInfo = (params) => defHttp.get({url: Api.invHuntingInfo, params});

