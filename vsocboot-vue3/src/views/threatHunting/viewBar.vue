<template>
  <div class="table_div">
    <div style="padding: 10px;line-height: 32px;display: flex">
      <div>{{ t('routes.threathunting.analysis') }}#{{ props.index + 1 }}</div>
      <a-divider type="vertical" style="margin: auto 15px;"/>
      <div style="width: 150px">
        <a-select v-model:value="orderField" style="width: 100%;" v-if="!view"
                  :options="props.statisListData" @change="loadData"/>
        <span v-if="view">{{orderField}}</span>
      </div>
      <div style="width: 80px;text-align: center;">——> Top</div>
      <div style="width: 100px;">
        <a-input v-model:value="limit" @focusout="loadData2" v-if="!view"/>
        <span v-if="view">{{limit}}</span>
      </div>
      <div style="width: 80px;text-align: center;">——></div>
      <div style="width: 180px">
        <a-select v-model:value="order" style="width: 100%;" @change="loadData" v-if="!view">
          <a-select-option value="desc">{{ t('routes.threathunting.descendingOrder') }}</a-select-option>
          <a-select-option value="asc">{{ t('routes.threathunting.ascendingOrder') }}</a-select-option>
        </a-select>
        <span v-if="view">{{order}}</span>
      </div>
    </div>
    <div ref="barChart" style="height: 200px">

    </div>
  </div>
</template>

<script lang="ts" setup>
import {defineEmits, defineProps, Ref, ref, watch} from 'vue'
import {useI18n} from "/@/hooks/web/useI18n";
import {useECharts} from "/@/hooks/web/useECharts";
import {getHump} from "/@/utils/ckTable";

const {t} = useI18n();

const props = defineProps({
  viewData: {
    type: Array
  },
  groupData: {
    type: Object
  },
  statisListData: {
    type: Array
  },
  index: {
    type: Number
  },
  view: {
    type: Boolean,
    default: false
  }
})
console.log(props)

const emit = defineEmits(["changeOrder"])


const orderField = ref("count")
const order = ref("desc")
const limit = ref<number>(10)
watch(() => props.viewData, () => {
  refData()
})
const barChart = ref<HTMLDivElement | null>(null);
const {setOptions} = useECharts(barChart as Ref<HTMLDivElement>);

const refData = () => {

  let viewData = props.viewData
  console.log(viewData)
  let groupData = props.groupData
  limit.value = groupData?.limit ?? 10
  order.value = groupData?.order ?? "desc"
  orderField.value = groupData?.orderField ?? "count"

  let field = "";
  let otherFields: any = [];
  let xAxisData: any = [];
  let seriesData: any = [];
  let otherDatas: any = {};
  for (let i in viewData) {
    let item = viewData[i]
    if (!field) {
      for (let key in item) {
        if (!key.startsWith("count")) {
          field = key
        } else {
          otherFields.push(key)
        }
      }
    }
    xAxisData.push(item[field])
    // src_ip: '************', count: 2, count_dst_ip: 2
    for (let k in otherFields) {
      if (otherDatas[otherFields[k]]) {
        otherDatas[otherFields[k]].push(item[otherFields[k]])
      } else {
        otherDatas[otherFields[k]] = [item[otherFields[k]]];
      }
    }
  }

  for (let k in otherFields) {
    seriesData.push({
      name: getHump(otherFields[k]),
      data: otherDatas[otherFields[k]],
      type: "bar"
    })
  }

  let option: any = {
    tooltip: {
      trigger: 'axis'
    },
    grid: {
      top: 10,
      left: 10,
      right: 10,
      bottom: 10,
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: xAxisData
    },
    yAxis: {
      type: 'value'
    },
    series: seriesData
  };
  setOptions(option);

}
refData()
const limitFlag = ref(false)
watch(() => limit.value, () => {
  limitFlag.value = true
})

const loadData = () => {
  props.groupData.limit = limit.value
  props.groupData.order = order.value
  props.groupData.orderField = orderField.value
  emit("changeOrder")
}
const loadData2 = () => {
  if (limitFlag.value) {
    limitFlag.value = false
    props.groupData.limit = limit.value
    props.groupData.order = order.value
    props.groupData.orderField = orderField.value
    emit("changeOrder")
  }
}


</script>

<style scoped lang="less">
.table_div {
  background-color: @bg-color;
  border-bottom: 1px solid @border-color;
}
</style>
