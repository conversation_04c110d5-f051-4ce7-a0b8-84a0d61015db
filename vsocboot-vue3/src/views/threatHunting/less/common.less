.flex-row{
  display: flex;
  flex-direction: row;

}
.flex-column{
  display: flex;
  flex-direction: column;

}
.flex-between{
  justify-content: space-between;
}

.flex-wrap{
  flex-wrap: wrap;
}
.flex-align-center{
  align-items: center;
}
.flex-content-left {
  justify-content: left;
}

.flex-content-center {
  justify-content: center;
}

.gap16{
  gap: 16px;
}
.gap12{
  gap: 12px;
}
.gap8{
  gap: 8px;
}
.flex1{
  flex : 1
}
.flex2{
  flex : 2
}
.cursor{
  cursor: pointer;
}
.margin-bottom-12{
  margin-bottom: 12px;
}

.ellipsis {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  word-break: keep-all;
}
.padding0-16{
  padding: 0px 16px;
}
.padding12-16{
  padding: 12px 16px!important;
}
.pl-16{
  padding-left: 16px;
}
.page_top{
  height:48px;
  align-items: center;
}
.border-bottom{
  border-bottom: 1px solid @border-color;
}
.border-bottom0{
  border-bottom: 0px !important;
}
