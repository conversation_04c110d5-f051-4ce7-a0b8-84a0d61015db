<script setup lang="ts">
import {defineProps, inject, ref} from 'vue';
import { TextEllipsis } from "/@/components/Form";
import {E_EditType, languageColor} from "/@/views/clickhouse/ts/ToolUtils";

// 定义 props 并添加类型注解
const props = defineProps<{
  value: String;
}>();
const globalLanguageTypeData = inject('globalLanguageTypeData',ref());
console.log('globalLanguageTypeData====',globalLanguageTypeData.value)

const parts = props.value.split(/(\s+)/);


</script>

<template>
  <TextEllipsis  :value="value" :slot="true">
    <template #text>
      <template v-for="(part,index ) in parts" :key="index">
        <span v-if="globalLanguageTypeData[E_EditType.KEYWORD].includes(part)" :style="{color:languageColor[E_EditType.KEYWORD]}">{{part}}</span>
        <span v-else-if="globalLanguageTypeData[E_EditType.OPERATE].includes(part)" :style="{color:languageColor[E_EditType.OPERATE]}">{{part}}</span>
        <span v-else-if="globalLanguageTypeData[E_EditType.LOGICAL].includes(part)" :style="{color:languageColor[E_EditType.LOGICAL]}">{{part}}</span>
        <span :style="{color:languageColor[E_EditType.VARIABLE]}" v-else>{{part}}</span>
      </template>
    </template>
  </TextEllipsis>
</template>

<style scoped lang="less">
/* 可以在这里添加样式 */
</style>
