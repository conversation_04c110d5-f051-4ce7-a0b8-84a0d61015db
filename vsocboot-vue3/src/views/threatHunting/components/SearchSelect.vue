<template>
  <div class="search-container">
    <!--    树型查询按钮
    =======================================================-->
    <div class="w-32px h-32px flex flex-row items-center justify-center">
      <div class="ax-icon-button ax-icon-small">
        <div  class="soc ax-com-Compositefilters fcolor1 search-tree-btn"  @click="showTree"/>
      </div>
    </div>


    <!--    分割线
   =======================================================-->
    <div class="split-vertical-line pt-8px pb-8px" > </div>

    <!--    树型查询展开内容
    ========================================================-->
    <div class="tree-search" v-if="isTreeMode" :style="{ top: monacoHeight + 'px' }">
      <TreeNode v-model:value="treeData"></TreeNode>
    </div>

    <!--  code content
     ========================================================-->
    <div class="code_wrapper" @click.stop id="search_key_div">
      <!--  code string
      ========================================================-->
      <MonacoEditor
        v-model:width="huntingSearchWidth"
        ref="monacoRef"
        v-model:height="monacoHeight"
        @focus="doFocus"
        @search="isSearch"
        :readOnly="isTreeMode"
        @showType="showByFilter"
        @showFieldInfo="showFieldInfo"
      />
      <!-- 用v-show用于占位：if不显示时，MonacoEditor宽度大，再显示时会超出边框-->
      <div class="option_btns" v-show="!isTreeMode" id="search-operate-button-wrapper">
        <template v-if="monacoRef && getMonacoEditorValue()">
          <!-- 清空查询条件-->
          <div class="ax-icon-button ax-icon-small"   @click="clearSearch()">
            <span class="soc ax-com-Fault ax-icon"/>
          </div>
          <!-- 保存模板-->
          <div class="ax-icon-button ax-icon-small"  v-if="showBtn"  @click="emits('saveTemplate')">
            <span class="soc ax-com-Add ax-icon"/>
          </div>
        </template>

        <!-- 保存报表-->
        <div class="ax-icon-button ax-icon-small"   @click="emits('report')"  v-if="saveReportShow && showBtn">
          <span class="soc ax-com-Record ax-icon"/>
        </div>

        <!-- 查询-->
        <div class="ax-icon-button ax-icon-small"  @click="doSearch" v-if="showQuery" >
          <span class="soc ax-com-Search ax-icon"/>
        </div>
        <!-- 下载日志json-->
        <a-dropdown trigger="click">
          <div class="ax-icon-button ax-icon-small"  v-if="donwloadShow && showBtn" >
            <span class="soc ax-com-Download ax-icon"/>
          </div>
           <template #overlay>
            <a-menu>
              <a-menu-item key="json" @click="download('json')">{{ t('routes.threathunting.jsonformat') }}</a-menu-item>
              <a-menu-item key="xlsx" @click="download('xlsx')">{{ t('routes.threathunting.xlsxformat') }}</a-menu-item>
              <a-menu-item key="csv" @click="download('csv')">{{ t('routes.threathunting.csvformat') }}</a-menu-item>
            </a-menu>
          </template>
        </a-dropdown>
      </div>

      <!--  code select list
      ========================================================-->
      <div class="check_wrapper" :style="{ top: monacoHeight + 'px' }" v-if="!isTreeMode && selectOpen">

          <!--   tab item start -->
          <div class="tab-head min-w-400px" v-if="showBtn">
            <div
              :class="['font13', 'tab-head_title', { 'ax-color-active': item.key == currentIndex }]"
              v-for="item in tabNameArray"
              @click="changeTab(item.key)"
              >{{ item.name }}</div
            >
          </div>
          <!--   tab item end -->
          <!--   search item list-->
          <div id="listContent" tabindex="0" >
            <a-list size="small"  :data-source="checkData.dataSource" v-if="currentIndex == 1">
              <template #renderItem="{ item, index }">
                <a-list-item :class="['source-item', { active: index == keyClickIndex }]" @click="setValue(item)" @mouseenter="keyClickIndex = index">
                  <MonacoListItem :item="item"/>
                </a-list-item>
              </template>
            </a-list>
            <!--  history item list-->
            <a-list size="small"  :data-source="historyData" v-if="currentIndex == 2">
              <template #renderItem="{ item, index }">
                <a-list-item
                  :class="['source-item', { active: index == keyClickIndex }]"
                  @click="setHistoryValue(item)"
                  @mouseenter="keyClickIndex = index"
                >
                  <FilterValue :value="item.queryContent"></FilterValue>

                </a-list-item>
              </template>
            </a-list>
            <!--  template item list-->
            <a-list size="small"  :data-source="templateData" v-if="currentIndex == 3">
              <template #renderItem="{ item, index }">
                <a-list-item
                  :class="['source-item', { active: index == keyClickIndex }]"
                  @click="checkTemplate(item)"
                  @mouseenter="keyClickIndex = index"
                >
                  <!-- 模板名称-->
                  <div class="w-200px flex-shrink-0 font12 fcolor1"><TextEllipsis  :value="item.filterName" /> </div>
                  <!-- 模板查询值-->
                  <div class="item-filter">  <FilterValue :value="item.filter"></FilterValue></div>
                  <!--   删除按钮-->
                  <div class="ax-icon-button ax-icon-smallest is_del" @click.stop="deleteTemplate(item.id)">
                    <span class="soc ax-com-Decrease ax-icon"/>
                  </div>
                </a-list-item>
              </template>
            </a-list>
          </div>


      </div>
    </div>

    <div v-show="fieldInfoVisible" class="fieldInfoDiv" id="fieldInfoDiv" @click.stop="fieldInfoClick">
      <div style="position: absolute; right: 10px; top: 5px; cursor: pointer">
        <div class="ax-icon-button ax-icon-small" @click="showFieldEdit(fieldInfo)">
          <span   class="soc ax-com-Edit ax-icon"/>
        </div>
      </div>
      <div>{{ t('routes.threathunting.fieldType') }} : {{ fieldInfo?.fieldType }}</div>
      <div
        >{{ t('routes.threathunting.fieldTag') }} :
        <template v-if="fieldInfo?.tag">
          <span v-for="(item, index) in fieldInfo.tag.split(',')" :key="index" class="tagName">
            {{ item }}
          </span>
        </template>
      </div>
      <div>{{ t('routes.threathunting.description') }} : {{ fieldInfo?.desc }}</div>
      <div>{{ t('routes.threathunting.example') }} : {{ fieldInfo?.examples }}</div>
    </div>
  </div>
  <TblFieldModal  ref="tblFieldRef" @success="handleSuccess"/>
</template>

<script lang="ts" setup>
  import MonacoEditor from '/@/views/clickhouse/components/MonacoEditorCopy.vue';
  import { computed, defineEmits, defineExpose, inject, nextTick, onBeforeUnmount, onMounted, provide, ref, unref, watch } from 'vue';
  import { getKeywords } from '/@/views/clickhouse/ts/ThreatHuntingLanguage';
  import TreeNode from '/@/views/clickhouse/components/TreeNode.vue';
  import { getSqlCode, toMonacoCode, toTreeCode, validateCode } from '/@/views/clickhouse/ts/TreeUtils';
  import parser from 'js-sql-parser';
  import { message, Modal } from 'ant-design-vue';
  import { deleteOne, getHistoryList, list, saveHistory } from '/@/views/threatHunting/ThreatHuntingVO.api';
  import { useI18n } from '/@/hooks/web/useI18n';
  import TblFieldModal from '/@/views/tblField/modules/TblFieldModal.vue';
  import { getTabFieldList, loadField } from '/@/utils/ckTable';
  import { useFilterContent } from '/@/views/clickhouse/ts/useSuggestContent';
  import {TextEllipsis} from "/@/components/Form";
  import MonacoListItem from "/@/views/clickhouse/components/MonacoListItem.vue";
  import FilterValue from "/@/views/threatHunting/components/FilterValue.vue";

  const { getDataSource, initLanguage,globalLanguageTypeData, selectDataArr, fieldDataType, globalLanguage } = useFilterContent();
  const { t } = useI18n();
  const emits = defineEmits([
    'searchList',
    'saveTemplate',
    'report',
    'delTemplate',
    'checkTemplate',
    'download',
    'update:keyFormat',
    'requestColumns',
  ]);

  const tableSource = inject('tableSource');
  const currentIndex = ref(1);
  const isDel = ref(false);
  const isMonaco = ref(false);
  const isTreeMode = ref(false);
  const tblFieldRef = ref();
  // const fieldDataType = ref({});
  // const selectData = ref([]);
  const templateId = ref('');
  const historyId = ref('');
  const isDoSearch = ref(false);
  const isSettingVal = ref(false);
  provide('isDel', isDel);
  provide('fieldDataType', fieldDataType);
  provide('selectData', selectDataArr);
  provide('globalLanguage', globalLanguage);
  provide('globalLanguageTypeData', globalLanguageTypeData);

  const props = defineProps({
    value: {
      type: Object,
      default: () => {},
    },
    tabActiveKey: {
      type: String,
      default: '1',
    },
    tableSource: Number,
    showBtn: {
      type: Boolean,
      default: true,
    },
    showQuery: {
      type: Boolean,
      default: true,
    },
    searchStr: {
      type: String,
      default: '',
    },
  });



  onMounted(() => {
    addEventListener();
    handleResize();
    if (props.showBtn) {
      loadHistory();
      loadTemplateList();
    }
  });


  onBeforeUnmount(() => {
    document.getElementById('search_key_div')?.removeEventListener('keydown', handleKeyDown);
    document.getElementById('listContent')?.removeEventListener('scroll', scrollTop, true);
    window.removeEventListener('resize', handleResize);
  });

  const saveReportShow = computed(() => props.tabActiveKey == '2');
  const donwloadShow = computed(() => props.tabActiveKey == '1');
  const tabNameArray = ref([
    { name: t('routes.threathunting.searchBy'), key: 1 },
    { name: t('routes.threathunting.history'), key: 2 },
    { name: t('routes.threathunting.template'), key: 3 },
  ]);
  const huntingSearchWidth = ref(100);
  //monaco width and height
  const monacoHeight = ref('32');
  let showScrollTop = 0;
  const searchStr = ref('');
  const keyClickIndex = ref<number>(0);
  const checkData = ref<any>({
    title: 'Common variables',
    dataSource: [],
    type: 'keyword',
  });
  const treeData = ref({
    rel: 'AND',
    list: [],
  });
  const monacoRef = ref();

  //select content show
  const selectOpen = ref(false);
  provide('selectOpen', selectOpen);
  // template datasource
  const templateData = ref([]);
  //history data
  const historyData = ref([]);

  let isShowSelect = true;
  watch(
    () => props.value,
    (n) => {
      console.log('关键字段:', n);
      initLanguage(n);
      nextTick(() => {
        monacoRef.value?.clearContentValue();
        monacoRef.value?.refreshLanguage(n);

        fieldInfoVisible.value = false;
        if (props.searchStr) {
          // console.log('setStringValue------->', props.searchStr);
          // monacoRef.value?.setStringValue(props.searchStr);
          setStringValue(props.searchStr);
          isShowSelect = false;
        }
        closeFilter();
      });
    },
    { deep: true, immediate: true }
  );

  // list data
  watch(
    () => checkData.value.dataSource,
    (n) => {
      keyClickIndex.value = 0;
    }
  );
  // tree data
  watch(
    () => treeData.value,
    (n) => {
      if (isDel.value) {
        //清除空的list
        isDel.value = false;
        refreshTreeCode(n);
      }
      if (isTreeMode.value && !isMonaco.value) {
        let str = toMonacoCode(treeData.value);
        monacoRef.value.setStringValue(str);
      }
      if (isTreeMode.value) {
        isMonaco.value = false;
      }
    },
    { deep: true }
  );



  /**
   * change tab
   * @param value
   */

  function changeTab(value) {
    currentIndex.value = value;
    if (value == 1) {
      //dai改
      checkData.value = {
        title: 'Common variables',
        type: 'keyword',
      };
      showByFilter({ type: 1 });
    } else if (value == 2) {
      loadHistory();
    } else if (value == 3) {
      loadTemplateList();
    }
    nextTick(()=>{
      keyClickIndex.value = 0;
      doFocus();
    })
  }

  /**
   * 删除后，去除空的List
   * @param n
   */
  function refreshTreeCode(n) {
    let listData = n.list;
    listData.forEach((data, index) => {
      if (data.list && data.list.length == 0) {
        listData.splice(index, 1);
        isDel.value = true;
      } else if (data.list) {
        refreshTreeCode(data);
      }
    });
  }

  /**
   * add listener
   */
  function addEventListener() {
    //keydown直接绑定到getElementById("listContent")上，会导致回车赋值失效，要先按向下箭头选择
    document.getElementById('search_key_div')?.addEventListener('keydown', handleKeyDown);
    // document.addEventListener("keydown", handleKeyDown);
    document.getElementById('listContent')?.addEventListener('scroll', scrollTop, true);
    //组件宽度
    window.addEventListener('resize', handleResize);
  }
  /**
   * listener scroll
   */
  function scrollTop(e) {
    console.log('scrollTop',e)
    let item: any = document.getElementById('listContent');
    if (item.scrollTop === showScrollTop) {
      return false;
    }
  }

  function handleKeyDown(e) {
    // if(!props.showBtn){
    //     return;
    // }
    console.log('handleKeyDown=', e.keyCode);
    // e.keyCode === 13 ||
    // if(e.keyCode == 13){
    //   e.preventDefault();
    //   return;
    // }
   if (e.keyCode === 32) {
      //回车或空格
      // console.log('is in ===============savechecked');
      let flag = saveChecked();
      if (flag) {
        e.preventDefault();
      }
    } else if (e.keyCode === 38) {
      keyUp();
    } else if (e.keyCode === 40) {
      keyDown();
    }
  }

  function doFocus() {
    document.getElementById('listContent')?.focus();
  }

  /**
   * 获取monaco editor value
   */
  function getMonacoEditorValue() {
    var content = monacoRef.value.getContentValue();
    content = content.replace(/\[ +/g, '[').replace(/ +\]/g, ']');
    return content;
  }

  function showTree() {
    isTreeMode.value = !isTreeMode.value;
    if (isTreeMode.value) {
      const content = getMonacoEditorValue();
      if (content && content.trim().length > 0) {
        isMonaco.value = true; //不刷新
        treeData.value = toTreeCode(content);
        console.log('treeData.value ',treeData.value )
      } else {
        treeData.value = {
          rel: 'AND',
          list: [],
        };
      }
    } else {
      let str = toMonacoCode(treeData.value);
      monacoRef.value.setStringValue(str);
    }
  }

  /**
   * 获取解析值
   */
  function getParsingContentValue() {
    let content = getMonacoEditorValue();
    if (content == '') {
      return '';
    }
    let data = toTreeCode(content);

    let str = toMonacoCode(data);

    isSettingVal.value = true;
    monacoRef.value.setStringValue(str);
    let rel = data.rel;
    let list = data.list;
    content = getSqlCode(rel, list, fieldDataType.value);

    //前端校验sql格式合法性
    try {
      parser.parse('select * from dual where ' + content);

    } catch (e) {
      message.warning('Incorrect condition configuration!');
      throw e;
    }
    return content;
  }

  /**
   * 获取解析值
   */
  function getParsingTreeContentValue() {
    let content = getMonacoEditorValue();
    if (content == '') {
      return '';
    }
    let data = toTreeCode(content);
    // console.log('to tree code',data)

    let str = toMonacoCode(data);

    isSettingVal.value = true;
    monacoRef.value.setStringValue(str);
    // let rel = data.rel;
    // let list = data.list;
    // content = getSqlCode(rel, list, fieldDataType.value);
    //
    // //前端校验sql格式合法性
    // try {
    //   parser.parse('select * from dual where ' + content);
    // } catch (e) {
    //   message.warning('Incorrect condition configuration!');
    //   throw e;
    // }
    return JSON.stringify(data);
  }

  function getParsingDataValue() {
    let content = getMonacoEditorValue();
    if (content == '') {
      return '';
    }
    let data = toTreeCode(content);
    let str = toMonacoCode(data);
    monacoRef.value.setStringValue(str);
    //暂时注释掉 start，影响fieldvalue 带引号查询--------2025-01-08---------
    // let rel = data.rel;
    // let list = data.list;
    // content = getSqlCode(rel, list, fieldDataType.value);
    // console.log('content:',content)

    // //前端校验sql格式合法性
    // try {
    //   // parser.parse('select * from dual where ' + content);
    // } catch (e) {
    //   message.warning('Incorrect condition configuration!');
    //   throw e;
    // }
    //暂时注释掉 start，影响fieldvalue 带引号查询--------2025-01-08---------
    return data;
  }

  /**
   * 校验sql
   */
  function validateSearchCode() {
    let content = getMonacoEditorValue();
    if (content.split(' ').length < 3) {
      return 'Format error';
    }
    let data = toTreeCode(content);
    console.log('data', data);
    let rel = data.rel;
    let list = data.list;

    let error = validateCode(rel, list);
    console.log('error', error);
    if (error.length > 0) {
      return error.toString();
    }
    return '';
  }

  // keyword listen start--------------------------------------------------

  /**
   * 添加监听
   */
  function keyUp() {
    if (keyClickIndex.value > 0) {
      keyClickIndex.value--;
    }
  }

  function keyDown() {
    let len = checkData.value.dataSource.length;
    if(currentIndex.value == 2){
      len = historyData.value.length;
    }else if(currentIndex.value == 3){
      len = templateData.value.length;
    }
    if (len > keyClickIndex.value + 1) {
      keyClickIndex.value++;
    }
  }

  /**
   * 选中
   */
  function saveChecked(item) {
    if (currentIndex.value == 1) {
      let item = checkData.value.dataSource[keyClickIndex.value];
      setValue(item);
    } else if (currentIndex.value == 2) {
      let item = historyData.value[keyClickIndex.value];
      setHistoryValue(item);
    } else if (currentIndex.value == 3) {
      let item = templateData.value[keyClickIndex.value];

      checkTemplate(item);
    }

    keyClickIndex.value = 0;
    showScrollTop = 0;
    return true;
  }

  // ------------------------------keyword listen end-----------------------

  /**
   * 下拉选项【关闭】
   */
  function closeFilter() {
    currentIndex.value = 1;
    checkData.value.dataSource = [];
    //关闭字段详情
    fieldInfoVisible.value = false;
    selectOpen.value = false;
  }

  function closeTree() {
    isTreeMode.value = false;
  }

  /**
   * 关闭
   */
  function closeAll() {
    closeFilter();
    closeTree();
  }

  /**
   * 下拉选项【显示】
   * @param filter
   */
  function showByFilter(filter) {
    console.log('showByFilter',filter)
    if(fieldInfoVisible.value){
      closeFilter();
      fieldInfoVisible.value = true;
      return;
    }
    // console.log('showByFilter==', filter);
    if (isSettingVal.value) {
      isSettingVal.value = false;
      closeFilter();
      return;
    }
    if (!isShowSelect) {
      isShowSelect = true;
      closeFilter();
      return;
    }
    if (filter.type < 0 || isDoSearch.value) {
      isDoSearch.value = false;
      selectOpen.value = false;
    } else {
      fieldInfoVisible.value = false;
      selectOpen.value = true;
    }

    nextTick(() => {
      checkData.value.dataSource = getDataSource(filter, fieldDataType.value) || [];
      checkData.value.type = filter.type;
      checkData.value.filter = filter;
    });
  }

  /**
   * 保存值
   * @param item
   */
  function setValue(item) {
    // console.log('setValue=', item);
    // console.log('checkData.value.filter=', checkData.value.filter);
    if (checkData.value.filter) {
      //固定位置替换
      monacoRef.value.setContentValue(item, checkData.value.filter);
      return;
    }
    //末尾追加
    monacoRef.value.setContentValue(item, null);
  }

  /**
   * 追加
   * @param str
   */
  function addValue(str) {
    var content = monacoRef.value.getContentValue();
    if (content.length > 0) {
      content = content.trim() + ' AND ';
    }
    monacoRef.value.setStringValue(content + str);
  }

  function setInitValue(str) {
    monacoRef.value.setInitValue(str);
  }
  function setStringValue(str) {
    monacoRef.value.setStringValue(str);
  }

  /**
   * 清空值reset
   */
  function clearValue() {
    historyId.value = '';
    templateId.value = '';
    isDoSearch.value = false;
    isSettingVal.value = false;
    monacoHeight.value = '32';
    monacoRef.value?.clearContentValue();
    treeData.value = {
      rel: 'AND',
      list: [],
    };
  }

  /**
   * get search value
   */
  function getContentValue() {
    var content = monacoRef?.value.getContentValue();
    return content;
  }

  /**
   * clear search value
   */
  function clearSearch() {
    clearValue();
    closeAll();

    emits('searchList');
  }

  /**
   * 查询
   */
  function doSearch() {
    console.log('doSearch-------------')
    isDoSearch.value = true;
    if (props.showBtn) {
      doSaveHistory();
    }
    emits('searchList');
    nextTick(()=>{
      isShowSelect = false;
      monacoRef.value.setCursorEnd();
      closeFilter();
    })
  }

  function isSearch() {
    console.log('isSearch====@@@@@@@@@@@----', selectOpen.value);
    // if (selectOpen.value === true) {
    //   selectOpen.value = false;
    //   isDoSearch.value = true;
    //   return;
    // }
    doSearch();

  }
  /**
   * 下载json
   */
  function download(key) {
    // 触发 requestColumns 事件，并接收返回的数据
    emits('requestColumns', (data) => {
      let columns = data;
      if (columns.length > 0) {
        let filterColumns = columns.filter((item) => item.dataIndex != 'soc_tenant_id' && item.dataIndex != 'soc_tenant_name');
        console.log('key', key);
        emits('update:keyFormat', key);
        emits('download', filterColumns.map((item) => item.dataIndex).join(','));
      }
    });
  }
  //---------------------------------history start------------------------------------------
  /**
   * save history data
   */
  function doSaveHistory() {
    let queryContent = getContentValue();
    if (!queryContent) {
      return;
    }
    if (!historyId.value || searchStr.value.trim() != queryContent) {
      saveHistory({ tableName: tableSource.value, queryContent: queryContent }, false);
    } else {
      saveHistory({ id: historyId.value, tableName: tableSource.value, queryContent: queryContent }, true);
    }

    loadHistory();
  }

  /**
   * load history data
   */
  function loadHistory() {
    getHistoryList({ tableName: tableSource.value }).then((result) => {
      historyData.value = result;
      doFocus();
    });
  }

  /**
   * set history value
   * @param val
   */
  function setHistoryValue(item) {
    searchStr.value = item.queryContent;
    historyId.value = item.id;
    templateId.value = '';
    isSettingVal.value = true;
    setStringValue(item.queryContent);
  }
  //---------------------------------history end------------------------------------------
  //-----------------template start------------------------------------------
  function checkTemplate(item) {
    historyId.value = '';
    templateId.value = item.id;
    setStringValue(item.filter);
    isSettingVal.value = true;
    emits('checkTemplate', item);
  }
  /**
   * update template
   */
  function updateTemplate() {}
  /**
   * 查询模板
   */
  function loadTemplateList() {
    list({ tableName: tableSource.value }).then((data) => {
      templateData.value = data;
      doFocus();
    });
  }
  function deleteTemplate(id) {
    Modal.confirm({
      title: t('common.delText'),
      content: t('common.delConfirmText') + '?',
      okText: t('common.delText'),
      cancelText: t('common.cancelText'),
      wrapClassName: 'delete_confirm',
      onOk: () => {
        deleteOne({ id: id }, () => {
          loadTemplateList();
        });
      },
    });
  }
  //-----------------template end------------------------------------------

  const fieldInfoVisible = ref(false);
  const fieldInfo = ref<any>();

  async function showFieldInfo() {
    fieldInfoVisible.value = false;
    console.log('showFieldInfo checkData', unref(checkData.value));
    if (checkData.value.type != 1 || checkData.value.dataSource.length > 1  || checkData.value.dataSource.length == 0) {
      return;
    }

    fieldInfo.value = unref(checkData.value).dataSource[0];
    if (fieldInfo.value) {
      // closeFilter();
      await nextTick();
      //
      setTimeout(() => {
        const $searchDiv = document.getElementById('search_key_div');
        const selectText: any = $searchDiv?.getElementsByClassName('cslr selected-text');
        let left = 0;
        let height = 0;
        if (selectText) {
          left = selectText[0]?.offsetLeft + 40;
          height = selectText[0]?.offsetParent.offsetTop + 30;
        }
        const $fieldDiv = document.getElementById('fieldInfoDiv');
        if ($fieldDiv) {
          $fieldDiv.style.top = height + 'px';
          $fieldDiv.style.left = left + 'px';
        }
        fieldInfoVisible.value = true;
      }, 100);
    }
  }

  function fieldInfoClick() {}



  function showFieldEdit(record) {
    const dataInfo = unref(record);
    console.log('dataInfo',dataInfo)
    const data: any = {
      description: dataInfo?.desc,
      id: dataInfo?.id,
      tagName: dataInfo?.tag,
      fieldType: dataInfo?.fieldType,
      fieldValue: dataInfo?.insertText,
    };
    fieldInfoVisible.value = false;
    tblFieldRef.value.showModal(data);

  }
  /**
   * 设置宽度
   */
  function handleResize() {
    const s_div = document.getElementById('search_key_div');
    const btn_div = document.getElementById('search-operate-button-wrapper');

    if (s_div) {
      let w = 120;
      console.log('handleResize!!!!!!!!!!s_div!!!!!',s_div.clientWidth)
      console.log('handleResize!!!!!!!!!!btn_div!!!!!',btn_div.clientWidth)
      if(btn_div && btn_div.clientWidth > 120){
        w =  btn_div.clientWidth;
      }
      huntingSearchWidth.value = s_div.clientWidth - w - 16;
    }
  }
  async function handleSuccess() {
    await loadField();
    let result = getTabFieldList(props.tableSource);
    let sourceData: any = [];
    for (let i in result) {
      if (result[i].fieldValue == 'ck_enter_date') {
        continue;
      }
      sourceData.push(result[i]);
    }
    const data = {
      sourceData: sourceData,
    };
    getKeywords(data);
  }

  defineExpose({
    closeFilter,
    closeTree,
    getParsingContentValue,
    getParsingDataValue,
    clearValue,
    addValue,
    setInitValue,
    getContentValue,
    validateSearchCode,
    setStringValue,
    loadTemplateList,
    loadHistory,
    getParsingTreeContentValue
  });
</script>

<style lang="less" scoped>
  @import "../../clickhouse/less/search.less";
</style>
