<template>
  <div class="table_div">
    <div style="padding: 10px;line-height: 32px;display: flex">
      <div>{{ t('routes.threathunting.analysis') }}#{{ props.index + 1 }}</div>
      <a-divider type="vertical" style="margin: auto 15px;"/>
      <div style="width: 150px">
        <a-select v-model:value="orderField" style="width: 100%;" v-if="!view"
                  :options="props.statisListData" @change="loadTable"/>
        <span v-if="view">{{ orderField }}</span>
      </div>
      <div style="width: 80px;text-align: center;">——> Top</div>
      <div style="width: 100px;">
        <a-input v-model:value="limit" @focusout="loadTable2" v-if="!view"/>
        <span v-if="view">{{ limit }}</span>
      </div>
      <div style="width: 80px;text-align: center;">——></div>
      <div style="width: 180px">
        <a-select v-model:value="order" style="width: 100%;" @change="loadTable" v-if="!view">
          <a-select-option value="desc">{{ t('routes.threathunting.descendingOrder') }}</a-select-option>
          <a-select-option value="asc">{{ t('routes.threathunting.ascendingOrder') }}</a-select-option>
        </a-select>
        <span v-if="view">{{ order }}</span>
      </div>
    </div>
    <a-table :columns="columns"
             :data-source="dataSource"
             :pagination="false"
             :loading="loading">
      <template #bodyCell="{ text, record, column }">
        <a-dropdown :trigger="['contextmenu']">
          <div class="ant-table-cell-ellipsis">{{ text }}</div>
          <template #overlay v-if="column.isShow">
            <a-menu>
              <a-menu-item key="0" @click="menuClick(text, record, 0, column)">
                {{ t('routes.riskLogs.include') }}
              </a-menu-item>
              <a-menu-divider/>
              <a-menu-item key="1" @click="menuClick(text, record, 1, column)">
                {{ t('routes.riskLogs.exclude') }}
              </a-menu-item>
              <a-menu-divider/>
              <a-menu-item key="3" @click="menuClick(text, record, 3, column)">
                {{ t('routes.riskLogs.copy') }}
              </a-menu-item>
            </a-menu>
          </template>
        </a-dropdown>
      </template>
    </a-table>
  </div>
</template>

<script lang="ts" setup>
import {defineEmits, defineProps, reactive, ref, watch} from 'vue'
import {useI18n} from "/@/hooks/web/useI18n";
import {getHump} from "/@/utils/ckTable";
import {copyPageUrl} from "/@/utils/copyUtil";

const {t} = useI18n();

const props = defineProps({
  viewData: {
    type: Array
  },
  groupData: {
    type: Object
  },
  statisListData: {
    type: Array
  },
  index: {
    type: Number
  },
  view: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(["changeOrder", 'whereChange'])

const loading = ref<boolean>(true);
const columns = reactive<any>([]);
const dataSource = reactive<any>([]);
const orderField = ref("count")
const order = ref("desc")
const limit = ref<number>(10)
watch(() => props.viewData, () => {
  refTable()
})

const refTable = () => {
  columns.splice(0, columns.length)
  dataSource.splice(0, dataSource.length)
  let viewData = props.viewData as []
  let groupData = props.groupData
  limit.value = groupData?.limit ?? 10
  order.value = groupData?.order ?? "desc"
  orderField.value = groupData?.orderField ?? "count"
  let groupList = groupData?.groupList;
  let statisList = groupData?.statisList;
  for (let i in groupList) {
    columns.push({
      title: getHump(groupList[i].group),
      dataIndex: groupList[i].group,
      ellipsis: true,
      isShow: true
    })
  }

  for (let i in statisList) {
    columns.push({
      title: "Count of " + getHump(statisList[i].statis),
      dataIndex: "count_" + statisList[i].statis,
      ellipsis: true
    })
  }
  columns.push({title: "Count", dataIndex: "count", ellipsis: true})
  dataSource.push(...viewData)
  loading.value = false
}
refTable()
const limitFlag = ref(false)
watch(() => limit.value, () => {
  limitFlag.value = true
})
const loadTable = () => {
  loading.value = true
  props.groupData.limit = limit.value
  props.groupData.order = order.value
  props.groupData.orderField = orderField.value
  emit("changeOrder")
}
const loadTable2 = () => {
  if (limitFlag.value) {
    loading.value = true
    limitFlag.value = false
    props.groupData.limit = limit.value
    props.groupData.order = order.value
    props.groupData.orderField = orderField.value
    emit("changeOrder")
  }

}
const menuClick = (text, record, type, column) => {

  let data: any = {};
  if (type === 0) {//=
    data = {
      type: "0",
      field: column.dataIndex,
      value: text
    };
  } else if (type === 1) {//!=
    data = {
      type: "1",
      field: column.dataIndex,
      value: text
    };
  } else if (type === 2) {//groupBy
    data = {
      type: "2",
      field: column.dataIndex,
      value: text
    };
  } else if (type === 3) {//copy
    copyPageUrl(text);
  }
  if (data?.field) {
    emit('whereChange', data)
  }

};



</script>

<style scoped lang="less">
.table_div {
  background-color: @bg-color;
}

</style>
