/**
 * 工单基础信息配置项
 */
export interface IBasicInfo {
  ticketName: string; //工单名称
  entryTenant: string; //可用租户
  ticketDesc: string; //工单描述
  iconUrl: string; //工单图标路径
  id: string; //工单主键
}

/**
 * tab组件
 */
export interface ITabOption{
  key:number|string;
  name:string;
  component:any;
  data?:any;
}
/**
 * 用户
 */
export interface IUser{
  username:string;
  email:string;
  avatar:string;
}


/**
 * 用户
 */
export interface ISetting{
  designContent:string;//模板内容
  ticketName:string;//工单名称
  ticketDesc:string;//工单描述
  status:number;//状态1发布，0草稿
  socTenantId:string;//租户id
  ticketType:number;//1mssp内部，2 mssp下发，3 租户提交 ，4 租户内部工单
  ruleConfig:string;//规则配置
  processId:string;//bpmn id
  permissionConfig:string;//Permission Configuration
  bpmnXml:string;//bpmnXml
  flowError:string;//流程错误信息
  iconUrl:string;//工单图标
  entryTenant:string;//入口租户
  tenantType:string;//人员类型
}
