import {E_Element_Type, E_Task_Type} from "/@/views/ticket/enums/flowEnum";

/**
 * 测试数据
 */
export const startEvent = {
  name: 'Application',
  activityId: 'Event_start',
  type: E_Element_Type.START,
  isTenant:false,
  errorInfo:'',
  permission: {
    node: {
        user: [
          {username:'admin',avatar:'',email:'<EMAIL>'},
          {username:'bytest',avatar:'anxinsiem-bucket/temp/down_1711524089733.png',email:'<EMAIL>'},
        ],
        role: ['f6817f48af4fb3af11b9e8bf182f618b','1798640196703514625'],
        copy: [{username:'Admin',avatar:'',email:'<EMAIL>'}],

    },
    form: [],
  },
  permissionType: E_Task_Type.UNPRESET
}

export const taskEvent = {
  name: 'MSSP Task',
  activityId: 'Activity_15qgm63',
  type: E_Element_Type.TASK,
  isTenant:false,
  errorInfo:'Task are not configured!',
  permission: {
    node: {
      user: [
        {username:'Admin',avatar:'',email:'<EMAIL>'},
        {username:'bytest',avatar:'anxinsiem-bucket/temp/down_1711524089733.png',email:'<EMAIL>'},
      ],
      role: ['f6817f48af4fb3af11b9e8bf182f618b','1798640196703514625'],
      copy: [{username:'Admin',avatar:'',email:'<EMAIL>'}],
      isMultiInstance: true,//会签 true
      sign: 1,
    },
    form: [],
  },
  permissionType: E_Task_Type.UNPRESET
}


