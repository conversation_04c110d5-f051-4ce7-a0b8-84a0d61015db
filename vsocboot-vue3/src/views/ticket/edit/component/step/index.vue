<template>
  <div class="step-container">
    <!-- 步骤导航条 start
    ========================================-->
    <div class="steps">
      <div v-for="(item, index) in stepName" :key="'step-' + index" :class="['step', { current: index == current }, { finish: index < current }]">
        <div class="step-number font13 fcolor">{{ index + 1 }}</div>
        <div :class="['ft13-bold', index == current ? 'fcolor' : 'fcolor3']">{{ item }}</div>
      </div>
    </div>
    <!-- 步骤导航条 end -->
  </div>
</template>
<script setup lang="ts">
import {defineProps, inject, ref} from 'vue';
const current = inject('current', ref(0));
  defineProps({
    stepName: {
      type: Array,
      default: null,
    },
  });
</script>
<style scoped lang="less">
  .step-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 14px 20px;
  }
  .steps{
    display: flex;
    align-items: center;
    flex-direction: row;
    gap: 40px;
    .step {
      display: flex;
      align-items: center;
      gap: 8px;
      flex-direction: row;
    }
  }
  .step-number {
    display: inline-flex;
    justify-content: center;
    align-items: center;
    width: 20px;
    height: 20px;
    background-color: #1e90ff; /* Blue color for the number circle */
    color: white;
    border-radius: 50%;
    font-weight: bold;
  }

  .step-text {

  }
</style>
