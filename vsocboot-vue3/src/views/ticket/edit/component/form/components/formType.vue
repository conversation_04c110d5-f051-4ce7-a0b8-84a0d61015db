<template>
  <div class="components-panel">
    <div class="font14 fcolor list-item-title"> Components </div>
    <div v-for="(data, index) in ComponentItems" :key="index" class="list-items">
      <div class="font12 fcolor">{{ data.title }}</div>
      <div v-for="(liItem, index2) in data.column" :key="index2" @dragstart="dragstart" draggable="true" class="list-item">
        <div class="font13">
          <span :class="'soc ' + liItem.icon"></span>
          <span>{{ liItem.name }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
  import { ref } from 'vue';
  import { ComponentItems } from '/@/views/workflow/setting/ts/template.api';
  const { addNewItem } = defineProps(['addNewItem']);

  function dragstart(ev) {
    const group = ev.target.id.split('-')[0];
    const columnIndex = ev.target.id.split('-')[1];
    const item = ComponentItems[group].column[columnIndex];
    addNewItem(item);
  }
</script>

<style scoped>
  .components-panel {
    padding: 16px;
    background-color: #1a1b1f;
  }
</style>
