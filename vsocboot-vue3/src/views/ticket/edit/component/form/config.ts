import { buildUUID } from '/@/utils/uuid';
import { useI18n } from '/@/hooks/web/useI18n';

const { t } = useI18n();
type ComponentProps = {
  content?: string;
  align?: string;
  fontSize?: number;
  lineHeight?: number | null;
  width?: string;
  italic?: boolean;
  bold?: boolean;
  underline?: boolean;
  lineThrough?: boolean;
  disabled?: boolean;
  placeholder?: string;
  multiple?: boolean;
  showSearch?: boolean;
  staticData?: { label: string; value: string }[];
  showTime?: boolean;
  range?: boolean;
  valueFormat?: string;
  format?: string;
  checkedValue?: string;
  unCheckedValue?: string;
  text?: string;
  maxCount?: number;
  dragger?: boolean;
  type?: string | number;
  margin?: string | number;
  placeholderend?: string;
  getPopupContainer?: Function;
};

export type Column = {
  name: string;
  label: string;
  icon: string;
  type: string;
  field: string;
  component: string;
  colProps?: {
    span: number;
  };
  componentProps?: ComponentProps;
  rules?: any[];
  items?: any[];
  layout?: string;
  dataSource?: any[];
  option?: Array<{ text: string; value: string; format: string; valueFormat: string }>;
  defaultValue?: string | number | any; //值
  disable?: boolean;
};
type LayoutColumn = {
  field: string;
  name: string;
  icon: string;
  component: string;
  type: string;
  gutter: number;
  col: number[];
  align: string;
  justify: string;
  children: Array<{ list: any[] }>;
  key?: String | number;
};

type ComponentItem = {
  title: string;
  value: string;
  column: (Column | LayoutColumn)[];
  key: string | number;
};

const ComponentItems: ComponentItem[] = [
  {
    title: t('workflow.workflow.Layout'),
    value: 'layout',
    key: buildUUID(),
    column: [
      {
        field: '',
        name: t('workflow.workflow.Area'),
        //icon: 'appstore-outlined',
        icon: 'ax-bjq-grid',
        label: t('workflow.workflow.Area'),
        component: 'Area',
        type: 'Area',
        gutter: 8,
        col: [24],
        align: 'top',
        justify: 'start',
        children: [],
      },
    ],
  },
  {
    title: t('workflow.workflow.Genericcomponents'),
    value: 'baseComponents',
    key: buildUUID(),
    column: [
      {
        name: t('workflow.workflow.Text'),
        label: t('workflow.workflow.Text'),
        // icon: 'clock-circle',
        icon: 'ax-bjq-text',
        type: 'text',
        field: '',
        defaultValue: '',
        component: 'Text',
        colProps: {
          span: 24,
        },
        key: buildUUID(),
        rules: [],
        componentProps: {
          content: t('workflow.workflow.thisiscontent'),
          align: 'left',
          fontSize: 16,
          lineHeight: null,
          width: '100%',
          italic: false, //斜体 font-style: italic;
          bold: false, //加粗 font-weight: bold;
          underline: false, //下划线 text-decoration: underline;
          lineThrough: false, //删除线 text-decoration: line-through; 删除线和下划线只能存在一个
        },
      },
      {
        name: t('workflow.workflow.Textbox'),
        label: t('workflow.workflow.Textbox'),
        type: 'input',
        key: buildUUID(),
        //icon: 'code',
        icon: 'ax-bjq-textbox',
        field: '',
        component: 'Input',
        colProps: {
          span: 24,
        },
        componentProps: {
          disabled: true,
          placeholder: t('workflow.workflow.Pleaseentervalue'),
        },
        rules: [],
      },

      {
        name: t('workflow.workflow.Textfield'),
        label: t('workflow.workflow.Textfield'),
        type: 'textArea',
        // icon: 'container',
        key: buildUUID(),
        icon: 'ax-bjq-textfield',
        field: '',
        defaultValue: '',
        component: 'InputTextArea',
        componentProps: {
          placeholder: t('workflow.workflow.Pleaseentervalue'),
        },
        rules: [],
      },
      {
        name: t('workflow.workflow.Number'),
        label: t('workflow.workflow.Number'),
        type: 'inputNumber',
        key: buildUUID(),
        // icon: 'field-binary-outlined',
        icon: 'ax-bjq-numberbox',
        field: '',
        defaultValue: null,
        component: 'InputNumber',
        colProps: {
          span: 24,
        },
        componentProps: {
          disabled: true,
          placeholder: t('workflow.workflow.Pleaseentervalue'),
        },
        rules: [],
      },

      {
        name: t('workflow.workflow.Radiobox'),
        label: t('workflow.workflow.Radiobox'),
        type: 'radioGroup',
        key: buildUUID(),
        // icon: 'check-circle',
        icon: 'ax-bjq-radiobox',
        field: '',
        component: 'RadioGroup',
        defaultValue: null,
        componentProps: {
          width: '100%',
          type: '1', //1自定义，2字典
          staticData: [
            {
              label: 'Option1',
              value: buildUUID(),
            },
            {
              label: 'Option2',
              value: buildUUID(),
            },
          ],
        },
        layout: 'radio_inline',
        items: [
          {
            label: 'Option1',
            value: buildUUID(),
          },
          {
            label: 'Option2',
            value: buildUUID(),
          },
        ],
      },
      {
        name: t('workflow.workflow.Checkbox'),
        label: t('workflow.workflow.Checkbox'),
        type: 'checkboxGroup',
        key: buildUUID(),
        // icon: 'check-square',
        icon: 'ax-bjq-checkbox',
        field: '',
        component: 'CheckboxGroup',
        defaultValue: null,
        componentProps: {
          width: '100%',
          type: '1', //1自定义，2字典
          staticData: [
            {
              label: 'Option1',
              value: buildUUID(),
            },
            {
              label: 'Option2',
              value: buildUUID(),
            },
          ],
        },
        layout: 'radio_inline',
        items: [
          {
            label: 'Option1',
            value: buildUUID(),
          },
          {
            label: 'Option2',
            value: buildUUID(),
          },
        ],
      },

      {
        name: t('workflow.workflow.Dropdownbox'),
        label: t('workflow.workflow.Dropdownbox'),
        type: 'select',
        key: buildUUID(),
        // icon: 'profile',
        icon: 'ax-bjq-dropdownbox',
        field: '',
        defaultValue: null,
        component: 'Select',
        componentProps: {
          width: '100%',
          placeholder: t('workflow.workflow.Pleaseselect'),
          multiple: false, //是否多选
          showSearch: false, //是否可搜索
          type: '1', //1自定义，2字典
          staticData: [
            {
              label: 'Option1',
              value: buildUUID(),
            },
            {
              label: 'Option2',
              value: buildUUID(),
            },
          ],
        },
        items: [],
      },
      {
        name: t('workflow.workflow.Time'),
        label: t('workflow.workflow.Time'),
        // icon: 'clock-circle',
        key: buildUUID(),
        icon: 'ax-bjq-timebox',
        type: 'timePicker',
        field: '',
        defaultValue: null,
        component: 'TimePicker',
        colProps: {
          span: 24,
        },
        rules: [],
        componentProps: {
          showTime: true,
          range: false,
          valueFormat: 'HH:mm:ss',
          placeholder: '',
          placeholderend: '',
          width: '100%',
          getPopupContainer: () => document.body,
        },
      },
      {
        name: t('workflow.workflow.Date'),
        label: t('workflow.workflow.Date'),
        // icon: 'clock-circle',
        icon: 'ax-bjq-datebox',
        key: buildUUID(),
        type: 'datePicker',
        field: '',
        defaultValue: null,
        component: 'DatePicker',
        colProps: {
          span: 24,
        },
        rules: [],
        componentProps: {
          showTime: false,
          type: '3',
          format: 'YYYY-MM-DD',
          valueFormat: 'yyyy-MM-dd',
          placeholder: '',
          placeholderend: '',
          width: '100%',
        },
        option: [
          { text: 'Year', value: '1', format: 'YYYY', valueFormat: 'yyyy' },
          { text: 'Year-Month', value: '2', format: 'YYYY-MM', valueFormat: 'yyyy-MM' },
          { text: 'Date(in days)', value: '3', format: 'YYYY-MM-DD', valueFormat: 'yyyy-MM-dd' },
          {
            text: 'Date (in seconds)',
            value: '4',
            format: 'YYYY-MM-DD HH:mm:ss',
            valueFormat: 'yyyy-MM-dd HH:mm:ss',
          },
          {
            text: 'Date range(in days)',
            value: '5',
            format: 'YYYY-MM-DD',
            valueFormat: 'yyyy-MM-dd',
          },
          {
            text: 'Date range(in seconds)',
            value: '6',
            format: 'YYYY-MM-DD HH:mm:ss',
            valueFormat: 'yyyy-MM-dd HH:mm:ss',
          },
        ],
      },
      {
        name: t('workflow.workflow.Switch'),
        label: t('workflow.workflow.Switch'),
        // icon: 'clock-circle',
        icon: 'ax-bjq-switch',
        key: buildUUID(),
        type: 'switch',
        field: '',
        defaultValue: null,
        component: 'Switch',
        colProps: {
          span: 24,
        },
        rules: [],
        componentProps: {
          checkedValue: 'Y',
          unCheckedValue: 'N',
        },
      },

      {
        name: t('workflow.workflow.Uploadfiles'),
        label: t('workflow.workflow.Uploadfiles'),
        // icon: 'clock-circle',
        icon: 'ax-bjq-uploadfile',
        key: buildUUID(),
        type: 'upload',
        field: '',
        defaultValue: [],
        component: 'Upload',
        colProps: {
          span: 24,
        },
        rules: [],
        componentProps: {
          width: '100%',
          text: 'Click to upload file',
          maxCount: 1,
          multiple: false,
          dragger: false,
        },
      },
      {
        name: t('workflow.workflow.Boundary'),
        label: t('workflow.workflow.Boundary'),
        // icon: 'clock-circle',
        icon: 'ax-bjq-separator',
        key: buildUUID(),
        type: 'hr',
        field: '',
        defaultValue: '',
        component: 'Hr',
        colProps: {
          span: 24,
        },
        rules: [],
        componentProps: {
          margin: 20,
          width: '100%',
        },
      },
    ],
  },
  {
    title: t('workflow.workflow.Riskcomponents'),
    value: 'relation',
    key: buildUUID(),
    column: [
      {
        name: t('workflow.workflow.Alerts'),
        icon: 'ax-bjq-investgation',
        label: 'Risk Center',
        field: 'riskCenter', //相当于datasource
        component: 'riskCenter',
        type: 'riskCenter',
        defaultValue: '',
        dataSource: [],
        key: buildUUID(),
        componentProps: {
          width: '100%',
        },
      },
    ],
  },
];

const formConfig = {
  marginTop: 16,
  marginBottom: 16,
  marginRight: 16,
  marginLeft: 16,
  dimensions: 'big',
  popWidth: 1200,
  labelWidth: 100,
  labelAlign: 'top',
};

export const SPLIT_STR = '_';
export { ComponentItems, formConfig };
