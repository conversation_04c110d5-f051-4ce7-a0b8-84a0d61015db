import {queryTicketEventList} from "/@/views/ticket/api/TicketViewTenant.api";
import {SEVERITY_NUM_STR} from "/@/utils/valueEnum";
import {
  queryById as securityById
} from "/@/views/aggregationriskeventsecurity/AggregationRiskEventSecurity.api";
import {
  queryById as hostById
} from "/@/views/aggregationriskeventhost/AggregationRiskEventHost.api";
import {queryBadActorById, queryHistoryById} from "/@/views/badactors/BadActors.api";
import {queryById} from "/@/views/mlView/MlEvent.api";
import {queryAnomalyById} from "/@/views/anomalyrecords/AnomalyRecords.api";
import {queryById as queryHttpWafById} from "/@/views/httpWafEvent/HttpWafEvent.api";
/**
 * 步骤操作按钮
 */
export enum E_Ticket_Event_Type{
  RISK = 1,//event risk
  HOST = 2,//HOST
  ML = 3,//ML EVENT
  BADACTOR = 4,//BAD ACTOR
  ANOMALY = 5,//anomalyRecord
  HTTPWAF = 6,//HTTP WAF
}
/**
 * 工单事件同一封装方法
 */
export const useRiskCenter = () => {
  /**
   * 获取工单关联事件
   */
  const getRiskCenter = async (id) => {
    let result;
    await queryTicketEventList({id: id}).then(data => {
      result = data;
    });
    return result;
  };

  /**
   * 处理工单事件数据，显示到工单中，满足工单显示数据格式
   * @param list
   */
  const handleRiskEventData = (list) => {
    const result: any = [];
    if (list && list.length > 0) {
      for (let i = 0; i < list.length; i++) {
        let closeStatus = list[i].eventStatus;
        if (list[i].riskType == 4) {
          closeStatus = list[i].eventStatus == 1 ? 2 : 1;//badActor 关闭状态0未关闭，1关闭，和其它事件不一致，这里为了显示转换为同一的
        }
        let severity = list[i].eventLevel;
        if(list[i].riskType == 3){
          severity = SEVERITY_NUM_STR[list[i].eventLevel];
        }

        result.push({
          eventId: list[i].eventId,
          severity: severity,
          eventName: list[i].eventName,
          owner: list[i].username,
          triageStatus: list[i].triageStatus,
          closeStatus: closeStatus,
          type: list[i].type,
          riskType: list[i].riskType,
          updateTime: list[i].updateTime,
          conclusion: list[i].conclusion,
          socTenantId: list[i].socTenantId,
        })

      }
    }
    return result;
  }
  /**
   * 处理工单事件数据，记录选择按类型区分
   * @param list
   */
  const handleRiskEventMap = (list) => {
    const idsMap: any = [];
    if (list && list.length > 0) {
      for (let i = 0; i < list.length; i++) {

        if (idsMap[list[i].type]) {
          idsMap[list[i].type].push(list[i]);
        } else {
          idsMap[list[i].type] = [list[i]];
        }
      }
    }
    return idsMap;
  }

  const handleEventMap = {
    [E_Ticket_Event_Type.RISK]:handleRiskData,
    [E_Ticket_Event_Type.HOST]:handleHostData,
    [E_Ticket_Event_Type.ML]:handleMLData,
    [E_Ticket_Event_Type.BADACTOR]:handleBadData,
    [E_Ticket_Event_Type.ANOMALY]:handleAnomalyData,
    [E_Ticket_Event_Type.HTTPWAF]:handleHttpWafData,
  }

  const eventDetail = async (event, flag) => {
    console.log('eventDetail event init', event)
    //1、risk 2 host risk 3、ml 4、bad
    let data:any = null;
    if (event.riskType == E_Ticket_Event_Type.RISK) {
      data = await securityById({id: event.eventId});
      data.type = 1;
    } else if (event.riskType == E_Ticket_Event_Type.HOST) {
      data = await hostById({id: event.eventId})
      data.type = 2;
    } else if (event.riskType == E_Ticket_Event_Type.ML) {
      data = await queryById({id: event.eventId})
    } else if (event.riskType == E_Ticket_Event_Type.BADACTOR) {
      data = await queryBadActorById({id: event.eventId})
      if (data == null) {
        data = await queryHistoryById({id: event.eventId});
      }
    } else if (event.riskType == E_Ticket_Event_Type.ANOMALY) {
      data = await queryAnomalyById({id: event.eventId})
    } else if (event.riskType == E_Ticket_Event_Type.HTTPWAF) {
      data = await queryHttpWafById({id: event.eventId})
    }
    if (data) {
      const newData = handleEventMap[event.riskType](data)
      console.log('eventDetail===', newData)
      flag.flag = true;
      flag.data = newData;
      return newData;
    }
    return data;
  }

  function handleHttpWafData(data){
    return {

      eventId: data.id,
      severity: data.severity ? SEVERITY_NUM_STR[data.severity] : '',
      eventName: data.host,
      owner: data.owner_dictText,
      triageStatus: data.triageStatus,
      closeStatus: data.eventStatus,
      type: 'httpWaf',
      riskType: 6,
      updateTime: data.createTime,
      conclusion: data.conclusion,
      socTenantId: data.socTenantId,
    }
  }

  function handleAnomalyData(data){
    return {

        eventId: data.id,
        severity: data.alertLevel ? SEVERITY_NUM_STR[data.alertLevel] : '',
        eventName: data.entityName,
        owner: data.owner_dictText,
        triageStatus: data.triageStatus,
        closeStatus: data.riskStatus,
        type: 'ueba',
        riskType: 5,
        updateTime: data.statDate,
        conclusion: data.conclusion,
        socTenantId: data.socTenantId,
    }
  }

  function handleMLData(data){
    const array = ['', 'mlStatistic', 'mlOrder', 'mlContent'];
    return {
      eventId: data.id,
      severity: SEVERITY_NUM_STR[data.urgency],
      eventName: data.ruleName,
      owner: data.owner_dictText,
      triageStatus: data.triageStatus,
      closeStatus: data.riskStatus,
      type: array[data.ruleType],
      riskType: 3,
      updateTime: data.alarmTime,
      conclusion: data.conclusion,
      socTenantId: data.socTenantId,
    }
  }
  function handleBadData(data){
    return {
      eventId: data.id,
      severity: data.severity,
      eventName: data.ip,
      owner: data.owner_dictText,
      triageStatus: data.triageStatus,
      closeStatus: data.status == 1 ? 2 : 1, //badActor 关闭状态0未关闭，1关闭，和其它事件不一致，这里为了显示转换为同一的
      type: 'badActor',
      riskType: 4,
      updateTime: data.updateTime,
      socTenantId: data.socTenantId,
      threatScore: data.threatScore,
      ip: data.ip,
      attackFirstTime: data.attackFirstTime,
      attackLatestTime: data.attackLatestTime,
      conclusion: data.conclusion,
    }
  }

  function handleRiskData(data) {
    return {
      eventId: data.eventId,
      severity: data.eventLevel,
      eventName: data.eventName,
      owner: data.owner_dictText,
      triageStatus: data.triageStatus,
      closeStatus: data.eventStatus,
      type: 'risk',
      riskType: data.type,
      updateTime: data.updateTime,
      conclusion: data.conclusion,
      socTenantId: data.socTenantId,
    }
  }

  function handleHostData(data) {
    return {
      eventId: data.eventId,
      severity: data.detectionRuleLevel,
      eventName: data.detectionRuleTitle,
      owner: data.owner_dictText,
      triageStatus: data.triageStatus,
      closeStatus: data.eventStatus,
      type: 'risk',
      riskType: data.type,
      updateTime: data.updateTime,
      conclusion: data.conclusion,
      socTenantId: data.socTenantId,
    }
  }



  return {getRiskCenter, handleRiskEventData, handleRiskEventMap,eventDetail};
}
