import { nextTick, ref, watch } from 'vue';
import { Column } from '/@/views/ticket/edit/component/form/config';

export default function useDisplayPanel(props) {
  const childData = ref<Column>(props.value);
  watch(
    () => props.value,
    () => {
      nextTick(() => {
        childData.value = props.value ?? {};
      });
      // childData.value = props.value;
    },
    { deep: true, immediate: true }
  );
  return {
    childData,
  };
}
