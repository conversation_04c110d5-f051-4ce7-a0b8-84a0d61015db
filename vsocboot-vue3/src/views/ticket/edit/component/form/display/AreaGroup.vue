<template>

  <div
    class="item-row"
    :key="index"
    v-for="(formItem,index) in formDesign?.children">
    <div class="flex1">
      <a-form-item :label="formItem.label" :name="formItem.field">
        <component :is="modules[formItem.type]" v-model:value="formDesign!.children[index]"
                   v-model="formItem.children"/>
      </a-form-item>
    </div>
    <div class="permission">
      <a-select
        v-model:value="design[formItem.field]"
        style="width:100%"
        :options="permissionOption"/>
    </div>
  </div>

</template>
<script setup lang="ts">
import modules from "/@/views/ticket/edit/component/form/display";
import {defineProps, inject, provide, ref, watch} from "vue";
import {E_FORMITEM_PERMISSION} from "/@/views/ticket/enums/typeEnum";

const props = defineProps({
  value: {
    type: Object // prop 类型设置为 null 或不设置 type 属性，我们可以将 prop 类型设置为任意类型，即 any
  }
});
const readOnlyItem = ['text','hr']
const formDesign = ref(props.value);
const nodeConfig = inject('nodeConfig', ref());
//组件权限配置
// const design = inject('design', ref());
const design = ref(nodeConfig.value.permission.form ?? {});

//获取组件权限
//获取组件权限
formDesign.value?.children.forEach((item) => {
  if(nodeConfig.value.permission.form[item.field]){
    design.value[item.field] = nodeConfig.value.permission.form[item.field];
  }else if(readOnlyItem.includes(item.type)){
    design.value[item.field] = E_FORMITEM_PERMISSION.READONLY;
  }else{
    design.value[item.field] =  E_FORMITEM_PERMISSION.EDITABLE;
  }
})


//监听权限配置
watch(() => design.value, (n) => {
  nodeConfig.value.permission.form = n;
}, {deep: true, immediate: true})
//权限
const permissionOption = ref([{
  label: 'Required',
  value: E_FORMITEM_PERMISSION.REQUIRED
}, {
  label: 'Editable',
  value: E_FORMITEM_PERMISSION.EDITABLE
}, {
  label: 'Read Only',
  value: E_FORMITEM_PERMISSION.READONLY
}, {
  label: 'Hide',
  value: E_FORMITEM_PERMISSION.HIDE
}])
</script>
<style scoped lang="less">
.item-row {
  display: flex;
  flex-direction: row;

  .flex1 {
    flex: 1;
    border-right: 1px solid @border-color;
    padding-right: 16px;
  }

  .permission {
    padding-left: 16px;
    width: 125px;
    padding-top: 30px;
  }
}
</style>
