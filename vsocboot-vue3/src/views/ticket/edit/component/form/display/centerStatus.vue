<template>
  <div
    class="ax-label"
    :class="(item?.owner === 'unassign' || !item?.owner) ? 'ax-label-yellow' : 'ax-label-primary'">
    {{ t('routes.MlEvent.owner') }}:
    {{
      (item?.owner === 'unassign' || !item?.owner) ? t('common.Unassigned') : item.owner
    }}
  </div>
  <div
    class="ax-label ax-color-icon"
    :class="item?.triageStatus === 0 ? 'ax-label-yellow' : 'ax-label-primary'">
    <span class="soc ax-com-Warning"></span>
    <span v-if="item?.triageStatus === 0">{{ t('common.Untriaged') }}</span>
    <span v-else-if="item?.triageStatus == 1">{{ t('routes.RiskEventLogView.true') }}</span>
    <span v-else-if="item?.triageStatus == 2">{{ t('routes.RiskEventLogView.false') }}</span>
    <span v-else-if="item?.triageStatus == 3">{{ t('routes.RiskEventLogView.other') }}</span>
  </div>
  <div
    class="ax-label ax-color-icon"
    :class="item?.closeStatus === 1 ? 'ax-label-yellow' : 'ax-label-primary'">
    <span class="soc ax-com-Warning"></span>
    <span v-if="item?.closeStatus === 1">{{ t('common.Unclosed') }}</span>
    <span v-else-if="item?.closeStatus == 2">{{ t('common.Closed') }}</span>
  </div>
</template>
<script setup lang="ts">
import {ref, watch} from "vue";
import {useI18n} from "/@/hooks/web/useI18n";

const {t} = useI18n();
const item = ref();
const props = defineProps({
  value: {
    type: Object,
    default: () => {
    },
  },
  readOnly: {
    type: Boolean,
    default: false
  }

});
watch(() => props.value, async (n) => {
  if (props.readOnly) {
    //只读，仅显示保存数据
    item.value = n;
  } else {
    //处置事件后动态改变事件状态
    item.value = props.value;
  }
}, {immediate: true, deep: true})
</script>
<style scoped lang="less">

</style>
