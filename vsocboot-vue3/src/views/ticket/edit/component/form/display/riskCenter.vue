<template>
  <div class="risk-center_wrapper">
    <!--  操作按钮 start -->
    <div style="position:absolute;right:0;top:-30px;" >
      <a-dropdown>
        <div class="ax-icon-button ax-icon-small " @click.prevent >
          <div class="soc ax-com-More ax-icon -mt-4px"></div>
        </div>
        <template #overlay>
          <RiskBatchBtns :dataSource="childData.dataSource" :readOnly="childData.disable"/>
        </template>
      </a-dropdown>
    </div>
    <!--  操作按钮 end -->
    <!--  告警 list start-->
    <div  class="flex flex-col gap-4px" :style="{ width: childData?.componentProps?.width }">
      <!--  告警内容-->
      <template v-for="(item, index) in childData.dataSource" :key="'riskCenter' + item.eventId">
        <div class="risk_content_wrapper" :class="capitalizeFirstLetter(item.severity)">
          <!--  告警等级 start-->
          <div class="risk_level" :class="capitalizeFirstLetter(item.severity)">
            <span class="soc ax-com-Danger"></span>
            <span v-if="severityMap[item.severity] != null">
              {{ t('common.' + capitalizeFirstLetter(item.severity)) }}
            </span>
          </div>
          <!--  告警等级 end-->

          <div class="alert_info">
            <div class="alert_info_top">
              <div class="eventName font14">
                {{ item.eventName }}
              </div>
              <RiskBtns
                v-model:data="childData.dataSource![index]"
                :index="index"
                @removeEvent="removeEvent"
                v-model:conclusion="item.conclusion"
                :readOnly="childData.disable"/>
            </div>
            <div class="alert_row2">
              <centerStatusVue v-model:value="childData.dataSource![index]"
                               :readOnly="childData.disable"/>
              <!--  事件类型-->
              <div class="fcolor3">{{typeName[item.type]}} </div>
            </div>
            <div class="fcolor3"> {{ t('routes.workflow.lastAlertTime') }}: {{ item.updateTime }}</div>
            <div class="fcolor3  truncate-text"> {{ t('common.conclusion') }}: {{
                item.conclusion
              }}
            </div>
          </div>
        </div>
      </template>

      <!--  添加按钮 start -->
      <div style="margin-top: 8px" v-if="!childData.disable">
        <a-button  size="small"  @click="openEvent()">
          <span class="soc ax-com-Add ax-icon"></span>
          {{t('routes.riskEvent.AddEvent')}}
        </a-button>
      </div>
      <!--  添加按钮 end -->
    </div>
    <!--  告警 list end-->
    <riskCenterModal ref="riskCenterRef" @ok="addAlerts"/>
  </div>
</template>

<script setup lang="ts">
import {inject, nextTick, onMounted, ref, watch} from 'vue';
import useDisplayPanel from '../hooks/useDisplayPanel';
import riskCenterModal from '../components/riskCenterModal.vue';
import {useI18n} from '/@/hooks/web/useI18n';
import {useRiskCenter} from '/@/views/ticket/edit/component/form/hooks/useRiskCenter';
import RiskBtns from '/@/views/ticket/edit/component/form/components/riskBtns.vue';
import centerStatusVue from './centerStatus.vue';

import RiskBatchBtns from "/@/views/ticket/edit/component/form/components/riskBatchBtns.vue";
import {E_Ticket_Type} from "/@/views/ticket/enums/ticketEnum";

const {handleRiskEventMap} = useRiskCenter();

const {t} = useI18n();

const ticketFormData:any = inject('ticketForm');
const severityMap = {
  'Critical':"1",
  'High':'2',
  'Middle':'3',
  'Low':'4',
  'Information':'5',
  'Info':'5',
}
const emits = defineEmits(['update:value']);
const props = defineProps({
  value: {
    type: Object,
    default: () => {
    },
  },
});
const typeName = {
  'risk':t('routes.riskEvent.RiskEvents'),
  'mlStatistic':t('routes.riskEvent.MLStatistic'),
  'mlOrder':t('routes.riskEvent.MLOrder'),
  'mlContent':t('routes.riskEvent.MLContent'),
  'badActor':t('routes.riskEvent.Bad'),
  'anomaly':t('routes.riskEvent.anomalyRecord'),
  'httpWaf':t('routes.riskEvent.httpEvent'),
}
function capitalizeFirstLetter(str: string): string {
  if (!str) {
    return str;
  }
  if(!severityMap[str]){
    return "";
  }
  const firstChar = str.charAt(0).toUpperCase();
  const restOfString = str.slice(1);
  return firstChar + restOfString;
}

const {childData} = useDisplayPanel(props);

const {eventDetail} = useRiskCenter();

const riskCenterRef = ref();
/**
 * 添加的事件类型数据{risk:[],mlStatistic:[]...}
 */
let selectEventMap: any = {};
onMounted(() => {
  console.log('childData.value.defaultValue', childData.value.defaultValue)
  loadInfo(childData.value.defaultValue);
});

async function loadInfo(data) {
  if (data && data.length > 0) {
    const flagList: any[] = [];
    for (let i = 0; i < data.length; i++) {
      flagList.push({
        flag: false,
        data: {}
      });
      eventDetail(data[i], flagList[i])
    }

    const times = setInterval(() => {
      try {
        let flag = true;
        flagList.forEach(item => {
          if (!item.flag) {
            flag = false;
          }
        })
        if (flag) {
          clearInterval(times);
          const list: any = [];
          flagList.forEach(item => {
            list.push(item.data);
          })
          console.log('list', list)
          childData.value.dataSource = list ?? [];
          selectEventMap = handleRiskEventMap(childData.value.dataSource);
        }
      } catch (e) {
        clearInterval(times);
      }
    }, 100);

    setTimeout(() => {
      clearInterval(times);
    }, 1000 * 20)
  }


}

function openEvent() {
  if (childData.value.disable) {
    return;
  }
  if(ticketFormData?.formData?.ticketType == E_Ticket_Type.NOTICE && !ticketFormData?.formData?.socTenantId){
    return;
  }
  riskCenterRef.value.open(selectEventMap,ticketFormData?.formData?.socTenantId);
}

/**
 * 添加事件
 * @param list
 */
function addAlerts(list) {
  // console.log('list',list)
  // console.log('  childData.value.dataSource',  childData.value.dataSource)
  childData.value.dataSource = list;
}

watch(
  () => childData.value.dataSource,
  () => {
    childData.value.defaultValue = childData.value.dataSource;
    selectEventMap = handleRiskEventMap(childData.value.dataSource);
    emits('update:value', childData.value);
    nextTick(()=>{
      const elements = document.querySelectorAll('.truncate-text');
      console.log('elements',elements)
      elements.forEach(el => truncateText(el));
    })
  },
  {deep: true}
);



/**
 * 移除事件
 * @param index
 */
function removeEvent(index) {
  if (childData.value.dataSource) {
    childData.value.dataSource.splice(index, 1);
  }
}
function truncateText(element, maxLines = 2) {
  const text = element.textContent;
  const lineHeight = parseFloat(getComputedStyle(element).lineHeight);
  const maxHeight = lineHeight * maxLines;

  if (element.offsetHeight > maxHeight) {
    let truncated = text;
    while (element.offsetHeight > maxHeight && truncated.length > 0) {
      truncated = truncated.slice(0, -1);
      element.textContent = truncated + '...';
    }
  }
}



</script>

<style scoped lang="less">
@import '../template.less';
.risk-center_wrapper{
  display: flex;
  flex-direction: column;
}
.level_default {
  background: @border-color;
}

.alert_default {
  background: rgba(255, 255, 255, 0.2);
}


.risk_content_wrapper {
  display: flex;
  flex-direction: row;
  gap: 0;
  height: 144px;
  background:  rgba(@font-color-white, 0.1);
  &.Critical {
    background: rgba(@color-red, 0.1)!important;
  }

  &.High {
    background: rgba(@color-red-orange, 0.1)!important;
  }

  &.Middle {
    background: rgba(@color-orange, 0.1)!important;
  }

  &.Medium {
    background: rgba(@color-orange, 0.1)!important;
  }

  &.Low {
    background: rgba(@color-yellow, 0.1)!important;
  }

  &.Information {
    background: rgba(@color-primary, 0.1)!important;
  }

  &.Info {
    background: rgba(@color-primary, 0.1)!important;
  }

  //告警等级
  .risk_level {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    flex: 0 0 55px;
    height: 100%;
    gap: 12px;
    background: @color-default-bg;
    color: @color-default;
    &.Critical {
      background: rgba(@color-red, 0.2)!important;
      color: @color-red!important;
    }
    &.High {
      background: rgba(@color-red-orange, 0.2)!important;
      color: @color-red-orange!important;
    }
    &.Middle {
      background: rgba(@color-orange, 0.2)!important;
      color: @color-orange!important;
    }
    &.Medium {
      background: rgba(@color-orange, 0.2)!important;
      color: @color-orange!important;
    }
    &.Low {
      background: rgba(@color-yellow, 0.2)!important;
      color: @color-yellow!important;
    }
    &.Information {
      background: rgba(@color-primary, 0.2)!important;
      color: @color-primary!important;
    }
    &.Info {
      background: rgba(@color-primary, 0.2)!important;
      color: @color-primary!important;
    }
  }

  .alert_info {
    display: flex;
    flex-direction: column;
    padding: 12px 16px;
    gap: 8px;
    flex: 1;
    overflow: hidden;
    .alert_info_top{
      display: flex;
      flex-direction: row;
      align-items: center;
      gap: 16px;
      justify-content: space-between;
    }

    .alert_row2 {
      display: flex;
      flex-direction: row;
      align-items: center;
      gap: 8px 8px;
      flex-wrap: wrap;
      align-content: center;
      align-self: stretch;
    }

    .alert_text_div {
      display: flex;
      flex-direction: row;
      align-items: center;
      padding: 4px;
      gap: 4px;
      border-radius: 4px;
    }

    .no_status {
      background: rgba(246, 200, 77, 0.1);
      color: #f6c84d;
    }

    .yes_status {
      background: rgba(48, 140, 255, 0.1);
      color: #308cff;
    }
  }
}

.alert_add {
  width: 88px;
  height: 88px;
  border: 1px solid @border-color;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
  cursor: pointer;
}

.eventName {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  word-break: break-all;
  width: 300px;
}
</style>
