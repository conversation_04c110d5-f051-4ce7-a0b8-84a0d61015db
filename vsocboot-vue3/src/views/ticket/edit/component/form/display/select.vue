<template>
  <a-select
    v-model:value="childData.defaultValue"
    :id="childData.field"
    :style="{ width: childData?.componentProps?.width }"
    :mode="childData?.componentProps?.multiple ? 'multiple' : ''"
    :showSearch="childData?.componentProps?.showSearch"
    optionFilterProp="label"
    :placeholder="childData?.componentProps?.placeholder"
    :disabled="childData.disable ?? false"
  >
    <template v-for="(group, index) in childData.items" :key="'select' + index">
      <a-select-option :label="group.label" :value="group.value">{{ group.label }}</a-select-option>
    </template>
  </a-select>
</template>

<script setup lang="ts">
  import useDisplayPanel from '../hooks/useDisplayPanel';

  const props = defineProps({
    value: {
      type: Object,
      // eslint-disable-next-line vue/require-valid-default-prop
      default: {},
    },
  });
  const { childData } = useDisplayPanel(props);
  console.log('select childData=',childData)
</script>

<style scoped lang="less">
  @import '../template.less';
</style>
