<template>
  <a-textarea
    :id="childData.field"
    v-model:value="childData.defaultValue"
    :placeholder="childData?.componentProps?.placeholder"
    :style="{ width: childData?.componentProps?.width }"
    :disabled="childData.disable ?? false"/>
</template>

<script setup lang="ts">
  import useDisplayPanel from '../hooks/useDisplayPanel';

  const props = defineProps({
    value: {
      type: Object,
      // eslint-disable-next-line vue/require-valid-default-prop
      default: {},
    },
  });
  const { childData } = useDisplayPanel(props);
</script>

<style scoped lang="less">
  @import '../template.less';
</style>
