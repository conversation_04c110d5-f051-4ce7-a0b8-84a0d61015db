<template>
  <div class="w-85px h-80px pt-4px pb-4px pr-8px pl-8px userSet">
    <div class="w-32px h-32px m-auto">
      <img src="../../../../../../../../assets/images/roleImg.png" class="h-100% w-100%" alt=""/>
    </div>
    <div class="text-12px w-100% pt-2px pb-2px font-bold text-[#FFFFFF] text-center word-ellipsis"
         :title="record?.roleName">{{ record?.roleName }}
    </div>
    <div class="text-12px w-100% pt-2px pb-2px text-center word-ellipsis"
         style="color: rgba(255, 255, 255, 0.6)" :title="record?.roleName">{{ num }}
    </div>
    <div class="del" v-if="isEdit">
      <Icon
        icon="ant-design:close-outlined"
        :title="t('workflow.workflow.deleteItem')"
        @click="emit('del')"
      />
    </div>
  </div>
</template>
<script setup lang="ts">
import {ref, watch} from "vue";
import {queryUserNameByRoleIds} from "/@/views/system/user/user.api";
import Icon from "/@/components/Icon";
import {useI18n} from "/@/hooks/web/useI18n";
const { t } = useI18n();
const emit = defineEmits(['del']);
const num = ref(0);
const props = defineProps({
  record: Object,
  isEdit: {
    type: Boolean,
    default: false
  },
});
watch(() => props.record, (n) => {
  if (n?.id) {
    queryUserNameByRoleIds({ids: n.id}).then((result) => {
      num.value = result.length;
    })
  }
}, {immediate: true})
</script>
<style scoped lang="less">
.userSet {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 4px 8px;
  gap: 4px;
  border-radius: 4px;
  position: relative;
  background: rgba(255, 255, 255, 0.1);
  .del{
    display: none;
    position: absolute;
    right: 2px;
    top: 2px;
    cursor: pointer;
    z-index: 2;
  }
  &:hover{
    .del{
      display: block;
    }
  }
}

.word-ellipsis {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  width: 100%;
}
</style>
