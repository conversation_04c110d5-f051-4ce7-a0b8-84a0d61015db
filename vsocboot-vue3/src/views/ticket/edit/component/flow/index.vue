<template>
  <!-- 画布-->
  <FlowBpmn ref="bpmnRef"/>

</template>
<script setup lang="ts">
import {FlowBpmn} from './bpmn';
import {nextTick, provide, ref} from "vue";
import {useMessage} from "/@/hooks/web/useMessage";
import {elementErrorMap} from "/@/views/ticket/edit/component/flow/bpmn/event";
import {EStep_Type} from "/@/views/ticket/enums/editEnum";

const { createMessage } = useMessage();
const bpmnRef = ref();
const formDesign = ref([])
provide('formDesign',formDesign)
/**
 * 数据校验
 */
async function validate() {
  try {
    const result = bpmnRef.value.validateBpmn();
    if(result === true){
      return getData();
    }
    createMessage.warning(elementErrorMap[result]);
    return false;

  } catch (e) {
    return false;
  }
}

/**
 * 初始化数据
 * @param stepData 步骤数据
 */
function initData(stepData:any) {
  const formData = stepData[EStep_Type.FORM];
  nextTick(()=>{
    formDesign.value = formData?.designContent ? JSON.parse(formData.designContent) : [];
    bpmnRef.value.initBpmn(stepData[EStep_Type.FLOW],stepData[EStep_Type.BASE]);
  })

}

/**
 * 返回数据
 */
function getData() {
  return bpmnRef.value.saveBpmn();
}
defineExpose({
  validate,
  initData,
  getData,
});
</script>
<style scoped lang="less">

</style>
