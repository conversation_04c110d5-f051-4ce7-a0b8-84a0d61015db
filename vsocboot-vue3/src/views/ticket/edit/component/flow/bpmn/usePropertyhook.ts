import {toRaw} from "vue";

import {E_Element_Type} from "/@/views/ticket/enums/flowEnum";
import {globalGateName} from "/@/views/ticket/edit/component/flow/bpmn/event";
import {INodeItem, INodePermission} from "/@/views/ticket/ts/flow";


export const usePropertyHandle = (bpmnModeler, data) => {
  const {elementSelector, shapeConfig} = data.value;
  const {id} = elementSelector[0];
  const modeling = bpmnModeler.get("modeling");

  /**
   * 更新节点名称
   * @param businessObject
   */
  const updateNodeName = (businessObject) => {
    console.log('businessObject', businessObject)
    // propertyData.value.nodeInfo.name = businessObject?.name || businessObject?.text;
  }

  /**
   * 更新起始点
   */
  const updateApplication = () => {
    modeling.updateProperties(toRaw(elementSelector[0]), {
      name: ''
    });
  }

  /**
   * 更新Task节点
   * 1、更新task名称，更新关联网关的分支名称
   * 2、有会签配置更新
   */
  const updateTask = () => {
    const {isTenant,permission, errorInfo, name} = shapeConfig[id] as INodeItem;
    if (errorInfo) {
      modeling.updateProperties(toRaw(elementSelector[0]), {
        name: name,
      });
      return;
    }
    if(!isTenant){
      const {user, isMultiInstance, sign} = permission.node as INodePermission;
      if (isMultiInstance) {//有会签配置
        drawLoopCharacteristics(sign, name);
      } else if(user && user.length > 0){
        modeling.updateProperties(toRaw(elementSelector[0]), {
          name: name,
          'camunda:assignee': user![0].id,
        });
      }else{
        modeling.updateProperties(toRaw(elementSelector[0]), {
          name: name,
        });
      }
    }else{
      drawLoopCharacteristics(1, name);
    }

    //修改网关配置条件名称
    const gateArray = Object.keys(shapeConfig).filter(item => item.indexOf('Gateway') != -1);
    gateArray?.forEach(key => {
      shapeConfig[key]?.line?.forEach(line => {
        if (line.linkId == id) {
          line.name = shapeConfig[line.linkId]?.name ?? line.name;
          modeling.updateProperties(bpmnModeler.get('elementRegistry')?.get(key), {
            name: line.name,
          });
        }

      });
    })

  }

  /**
   * 每一条线(多个条件，每个条件都必须保证有值)
   */
  function updateExclusive() {
    shapeConfig[id].line.forEach(item => {
      const {conditionGroup, lineId} = toRaw(item)
      let expression = "${";
      let rel = '';
      conditionGroup.forEach((item, index) => {//多个条件OR关系
        if (index != 0) {
          rel = '||';
        }
        let str = "(";
        item.condition.forEach((d, j) => {//每个组件是AND关系
          str = str + (j > 0 ? '&&' : ' ') + getConditionStr(d) + ' ';
        })
        expression = expression + rel + str + ")";
      })
      expression = expression + "}";
      const conditionExpression = bpmnModeler._moddle.create("bpmn:FormalExpression", {
        body: expression,
      });
      const ele = bpmnModeler.get('elementRegistry').get(lineId);
      modeling.updateProperties(ele, {
        conditionExpression: conditionExpression
      });
    })
    modeling.updateProperties(toRaw(elementSelector[0]), {
      name: globalGateName,
    });
  }

  /**
   * 条件拼成字符串
   * @param item
   */
  function getConditionStr(item) {
    let value = item.value;
    if(Array.isArray(value)){
      value = value.length > 0 ? value.toString() : '';
    }
    if(typeof value === 'number'){
      return item.componentId + item.connector + value;
    }
    return item.componentId + '' + item.connector + ' \'' + value + '\'';
  }

  /**
   * 多实例配置
   * @param sign true为串行多实例，false为并行多实例
   * @param name
   */
  function drawLoopCharacteristics(sign, name) {
    const moddle = bpmnModeler.get('moddle');
    /**
     * completionCondition
     * ${nrOfInstances == nrOfCompletedInstances}表示所有人员审批完成后会签结束。
     * ${ nrOfCompletedInstances == 1}表示一个人完成审批。
     */
    const expression = '${nrOfCompletedInstances == 1}';
    const conditionExpression = moddle.create("bpmn:FormalExpression", {
      body: expression,
    });
    const loopCharacteristics = moddle.create('bpmn:MultiInstanceLoopCharacteristics', {
      isSequential: sign != 1, // 设置会签标志为false(同时执行)/true(串行)
      collection: elementSelector[0].id + 'List',
      elementVariable: 'assignee',
      completionCondition: conditionExpression
    });

    modeling.updateProperties(toRaw(elementSelector[0]), {
      name: name,
      assignee: '${assignee}',
      loopCharacteristics: loopCharacteristics
    });
  }

  /**
   * 更新节点
   */
  const updateConfig = () => {

    const element = elementSelector[0];
    if (element.type == E_Element_Type.START) {
      updateApplication();
    } else if (element.type == E_Element_Type.TASK) {
      updateTask();
    } else if (element.type == E_Element_Type.EXCLUSIVE) {
      updateExclusive();
    }

  }


  return {updateNodeName, updateConfig}
}

