import BpmnElementFactory from 'bpmn-js/lib/features/modeling/ElementFactory';
import inherits from 'inherits';
import {getBusinessObject, getDi, is} from "bpmn-js/lib/util/ModelUtil";
import {isExpanded} from "bpmn-js/lib/util/DiUtil";
import {E_Element_Type} from "/@/views/ticket/enums/flowEnum";
import {globalShapeConfig, globalTicketType} from "/@/views/ticket/edit/component/flow/bpmn/event";

export default function CustomElementFactory(bpmnFactory, moddle, translate) {
BpmnElementFactory.call(this, bpmnFactory, moddle, translate);
}
inherits(CustomElementFactory, BpmnElementFactory);

CustomElementFactory.$inject = [
'bpmnFactory',
'moddle',
'translate'
];
// 重写bpmn中BpmnElementFactory的一些方法
/**
 * 解决连线不水平问题
 * @param element
 * @param di
 * @returns {{width: number, height: number}}
 */
CustomElementFactory.prototype.getDefaultSize = function(element, di) {
  var bo = getBusinessObject(element);
  di = di || getDi(element);
  if (is(bo, 'bpmn:SubProcess')) {
    if (isExpanded(bo, di)) {
      return { width: 350, height: 200 };
    } else {
      return { width: 100, height: 80 };
    }
  }
  if (is(bo, E_Element_Type.TASK)) {

    return { width: 200, height: 32 };
  }
  if (is(bo, 'bpmn:Task')) {
    return { width: 200, height: 32 };
  }

  if (is(bo, E_Element_Type.END)) {
    return { width: 64, height: 64 };
  }

  if (is(bo, E_Element_Type.EXCLUSIVE)) {
    return { width: 200, height: 32 };
  }

  if (is(bo, 'bpmn:Event')) {
    return { width: 200, height: 32 };
  }

  if (is(bo, 'bpmn:Participant')) {
    if (isExpanded(bo, di)) {
      return { width: 600, height: 250 };
    } else {
      return { width: 400, height: 60 };
    }
  }

  if (is(bo, 'bpmn:Lane')) {
    return { width: 400, height: 100 };
  }

  if (is(bo, 'bpmn:DataObjectReference')) {
    return { width: 36, height: 50 };
  }

  if (is(bo, 'bpmn:DataStoreReference')) {
    return { width: 50, height: 50 };
  }

  if (is(bo, E_Element_Type.TEXT)) {
    return { width: 200, height: 30 };
  }

  if (is(bo, 'bpmn:Group')) {
    return { width: 300, height: 300 };
  }

  return { width: 100, height: 80 };
};
