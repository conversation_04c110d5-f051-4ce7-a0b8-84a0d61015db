import Start from '/@/assets/bpmn/start.png';
import UserTask from '/@/assets/bpmn/task.png';
import Gateway from '/@/assets/bpmn/gateway.png';
import End from '/@/assets/bpmn/end.png';
import TextAnnotation from '/@/assets/bpmn/end.png';

const hasLabelElements = ['bpmn:StartEvent', 'bpmn:EndEvent'] // 一开始就有label标签的元素类型
const headerElements = ['bpmn:UserTask', 'bpmn:StartEvent', 'bpmn:ExclusiveGateway'];
/**
 * Task的header
 * @type {{padding: number, bgColor: string, icon: {width: number, height: number}, fontSize: number, fontColor: string, fontWeight: number, height: number}}
 */
const headerStyle = {
  fontColor: '#FFFFFF',
  bgColor:'#18191D',
  headerHeight:24,
  padding:4,
  icon: {
    height:12,
    width:14,
  },
  fontSize:12,
  fontWeight:600

}

const endStyle = {
  fontColor: '#FFFFFF',
  bgColor:'#308CFF',
  icon: {
    height:19,
    width:19
  },
  fontSize:13,
  fontWeight:600,
  x:20,
  y:10
}

const customConfig = { // 自定义元素的配置
  'bpmn:UserTask': {
    'url': UserTask,
    'attr': {x: 0, y: 0, width: 200, height: 90, defaultName: 'Task'},
    'style':headerStyle
  },
  'bpmn:StartEvent': {
    'url': UserTask,
    'attr': {x: 0, y: 0, width: 200, height: 90, defaultName: 'Application'},
    'style':headerStyle
  },
  'bpmn:EndEvent': {
    'url': End,
    'attr': {x: 0, y: 0, width: 64, height: 64, defaultName: 'End'},
    'style':endStyle

  },
  'bpmn:ExclusiveGateway': {
    'url': Gateway,
    'attr': {x: 0, y: 0, width: 280, height: 90, defaultName: 'IF'},
    'style':headerStyle
  },
  'bpmn:TextAnnotation': {
    'url': TextAnnotation,
    'attr': {x: 0, y: 0, width: 200, height: 20, defaultName: 'this is a annotation',},
    'style':headerStyle
  },

}

export {customConfig, hasLabelElements,headerElements}
