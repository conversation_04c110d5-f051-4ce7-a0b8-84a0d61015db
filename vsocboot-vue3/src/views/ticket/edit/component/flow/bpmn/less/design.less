
/deep/ .djs-parent {
  --context-pad-entry-background-color: @dark-bg1 !important;
  --context-pad-entry-hover-background-color: @dark-bg3 !important;
  --shape-drop-allowed-fill-color: transprent !important;
  --popup-background-color: @dark-bg2 !important;
  --popup-border-color: @border-color !important;
  --palette-entry-color: @font-color-white;
  --palette-separator-color: @border-color;
  --color-blue-205-100-45-opacity-30: @border-color !important; //鼠标拖动垂直线颜色
  --common-stroke: @font-color-white;
  --common-stroke-line: @font-color-white; //连线颜色
  --common-fill: @font-color-white;
  --popup-entry-hover-color: @bg-color;
  --palette-background-color: @dark-bg3 !important;
  --palette-border-color: @border-color !important;
  --shape-connect-allowed-fill-color: rgba(48, 140, 255, 0.2) !important;
  --shape-drop-not-allowed-fill-color: rgba(48, 140, 255, 0.2) !important;

}

/deep/ [class^="bpmn-icon-"]:before, /deep/ [class*=" bpmn-icon-"]:before {
  font-family: "soc" !important;

}
.hidePalette{
  :deep(.djs-container) {
    .djs-palette{
      display: none!important;
    }
  }
}
.bpmn-canvas {
  height: 100%;
  flex: 1;
  :deep(.djs-container) {
    .djs-palette-entries {
      .separator {
        width: 16px;
        margin:0 auto;
        padding-top: 8px;
        margin-bottom: 7px;
      }
    }

    .djs-palette {
      border: 0;
      border-radius: 8px;
      width: 40px;
      justify-content: center;
      top: 50%;
      margin-top: -136px;
      padding: 8px 0;
      .bpmn-icon-lasso-tool, .bpmn-icon-space-tool,
      .bpmn-icon-intermediate-event-none, .bpmn-icon-subprocess-expanded,
      .bpmn-icon-data-store,
      .bpmn-icon-data-object,
      .bpmn-icon-group,
      .bpmn-icon-participant,
      .bpmn-icon-start-event-none,
      .bpmn-icon-task {
        display: none;
      }

      .entry {
        width: 40px;
        height: 40px;
        line-height: 40px;
        font-size: 24px;
      }
    }

    .djs-context-pad {
      &.open {
        display: none;
      }

      .entry {
        font-size: 16px;
      }

      .bpmn-icon-screw-wrench, .bpmn-icon-intermediate-event-none, .bpmn-icon-text-annotation, .bpmn-icon-task {
        display: none;
      }
    }

    .djs-palette.two-column.open {
      width: 40px !important;
     /* top: 60px;*/
      background: @dark-bg3;
      border: 0 !important;

    }

  }
}

/deep/.djs-direct-editing-parent {
  background-color: @dark-bg3 !important;
  border-width: 0 !important;
  border-radius: 10px;
  display: none;
}

/deep/ [data-element-id^='Gateway'], [data-element-id^='Activity_'] {
  path {
    display: none !important;
  }
}

/deep/ [data-element-id^='Gateway'][data-element-id$='_label'] {
  display: none !important;
}

/deep/ [data-element-id^='Flow_'] {
  path {
    display: block !important;
  }
}

/deep/ [data-element-id^='TextAnnotation_'] {

  g.djs-visual {
    & > :nth-child(1) {
      stroke-width: 0 !important;
    }

    rect {
      stroke-width: 0px !important;
    }
  }
}

/deep/ .bjs-container {

  g.djs-visual {
    path {
      display: none;
    }

    //text {
    //  fill: var(--common-fill) !important;
    //  stroke: none !important;
    //  font-size: 13px;
    //
    //}

    //& > :nth-child(1):not(circle) { //shape
    //  fill: transparent !important;
    //  stroke: var(--common-stroke) !important;
    //}

    & > :nth-child(1):not(text), & > :nth-child(1):not(path) { //shape
      display: block;
    }

    & > path:nth-child(1) { //line
      stroke: var(--common-stroke-line) !important;
    }

    & > path:first-child {
      display: none;
    }
  }

  .djs-direct-editing-parent {
    background-color: @dark-bg3 !important;
    border-width: 0 !important;
    border-radius: 10px;
    display: none;
  }

  .djs-connection-preview { //拖拽连线时虚线颜色
    path {
      stroke: @font-color-1 !important;
    }
  }


}
 vs
/deep/ .djs-connection {
  g.djs-visual {
    path:first-child {
      stroke: @border-color!important;
      marker-start: url("#sequenceflow-arrow-normal");

    }

  }
}

/deep/ marker {
  transform: rotate(90deg);
}

/deep/ marker > path { //连线三角的颜色
  //stroke: var(--common-stroke-line) !important;
  //fill:var(--common-stroke-line) !important;
  stroke: @primary-color !important;
  fill: @primary-color !important;
  stroke-width: 2px !important;
}

/deep/ .bpmn-icon-hand-tool:before {
  content: "\e613";
}

/deep/ .bpmn-icon-connection-multi:before {
  content: "\e614";
}

/deep/ .bpmn-icon-start-event-none:before {
  content: "\e60f";
  color: #308CFF;
}

/deep/ .bpmn-icon-end-event-none:before {
  content: "\e612";
  color: #308CFF;
}

/deep/ .bpmn-icon-gateway-none:before {
  content: "\e615";
  color: #F8A556;
}

/deep/ .bpmn-icon-user-task:before {
  content: "\e610";
  color: #308CFF;
}

/deep/ .bpmn-icon-trash:before {
  font-family: bpmn !important;
}

/deep/ .bpmn-icon-user-task {
  font-family: soc !important;
  content: "\e610";
}

/deep/ .ax-bjq-note {
  color: #2ECF99;
}

circle:before {
  font-family: soc;
  content: "\e60f";
  color: #308CFF;
}

.ticket-node-menu {
  padding: 4px;
  border-radius: 8px;
  background: @dark-bg3;
  //box-sizing: border-box;
  //border: 1px solid @border-color;
  box-shadow: 0px 0px 20px 0px rgba(0, 0, 0, 0.5);
  position: absolute;
  z-index: 100;
  display: none;
  top: 1px;
  .ticket-node-menu_item {
    padding: 8px 12px;
    font-size: 12px;
    font-weight: normal;
    line-height: 16px;
    color: @font-color-default;
    cursor: pointer;
    &:hover{
      background: @border-color-01;
      border-radius: 4px;
    }
  }


}



