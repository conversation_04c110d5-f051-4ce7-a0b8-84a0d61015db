import {E_Element_Error, E_Element_Event, E_Element_Type} from "/@/views/ticket/enums/flowEnum";
import {elementErrorMap, xmlEvents} from "/@/views/ticket/edit/component/flow/bpmn/event";
import {
  ICondition,
  IConditionGroup,
  IGatewayItem,
  ILineError,
  INodeItem,
  INodePermission,
  ITenantPermission
} from "/@/views/ticket/ts/flow";
import {E_FORMITEM_PERMISSION} from "/@/views/ticket/enums/typeEnum";


export const useBpmnValidate = (modeler, tenantList) => {
  const elementRegistry = modeler.get('elementRegistry');

  /**
   * 组件添加校验，禁止添加不合理的组件
   * @param shape
   */
  const addShape = (shape) => {
    let error = '';
    const shapeType = shape.type;
    if (xmlEvents.includes(shapeType)) {
      const elements = elementRegistry.filter(item => item.type == shapeType);
      if (elements.length > 1) {
        error = shapeType == E_Element_Type.START ? E_Element_Error.MORE_START : E_Element_Error.MORE_END;
      }
    }
    return error;
  }

  /**
   * 验证任务
   * @param data
   */
  const validateTask = (data: INodeItem) => {
    if(!data){
      return {errorInfo : elementErrorMap[E_Element_Error.NO_TASK]};
    }
    if (data.isTenant) {
      const tenantArray = data.permission.node as ITenantPermission[];
      if (tenantArray.length == 0) {
        data.errorInfo = elementErrorMap[E_Element_Error.NO_TASK];
        return data;
      }
      data.errorInfo = [];
      for (const tenant of tenantArray) {
        //没考虑到的情况：角色下面没有人员~~~
        const tPermission = tenant.permission as INodePermission;
        //没有配置用户和角色~~~
        if ((!tPermission?.role && !tPermission?.user) || (tPermission?.role?.length == 0 && tPermission?.user?.length == 0)) {
          data.errorInfo.push(findTenantName(tenant.tenantId));
        }
      }
      if (data.errorInfo.length == 0) {
        data.errorInfo = '';
      }
      return data
    } else {
      const node = data.permission.node as INodePermission;
      const isError = (!node?.role && !node?.user) || (node.user?.length == 0 && node.role?.length == 0);
      if (isError) {
        data.errorInfo = elementErrorMap[E_Element_Error.NO_TASK];
      } else {//用户修改后清空错误信息
        data.errorInfo = '';
      }
    }

    return data;

  }

  /**
   * 租户名称
   * @param id
   */
  const findTenantName = (id: string) => {
    const arr = tenantList.filter(item => item.id == id);
    return arr.length > 0 ? arr[0].name : '';
  }


  /**
   * 验证IF节点
   * @param data
   */
  const validateIFEvent = (data) => {
    let error = [] as ILineError[];
    data?.line.forEach(item => {
      const {name, conditionGroup, lineId} = item;
      const code = validateConditionGroup(conditionGroup);
      if (code) {
        error.push({name, code, lineId});
      }
    })
    data.errorInfo = error.length > 0 ? error : '';
    return data;

  }
  /**
   * 校验分支条件
   * @param conditionGroup
   */
  const validateConditionGroup = (conditionGroup: IConditionGroup[]) => {
    if (!conditionGroup || conditionGroup.length == 0) {
      return elementErrorMap[E_Element_Error.GATEWAY_CONFIG];
    }
    for (const group of conditionGroup) {
      const condition = group.condition as ICondition[];
      if (!condition || condition.length == 0) {
        return elementErrorMap[E_Element_Error.GATEWAY_CONFIG];
      }
      const array = condition.filter(item => !item.value);
      if (array.length > 0) {
        return elementErrorMap[E_Element_Error.GATEWAY_CONFIG];
      }
    }

  }
  /**
   * 校验方法Map
   */
  const funcMap = {
    [E_Element_Event.ADD_SHAPE]: addShape,
    [E_Element_Event.ADD_LINE]: addShape,
    [E_Element_Type.START]: validateTask,
    [E_Element_Type.TASK]: validateTask,
    [E_Element_Type.EXCLUSIVE]: validateIFEvent
  }


  /**
   * 验证组件包含
   * 1、起始节点是否存在，配置项是否完整
   * 2、结束节点是否存在
   * 3、Task任务：连线数量、任务配置项
   * 4、Gate网关：连线数量、配置项
   * 5、网关涉及条件组件自动设置为必填
   */
  const validateActivity = (shapeConfig: any) => {


    /**
     * 起始节点校验
     * 1、只能有1个开始节点
     * 2、开始节点没有配置
     * 3、开始节点只能有一个出口线
     */
    const startEvent = elementRegistry.filter(item => item.type == E_Element_Type.START);
    if (startEvent.length == 0) {
      return E_Element_Error.NO_START;
    }
    const error = validateTask(shapeConfig[startEvent[0].id]).errorInfo;
    if (error){
      return E_Element_Error.START_CONFIG;
    }
    if (startEvent[0].outgoing.length == 0 || startEvent[0].outgoing.length > 1) {
      return E_Element_Error.OUT_START_LINE;
    }
    /**
     * 终止节点校验
     */
    const endEvent = elementRegistry.filter(item => item.type == E_Element_Type.END);
    if (endEvent.length == 0) {
      return E_Element_Error.NO_END;
    }
    else if (endEvent.length > 1) {
      return E_Element_Error.MORE_END;
    }

    /**
     * 任务校验
     * 1、任务不能为空
     * 2、任务配置条件不能空
     */
    const userTaskList = elementRegistry.filter((item) => item.type === E_Element_Type.TASK);
    //没有配置任务
    if (!userTaskList || userTaskList.length == 0)
      return E_Element_Error.NO_TASK;
    //校验Task配置
    for (const task of userTaskList) {
      if (task.incoming.length == 0 || task.outgoing.length == 0) {
        return E_Element_Error.MISS_TASK_LINE;
      }
      if (task.outgoing.length > 1) {
        return E_Element_Error.MORE_TASK_LINE;
      }
      if (!shapeConfig[task.id] || validateTask(shapeConfig[task.id])?.errorInfo) {
        return E_Element_Error.MISS_TASK_PERMISSION;
      }
    }

    /**
     * 连线数量校验
     */
    const sequenceFlow = elementRegistry.filter(item => item.type == E_Element_Type.LINE);
    //缺少连线
    if (sequenceFlow.length < userTaskList.length + 1)
      return E_Element_Error.MISS_LINE;


    /**
     * 排他网关校验（可以没有网关）
     */
    const exclusiveGateway = elementRegistry.filter(item => item.type == E_Element_Type.EXCLUSIVE);
    //校验分支配置
    for (const gateway of exclusiveGateway) {
      if (gateway.incoming.length == 0 || gateway.outgoing.length == 0) {
        return E_Element_Error.MISS_GATEWAY_LINE;
      }
      if (!shapeConfig[gateway.id] || !shapeConfig[gateway.id].line) {
        return E_Element_Error.MISS_GATEWAY_PERMISSION;
      }
      const result = validateIFEvent(shapeConfig[gateway.id]);
      if (result?.errorInfo) {
        return E_Element_Error.GATEWAY_CONFIG;
      }

      /**
       * 分支条件组件需要设置为必填
       */
      const {beforeShape, line} = shapeConfig[gateway.id] as IGatewayItem;
      //涉及的组件ID
      const componentIdArray = [] as string[];
      //分支连线
      const haveLines = getConditionLine(gateway.id);
      //分支连线变更，缺少配置项
      if(haveLines.length != line.length || haveLines.length != line.filter(item=>haveLines.includes(item.lineId)).length){
        return E_Element_Error.GATEWAY_CONFIG;
      }
      line.forEach(item => {
        item.conditionGroup.forEach(group => {
          group.condition.forEach(condition => {
            if (!componentIdArray.includes(condition.componentId)) {
              componentIdArray.push(condition.componentId);
            }
          })
        })
      })
      //表单所属的组件ID改为必填
      componentIdArray.length > 0 && beforeShape.forEach(key => {
        if (shapeConfig[key].type == E_Element_Type.TASK || shapeConfig[key].type == E_Element_Type.START) {
          const node = shapeConfig[key] as INodeItem;
          const form = node.permission.form;
          componentIdArray.forEach(cId => {
            if (form && (!form[cId] || form[cId] != E_FORMITEM_PERMISSION.REQUIRED))
              form[cId] = E_FORMITEM_PERMISSION.REQUIRED;
          })
        }
      })
    }

    return true;
  }

  /**
   * 分支连线
   * @param id
   */
  const getConditionLine = (id) => {
    const outgoing = elementRegistry.find((item) => item.id === id)?.outgoing;
    if (outgoing.length == 0) {
      return [];
    }
    return outgoing.map((item) => {
      if (item.target.type == E_Element_Type.TASK || item.target.type == E_Element_Type.END)
        return item.id
    });
  }
  /**
   * 校验方法
   * @param eventName
   * @param shape
   */
  const validateFunc = (eventName, shape) => {
    return funcMap[eventName](shape);
  }

  /**
   * 校验方法
   * @param type
   * @param data
   */
  const validateConfig = (type, data) => {
    return funcMap[type](data);
  }

  return {
    validateFunc, validateConfig, validateActivity
  }
}
