import {E_Element_Type, E_Shape_Menu} from "/@/views/ticket/enums/flowEnum";
import {nextTick} from "vue";

import {globalShapeConfig} from "/@/views/ticket/edit/component/flow/bpmn/event";


export const useElementHandle = (modeler,configData)=>{

  const elementFactory = modeler.get("elementFactory");
  const modeling = modeler.get("modeling")
  /**
   * 获取 Shape
   * @param id
   */
  const getShape = (id)=> {
    return modeler.get('elementRegistry')?.get(id);
  }

  /**
   * 处理 element
   * @param shape
   * @param element
   */
  const handleElement = (shape,element)=>{

    //解决bpmn:TextAnnotation拖拽后是个小标签
    if (shape.type == E_Element_Type.TEXT) {
      nextTick(() => {
        modeling.updateProperties(element, {
          text: 'This is an annotation'
        });
      })
    }
  }
  /**
   * 删除元素
   * @param shape
   */
  const removeElements = (shape)=>{
    nextTick(() => {
      modeling.removeElements([shape])
    })
  }



  /**
   * 自动添加节点
   * @param type
   */
  const addShape = (type:string)=> {
    const autoPlace = modeler.get("autoPlace");
    const startShape = getShape(configData.value.elementSelector[0].id)
    const endShape = elementFactory.createShape({type});
    autoPlace.append(startShape, endShape);
  }

  /**
   * 创建节点
   * @param attr
   */
  const createShape = (attr)=> {
    //根元素
    const rootElement = modeler.get('canvas').getRootElement()
    const branchShape = elementFactory.createShape({
      type: attr.type,
      businessObject:{
        name :  attr?.name ?? ''
      }
    });
    // branchShape.businessObject.name = attr?.name;
    return modeling.createShape(branchShape,
      {
        x: attr.x,
        y: attr.y
      },
      rootElement)
  }

  /**
   * 添加note
   */
  function addNote(shape){
    const attr = {
      type: E_Element_Type.TEXT,
      name: 'This is an annotation',
      x : shape.x + 50,
      y : shape.y - 10
    }
    createShape(attr);
    cancelSelected();
  }
  /**
   * 拷贝Task
   */
  function copyShape(shape) {

    const attr = {
      x: shape.x + 50,
      y: shape.y + shape.height + 50,
      type: shape.type,
      name: shape?.businessObject?.name
    }

    //创建节点
    const branchShape = createShape(attr);

    //复制配置项
    const config = globalShapeConfig[shape.id];
    if (config) {
      globalShapeConfig[branchShape.id] = config;
      const newShape = getShape(branchShape.id);
      modeling.updateProperties(newShape, {
        'camunda:assignee': config.users,
      });
    }
    // //取消选中
    // if(configData.value.clickShape){
    //   cancelSelected();
    // }
  }

  /**
   * 取消选中,没实现去选选中的功能
   */
  function cancelSelected() {
    const eventBus = modeler.get('eventBus')
    // eventBus.fire('element.click', configData.value.currentEvent);
    // modeler.get('elementRegistry').find(el => el.type === 'bpmn:Process')
    eventBus.fire('canvas.resized');
  }

  /**
   * 删除
   */
  function deleteShape(element) {
    console.log('delete shape element',element)
    if(!element) return;
    //删除shape配置信息
    if (globalShapeConfig && globalShapeConfig[element.id]) {
      console.log('delete config ！！！！！')
      Reflect.deleteProperty(globalShapeConfig, element.id);
    }
    //画布上删除shape
    removeElements(element);
  }

  /**
   * 打开配置页面
   */
  function openConfigDialog(){
    console.log('openConfigDialog====', configData.value.isCloseCondition)
    configData.value.isCloseCondition = false;
  }
  /**
   * 菜单事件
   */
  const menuItemEvent = {
    [E_Shape_Menu.CONFIG]: openConfigDialog,
    [E_Shape_Menu.ADD_TASK]: addShape,
    [E_Shape_Menu.ADD_GATEWAY]: addShape,
    [E_Shape_Menu.ADD_END]: addShape,
    [E_Shape_Menu.ADD_NOTE]:addNote,
    [E_Shape_Menu.COPY]: copyShape,
    [E_Shape_Menu.DELETE]: deleteShape,
    [E_Shape_Menu.EDIT_NAME]: deleteShape,
  }


  return {addShape,handleElement,removeElements,getShape,menuItemEvent}
}
