import {E_Element_Type, E_IF_Type} from "/@/views/ticket/enums/flowEnum";
import {IGatewayItem, ILineItem} from "/@/views/ticket/ts/flow";


export const useCondition = (modeler, configData) => {
  const {clickShape, shapeConfig} = configData;
  const elementRegistry = modeler.get("elementRegistry");


  const getBeforeTask = () => {
    const incoming = elementRegistry.find((item) => item.id === clickShape.id)?.incoming;
    if (incoming.length == 0) {
      return [];
    }
    return incoming.map((item) => {
      if (item.source.type == E_Element_Type.TASK || item.source.type == E_Element_Type.START)
        return item.source.id;
    })
  }

  const getTabTask = () => {
    const outgoing = elementRegistry.find((item) => item.id === clickShape.id)?.outgoing;
    if (outgoing.length == 0) {
      return [];
    }
    return outgoing.map((item, index) => {
      if (item.target.type == E_Element_Type.TASK || item.target.type == E_Element_Type.END)
        return {
          lineId: item.id,
          linkId: item.target.id,
          ifType: index == 0 ? E_IF_Type.IF : E_IF_Type.ELSEIF,
          name: getShapeName( item.target.id),
          conditionGroup: []
        } as ILineItem;
    });
  }

  /**
   * 获取显示名称
   * @param activityId
   */
  const getShapeName = (activityId:string)=>{
    const shape =  elementRegistry.get(activityId);
    console.log(shape)
    if(!shape.businessObject.name){
      return shape.type == E_Element_Type.END ? 'End' : 'Task';
    }
    return shape.businessObject.name;
  }


  const initCondition = () => {
    const lines = getTabTask();
    const newLines = [] as ILineItem[];
    const beforeShape = getBeforeTask();
    //防止连线删除
    if (shapeConfig[clickShape.id]) {
      const configLines = {};
      shapeConfig[clickShape.id].line.forEach(item=>{
        configLines[item.lineId] = item;
      })
      lines.forEach(line=>{
        newLines.push(configLines[line.lineId] ?? line) ;
      })
      shapeConfig[clickShape.id].line = newLines;
      shapeConfig[clickShape.id].beforeShape = beforeShape;
    }
    return   {
      activityId: clickShape.id,
      name: clickShape.name,//显示名称
      beforeShape: beforeShape,
      line: newLines.length > 0 ? newLines : lines,
      errorInfo:''
    } as IGatewayItem;
  }




  return {initCondition}
}
