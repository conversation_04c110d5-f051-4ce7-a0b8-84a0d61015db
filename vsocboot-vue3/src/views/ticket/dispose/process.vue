<template>
  <!-- 表单项  start-->
  <TicketForm v-model:value="ticketForm" ref="ticketFormRef" :readOnly="readOnly"/>
  <!-- 表单项  end-->

  <!-- 下一步  start-->
  <NextComponent v-if="isSave"/>
  <!-- 下一步  end-->
</template>
<script setup lang="ts">
import {inject, provide, ref, watch, watchEffect} from "vue";
import {TicketForm} from "/@/views/ticket/component/TicketForm/index";
import {useDisposeContent} from "/@/views/ticket/dispose/useDisposeHook";
import {NextComponent} from "/@/views/ticket/dispose/component/index";
import {executionTask, saveTask} from "/@/views/ticket/api/TicketViewTenant.api";
import {E_Ticket_Status_Type, E_Ticket_View_Type} from "/@/views/ticket/enums/ticketEnum";
import {useMessage} from "/@/hooks/web/useMessage";
import {useI18n} from "/@/hooks/web/useI18n";
const { createMessage } = useMessage();
const {init,ticketForm,showNext,flow,getSubmitData,changeArrayValue} = useDisposeContent()
provide('flow',flow)
const ticketFormRef = ref();
const ticketInfo = inject('ticketInfo',ref())
const isSave = inject('isSave',ref())
const menuActive = inject('menuActive',ref())
const readOnly = ref(false);
const { t } = useI18n();
watch(()=>menuActive.value,(n)=>{
  readOnly.value = n != E_Ticket_View_Type.PENDING;
},{immediate:true})

watch(()=>ticketInfo.value,(n)=>{
  if(n){
    init(n,false);
  }
},{immediate:true,deep:true})

watch(()=>isSave.value,(n)=>{
  if(n === true){
    readOnly.value = true;
    showNext();
  }
})
const doSave = async () => {
  const validate = await ticketFormRef.value.validate();
  ticketForm.formData['statusType'] = validate === false ? E_Ticket_Status_Type.ERROR : 0;
  const data = await saveTask({
    vals: changeArrayValue(ticketForm.formData),
    processInstanceId: flow.processInstanceId,
  });
  return {data,validate}
}

const doSubmit = async () => {
  const validate = await ticketFormRef.value.validate();
  if (validate === false) {
    return false;
  }
  const data = getSubmitData();
  //存在下一步,但是未选人
  if(data.nextId && data.nextId.indexOf("Activity")!=-1 && JSON.parse(data.nextUsers).length == 0){
    createMessage.warning(t('routes.workflow.checkNextUser'));
    return false;
  }
  return executionTask(getSubmitData());
}
defineExpose({
  doSubmit,doSave
})
</script>
<style scoped lang="less">

</style>
