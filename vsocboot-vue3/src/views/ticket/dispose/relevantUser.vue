<template>
  <div class="relevant_user">
    <!-- 需要审批人 start-->
    <div class="user_block_bg">
      <div class="block_header" :class="{ block_header_border: isOpen }">
        <div class="font14 font_bg"> {{ applyUser.length }} {{ t('workflow.workflow.applyTip') }} </div>
        <template v-if="applyUser.length > 0">
          <Icon @click="isOpen = !isOpen" icon="ant-design:down-outlined" class="cursor fcolor1" v-if="isOpen" />
          <Icon @click="isOpen = !isOpen" icon="ant-design:right-outlined" class="cursor fcolor1" v-else />
        </template>
      </div>

      <div class="block_content" v-if="isOpen">
        <template :key="item.id" v-for="(item, index) in applyUser">
          <div class="dispose-row_user">
            <div class="user_head">
              <!--  头像 start-->
              <img :src="render.renderUploadImageSrc(item?.avatar)" v-if="item?.avatar" alt="" />
              <div class="img_title font20 fcolor" v-else>
                {{ item?.username?.substring(0, 1).toUpperCase() }}
              </div>
              <!--  头像 end-->
            </div>
            <div class="user_content">
              <div class="fontb13 fcolor">{{ item.username }}</div>
              <div class="user-row font12 fcolor4">
                <div>{{ item.email }}</div>
                <div>{{ item.createTime }}</div>
              </div>
              <div class="font12 fcolor1 user_desc">{{ item.applyContent }}</div>
              <div class="user_btn_group">
                <div class="user_btn font12 fcolor1" @click="handleUser(item.id, 2)">
                  <Icon icon="ant-design:close-outlined" :size="14" />
                  <div>{{ t('workflow.workflow.refuse') }}</div>
                </div>
                <div class="user_btn font12 fcolor1" @click="handleUser(item.id, 1)">
                  <Icon icon="ant-design:check-outlined" :size="14" />
                  <div>{{ t('workflow.workflow.agree') }}</div>
                </div>
              </div>
            </div>
          </div>
          <div class="border-bottom" v-if="index != applyUser.length - 1"></div>
        </template>
      </div>
    </div>
    <div class="border-bottom"></div>
    <!-- 需要审批人 end-->

    <!-- 当前处置人 start-->
    <div class="user_block">
      <div class="font14 fcolor">{{ t('workflow.workflow.currentProcessor') }}</div>
      <div class="dispose-row_user">
        <UserIcon v-for="(item, index) in currentUser" :key="index" :record="item" />
      </div>
    </div>
    <!-- 当前处置人  end-->

    <div class="border-bottom"></div>

    <!-- 有查看权限的人 start-->
    <div class="user_block">
      <div class="font14 fcolor">{{ t('workflow.workflow.ProcessorUser') }}</div>
      <div class="dispose-row_user">
        <UserIcon v-for="(item, index) in permissionUser" :key="index" :record="item" />
        <AddBtn :modal-type="E_Modal_Type.USER" v-model:value="relevantUser" />
      </div>
    </div>
    <!-- 有查看权限的人  end-->
  </div>
</template>
<script setup lang="ts">
  import { inject, nextTick, ref, unref, watch } from 'vue';
  import UserIcon from '/@/views/ticket/edit/component/flow/permission/node/components/userIcon.vue';
  import { E_Modal_Type } from '/@/views/ticket/enums/flowEnum';
  import { AddBtn } from '/@/views/ticket/component/AddBtn/index';
  import { queryRelevantUserList, addRelevantUser, updateRelevantUser } from '/@/views/ticket/api/TicketViewTenant.api';
  import { useI18n } from '/@/hooks/web/useI18n';
  import { render } from '/@/utils/common/renderUtils';
  const emit = defineEmits(['refresh']);
  const { t } = useI18n();
  //新增人
  const relevantUser = ref([]);
  const ticketInfo: any = unref(inject('ticketInfo'));
  console.log('ticketInfo', ticketInfo);
  //当前步骤操作人
  const currentUser = ref();
  if (ticketInfo.nextUsers) {
    currentUser.value = JSON.parse(ticketInfo.nextUsers);
  }

  //申请人
  const applyUser = ref([]);
  //有权限人
  const permissionUser = ref([]);
  //是否展开
  const isOpen = ref(false);
  /**
   * 新增只读人
   */
  watch(
    () => relevantUser.value,
    (n) => {
      console.log('relevantUser=', n);
      for (let item of n) {
        const findData = permissionUser.value.filter((user) => user.userId == item.id);
        if (findData.length == 0) {
          const data = {
            userId: item.id,
            userType: 2,
            userStatus: 1,
            processInstanceId: ticketInfo.processInstanceId,
          };
          addRelevantUser(data).then(() => {
            getPermissionUser();
          });
        }
      }
    },
    { deep: true }
  );

  /**
   * 申请权限用户
   */
  function getApplyUser() {
    queryRelevantUserList({
      userType: 2,
      userStatus: 0,
      processInstanceId: ticketInfo.processInstanceId,
    }).then((result) => {
      applyUser.value = result;
      if (result.length == 0) {
        isOpen.value = false;
      }
    });
  }

  /**
   * 有权限用户
   */
  function getPermissionUser() {
    queryRelevantUserList({
      userType: 2,
      userStatus: 1,
      processInstanceId: ticketInfo.processInstanceId,
    }).then((result) => {
      permissionUser.value = result;
    });
  }
  //申请人
  getApplyUser();
  //有权限人
  getPermissionUser();

  /**
   * 审批
   * @param id
   * @param userStatus
   */
  function handleUser(id, userStatus) {
    updateRelevantUser({ id, userStatus }).then(() => {
      getApplyUser();
      getPermissionUser();
      emit('refresh', 'relevantUser');
    });
  }
</script>
<style scoped lang="less">
  .user_block {
    display: flex;
    flex-direction: column;
    gap: 4px;
  }

  .border-bottom {
    border-bottom: 1px solid @border-color;
  }

  .dispose-row_user {
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    gap: 8px;
  }

  .relevant_user {
    display: flex;
    flex-direction: column;
    gap: 16px;
  }

  .user_block_bg {
    background: @menu-active-bg;
    border-radius: 8px;

    .font_bg {
      background: @font-active-color;
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
      text-fill-color: transparent;
    }

    .block_header {
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      padding: 8px 16px;
      align-items: center;

      &.block_header_border {
        border-bottom: 1px solid @border-color;
      }
    }

    .block_content {
      display: flex;
      flex-direction: column;
      gap: 16px;
      padding: 16px;

      .user_head {
        width: 40px;
        height: 40px;
        border-radius: 20px;
        opacity: 1;

        img {
          width: 100%;
          height: 100%;
          border-radius: 50%;
        }

        .img_title {
          background: #308cff;
          width: 100%;
          height: 100%;
          border-radius: 50%;
          display: flex;
          flex-direction: row;
          align-items: center;
          justify-content: center;
        }
      }

      .dispose-row_user {
        position: relative;

        .user_btn_group {
          display: flex;
          flex-direction: row;
          align-items: center;
          gap: 8px;
          position: absolute;
          right: 0;
          top: 0;

          .user_btn {
            cursor: pointer;
            border: 1px solid @border-color;
            height: 24px;
            border-radius: 4px;
            display: flex;
            flex-direction: row;
            justify-content: center;
            align-items: center;
            padding: 4px 8px 4px 6px;
            gap: 4px;
          }
        }
      }

      .user_content {
        display: flex;
        flex-direction: column;
        gap: 4px;
      }

      .user-row {
        display: flex;
        flex-direction: row;
        gap: 16px;
      }

      .user_desc {
        margin-top: 4px;
      }
    }
  }
</style>
