<template>
  <Drawer v-model:visible="visible" @close="close" :width="640" :getContainer="getContainer">
    <template #extra>
      <!--关闭按钮 start-->
      <a-button @click="close">{{ t('common.cancelText') }}</a-button>
      <!--关闭按钮 end-->

      <!--保存按钮 start-->
      <!--      <a-button type="primary" v-if="!isSave" @click="saveTicket">{{ t('common.saveText') }}
      </a-button>-->
      <!--保存按钮 end-->

      <!--提交按钮 start-->
      <a-button type="primary" :disabled="isSubmit" @click="onSubmit">
        {{ t('common.Submit') }}
      </a-button>
      <!--提交按钮 end-->
    </template>
    <template #content>
      <div class="dispose-container">
        <!-- 表单项  start-->
        <TicketForm v-model:value="ticketForm" ref="ticketFormRef" :read-only="isSave" />
        <!-- 表单项  end-->

        <!-- 下一步  start-->
        <NextComponent :isApply="true" />
        <!-- 下一步  end-->
      </div>
    </template>
  </Drawer>
</template>
<script setup lang="ts">
  import { NextComponent } from '/@/views/ticket/dispose/component/index';
  import { useI18n } from '/@/hooks/web/useI18n';
  import { nextTick, provide, ref } from 'vue';
  import { apply, queryById } from '/@/views/ticket/api/TicketViewTenant.api';
  import { ISetting } from '/@/views/ticket/ts/common';
  import { TicketForm } from '/@/views/ticket/component/TicketForm/index';
  import { useDisposeContent } from '/@/views/ticket/dispose/useDisposeHook';
  import { Drawer } from '/@/views/ticket/component/Drawer/index';
  import { handleData } from '/@/views/aggregationRiskEventView/RiskEventView.data';
  import { handleData as mlHandleData } from '/@/views/mlView/MlEvent.data';
  import { handleData as badHandleData } from '/@/views/badactors/BadActors.data';
  import { handleData as uebaHandleData } from '/@/views/anomalyrecords/AnomalyRecords.data';
  import { useMessage } from '/@/hooks/web/useMessage';
  import {httpWafHandleData} from "/@/views/httpWafEvent/HttpWafEvent.data";

  const { createMessage } = useMessage();
  const emit = defineEmits(['ok']);
  const { t } = useI18n();
  const title = ref('');
  const isSave = ref(true);
  const isSubmit = ref(false);
  provide('isSave', isSave);
  //显示/隐藏
  const visible = ref(false);
  provide('visible', visible);

  function getContainer() {
    return document.body;
  }

  //表单
  const ticketFormRef = ref();
  const { init, ticketForm, nextHandle, flow, getSubmitData } = useDisposeContent();
  provide('flow', flow);
  /**
   * 申请
   */
  const open = (id: string, ...param: any) => {
    reSave();
    queryById({ id: id }, async (data: ISetting) => {
      title.value = data.ticketName;
      nextTick(() => {
        init(data, true);
        flow.processInstanceId = data.processId;
        flow.settingId = id;
        //事件数据
        if (param[0] && param[1]) {
          initEventData(param[0], param[1]);
        }
      });
      //要放到最后
      visible.value = true;
    });
  };

  /**
   * 事件新建工单
   * @param eventType 事件类型
   * @param eventData 事件数据
   */
  function initEventData(eventData: any, eventType: String) {
    //找到工单事件组件，有可能有多个，只要第一个
    let fieldId = '';
    console.log(JSON.parse(JSON.stringify(ticketForm)));
    if (ticketForm.design) {
      for (let i = 0; i < ticketForm.design.length; i++) {
        if (ticketForm.design[i].component == 'riskCenter') {
          fieldId = ticketForm.design[i].field;
          break;
        }
      }
    }
    //需要给riskEvent组件赋值，找到
    if (fieldId) {
      if (eventType == '1') {
        ticketForm.formData[fieldId] = eventData.map((item) => {
          return handleData(item);
        });
      } else if (eventType == '3') {
        ticketForm.formData[fieldId] = eventData.map((item) => {
          return mlHandleData(item);
        });
      } else if (eventType == '4') {
        ticketForm.formData[fieldId] = eventData.map((item) => {
          return badHandleData(item);
        });
      } else if (eventType == '5') {
        ticketForm.formData[fieldId] = eventData.map((item) => {
          return uebaHandleData(item);
        });
      } else if (eventType == '6') {
        ticketForm.formData[fieldId] = eventData.map((item) => {
          return httpWafHandleData(item);
        });
      }
    }
    console.log(JSON.parse(JSON.stringify(ticketForm)));
  }

  const saveTicket = async () => {
    const values = await ticketFormRef.value.validate();
    if (values === false) {
      reSave();
      return;
    }
    isSave.value = true;
    //显示下一步
    await nextHandle(flow.shapeConfig[flow.nextId], ticketForm.formData?.socTenantId);
  };

  /**
   * 重新保存
   */
  function reSave() {
    isSave.value = false;
    isSubmit.value = false;
  }
  /**
   * submit
   */
  async function onSubmit() {
    isSubmit.value = true;
    await nextHandle(flow.shapeConfig[flow.nextId], ticketForm.formData?.socTenantId);
    const values = await ticketFormRef.value.validate();
    if (values === false) {
      reSave();
      return;
    }
    const data = getSubmitData();
    console.log('apply data is ', data);
    //存在下一步,但是未选人
    // if(data.nextId && data.nextId.indexOf("Activity")!=-1 && JSON.parse(data.nextUsers).length == 0){
    //   createMessage.warning(t('routes.workflow.checkNextUser'));
    //   reSave();
    //   return false;
    // }
    apply(data, () => {
      visible.value = false;
      isSubmit.value = false;
      emit('ok');
      //刷新待办 和 抄送未读
    }).catch((errorInfo) => {
      reSave();
      console.log('Failed:', errorInfo);
    });
  }

  /**
   * 关闭
   */
  const close = () => {
    visible.value = false;
  };
  defineExpose({
    open,
  });
</script>
<style scoped lang="less">
  .dispose-container {
    display: flex;
    flex-direction: column;
    padding: 16px;
  }
</style>
