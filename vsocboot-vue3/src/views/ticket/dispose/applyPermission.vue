<template>
  <Drawer v-model:visible="visible" :title="title" @close="close" :width="600"
          :getContainer="getContainer">
    <template #extra>
      <!--关闭按钮 start-->
      <a-button @click="close">{{ t('common.cancelText') }}</a-button>
      <!--关闭按钮 end-->
      <!--提交按钮 start-->
      <a-button type="primary" :disabled="isSubmit" @click="onSubmit">
        {{ t('common.Submit') }}
      </a-button>
      <!--提交按钮 end-->
    </template>
    <template #content>

      <div class="dispose-container">

        <!--  申请人 start-->
        <div class="user_block">
          <div class="font14 fcolor">{{ t('routes.workflow.applicant') }}</div>
          <div class="dispose-row_user">
            <UserIcon
              v-for="(item,index) in applyUser"
              :key="index"
              :record="item"
            />
          </div>
        </div>
        <!--  申请人 end-->
        <div class="border-bottom"></div>
        <!-- 当前处置人 start-->
        <div class="user_block">
          <div class="font14 fcolor">{{ t('workflow.workflow.currentProcessor') }}</div>
          <div class="dispose-row_user">
            <UserIcon
              v-for="(item,index) in currentUser"
              :key="index"
              :record="item"
            />
          </div>
        </div>
        <!-- 当前处置人  end-->

        <div class="border-bottom"></div>

        <div class="user_block">
          <div class="font13 fcolor1  ">{{ t('routes.workflow.noPermission') }}</div>
          <a-textarea v-model:value="formData.applyContent" :rows="3" maxlength="1000"></a-textarea>
        </div>
      </div>
    </template>
  </Drawer>


</template>
<script setup lang="ts">
import {useI18n} from "/@/hooks/web/useI18n";
import {reactive, ref} from "vue";
import {addRelevantUserApply} from "/@/views/ticket/api/TicketViewTenant.api";
import {Drawer} from "/@/views/ticket/component/Drawer/index";
import UserIcon from "/@/views/ticket/edit/component/flow/permission/node/components/userIcon.vue";
import {useUserStore} from "/@/store/modules/user";
import {defHttp} from "/@/utils/http/axios";

const userStore = useUserStore();

function getContainer() {
  return document.body;
}

const formData = reactive({
  applyContent: '',
  userType: 2,//申请
  userStatus: 0,//待审核
  processInstanceId: '',
  username: userStore.getUserInfo.username
})
const emit = defineEmits(['ok']);
const {t} = useI18n();
const title = ref('');
const currentUser = ref();
const applyUser = ref([]);


//显示/隐藏
const visible = ref(false);
const isSubmit = ref(false);


/**
 * 申请
 */
const open = (data: any) => {
  console.log('apply permission',data)
  title.value = data.ticketName;
  formData.processInstanceId = data.processInstanceId;
  formData.applyContent = '';
  if(data.nextUsers){
    currentUser.value = JSON.parse(data.nextUsers);
  }
  getApplyUser(data.applyUser);
  //要放到最后
  visible.value = true;
}

function getApplyUser(user) {
 defHttp.get({url: '/sys/user/queryUserListByIds',params:{ids:user}}).then((result)=>{
   applyUser.value = result;
 });
}

/**
 * submit
 */
async function onSubmit() {
  isSubmit.value = true;
  addRelevantUserApply(formData).then(() => {
    visible.value = false;
    isSubmit.value = false;
  })


}

/**
 * 关闭
 */
const close = () => {
  visible.value = false;

};
defineExpose({
  open,

})
</script>
<style scoped lang="less">
.dispose-container {
  display: flex;
  flex-direction: column;
  padding: 16px;
  gap: 16px;
}

.user_block {
  display: flex;
  flex-direction: column;
  gap: 4px;

  .dispose-row_user {
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    gap: 8px;
  }

}

.permissionTip {
  overflow-wrap: break-word;
  word-break: break-all;
  display: block;
  width: 500px;
  word-wrap: break-word;
}

.border-bottom {
  border-bottom: 1px solid @border-color;
}
</style>
