<template>
  <BasicModal v-bind="$attrs" @register="registerModal" :title="t('common.chooseUser')" @ok="handleSubmit" width="900px">
    <BasicTable  ref="tableRef" :searchInfo="searchInfo" @register="registerTable" :row-selection="rowSelection"  @row-click="handleSelectChange" >
      <template #userInfo="{ text, record }">
        <UserName :record="record"></UserName>
      </template>
    </BasicTable>
  </BasicModal>
</template>

<script lang="ts" setup>
import {computed, nextTick, reactive, ref, toRaw, unref, watch, watchEffect} from 'vue';
import {BasicModal, useModalInner} from '/@/components/Modal';
import {useI18n} from '/@/hooks/web/useI18n';
import {BasicColumn, BasicTable} from "/@/components/Table";
import {useListPage} from "/@/hooks/system/useListPage";
import { getUserList } from '/@/api/common/api';
import UserName from "/@/components/vsoc/UserName.vue";
import {useSelectBiz} from "/@/components/Form/src/jeecg/hooks/useSelectBiz";
import {defHttp} from "/@/utils/http/axios";

const { t } = useI18n();
// Emits声明
const emit = defineEmits(['getSelectResult']);
const props = defineProps({
  customType: String,
  params:Object,
  rowKey:String
});
const searchInfo = ref(props.params);

const tableRef = ref();
const columns: BasicColumn[] = [
  {
    title: t('routes.sysUser.username'),
    dataIndex: 'username',
    align: 'left',
    slots: {customRender: 'userInfo'},
  },
  {
    title: t('routes.sysUser.phone'),
    dataIndex: 'phone',
  },
  {
    title: t('routes.sysUser.email'),
    dataIndex: 'email',
    // width: 40,
  },
];
const formConfig = {
  schemas: [
    {
      label: '',
      field: 'username',
      component: 'JInput',
      componentProps: {
        search: true,
        placeholder: t('routes.sysUser.username')
      },
    },
  ],
};

//注册table数据
const { tableContext } = useListPage({
  tableProps: {
    api: getUserList,
    columns: columns,
    rowKey: props.rowKey,
    formConfig:formConfig,
    canResize: false,
    useSearchForm: true,
    showTableSetting: false,
    showActionColumn: false,
  },
});

const [registerTable,{},{selectedRowKeys,rowSelection}] = tableContext;



//表单赋值
const [registerModal, { setModalProps, closeModal }] = useModalInner(async (data) => {
  nextTick(() => {
    //刷新参数
    tableRef.value.reload({ searchInfo: props.params });
    setModalProps({ confirmLoading: false, showCancelBtn: data?.showFooter, showOkBtn: data?.showFooter });
    if(data.selectUser){
      selectedRowKeys.value = data.selectUser?.map(item=>item[props.rowKey]);
    }
  });
});
function handleSelectChange(data){
  console.log('props.rowKey',data)
  const username = data[props.rowKey];
  if(selectedRowKeys.value.includes(username)){
    selectedRowKeys.value = selectedRowKeys.value.filter(item=>item!==username);
  }else{
    selectedRowKeys.value.push(username);
  }
}

//表单提交事件
async function handleSubmit() {
  closeModal();
  if (selectedRowKeys.value.length == 0) {
    emit('getSelectResult', [], props.customType)
  } else {
    const result = await defHttp.get({
      url: '/sys/user/queryUserListByIds',
      params: {ids: toRaw(selectedRowKeys.value).toString()}
    });

    emit('getSelectResult', result, props.customType)
  }

}
</script>

<style lang="less" scoped>
/deep/.searchForm{
  padding: 0 16px;
  input,.ant-form-item-control-input{
    width: 200px!important;
  }
  label,.ant-form-item-label{
    display: none!important;
  }
}
</style>
