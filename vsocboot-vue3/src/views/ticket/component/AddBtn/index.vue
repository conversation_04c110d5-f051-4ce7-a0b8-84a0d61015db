<template>
  <div class="addBtn font16 fcolor3 cursor" @click="openModal">
    <img :src="AddBtn" alt="+"/>
  </div>
  <Component
    rowKey="id"
    :is="componentsTab[modalType]"
    @register="registerModal"
    @getSelectResult="onSelectOk"
    v-model:params="params"
    :dataSource="dataSource"
  />

</template>
<script setup lang="ts">
import AddBtn from '/@/assets/images/ticket/addBtn.png'
import {RoleSelectModal,UserSelectModal,UserCheckModal} from "/@/views/ticket/component/User/index";
import TenantSelectModal from "/@/components/Form/src/jeecg/components/modal/TenantSelectModal.vue";
import {ref, unref} from "vue";
import {useModal} from "/@/components/Modal";
import {E_Modal_Type} from "/@/views/ticket/enums/flowEnum";
import {isAdministrator, isTenant} from "/@/utils/auth";

const emits = defineEmits(["update:value"])
const componentsTab = ref({
  [E_Modal_Type.USER]:UserSelectModal,
  [E_Modal_Type.PRESET_USER]:UserCheckModal,
  [E_Modal_Type.ROLE]:RoleSelectModal,
  [E_Modal_Type.TENANT]:TenantSelectModal,
})
const [registerModal, modal] = useModal();
const props = defineProps({
  modalType:{
    type:Number,
    default:E_Modal_Type.USER
  },
  value:{
    type:Array,
    default:[]
  },
  dataSource:{
    type:Array,
    default:[]
  },

});
const params = ref({
  tenantType:null
})
if(E_Modal_Type.USER == props.modalType){
  params.value.tenantType = isTenant() ? 2 : isAdministrator() ? 1 : null;
}
/**
 * 选择用户成功
 * @param options
 * @param list
 */
async function onSelectOk(options, list) {
    let data = JSON.parse(JSON.stringify(unref(options)))
    if(E_Modal_Type.USER == props.modalType || E_Modal_Type.PRESET_USER == props.modalType){
      data = data.map(item=>{
        const {username,email,avatar,id} = item;
        return {username,email,avatar,id}
      })
    }

  emits('update:value',data)
}



/**
 * 选择用户
 */
function openModal() {
  modal.openModal();
}
</script>
<style scoped lang="less">
.addBtn{
  padding: 18px 4px;
  display: flex;
  flex-direction: row;
  align-items: center;
  border-radius: 4px;
  border: 1px solid @border-color-01;
}
</style>
