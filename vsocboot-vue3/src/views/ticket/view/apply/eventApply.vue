<template>
  <a-menu-item v-for="(obj, index) in workflowList" :key="index" @click="applyTicket(obj)">
    {{ obj?.ticketName }}
  </a-menu-item>

  <ApplyTicket ref="applyRef" @ok="emits('applyOk')" />
</template>

<script setup lang="ts">
  import { ref } from 'vue';
  import { queryTicketList } from '/@/views/ticket/api/TicketViewTenant.api';
  import { ApplyTicket } from '/@/views/ticket/dispose/index';
  import { isApply } from '/@/views/ticket/ts/data';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { useI18n } from '/@/hooks/web/useI18n';
  import { getTenantMode, isAdministrator, isTenant } from '/@/utils/auth';
  import { E_Ticket_Type } from '/@/views/ticket/enums/ticketEnum';

  const { createMessage } = useMessage();
  const emits = defineEmits(['applyOk']);
  const { t } = useI18n();
  const props = defineProps({
    record: Object as PropType<{
      type: '1' | '2' | '3' | '4' | '5' | '6' | '';
      eventId: string;
    }>,
    eventType: String,
    records: Array,
  });
  const applyRef = ref();
  const workflowList = ref<any[]>([]);
  //申请工单租户
  let queryTenant = null;
  //申请工单类型
  let ticketTenantType = E_Ticket_Type.TENANT + '';

  function getTicketList() {
    if (isAdministrator()) {
      ticketTenantType = E_Ticket_Type.MSSP + ',' + E_Ticket_Type.NOTICE;
    } else if (isTenant()) {
      if (props.records && props.records.length > 0) {
        queryTenant = props.records[0]?.socTenantId;
      } else if (props.record) {
        queryTenant = props.record?.socTenantId;
      }
      ticketTenantType = E_Ticket_Type.TENANT + ',' + E_Ticket_Type.SERVICE;
    }
    queryTicketList({ status: 1, tenants: queryTenant, ticketTenantType: ticketTenantType }, getWorkflowList);
  }
  getTicketList();

  async function getWorkflowList(result) {
    workflowList.value = [];
    for (const data of result) {
      const flag = await isApply(data);
      if (flag) {
        workflowList.value.push(data);
      }
    }
  }

  function applyTicket(data) {
    if (props.record) {
      applyRef.value.open(data.id, [props.record], props.eventType);
    } else if (props.records) {
      //判断多选事件的租户是否有权限
      let isValidate = true;

      for (const r of props.records) {
        //租户事件对应租户工单,租户工单只能选择租户事件
        if ((data.entryTenant && !r?.socTenantId) || (data.entryTenant && r.socTenantId && -1 == data.entryTenant.indexOf(r?.socTenantId))) {
          isValidate = false;
          createMessage.info(t('routes.workflow.mismatch'));
          break;
        }
      }

      isValidate && applyRef.value.open(data.id, props.records, props.eventType);
    }
  }
</script>

<style scoped lang="less"></style>
