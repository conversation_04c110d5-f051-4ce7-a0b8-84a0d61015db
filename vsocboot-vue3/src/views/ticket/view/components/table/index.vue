<template>
  <a-table :pagination="{size:'small'}"
    width="100%" :data-source="dataSource" :columns="columns" id="ticketTableWrapper" :showHeader="false">
    <template #fieldValue="{ record }">
      <TicketItem   :key="record.id" :value="record" @refresh="emit('refresh')"/>
    </template>
  </a-table>
</template>
<script setup lang="ts">
import {TicketItem} from "/@/views/ticket/view/components/ticketItem/index";

const emit = defineEmits(['refresh']);
defineProps({
  dataSource: Array,
});
const columns = [{
  title: '',
  dataIndex: 'fieldValue',
  slots: { customRender: 'fieldValue' },
}]

</script>

<style scoped lang="less">

</style>
