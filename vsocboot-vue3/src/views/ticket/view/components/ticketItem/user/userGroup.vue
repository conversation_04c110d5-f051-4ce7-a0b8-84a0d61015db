<template>

  <div class="user-group_content" ref="userRef">
    <div class="user-list-row">
      <UserItem v-for="(item,index) in displayData" v-model:value="displayData[index]"
                :is-wait="waitList && waitList.includes(item.id)"/>

    </div>
    <!--    后续做轮播-->
    <div class="operate-row" v-if="total > pageData.pageSize" @click.stop="">
      <div class="ax-icon-button ax-icon-small"  @click="showBefore" :class="{'is-disabled' : pageData.pageNum == 1}" >
        <div class="soc ax-com-Arrow-left"></div>
      </div>
      <div class="ax-icon-button ax-icon-small"  @click="showNext" :class="{'is-disabled' : pageData.pageNum == pageData.totalNum}" >
        <div class="soc ax-com-Arrow-right"></div>
      </div>
    </div>
  </div>


</template>
<script setup lang="ts">
import {nextTick, onMounted, onUnmounted, reactive, ref, watchEffect} from "vue";
import {UserItem} from "./index";

const props = defineProps({
  value: String,
  width: Number,
  waitList: Object
});
let observer;
let resizeTimer;
const userWidth = ref();
const userRef = ref();
const displayData = ref([])
console.log('user group props.value',props.value)
const userList = ref(props.value ? JSON.parse(props.value) : []);
const waitList = ref(props.waitList);
const total = userList.value?.length;
onMounted(() => {
  createObserver();
})
onUnmounted(() => {
  if (observer) {
    observer.disconnect();
  }
})

function createObserver() {
  observer = new ResizeObserver((entries) => {
    clearTimeout(resizeTimer);
    resizeTimer = setTimeout(() => {
      for (const entry of entries) {
        const {width} = entry.contentRect;
        userWidth.value = width;
        if(userList.value && userList.value.length > 0){
          userShow();
        }
      }
    }, 200);
  });

  observer.observe(userRef.value);
}

const defaultData = {
  pageNum:1,
  pageSize:5,
  totalNum:1,

};
let pageData = reactive(defaultData);
watchEffect(() => {
  waitList.value = props.waitList;
  pageData = JSON.parse(JSON.stringify(defaultData))
})

function userShow() {
  displayData.value = [];
  pageData.pageNum = 1;
  //每页显示数量
  pageData.pageSize = Math.floor((userWidth.value - 32 ) / 60 );
  //总页数
  let totalNum = userList.value.length / pageData.pageSize;
  if( userList.value.length % pageData.pageSize > 0){
    totalNum = parseInt(totalNum) + 1;
  }
  pageData.totalNum = totalNum;
  getPageContent()
}

function showNext(){
  pageData.pageNum = pageData.pageNum + 1;
  getPageContent();
}

function showBefore(){
  pageData.pageNum = pageData.pageNum - 1;
  getPageContent();
}

function getPageContent(){
  //起始
  const start = (pageData.pageNum - 1) * pageData.pageSize;
  //终止
  let end = start +  pageData.pageSize;
  if(total < end ){
    end = total;
  }
  displayData.value = userList.value.slice(start, end)

}
</script>


<style scoped lang="less">

.user-group_content {
  top: 0;
  width: 100%;
  height: 100%;
  position: relative;

  .user-list-row {
    padding: 0 16px;
    display: flex;
    flex-direction: row;
    align-items: center;
    gap: 24px;
    height: 100%;
    flex: 1;
  }

  .operate-row {
    width: 32px;
    //border-left: 1px solid @border-color;
    display: flex;
    flex-direction: column;
    justify-content: center;
    padding: 12px 4px;
    gap: 12px;
    position: absolute;
    top: 0;
    right: 0;

  }
}

</style>
