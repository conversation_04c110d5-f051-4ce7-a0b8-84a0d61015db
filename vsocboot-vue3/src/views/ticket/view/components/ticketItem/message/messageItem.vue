<template>
  <div class="icon-wrapper">
    <div class="icon-img">
      <img :src="message?.icon" :alt="message?.name"/>
      <div class="number font12 fcolor" v-if="message?.num > 0">{{message?.num}}</div>
    </div>
    <div class="font13 fcolor3">{{message?.name}}</div>

  </div>
</template>
<script setup lang="ts">
import {ref, watch} from "vue";

const message = ref();

const props = defineProps({
  value: Object,
});
watch(()=>props.value,(n)=>{
  message.value = n;
},{immediate:true,deep:true})
</script>
<style scoped lang="less">
.icon-wrapper{
  display: flex;
  flex-direction: column;
  gap: 4px;
  cursor: pointer;
  align-items: center;

  .icon-img{
    width: 24px;
    height: 24px;
    display: flex;
    flex-direction: row;
    align-items: center;
    position: relative;
    .number{
      position: absolute;
      background: @color-red;
      width: 20px;
      height: 20px;
      border-radius: 50%;
      right: -10px;
      top: -10px;
      font-weight: bold;
      text-align: center;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }

}
</style>
