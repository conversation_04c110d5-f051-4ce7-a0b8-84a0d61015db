<template>
  <div class="event_table_div">
    <a-table :columns="columns(isHandle)" :loading="loading" :data-source="tableData" :pagination="{ hideOnSinglePage: true }">
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'eventLevel'">
          <Severity :value="record.eventLevel" :type="record.riskType == 1 ? record.ruleType == 4 ? 'log' : 'number' : ''" />
        </template>
        <template v-else-if="column.dataIndex === 'action' && isHandle">
          <a-space :size="5" class="action-border"  style="padding:0 8px!important;">
            <!--查看-->
            <RiskEventView
              v-if="record.riskType == 1 || record.riskType == 2"
              :record="{ eventId: record.eventId, ruleType: record.ruleType }"
              :reload="reload"
            />
            <MlEventView v-else-if="record.riskType == 3" :record="{ id: record.eventId, ruleType: mlRuleTypeMap[record.type] }" />
            <BadActorView
              v-else-if="record.riskType == 4"
              :record="{
                ip: record.ip,
                socTenantId: record.socTenantId,
                severity: record.severity,
                threatScore: record.threatScore,
                attackFirstTime: record.attackFirstTime,
                attackLatestTime: record.attackLatestTime,
              }"
            />

            <AnomalyRecordsView v-else-if="record.riskType == 5" :record="{ id: record.eventId, type: record.riskType }" :reload="reload" />
            <HttpEventView v-else-if="record.riskType == 6" :id="record.eventId" :reload="reload"/>

            <!--            <template v-if="hasHunting('/threatHunting/Index')">-->
            <!--              <a-divider type="vertical"/>-->
            <!--              <span @click="toThreatHunting(record)">{{ t('routes.riskLogs.hunting') }}</span>-->
            <!--            </template>-->

            <a-divider type="vertical" />
            <a-dropdown v-if="showMoreBtn()">
               <span class="ax-dropdown-link" @click.prevent>
                {{ t('common.moreBtn') }}
                <span class="soc ax-com-Arrow-down"></span>
              </span>
              <template #overlay>
                <a-menu>
                  <!-- 有新建调查或参与调查权限显示按钮-->
<!--                  <RiskEventInvestigate-->
<!--                    v-if="record.riskType == 1 || record.riskType == 2"-->
<!--                    :record="{ type: record.riskType, eventId: record.eventId, socTenantId: record.socTenantId }"-->
<!--                  />-->
<!--                  <MlEventInvestigate v-else-if="record.riskType == 3" :record="{ eventId: record.eventId, socTenantId: record.socTenantId }" />-->

<!--                  <BadActorInvestigate v-else-if="record.riskType == 4" :record="{ id: record.eventId, socTenantId: record.socTenantId }" />-->

                  <!-- 有权限且没有关闭 -->
                  <a-menu-item v-if="hasPermission('risk:assign_other') && getCloseStatus(record)">
                    <RiskEventAssign
                      v-if="record.riskType == 1 || record.riskType == 2"
                      :record="{ type: record.riskType, eventId: record.eventId, socTenantId: record.socTenantId }"
                      type="assign_other"
                      :reload="reload"
                    />
                    <MlEventAssign
                      v-else-if="record.riskType == 3"
                      :reload="reload"
                      type="assign_other"
                      :record="{ id: record.eventId, socTenantId: record.socTenantId }"
                    />

                    <BadActorAssign
                      v-else-if="record.riskType == 4"
                      :reload="reload"
                      type="assign_other"
                      :record="{ id: record.eventId, socTenantId: record.socTenantId }"
                    />
                    <AnomalyRecordsAssign
                      v-else-if="record.riskType == 5"
                      :reload="reload"
                      type="assign_other"
                      :record="{ id: record.eventId, socTenantId: record.socTenantId }"
                    />
                    
                    <HttpEventAssign
                      v-else-if="record.riskType == 6"
                      :reload="reload"
                      type="assign_other"
                      :record="{ id: record.eventId, socTenantId: record.socTenantId }"
                    />
                  </a-menu-item>
                  <a-menu-item v-else-if="hasPermission('risk:assign_self') && getCloseStatus(record)">
                    <RiskEventAssign
                      v-if="record.riskType == 1 || record.riskType == 2"
                      :record="{ type: record.riskType, eventId: record.eventId, socTenantId: record.socTenantId }"
                      type="assign_self"
                    />
                    <MlEventAssign
                      v-else-if="record.riskType == 3"
                      :reload="reload"
                      type="assign_self"
                      :record="{ id: record.eventId, socTenantId: record.socTenantId }"
                    />

                    <BadActorAssign
                      v-else-if="record.riskType == 4"
                      :reload="reload"
                      type="assign_self"
                      :record="{ id: record.eventId, socTenantId: record.socTenantId }"
                    />

                    <AnomalyRecordsAssign
                      v-else-if="record.riskType == 5"
                      :reload="reload"
                      type="assign_self"
                      :record="{ id: record.eventId, socTenantId: record.socTenantId }"
                    />
                    <HttpEventAssign
                      v-else-if="record.riskType == 6"
                      :reload="reload"
                      type="assign_self"
                      :record="{ id: record.eventId, socTenantId: record.socTenantId }"
                    />
                  </a-menu-item>

                  <!-- 有权限且没有关闭且分配给登录人 -->
                  <a-menu-item v-if="hasPermission('risk:triage') && getCloseStatus(record) && record?.owner == userStore.getUserInfo.id">
                    <RiskEventTriage
                      v-if="record.riskType == 1 || record.riskType == 2"
                      :reload="reload"
                      :record="{ type: record.riskType, eventId: record.eventId, socTenantId: record.socTenantId }"
                    />

                    <MlEventTriage v-else-if="record.riskType == 3" :record="{ id: record.eventId }" :reload="reload" />

                    <BadActorTriage v-else-if="record.riskType == 4" :record="{ id: record.eventId }" />

                    <AnomalyRecordsTriage v-else-if="record.riskType == 5" :reload="reload" :record="{ id: record.eventId }" />
                    <HttpEventTriage v-else-if="record.riskType == 6" :reload="reload"
                                     :record="{ id: record.eventId }"/>
                  </a-menu-item>

                  <!-- 有权限且已验证且分配给登录人且没有关闭 -->
                  <a-menu-item
                    v-if="
                      hasPermission('risk:close') && record?.triageStatus !== 0 && record?.owner == userStore.getUserInfo.id && getCloseStatus(record)
                    "
                  >
                    <RiskEventClose
                      v-if="record.riskType == 1 || record.riskType == 2"
                      :reload="reload"
                      type="close"
                      :record="{ type: record.riskType, eventId: record.eventId, eventStatus: record.eventStatus }"
                    />

                    <MlEventClose
                      v-else-if="record.riskType == 3"
                      type="close"
                      :reload="reload"
                      :record="{ id: record.eventId, riskStatus: record.eventStatus }"
                    />

                    <BadActorClose
                      v-else-if="record.riskType == 4"
                      :reload="reload"
                      :record="{ id: record.eventId, status: record.eventStatus == 1 ? 0 : 1 }"
                    />

                    <AnomalyRecordsClose
                      v-else-if="record.riskType == 5"
                      type="close"
                      :reload="reload"
                      :record="{ id: record.eventId, riskStatus: record.eventStatus }"
                    />
                    <HttpEventClose v-else-if="record.riskType == 6"  type="close" :reload="reload" :record="{ id: record.eventId, eventStatus: record.eventStatus }"/>
                  </a-menu-item>

                  <!-- 开启 badActor没有开启-->
                  <a-menu-item
                    v-if="hasPermission('risk:close') && record?.owner == userStore.getUserInfo.id && !getCloseStatus(record) && record.riskType != 4"
                  >
                    <RiskEventClose
                      v-if="record.riskType == 1 || record.riskType == 2"
                      :reload="reload"
                      type="open"
                      :record="{ type: record.riskType, eventId: record.eventId, eventStatus: record.eventStatus }"
                    />
                    <MlEventClose
                      v-else-if="record.riskType == 3"
                      type="open"
                      :reload="reload"
                      :record="{ id: record.eventId, riskStatus: record.eventStatus }"
                    />

                    <AnomalyRecordsClose
                      v-else-if="record.riskType == 5"
                      type="open"
                      :reload="reload"
                      :record="{ id: record.eventId, riskStatus: record.eventStatus }"
                    />
                    <HttpEventClose v-else-if="record.riskType == 6"  type="open" :reload="reload" :record="{ id: record.eventId, eventStatus: record.eventStatus }"/>
                  </a-menu-item>
                  <!-- 有权限且已验证且分配给登录人且没有关闭 -->

                  <a-menu-item v-if="hasPermission('risk:record')">
                    <RiskEventRecord
                      v-if="record.riskType == 1 || record.riskType == 2"
                      :record="{ type: record.riskType, eventId: record.eventId }"
                    />

                    <MlEventRecord v-else-if="record.riskType == 3" :record="{ id: record.eventId }" />
                    <BadActorRecord v-else-if="record.riskType == 4" :record="{ id: record.eventId }" />
                    <AnomalyRecordsRecord v-else-if="record.riskType == 5" :record="{ id: record.eventId }" />
                    <HttpEventRecord v-else-if="record.riskType == 6" :record="{ id: record.eventId }"/>
                  </a-menu-item>
                </a-menu>
              </template>
            </a-dropdown>
          </a-space>
        </template>
      </template>
    </a-table>
  </div>
</template>
<script setup lang="ts">
  import { columns } from './index';
  import { defineProps, ref } from 'vue';
  import { queryTicketEventList } from '/@/views/ticket/api/TicketViewTenant.api';
  import Severity from '/@/components/Severity/Severity.vue';
  import RiskEventView from '/@/views/aggregationRiskEventView/component/riskEvent/RiskEventView.vue';
  import RiskEventAssign from '/@/views/aggregationRiskEventView/component/riskEvent/RiskEventAssign.vue';
  import RiskEventRecord from '/@/views/aggregationRiskEventView/component/riskEvent/RiskEventRecord.vue';
  import RiskEventTriage from '/@/views/aggregationRiskEventView/component/riskEvent/RiskEventTriage.vue';
  import RiskEventClose from '/@/views/aggregationRiskEventView/component/riskEvent/RiskEventClose.vue';
  import { usePermission } from '/@/hooks/web/usePermission';
  import { useUserStore } from '/@/store/modules/user';
  import { useI18n } from '/@/hooks/web/useI18n';
  import MlEventView from '/@/views/aggregationRiskEventView/component/mlEvemt/MlEventView.vue';
  import BadActorView from '/@/views/aggregationRiskEventView/component/badActor/BadActorView.vue';
  import MlEventAssign from '/@/views/aggregationRiskEventView/component/mlEvemt/MlEventAssign.vue';
  import MlEventTriage from '/@/views/aggregationRiskEventView/component/mlEvemt/MlEventTriage.vue';
  import MlEventClose from '/@/views/aggregationRiskEventView/component/mlEvemt/MlEventClose.vue';
  import MlEventRecord from '/@/views/aggregationRiskEventView/component/mlEvemt/MlEventRecord.vue';
  import BadActorAssign from '/@/views/aggregationRiskEventView/component/badActor/BadActorAssign.vue';
  import BadActorTriage from '/@/views/aggregationRiskEventView/component/badActor/BadActorTriage.vue';
  import BadActorClose from '/@/views/aggregationRiskEventView/component/badActor/BadActorClose.vue';
  import BadActorRecord from '/@/views/aggregationRiskEventView/component/badActor/BadActorRecord.vue';
  import AnomalyRecordsAssign from '/@/views/aggregationRiskEventView/component/uebaRecords/AnomalyRecordsAssign.vue';
  import AnomalyRecordsClose from '/@/views/aggregationRiskEventView/component/uebaRecords/AnomalyRecordsClose.vue';
  import AnomalyRecordsTriage from '/@/views/aggregationRiskEventView/component/uebaRecords/AnomalyRecordsTriage.vue';
  import AnomalyRecordsRecord from '/@/views/aggregationRiskEventView/component/uebaRecords/AnomalyRecordsRecord.vue';
  import AnomalyRecordsView from '/@/views/aggregationRiskEventView/component/uebaRecords/AnomalyRecordsView.vue';
  import HttpEventView
    from "/@/views/aggregationRiskEventView/component/httpEvemt/HttpEventView.vue";
  import HttpEventAssign
    from "/@/views/aggregationRiskEventView/component/httpEvemt/HttpEventAssign.vue";
  import HttpEventTriage
    from "/@/views/aggregationRiskEventView/component/httpEvemt/HttpEventTriage.vue";
  import HttpEventClose
    from "/@/views/aggregationRiskEventView/component/httpEvemt/HttpEventClose.vue";
  import HttpEventRecord
    from "/@/views/aggregationRiskEventView/component/httpEvemt/HttpEventRecord.vue";

  const props = defineProps({
    id: String,
    isHandle: Boolean,
  });
  const tableData = ref<any[]>([]);
  const mlRuleTypeMap = { mlStatistic: 1, mlOrder: 2, mlContent: 3 };
  const { hasPermission } = usePermission();
  const userStore = useUserStore();
  const { t } = useI18n();
  const loading = ref(false);
  reload();

  function reload() {
    if (props.id) {
      loading.value = true;
      queryTicketEventList({ id: props.id }).then((data) => {
        console.log(data);
        tableData.value = data;
        loading.value = false;
      });
    }
  }

  function showMoreBtn() {
    return (
      hasPermission('investigation:add') ||
      hasPermission('investigation:join') ||
      hasPermission('ticket:useinternel-2') ||
      hasPermission('ticket:useSS-2') ||
      hasPermission('ticket:useinternel-1') ||
      hasPermission('ticket:useIssued-1') ||
      hasPermission('risk:assign_other') ||
      hasPermission('risk:assign_self') ||
      hasPermission('risk:triage') ||
      hasPermission('risk:close') ||
      hasPermission('risk:record')
    );
  }

  /**
   * 获取开启状态
   * @param record
   */
  function getCloseStatus(record) {
    return (record?.eventStatus !== 2 && record.riskType != 4) || (record.riskType == 4 && record?.eventStatus !== 1);
  }
</script>

<style scoped lang="less">
  .ticket-list .ticket-list_content {
    .event_table_div {
      width: 100%;
      /deep/.ant-table-tbody > tr:not(.ant-table-measure-row) > td {
        padding: 8px 12px !important;
      }

      /deep/.ant-table-thead {
        th {
          border-bottom: 1px solid rgba(255, 255, 255, 0.08) !important;
        }
      }

      /deep/ .ant-table-tbody > tr > td {
        border-bottom: 1px solid rgba(255, 255, 255, 0.08) !important;
      }
    }
  }
</style>
