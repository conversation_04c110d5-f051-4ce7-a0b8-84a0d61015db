import { BasicColumn } from '/@/components/Table';
import { FormSchema } from '/@/components/Table';
import { useI18n } from '/@/hooks/web/useI18n';
import { getTenantMode, isAdministrator, isTenant } from '/@/utils/auth';
import { createVNode } from 'vue';
import { tenantTypeMap } from '/@/utils/ckTable';
import { JInputTypeEnum } from '/@/enums/jeecgEnum';
const { t } = useI18n();

/**
 * 是否可以编辑
 * @param data
 */
export function getIsEdit(data) {
  //没有开启租户
  if (!getTenantMode()) {
    return true;
  }
  const isAdmin = isAdministrator();
  //tenantType== 1 MSSP管理员创建 ; tenantType==2 租户创建
  //ruleScope==1私有的；ruleScope==2共享的

  //管理员创建的，管理员可以修改
  if (data?.tenantType == 1 && isAdmin) {
    return true;
  }
  //管理员创建的私有的，私有租户可以修改,列表能看到就可以修改
  if (data?.tenantType == 1 && data?.ruleScope == 1) {
    return true;
  }
  //租户自己创建的，只有租户自己可以修改
  const flag = data?.tenantType == 2 && !isAdmin;
  return flag;
}

/**
 * 是否可以修改状态
 * @param data
 */
export function getUpdStatus(data) {
  //没有开启租户
  if (!getTenantMode()) {
    return true;
  }
  const isAdmin = isAdministrator();
  //tenantType== 1 MSSP管理员创建 ; tenantType==2 租户创建
  //ruleScope==1私有的；ruleScope==2共享的

  //管理员创建的，管理员可以修改
  if (data?.tenantType == 1 && isAdmin) {
    return true;
  }
  //管理员创建的私有的，私有租户可以修改,列表能看到就可以修改
  if (data?.tenantType == 1 && data?.ruleScope == 1) {
    return true;
  }
  //租户自己创建的，只有租户自己可以修改
  if (data?.tenantType == 2 && !isAdmin) {
    return true;
  }
  //共享的，租户可以修改状态
  if (data?.ruleScope == 2 && !isAdmin) {
    return true;
  }
  return false;
}

export const getColumns = (): BasicColumn[] => [
  {
    title: t('routes.WhitelistVO.name'),
    dataIndex: 'name',
  },
  {
    title: t('routes.WhitelistVO.createBy'),
    dataIndex: 'createBy',
    slots: { customRender: 'userInfo' },
  },
  {
    title: t('routes.WhitelistVO.createTime'),
    dataIndex: 'createTime',
  },
  {
    title: t('routes.WhitelistVO.updateTime'),
    dataIndex: 'updateTime',
  },
  {
    title: t('routes.WhitelistVO.riskModule'),
    dataIndex: 'riskModule',
    ellipsis: true,
    customRender: ({ record }) => {
      const data: any = record;
      const map1: any = {
        '1': t('routes.WhitelistVO.Risk_Event'),
        '2': t('routes.WhitelistVO.ML_View'),
      };
      return map1[data?.riskModule];
    },
  },
  {
    title: t('routes.WhitelistVO.ruleScope'),
    dataIndex: 'ruleScope',
    ellipsis: true,
    customRender: ({ value }) => {
      if (value == 1) {
        return t('common.exclusiveRule');
      } else if (value == 2) {
        return t('common.shareableRule');
      }
    },
    ifShow: getTenantMode,
  },
  {
    title: t('routes.WhitelistVO.tenantType'),
    dataIndex: 'tenantType',
    customRender: ({ text }) => {
      return tenantTypeMap(t)[text];
    },
    ifShow: getTenantMode,
  },
  {
    title: t('routes.MlRuleVO.tenant'),
    dataIndex: 'tenant',
    customRender: ({ text }) => {
      if (text) {
        const array = text.split(',');
        const nodes: any = [];
        for (const i in array) {
          nodes.push(createVNode('span', { class: 'tenantSpan ant-table-cell-ellipsis' }, [array[i]]));
        }
        return createVNode('div', { class: 'ant-table-cell-ellipsis', title: text, style: 'display: flex;flex-wrap: wrap;max-height: 55px;' }, [
          nodes,
        ]);
      }
    },
    ifShow: isAdministrator(),
  },
  {
    title: t('routes.WhitelistVO.rule'),
    dataIndex: 'ruleName',
  },
  {
    title: t('routes.WhitelistVO.comment'),
    dataIndex: 'comment',
  },
  {
    title: t('routes.WhitelistVO.status'),
    dataIndex: 'status',
    slots: { customRender: 'switchStatus' },
    ifShow: isTenant(),
  },
];

export const searchFormSchema: FormSchema[] = [
  {
    label: '',
    field: 'name',
    component: 'JInput',
    componentProps: {
      search: true,
      type: JInputTypeEnum.JINPUT_QUERY_EQ,
      placeholder: t('routes.WhitelistVO.name'),
    },
  },
  {
    label: '',
    field: 'createBy',
    component: 'JDictSelectTag',
    componentProps: {
      dictCode: 'sysUserNameDict',
      showSearch: true,
      placeholder: t('routes.WhitelistVO.createBy'),
    },
  },
  {
    label: '',
    field: 'createTimeStr',
    component: 'RangeDate',
    componentProps: {
      datetime: true,
    },
    colProps: {
      lg: 10, // ≥992px
      xl: 5, // ≥1200px
      xxl: 5, // ≥1600px
    },
  },
  {
    label: '',
    field: 'riskModule',
    component: 'Select',
    componentProps: {
      placeholder: t('routes.WhitelistVO.riskModule'),
      options: [
        { label: t('routes.WhitelistVO.Risk_Event'), value: '1' },
        { label: t('routes.WhitelistVO.ML_View'), value: '2' },
      ],
      change: (value) => {
        console.log(value);
      },
    },
  },
  {
    label: '',
    field: 'ruleId',
    component: 'JDictSelectTag',
    componentProps: {
      placeholder: t('routes.WhitelistVO.rule'),
      dictCode: 'detectionRuleDict',
      showSearch: true,
    },
    ifShow: ({ values }) => {
      return values.riskModule == '1';
    },
  },
  {
    label: '',
    field: 'ruleId2',
    component: 'JDictSelectTag',
    componentProps: {
      placeholder: t('routes.WhitelistVO.rule'),
      dictCode: 'mlRuleDict',
      showSearch: true,
    },
    ifShow: ({ values }) => {
      return values.riskModule == '2';
    },
  },
];

export const formSchema: FormSchema[] = [
  // TODO 主键隐藏字段，目前写死为ID
  { label: '', field: 'id', component: 'Input', show: false },
  {
    label: t('routes.WhitelistVO.name'),
    field: 'name',
    component: 'Input',
  },
  {
    label: t('routes.WhitelistVO.comment'),
    field: 'comment',
    component: 'Input',
  },
  {
    label: t('routes.WhitelistVO.riskModule'),
    field: 'riskModule',
    component: 'Input',
  },
  {
    label: t('routes.WhitelistVO.riskModuleType'),
    field: 'riskModuleType',
    component: 'Input',
  },
  {
    label: t('routes.WhitelistVO.ruleJson'),
    field: 'ruleJson',
    component: 'Input',
  },
];
