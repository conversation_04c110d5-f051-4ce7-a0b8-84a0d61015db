<template>
  <div style="cursor: pointer; margin: 10px; margin-bottom: -15px" v-if="route.query && route.query.toback">
    <div @click="goBack">
      <Icon icon="ant-design:left-outlined" style="margin-left: 5px; cursor: pointer" />
      {{ t('routes.riskLogs.goBack') }}
    </div>
  </div>
  <div v-else> </div>
  <div class="order-log page_div">
    <!--引用表格-->
    <BasicTable @register="registerTable" class="auto-min-height" :isSearch="isSearch" :row-selection="rowSelection" @columns-change="columnsChange">
      <!--高级查询-->
      <template #form-formHeader>
        <TableSearchBtn
          @search="search"
          ref="tableSearchBtnRef"
          source="risk"
          :advancedQueryFlag="advancedQueryFlag"
          v-if="pageSource != 'badActor'"
        />
      </template>
      <!--插槽:table标题-->
      <template #form-formFooter>
        <div class="ax-icon-button" @click="emits('switch')"  v-if="pageSource != 'asset' && pageSource != 'badActor' && pageSource != 'ticket'">
          <span class="soc ax-com-Barview ax-icon"></span>
        </div>

        <a-dropdown :visible="dropdownVisible" v-if="pageSource != 'badActor' && pageSource != 'ticket'">
          <template #overlay>
            <a-menu @click="dropdownVisible = false">
              <a-menu-item v-if="hasPermission('risk:assign_other')">
                <RiskEventBatchAssign :records="selectedRows" type="assign_other" :reload="reload" @cleanSelect="cleanSelect" />
              </a-menu-item>
              <a-menu-item v-else-if="hasPermission('risk:assign_self')">
                <RiskEventBatchAssign :records="selectedRows" type="assign_self" :reload="reload" @cleanSelect="cleanSelect" />
              </a-menu-item>
              <a-menu-item v-if="hasPermission('risk:triage')">
                <RiskEventBatchTriage :reload="reload" :records="selectedRows" @cleanSelect="cleanSelect" />
              </a-menu-item>
              <a-menu-item v-if="hasPermission('risk:close')">
                <RiskEventBatchClose :reload="reload" :records="selectedRows" type="close" @cleanSelect="cleanSelect" />
              </a-menu-item>
              <a-menu-item v-if="hasPermission('risk:close')">
                <RiskEventBatchClose :reload="reload" :records="selectedRows" type="open" @cleanSelect="cleanSelect" />
              </a-menu-item>
              <a-sub-menu
                v-if="
                  (hasPermission('ticket:useinternel-2') ||
                    hasPermission('ticket:useSS-2') ||
                    hasPermission('ticket:useinternel-1') ||
                    hasPermission('ticket:useIssued-1')) &&
                  pageSource != 'ticket'
                "
                key="sub2"
                :title="t('common.ticketBtn')"
              >
                <EventApply :records="selectedRows" eventType="1" @applyOk="reload" />
              </a-sub-menu>
            </a-menu>
          </template>
          <div class="ax-icon-button" @click.stop="showBatchBtn">
            <span class="soc ax-com-Batch ax-icon"></span>
          </div>
        </a-dropdown>
        <!-- 工单进来不显示白名单按钮 -->
        <div class="ax-icon-button" v-if="pageSource != 'ticket' && pageSource != 'badActor'"  @click="exportList">
          <span class="soc ax-Export ax-icon"></span>
        </div>
        <div class="ax-icon-button" v-if="pageSource != 'ticket' && pageSource != 'badActor'"  @click="showAddWhite">
          <span class="soc ax-com-Submit ax-icon"></span>
        </div>
      </template>

      <template #triageStatus="{ record }">
        <div class="flex">
          <TriageStatus :status="record.triageStatus" />
        </div>
      </template>
      <template #severity="{ record,text }">
        <Severity2 :value="text"/>
      </template>
      <!-- 工单 start-->
      <template #ticket="{ record }">
        <Ticket :ticketInfo="record" @applyOk="reload" />
      </template>
      <!-- 工单 end-->
      <template #action="{ record }">
        <a-space size="5" class="action-border" style="padding: 0 8px !important" v-if="props.pageSource != 'ticket'">
          <RiskEventView :record="record" :reload="reload" />

          <template v-if="hasHunting('/threatHunting/Index') && pageSource != 'asset'">
            <a-divider type="vertical" />
            <span @click="toThreatHunting(record, router, 'event')">{{ t('routes.riskLogs.hunting') }}</span>
          </template>

          <a-divider type="vertical" />
          <a-dropdown v-if="showMoreBtn()">
              <span class="ax-dropdown-link" @click.prevent>
              {{ t('common.moreBtn') }}
              <span class="soc ax-com-Arrow-down"></span>
            </span>
            <template #overlay>
              <a-menu>
                <!--                有新建调查或参与调查权限显示按钮-->
                <!--                <RiskEventInvestigate :record="record" />-->
                <a-sub-menu v-if="hasTicketPermission()" key="sub2" :title="t('common.ticketBtn')">
                  <!--                  现场目前还在使用原来的工单-->
                  <EventApply :record="record" eventType="1" @applyOk="reload" />
                  <!--
                                                      <ApplyMenu v-model:workflowList="workflowList" @close="reload" :eventType="1" :type="2" :record="record" />
                  -->
                </a-sub-menu>

                <!-- 有权限且没有关闭 -->
                <a-menu-item v-if="hasPermission('risk:assign_other') && record?.eventStatus !== 2">
                  <RiskEventAssign :record="record" type="assign_other" :reload="reload" />
                </a-menu-item>
                <a-menu-item v-else-if="hasPermission('risk:assign_self') && record?.eventStatus !== 2">
                  <RiskEventAssign :record="record" type="assign_self" :reload="reload" />
                </a-menu-item>

                <!-- 有权限且没有关闭且分配给登录人 -->
                <a-menu-item v-if="hasPermission('risk:triage') && record?.eventStatus !== 2 && record?.owner == userStore.getUserInfo.id">
                  <RiskEventTriage :reload="reload" :record="record" />
                </a-menu-item>

                <!-- 有权限且已验证且分配给登录人且没有关闭 -->
                <a-menu-item
                  v-if="
                    hasPermission('risk:close') &&
                    record?.triageStatus !== 0 &&
                    record?.owner == userStore.getUserInfo.id &&
                    record?.eventStatus !== 2
                  "
                >
                  <RiskEventClose :reload="reload" :record="record" type="close" @ok="closeDown"/>
                </a-menu-item>
                <a-menu-item v-if="hasPermission('risk:close') && record?.owner == userStore.getUserInfo.id && record?.eventStatus === 2">
                  <RiskEventClose :reload="reload" :record="record" type="open"  @ok="closeDown"/>
                </a-menu-item>
                <!-- 有权限且已验证且分配给登录人且没有关闭 -->

                <a-menu-item v-if="hasPermission('risk:record')">
                  <RiskEventRecord :record="record" />
                </a-menu-item>
              </a-menu>
            </template>
          </a-dropdown>
        </a-space>
      </template>
      
    </BasicTable>
  </div>
  <WhitelistVOModal @register="registerModal" type="1" />

  <!--  <TableSearchModel ref="tableSearchModelRef" @search="search" source="risk"-->
  <!--                    @refTemplate="loadTemplateOption" @clearTemplate="clearTemplate"/>-->

</template>

<script lang="ts" name="aggregationRiskEventView-riskEventLogView" setup>
  import { useRoute, useRouter } from 'vue-router';
  import { nextTick, onMounted, ref, unref, defineAsyncComponent } from 'vue';
  import { BasicTable, TriageStatus } from '/@/components/Table';
  import { useListPage } from '/@/hooks/system/useListPage';
  import { columns, getCardSearchFormSchema, handleData, handleData2, hasTicketPermission, toThreatHunting } from './RiskEventView.data';
  import { tableList, getExportUrl } from './RiskEventView.api';
  import { useI18n } from '/@/hooks/web/useI18n';
  import { formLayout } from '/@/settings/designSetting';
  import WhitelistVOModal from '/@/views/whitelist/modules/WhitelistVOModal.vue';
  import { useModal } from '/@/components/Modal';
  import Icon from '/@/components/Icon';
  import { usePermission } from '/@/hooks/web/usePermission';
  import { queryEntryTicket } from '/@/views/workflow/view/ts/TicketUtils';
  import { hasHunting } from '/@/utils/auth';
  import { handleLastTime } from '/@/utils/searchTableListTimeUtil';
  import TableSearchBtn from '/@/views/tableSearch/TableSearchBtn.vue';
  import { TABLE_CACHE_KEY } from '/@/utils/valueEnum';
  
  import { useUserStore } from '/@/store/modules/user';
  import RiskEventAssign from '/@/views/aggregationRiskEventView/component/riskEvent/RiskEventAssign.vue';
  import RiskEventBatchAssign from '/@/views/aggregationRiskEventView/component/riskEvent/RiskEventBatchAssign.vue';
  import RiskEventTriage from '/@/views/aggregationRiskEventView/component/riskEvent/RiskEventTriage.vue';
  import RiskEventBatchTriage from '/@/views/aggregationRiskEventView/component/riskEvent/RiskEventBatchTriage.vue';
  import RiskEventClose from '/@/views/aggregationRiskEventView/component/riskEvent/RiskEventClose.vue';
  import RiskEventBatchClose from '/@/views/aggregationRiskEventView/component/riskEvent/RiskEventBatchClose.vue';
  import RiskEventRecord from '/@/views/aggregationRiskEventView/component/riskEvent/RiskEventRecord.vue';
  import { Ticket } from '/@/views/aggregationRiskEventView/component/ticket/index';
  import { getLastTime } from '/@/utils/ckTable';
  import { message } from 'ant-design-vue';
  import { useMethods } from '/@/hooks/system/useMethods';
  import { filterObj } from '/@/utils/common/compUtils';
  import Severity2 from "/@/components/Severity/Severity2.vue";
  
  const RiskEventView = defineAsyncComponent(() => import("/@/views/aggregationRiskEventView/component/riskEvent/RiskEventView.vue"));
  const EventApply = defineAsyncComponent(() => import("/@/views/ticket/view/apply/eventApply.vue"));
  
  const props = defineProps({
    ip: String,
    pageSource: String,
    selectIds: Array,
    socTenantId: String,
    badActorParams: Object,
    ruleType: String,
  });
  const emits = defineEmits(['switch','closeDown']);
  const { hasPermission } = usePermission();
  const userStore = useUserStore();
  const { t } = useI18n();
  const isSearch = ref<boolean>(true);
  const router = useRouter();
  const route = useRoute();
  const { handleExportXls } = useMethods();

  //-------------ticket variable start----------------
  const workflowList = ref([]);

  //-------------ticket variable end------------------

  function showMoreBtn() {
    return (
      hasPermission('investigation:add') ||
      hasPermission('investigation:join') ||
      hasPermission('ticket:useinternel-2') ||
      hasPermission('ticket:useSS-2') ||
      hasPermission('ticket:useinternel-1') ||
      hasPermission('ticket:useIssued-1') ||
      hasPermission('risk:assign_other') ||
      hasPermission('risk:assign_self') ||
      hasPermission('risk:triage') ||
      hasPermission('risk:close') ||
      hasPermission('risk:record')
    );
  }
  let lastTime: any = getLastTime('riskEventLastTime');
  // 高级查询代码开始

  const tableSearchBtnRef = ref();
  //高级查询数据
  let searchData: any = '';
  //高级查询标识
  const advancedQueryFlag = ref(false);

  const searchCacheKey = 'riskAdvancedQueryData';
  if (sessionStorage.getItem(searchCacheKey)) {
    let data = sessionStorage.getItem(searchCacheKey);
    if (data) {
      data = JSON.parse(data);
      searchData = data;
      advancedQueryFlag.value = true;
      nextTick(() => {
        tableSearchBtnRef.value.setData(searchData);
      });
      lastTime = null;
    }
  }

  /**
   * 高级查询
   */
  function search(data: any) {
    console.log(data);
    searchData = data;
    if (data) {
      advancedQueryFlag.value = true;
      //把高级查询条件存到缓存中，防止路由跳转后再回来高级查询条件消失
      sessionStorage.setItem(searchCacheKey, JSON.stringify(data));
      getForm().resetFields();
      // reload();
    } else {
      sessionStorage.removeItem(searchCacheKey);
      advancedQueryFlag.value = false;
      getForm().setFieldsValue({ lastTime: getLastTime('riskEventLastTime') });
    }
  }

  async function exportList() {
    let exportConfig = {
      url: getExportUrl,
      name: 'Risk Event',
      params: buildExportParams(),
    };
    let {  params } = exportConfig;
    let realUrl = getExportUrl;
    if (realUrl) {
      let title = 'Risk Event';
      let paramsForm: any = {};
      try {
        paramsForm = await getForm().validate();
      } catch (e) {
        console.error(e);
      }

      if (!paramsForm?.column) {
        Object.assign(paramsForm, { column: 'updateTime', order: 'desc' });
      }

      if (params) {
        Object.keys(params).map((k) => {
          let temp = (params as object)[k];
          if (temp) {
            paramsForm[k] = unref(temp);
          }
        });
      }
      let selections = selectedRowKeys.value.join(',');
      if (selections) {
        paramsForm['selections'] = selections;
      }
      return handleExportXls(title as string, realUrl, filterObj(paramsForm));
    } else {
      message.warn('没有传递 exportConfig.url 参数');
      return Promise.reject();
    }
  }

  // 新增公共参数构建方法
  function buildExportParams() {
    let params: any = {};

    if (props.ip) {
      params.fromMainIp = props.ip;
    }
    if (props.socTenantId) {
      params.socTenantId = props.socTenantId;
    }
    if (advancedQueryFlag.value) {
      let updateTimeStr = searchData.updateTimeStr;
      if (updateTimeStr) {
        let strs = updateTimeStr.split(',');
        params.updateTime_begin = strs[0];
        params.updateTime_end = strs[1];
      }
      params.advancedQuery = JSON.stringify(searchData.whereList);
    } else {
      params.advancedQuery = '';
      if (!params.lastTime) {
        params.lastTime = getLastTime('riskEventLastTime');
      }
      handleLastTime(params, 'updateTime');
      sessionStorage.setItem('riskEventLastTime', params.lastTime);
    }
    return params;
  }

  // 高级查询代码结束

  let actionColumn: any = {
    width: 230,
    fixed: 'right',
  };
  if (props.pageSource == 'ticket') {
    actionColumn = {
      width: 50,
      title: '',
      fixed: 'right',
    };
  }
  //注册table数据
  const { tableContext } = useListPage({
    tableProps: {
      title: 'risk event',
      api: tableList,
      rowKey: 'eventId',
      columns: columns(),
      canResize: false,
      formConfig: {
        model: { lastTime: lastTime },
        schemas: getCardSearchFormSchema(props.socTenantId, !props.badActorParams),
        autoSubmitOnEnter: true,
        showAdvancedButton: true,
        // 超过指定列数默认折叠
        autoAdvancedCol: 12,
        layout: formLayout,
        baseColProps: {
          lg: 6, // ≥992px
          xl: 3, // ≥1200px
          xxl: 2, // ≥1600px
        },
      },
      showTableSetting: true,
      tableSetting: {
        redo: false,
        size: false,
        fullScreen: false,
        cacheKey: TABLE_CACHE_KEY.risk,
      },
      defSort: {
        column: 'updateTime',
        order: 'desc',
      },
      actionColumn: actionColumn,
      beforeFetch: (params) => {
        //资产查看
        if (props.ip) {
          params.fromMainIp = props.ip;
        }
        
        if(props.ruleType){
          params.ruleType = props.ruleType;
        }
        
        if (props.socTenantId) {
          params.socTenantId = props.socTenantId;
        }

        if (advancedQueryFlag.value) {
          let updateTimeStr = searchData.updateTimeStr;
          if (updateTimeStr) {
            let strs = updateTimeStr.split(',');
            params.updateTime_begin = strs[0];
            params.updateTime_end = strs[1];
          }
          params.advancedQuery = JSON.stringify(searchData.whereList);
        } else {
          params.advancedQuery = '';
          params.updateTime_begin = '';
          params.updateTime_end = '';
          if (!params.lastTime) {
            params.lastTime = getLastTime('riskEventLastTime');
          }
          handleLastTime(params, 'updateTime');
        }

        if (props.badActorParams) {
          for (const key in props.badActorParams) {
            if (key === 'eventType') {
              params['eventName'] = props.badActorParams[key];
            } else if (key === 'updateTimeStr') {
              const arr = props.badActorParams[key].split(',');
              params.updateTime_begin = arr[0];
              params.updateTime_end = arr[1];
            } else {
              params[key] = props.badActorParams[key];
            }
          }
        }

        console.log(params);
        sessionStorage.setItem('riskEventLastTime', params.lastTime);
        return params;
      },
    },
  });

  const [registerTable, { reload, getForm, setSelectedRowKeys }, { rowSelection, selectedRows, selectedRowKeys }] = tableContext;

  const bodyDiv = ref<any>();
  /**
   * 初始化
   */
  onMounted(() => {
    bodyDiv.value = document.getElementsByTagName('body')[0];
    console.log(bodyDiv.value);
    console.log(props.selectIds);
    if (props.selectIds) {
      const keys: any[] = props.selectIds;
      const ids: any = [];
      for (let i = 0; i < keys.length; i++) {
        ids.push(keys[i].eventId);
      }
      setSelectedRowKeys(ids);
    }
    getTickets();
  });

  /**
   * 获取entry ticket
   */
  async function getTickets() {
    workflowList.value = await queryEntryTicket(2);
    console.log('workflowList.value---------->', workflowList.value);
  }

  function goBack() {
    router.push({
      path: '/badactors/BadActorsViewModal',
    });
  }

  const [registerModal, { openModal }] = useModal();

  function showAddWhite() {
    openModal(true, {
      isUpdate: false,
      showFooter: true,
    });
  }

  function columnsChange(data) {
    console.log(data);
  }

  /**
   * 获取选择的数据
   */
  function getSelectRowsData() {
    const list = unref(selectedRows);
    const listKeys = unref(selectedRowKeys);
    console.log(list);
    console.log(listKeys);
    const dataList: any = [];
    if (list) {
      for (let i = 0; i < list.length; i++) {
        if (listKeys.indexOf(list[i].eventId) > -1) {
          listKeys.splice(listKeys.indexOf(list[i].eventId), 1);
        }
        dataList.push(handleData(list[i]));
      }
    }
    const oldList: any = props.selectIds;
    const oldDataMap: any = {};
    oldList.forEach((item) => {
      oldDataMap[item.eventId] = item;
    });
    console.log(oldDataMap);
    if (listKeys) {
      for (let i = 0; i < listKeys.length; i++) {
        dataList.push(handleData2(oldDataMap[listKeys[i]]));
      }
    }

    return dataList;
  }

  function cleanSelect() {
    setSelectedRowKeys([]);
  }

  const dropdownVisible = ref(false);

  function showBatchBtn() {
    if (unref(selectedRows)?.length > 0) {
      dropdownVisible.value = true;
    } else {
      dropdownVisible.value = false;
    }
  }

  onMounted(() => {
    document.addEventListener('click', () => {
      dropdownVisible.value = false;
    });
  });
  
  const closeDown = ()=>{
    emits('closeDown');
  }

  defineExpose({
    getSelectRowsData,
  });
</script>
<style lang="less" scoped>
:deep(.ax-search-wrapper .ax-search-input) {
  width: 210px !important;
}
</style>
