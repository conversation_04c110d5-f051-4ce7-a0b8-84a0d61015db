import {BasicColumn} from '/@/components/Table';
import {useI18n} from "/@/hooks/web/useI18n";

const {t} = useI18n();

//该属性是国际化中英文配置，需要在/src/locales/lang/en/routes中创建RiskEventRecord.ts文件，把下方属性复制到文件中
/*
export default {
     'eventId': 'risk event id',
     'eventType': '1:tbl_aggregation_risk_event_security,2:tbl_aggregation_risk_event_host',
     'eventAction': '1:Assign，2:Triage，3:Close',
};
*/

export const columns: BasicColumn[] = [
  {
    title: t('routes.RiskEventRecord.eventAction'),
    dataIndex: 'eventAction',
    customRender: ({text}) => {
      if (text === 1) {
        return 'Assign'
      } else if (text === 2) {
        return 'Triage'
      } else if (text === 3) {
        return 'Close'
      } else if (text === 4) {
        return 'Open'
      }
    }
  },
  {
    title: t('routes.RiskEventRecord.content'),
    dataIndex: 'content',
    customRender: ({text, record}) => {
      const data: any = record
      if (data?.eventAction === 1) {
        return text
      } else if (data?.eventAction === 2) {
        if (text == 1) {
          return t('routes.RiskEventLogView.true')
        } else if (text == 2) {
          return t('routes.RiskEventLogView.false')
        } else if (text == 3) {
          return t('routes.RiskEventLogView.other')
        }
      } else if (data?.eventAction === 3) {
        return text
      }
    }
  },
  {
    title: t('routes.RiskEventRecord.createBy'),
    dataIndex: 'createBy'
  },
  {
    title: t('routes.RiskEventRecord.createTime'),
    dataIndex: 'createTime'
  },
];

