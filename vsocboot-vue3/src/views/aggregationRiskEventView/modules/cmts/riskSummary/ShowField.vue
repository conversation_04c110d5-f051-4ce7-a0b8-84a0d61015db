<template>
  <!-- <Teleport to="#filter-button"> -->
  <Tooltip placement="topRight">
    <template #title>
      <span>{{ titleText }}</span>
    </template>
    <Popover
      v-model:visible="popoverVisible"
      placement="bottomLeft"
      trigger="click"
      @visible-change="handleVisibleChange"
      :overlayClassName="`${prefixCls}__cloumn-list`"
      :getPopupContainer="getPopupContainer"
    >
      <template #content>
        <ScrollContainer ref="scroll" v-if="!ifOrder">
          <!-- show item -->
          <!-- hide item -->
          <div class="" ref="columnListRef">
            <!-- 搜索框 -->
            <div class="pl-14px pr-8px mb-16px">
              <a-input
                v-model:value="fieldValue"
                :placeholder="t('common.column.fieldSearchPlaceholder')"
                style="width: 200px"
                @input="onSearch(fieldValue)"
                @search="onSearch"
                allowClear
              >
                <template #suffix>
                  <span class="soc ax-com-Search" />
                </template>
              </a-input>
            </div>
            <div class="w-[calc(100%-8px)] h-1px bg-[#28282C]" style="margin: 12px auto"></div>
            <!-- end -->
            <div class="mb-16px pl-16px">{{ t('common.showitem') }}</div>
            <div ref="visibleDrap1" id="visibleDrap1">
              <div
                v-for="i in showOptions"
                :key="i.value"
                class="visibleItem flex items-center pt-8px pb-8px pl-16px pr-16px"
                :style="i.isSearch ? 'display:none' : ''"
                :class="{ 'ignore-elements': showOptions.length === 1 && !isSearch }"
              >
                <div class="flex items-center mr-12px">
                  <MoreOutlined class="w-0.5em" />
                  <MoreOutlined class="w-0.5em" />
                </div>
                <div>{{ i.label }}</div>
              </div>
            </div>
            <div class="w-[calc(100%-8px)] h-1px bg-[#28282C]" style="margin: 12px auto"></div>
            <div class="mb-16px pl-16px">{{ t('common.hideitem') }}</div>
            <div ref="invisibleDrap1" id="invisibleDrap1">
              <div
                v-for="i in hideOptions"
                :key="i.value"
                :style="i.isSearch ? 'display:none' : ''"
                class="invisibleItem flex items-center pt-8px pb-8px pl-16px pr-16px"
              >
                <div class="flex items-center mr-12px">
                  <MoreOutlined class="w-0.5em" />
                  <MoreOutlined class="w-0.5em" />
                </div>
                <div>{{ i.label }}</div>
              </div>
            </div>
          </div>
        </ScrollContainer>
        <ScrollContainer ref="scroll" v-if="ifOrder">
          <!-- show item -->
          <!-- hide item -->
          <div class="" ref="columnListRef">
            <!-- 搜索框 -->
            <div class="pl-14px pr-8px mb-16px">
              <a-input v-model:value="fieldValue" :placeholder="t('common.column.fieldSearchPlaceholder')" style="width: 200px" @search="onSearch">
                <template #suffix>
                  <span class="soc ax-com-Search" />
                </template>
              </a-input>
            </div>
            <div class="w-[calc(100%-8px)] h-1px bg-[#28282C]" style="margin: 12px auto"> </div>
            <!-- end -->
            <div class="mb-16px pl-16px">
              <a-tabs v-model:activeKey="activeKey" @change="changeTable">
                <a-tab-pane v-for="item in tableList" :key="item.value" :tab="item.label"> </a-tab-pane>
              </a-tabs>
              <div class="mb-16px pl-16px">{{ t('common.showitem') }}</div>
              <div ref="visibleDrap1" id="visibleDrap1">
                <div
                  v-for="i in showOptions"
                  :key="i.value"
                  class="visibleItem flex items-center pt-8px pb-8px pl-16px pr-16px"
                  :style="i.isSearch ? 'display:none' : ''"
                  :class="{ 'ignore-elements': showOptions.length === 1 && !isSearch }"
                >
                  <div class="flex items-center mr-12px">
                    <MoreOutlined class="w-0.5em" />
                    <MoreOutlined class="w-0.5em" />
                  </div>
                  <div>{{ i.label }}</div>
                </div>
              </div>
              <div class="w-[calc(100%-8px)] h-1px bg-[#28282C]" style="margin: 12px auto"></div>
              <div class="mb-16px pl-16px">{{ t('common.hideitem') }}</div>
              <div ref="invisibleDrap1" id="invisibleDrap1">
                <div
                  v-for="i in hideOptions"
                  :key="i.value"
                  :style="i.isSearch ? 'display:none' : ''"
                  class="invisibleItem flex items-center pt-8px pb-8px pl-16px pr-16px"
                >
                  <div class="flex items-center mr-12px">
                    <MoreOutlined class="w-0.5em" />
                    <MoreOutlined class="w-0.5em" />
                  </div>
                  <div>{{ i.label }}</div>
                </div>
              </div>
            </div>
          </div>
        </ScrollContainer>
      </template>
      <slot name="btn"></slot>
    </Popover>
  </Tooltip>
</template>
<script lang="ts">
  import { defineComponent, nextTick, reactive, ref, toRefs, unref, watchEffect } from 'vue';
  import { message, Popover, Tooltip } from 'ant-design-vue';
  import { MoreOutlined } from '@ant-design/icons-vue';
  import { ScrollContainer } from '/@/components/Container';
  import { useI18n } from '/@/hooks/web/useI18n';
  import { useDesign } from '/@/hooks/web/useDesign';
  import { isFunction, isNullAndUnDef } from '/@/utils/is';
  import { getPopupContainer as getParentContainer } from '/@/utils';
  import { cloneDeep } from 'lodash-es';
  import Sortablejs from 'sortablejs';
  import { createLocalStorage } from '/@/utils/cache';
  import { defHttp } from '/@/utils/http/axios';

  interface State {
    checkAll: boolean;
    isInit?: boolean;
    checkedList: string[];
    defaultCheckList: string[];
    sortedList: string[];
  }

  interface Options {
    label: string;
    value: string;
    fixed?: boolean | 'left' | 'right';
  }

  export default defineComponent({
    name: 'ColumnSetting',
    props: {
      cacheKey: {
        type: String,
        required: true,
      },
      columns: {
        type: Array as any,
        required: true,
      },
      saveFlag: {
        type: Boolean,
        default: true,
      },
      defaultList: {
        type: Array as any,
        default: () => [],
      },
      ruleType: {
        type: Number,
      },
      tableList: {
        type: Array as any,
        default: () => [],
      },
      ifOrder: {
        type: Boolean,
        default: false,
      },
      title: String,
    },
    components: {
      Popover,
      Tooltip,
      ScrollContainer,
      MoreOutlined,
    },
    emits: ['columns-change', 'close', 'changeTableType'],

    setup(props, { emit, attrs }) {
      const { t } = useI18n();
      const popoverVisible = ref(true);
      const fieldValue = ref('');
      const activeKey = ref(props.tableList[0]?.value);
      // update-begin--author:sunjianlei---date:20221101---for: 修复第一次进入时列表配置不能拖拽
      nextTick(() => (popoverVisible.value = false));
      // update-end--author:sunjianlei---date:20221101---for: 修复第一次进入时列表配置不能拖拽
      let inited = false;
      const plainOptions = ref<any[] | any>([]);

      const plainSortOptions = ref<any[]>([]);

      const columnListRef = ref<ComponentRef>(null);
      const showOptions = ref<Options[] | any>([]);
      const hideOptions = ref<Options[] | any>([]);
      const originOptions = ref<any>({
        show: [],
        hide: [],
      });
      const isSearch = ref(false);
      const visibleDrap1 = ref();
      const invisibleDrap1 = ref();

      const state = reactive<State>({
        checkAll: true,
        checkedList: [],
        defaultCheckList: [],
        sortedList: [],
      });
      const showSearch = ref(false);
      const scroll = ref();
      const checkIndex = ref(false);
      const checkSelect = ref(false);

      const { prefixCls } = useDesign('basic-column-setting');

      const titleText = props.title;
      function getColumns() {
        return props.columns;
      }

      const $ls = createLocalStorage();
      watchEffect(() => {
        setTimeout(() => {
          const columns = getColumns();
          if (columns.length && !state.isInit) {
            init();
          }
        }, 0);
      });

      function init() {
        const columns = getColumns();
        plainOptions.value = [...columns];
        plainSortOptions.value = [...columns];
        showOptions.value = [];
        hideOptions.value = [];

        let columnCache = $ls.get(props.cacheKey);
        if (columnCache && columnCache.sortedList) {
          columns.sort((prev, next) => {
            return columnCache.sortedList.indexOf(prev.value) - columnCache.sortedList.indexOf(next.value);
          });
          state.sortedList = columnCache.sortedList;
        } else {
          state.sortedList = columns.map((item) => item?.value ?? '');
        }
        
        const checkList = props.defaultList;
        if (!plainOptions.value.length) {
          plainOptions.value = columns;
          plainSortOptions.value = columns;
        }
        unref(plainOptions).forEach((item: any) => {
          item.isShow = checkList.some((col) => col === item.value);
          item.isSearch = false;
          if (item.isShow) {
            unref(showOptions).push(item);
            const i: any = cloneDeep(item);
            unref(originOptions).show.push(i);
          } else {
            unref(hideOptions).push(item);
            const i: any = cloneDeep(item);
            unref(originOptions).hide.push(i);
          }
        });
        state.isInit = true;
        state.checkedList = checkList;
      }

      function changeTable(key) {
        activeKey.value = key;
        console.log('valuevalue', key);
        emit('changeTableType', key);
        nextTick(() => {
          init();
        });
      }

      // 新的拖拽逻辑
      function handleVisibleChange(flag: boolean) {
        //关闭弹出框了，触发
        if (!popoverVisible.value) {
          emit('close', unref(showOptions), activeKey.value ? activeKey.value : null);
        }
        if (flag && inited) {
          fieldValue.value = '';
          onSearch('');
          scroll.value?.setScrollTop();
        }
        if (inited) return;
        nextTick(() => {
          const columnListEl = unref(columnListRef);
          const showEL = visibleDrap1.value as HTMLElement;
          const invisibleEL = invisibleDrap1.value as HTMLElement;
          if (!columnListEl) return;

          new Sortablejs(showEL, {
            animation: 500,
            delay: 400,
            scroll: true,
            handle: '.visibleItem',
            draggable: '.visibleItem',
            delayOnTouchOnly: true,
            filter: '.ignore-elements',
            group: 'shared',
            onFilter: function () {
              if (showOptions.value.length === 1) {
                message.error(t('component.table.lastElTips'), 1);
                return;
              }
            },
            onEnd: function (evt) {
              if (isNullAndUnDef(evt.oldIndex) || isNullAndUnDef(evt.newIndex)) {
                return;
              }
              if (evt.to.id === evt.from.id && evt.oldIndex === evt.newIndex) {
                return;
              }
              if (evt.to.id === 'invisibleDrap1') {
                let itemToAdd: null | object = null;
                if (evt.oldIndex !== null) {
                  itemToAdd = showOptions.value.splice(evt.oldIndex, 1)[0];
                }
                if (evt.newIndex !== null && itemToAdd) {
                  const itemNew = { ...itemToAdd, isShow: false, defaultHidden: true };
                  hideOptions.value.splice(evt.newIndex, 0, itemNew);
                }
              } else {
                let itemToAdd = null;
                if (evt.oldIndex !== null) {
                  itemToAdd = showOptions.value.splice(evt.oldIndex, 1)[0];
                }
                if (evt.newIndex !== null && itemToAdd) {
                  showOptions.value.splice(evt.newIndex, 0, itemToAdd);
                }
              }
              setColumns([...showOptions.value]);
              state.checkedList = showOptions.value.map((i) => i.value);
              state.sortedList = showOptions.value.map((i) => i.value);
              state.sortedList.push(...hideOptions.value.map((i) => i.value));
              saveSetting();
            },
          });
          new Sortablejs(invisibleEL, {
            animation: 500,
            handle: '.invisibleItem',
            group: 'shared',
            onEnd: function (evt) {
              if (isNullAndUnDef(evt.oldIndex) || isNullAndUnDef(evt.newIndex)) {
                return;
              }
              if (evt.to.id === evt.from.id && evt.oldIndex === evt.newIndex) {
                return;
              }
              if (evt.to.id === 'visibleDrap1') {
                let itemToAdd: null | object = null;
                if (evt.oldIndex !== null) {
                  itemToAdd = hideOptions.value.splice(evt.oldIndex, 1)[0];
                }
                if (evt.newIndex !== null && itemToAdd) {
                  const itemNew = { ...itemToAdd, isShow: true, defaultHidden: false };
                  showOptions.value.splice(evt.newIndex, 0, itemNew);
                }
              } else {
                let itemToAdd = null;
                if (evt.oldIndex !== null) {
                  itemToAdd = hideOptions.value.splice(evt.oldIndex, 1)[0];
                }
                if (evt.newIndex !== null && itemToAdd) {
                  hideOptions.value.splice(evt.newIndex, 0, itemToAdd);
                }
              }

              setColumns([...showOptions.value]);
              state.checkedList = showOptions.value.map((i) => i.value);
              state.sortedList = showOptions.value.map((i) => i.value);
              state.sortedList.push(...hideOptions.value.map((i) => i.value));
              saveSetting();
            },
          });
          inited = true;
        });
      }

      function setColumns(columns: string[]) {
        console.log(unref(plainSortOptions));
        console.log(unref(columns));
        emit('columns-change', columns);
      }

      function getPopupContainer() {
        return isFunction(attrs.getPopupContainer) ? attrs.getPopupContainer() : getParentContainer();
      }

      function onSearch(value: string) {
        scroll.value?.setScrollTop();
        // 优化
        const updateSearchStatus = (options, searchValue) => {
          const searchText = searchValue.trim().toLowerCase();
          return options.map((item) => ({
            ...item,
            isSearch: searchText ? !item.label.toLowerCase().includes(searchText) : false,
          }));
        };

        if (!value || value.trim() === '') {
          showOptions.value = updateSearchStatus(showOptions.value, '');
          hideOptions.value = updateSearchStatus(hideOptions.value, '');
          isSearch.value = false;
        } else {
          showOptions.value = updateSearchStatus(showOptions.value, value);
          hideOptions.value = updateSearchStatus(hideOptions.value, value);
          isSearch.value = true;
        }
      }

      function saveSetting() {
        if (!props.saveFlag) {
          return;
        }
        const { checkedList, sortedList } = state;
        const data = {
          // 保存的列
          checkedList,
          // 排序后的列
          sortedList,
        };
        $ls.set(props.cacheKey, data);
        defHttp
          .post({
            url: '/tableColumnSetting/tableColumnSetting/add',
            params: { cacheKey: props.cacheKey, cacheJson: JSON.stringify(data) },
          })
          .finally(() => {
            // 保存之后直接关闭
            // popoverVisible.value = false;
          });
      }

      return {
        fieldValue,
        onSearch,
        t,
        ...toRefs(state),
        popoverVisible,
        plainOptions,
        saveSetting,
        prefixCls,
        columnListRef,
        visibleDrap1,
        invisibleDrap1,
        handleVisibleChange,
        checkIndex,
        checkSelect,
        getPopupContainer,
        showSearch,
        showOptions,
        hideOptions,
        isSearch,
        originOptions,
        scroll,
        titleText,
        activeKey,
        changeTable,
      };
    },
  });
</script>
<style lang="less">
  @prefix-cls: ~'@{namespace}-basic-column-setting';

  .table-column-drag-icon {
    // margin: 0 5px;
    cursor: move;
  }

  .@{prefix-cls} {
    &__popover-title {
      position: relative;
      display: flex;
      align-items: center;
      justify-content: space-between;
    }

    /* 卡片底部样式 */

    &__popover-footer {
      position: relative;
      top: 7px;
      text-align: right;
      padding: 4px 0 0;
      border-top: 1px solid @border-color;

      .ant-btn {
        margin-right: 6px;
      }
    }

    &__check-item {
      display: flex;
      align-items: center;
      min-width: 100%;
      padding: 4px 16px 8px 0;

      .ant-checkbox-wrapper {
        width: 100%;

        &:hover {
          color: @primary-color;
        }
      }
    }

    &__fixed-left,
    &__fixed-right {
      color: rgba(0, 0, 0, 0.45);
      cursor: pointer;

      &.active,
      &:hover {
        color: @primary-color;
      }

      &.disabled {
        color: @disabled-color;
        cursor: not-allowed;
      }
    }

    &__fixed-right {
      transform: rotate(180deg);
    }

    &__cloumn-list {
      svg {
        width: 1em !important;
        height: 1em !important;
      }

      .ant-popover-inner-content {
        // max-height: 360px;
        padding-right: 0;
        padding-left: 0;
        // overflow: auto;
      }

      .ant-checkbox-group {
        width: 100%;
        min-width: 260px;
        // flex-wrap: wrap;
      }

      .scrollbar {
        // min-height: 220px;
        max-height: 60vh;
        min-height: 200px;
        overflow-y: scroll;
      }
    }
  }
</style>

<style lang="less" scoped>
  .visibleItem,
  .invisibleItem {
    &:hover {
      @apply siem-gradient-bg;
      cursor: pointer;
      border-radius: 8px;
    }

    &:active {
      cursor: pointer;
      @apply siem-gradient-bg;
    }
  }

  .invisibleItem {
    color: rgba(255, 255, 255, 0.2);
  }
</style>
