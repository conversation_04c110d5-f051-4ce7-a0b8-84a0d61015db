<template>
  <div class="modal-top padding16">
    <h2>
      <span @click="back" style="cursor: pointer">
        <Icon icon="ant-design:left-outlined" style="margin-right: 5px; cursor: pointer" />
        {{ eventViewInfo?.eventName }}
      </span>
      <div style="position: absolute; right: 20px; top: 15px">
        <a-button v-if="hasPermission('risk:assign_other') && record?.eventStatus !== 2">
          <RiskEventAssign :record="record" type="assign_other" :reload="reload" />
        </a-button>
        <a-button v-else-if="hasPermission('risk:assign_self') && record?.eventStatus !== 2">
          <RiskEventAssign :record="record" type="assign_self" />
        </a-button>
        <a-button @click="showWhite = true" style="margin-right: 5px">{{ t('routes.WhitelistVO.addWhitelist') }}</a-button>
        <a-button style="margin-right: 5px" v-if="hasHunting('/threatHunting/ThreatHuntingIndex')" @click="toThreatHunting">{{
          tp('Hunting')
        }}</a-button>
        <a-dropdown :trigger="['click']" v-if="hasPermission('investigation:add') || hasPermission('investigation:join')">
          <a-button>{{ t('common.Investigation') }}</a-button>
          <template #overlay>
            <a-menu>
              <a-menu-item key="0" @click="showAddInvestigation" v-if="hasPermission('investigation:add')">
                <Icon icon="ant-design:plus-outlined" />
                {{ t('routes.riskLogs.add') }}
              </a-menu-item>
              <a-menu-divider />
              <a-menu-item v-for="item in investigationData" :key="item.id" @click="addInvestigation(item)">
                <span>{{ item['investigation'] }}</span>
              </a-menu-item>
              <a-menu-divider />
              <a-menu-item key="1" @click="showMoreInvestigation">
                <span>{{ t('routes.riskLogs.addMore') }}</span>
              </a-menu-item>
            </a-menu>
          </template>
        </a-dropdown>
      </div>
    </h2>
    <a-row class="background" style="margin-bottom: 10px">
      <a-col :span="3">
        <div>{{ tp('LogSource') }}</div>
        <div>{{ eventViewInfo?.fromIp }}</div>
      </a-col>
      <a-col :span="3">
        <div>{{ tp('Severity') }}</div>
        <div><Severity :value="eventViewInfo?.severity" /></div>
      </a-col>
      <a-col :span="3">
        <div>{{ tp('SourceIP') }}</div>
        <div>{{ eventViewInfo?.srcIp }}</div>
      </a-col>
      <a-col :span="3">
        <div>{{ tp('DestinationIP') }}</div>
        <div>{{ eventViewInfo?.dstIp }}</div>
      </a-col>
    </a-row>
    <a-row class="whitelist" v-if="showWhite">
      <a-col>{{ t('routes.WhitelistVO.addWhitelist') }}</a-col>
      <a-col class="r_ft12" style="margin: 8px 0">
        {{ tp('tip') }}
      </a-col>
      <a-col class="border_div">
        <div class="wh_filter">
          <div v-for="(item, index) in whitelistData.ruleJson.filter" :key="'whf_' + index">
            <div v-if="index > 0" class="r_ft12 andText"> And </div>
            <div class="field_where_div">
              <div style="width: 150px">
                <a-select
                  v-model:value="item.field_id"
                  style="width: 100%"
                  :options="fieldDataList"
                  class="rule_required"
                  @blur="checkedElRuleRequired"
                  @change="fieldChange(item)"
                  :showSearch="true"
                  optionFilterProp="label"
                />
              </div>
              <div style="width: calc(100% - 186px); display: flex; flex-flow: row wrap">
                <div class="where_div" v-for="(item2, index2) in item.filter_list" :key="'or_' + index2">
                  <div style="display: flex">
                    <div class="or_div"> or </div>
                    <div style="width: 120px; margin-right: 8px">
                      <a-select v-model:value="item2.compare" style="width: 100%">
                        <a-select-option :value="1">{{ t('common.compare.equal') }}</a-select-option>
                        <a-select-option :value="2">{{ t('common.compare.notEqual') }}</a-select-option>
                        <a-select-option :value="8" v-if="fieldTypeMap[item.field_id] == 'string'">
                          {{ t('common.compare.contains') }}
                        </a-select-option>
                        <a-select-option :value="9" v-if="fieldTypeMap[item.field_id] == 'string'">
                          {{ t('common.compare.notContains') }}
                        </a-select-option>
                        <a-select-option :value="10" v-if="fieldTypeMap[item.field_id] == 'string'">
                          {{ t('common.compare.startsWith') }}
                        </a-select-option>
                        <a-select-option :value="11" v-if="fieldTypeMap[item.field_id] == 'string'">
                          {{ t('common.compare.endsWith') }}
                        </a-select-option>
                        <a-select-option :value="3" v-if="fieldTypeMap[item.field_id] == 'number'">
                          {{ t('common.compare.larger') }}
                        </a-select-option>
                        <a-select-option :value="4" v-if="fieldTypeMap[item.field_id] == 'number'">
                          {{ t('common.compare.smaller') }}
                        </a-select-option>
                      </a-select>
                    </div>
                    <div style="width: 150px">
                      <div v-if="fieldTypeMap[item.field_id] == 'ipv4'">
                        <a-input v-model:value="item2.val" class="rule_ipv4" @focusout="checkedElRuleIp" />
                      </div>
                      <div v-else-if="fieldTypeMap[item.field_id] == 'number'">
                        <a-input-number v-model:value="item2.val" class="rule_required" style="width: 100%" @focusout="checkedElRuleRequired" />
                      </div>
                      <div v-else>
                        <a-input v-model:value="item2.val" />
                      </div>
                    </div>
                  </div>
                  <img src="../../../assets/images/del.png" @click="delOr(item, index2)" v-if="index > 0 || index2 > 0" class="delImg" />
                </div>
                <div style="line-height: 32px; height: 40px">
                  <img src="../../../assets/images/add.png" @click="addOr(item)" class="addImg" />
                </div>
              </div>
              <div class="del_where_div" v-if="index > 0">
                <Icon icon="ant-design:delete-outlined" @click="delWhere(index)" />
              </div>
            </div>
          </div>
          <a-button type="primary" ghost @click="addWhere" style="margin-top: 8px">
            <Icon icon="ant-design:plus-outlined" />
            {{ t('common.add') }}
          </a-button>
        </div>
      </a-col>
      <a-col style="margin-bottom: 8px">
        <a-button @click="showWhite = false" style="margin-right: 5px">{{ t('common.cancelText') }}</a-button>
        <a-button type="primary" @click="saveWhite">{{ t('common.saveText') }}</a-button>
      </a-col>
    </a-row>
    <a-row v-if="type == '2'" class="background" style="margin-bottom: 10px">
      <a-col :span="24">
        <a-form name="form" :label-col="{ span: 24 }" :wrapper-col="{ span: 24 }" autocomplete="off">
          <a-row :gutter="[30, 15]">
            <a-col :span="6" class="border-right">
              <a-form-item :label="tp('DetectedBy')">
                <span @click="showDetectionRule" style="cursor: pointer">{{ eventViewInfo?.eventName }}</span>
              </a-form-item>
            </a-col>
            <a-col :span="12" class="border-right">
              <a-form-item :label="tp('Description')">
                {{ eventDetectionRuleInfo.description }}
              </a-form-item>
            </a-col>
            <a-col :span="6">
              <a-form-item :label="tp('Tags')">
                <div v-if="eventDetectionRuleInfo && eventDetectionRuleInfo.tags && eventDetectionRuleInfo.tags.length > 0">
                  <a-tag v-for="(item, index) in eventDetectionRuleInfo.tags" :key="index">
                    {{ item }}
                  </a-tag>
                </div>
              </a-form-item>
            </a-col>
          </a-row>
        </a-form>
      </a-col>
    </a-row>
    <div v-if="type == '1'">
      <ThreatHuntingModal :id="eventId" :param="{ id: eventId }" :columns="columnsLog" />
    </div>
    <div v-if="type == '2'">
      <ThreatHuntingHostModal :id="eventId" :param="{ id: eventId }" />
    </div>

    <InvestigationListModal ref="registerRiskLogsModal" :eventId="LogId" :socTenantId="eventViewInfo.socTenantId" :type="eventViewInfo.type" />
    <a-modal v-model:visible="inveVisible" :title="t('common.confirm')" @ok="saveToInve" :maskClosable="false">
      <a-row style="padding: 0 24px">
        <a-col :span="24">
          {{ t('routes.riskLogs.addInvestigationPrompt') }}
        </a-col>
        <a-col :span="24">
          <a-form class="antd-modal-form" autocomplete="off" :layout="formLayout">
            <a-form-item :label="t('routes.WhitelistVO.Conclusion')">
              <a-textarea v-model:value="conclusion" />
            </a-form-item>
          </a-form>
        </a-col>
      </a-row>
    </a-modal>

    <a-modal
      v-model:visible="whiteVisible"
      :title="t('routes.WhitelistVO.addWhitelist')"
      @ok="saveWhiteInfo"
      :confirm-loading="whiteLoading"
      :maskClosable="false"
    >
      <a-form :model="whitelistData" autocomplete="off" :layout="formLayout" ref="whiteForm">
        <a-row style="padding: 0 24px">
          <a-col span="24" style="padding: 16px">
            <a-form-item :label="t('routes.WhitelistVO.name')" name="name" :rules="[{ required: true, message: t('common.inputText') }]">
              <a-input v-model:value="whitelistData.name" maxlength="64" />
            </a-form-item>
            <a-form-item :label="t('routes.WhitelistVO.comment')" name="comment">
              <a-textarea v-model:value="whitelistData.comment" maxlength="512" />
            </a-form-item>
            <a-form-item
              :label="t('routes.WhitelistVO.ruleScope')"
              name="ruleScope"
              v-if="isAdministrator()"
              :rules="[{ required: true, message: t('common.chooseText') }]"
            >
              <a-radio-group v-model:value="whitelistData.ruleScope" name="ruleScope" @change="ruleScopeChange">
                <a-radio :value="1">{{ t('common.exclusiveRule') }}</a-radio>
                <a-radio :value="2">{{ t('common.shareableRule') }}</a-radio>
              </a-radio-group>
            </a-form-item>
            <a-form-item
              :label="t('common.tenant')"
              name="tenant"
              v-if="isAdministrator()"
              :rules="[{ required: true, message: t('common.chooseText') }]"
            >
              <!--              <JSelectMultiple v-model:value="whitelistData.tenant" allowClear-->
              <!--                               dictCode="tenantActiveDict"-->
              <!--                               v-if="whitelistData.ruleScope === 2"/>-->
              <TenantSelectList v-model:value="whitelistData.tenant" v-if="whitelistData.ruleScope === 2" />
              <JSearchSelect v-model:value="whitelistData.tenant" allowClear :placeholder="t('common.chooseText')" dict="tenantActiveDict" v-else />
            </a-form-item>
            <div class="r_ft12" style="margin-bottom: 8px">
              {{ t('routes.WhitelistVO.tip') }}
            </div>
            <a-form-item :label="t('routes.WhitelistVO.riskModule')" name="riskModule" :rules="[{ required: true, message: t('common.noEmptyTip') }]">
              <a-select v-model:value="whitelistData.riskModule">
                <a-select-option value="1">{{ t('routes.WhitelistVO.Risk_Event') }}</a-select-option>
              </a-select>
            </a-form-item>
            <a-form-item name="riskModuleType" :rules="[{ required: true, message: t('common.noEmptyTip') }]">
              <a-select v-model:value="whitelistData.riskModuleType">
                <a-select-option value="2" v-if="whitelistData.riskModuleType == '2'">
                  {{ t('routes.WhitelistVO.RiskByEndpoint') }}
                </a-select-option>
                <a-select-option value="1" v-if="whitelistData.riskModuleType == '1'">
                  {{ t('routes.WhitelistVO.RiskBySecurityDevice') }}
                </a-select-option>
              </a-select>
            </a-form-item>
            <div v-if="whitelistData.riskModuleType == '2'">
              <div class="r_ft12" style="margin-bottom: 8px">
                {{ t('routes.WhitelistVO.tip2') }}
              </div>
              <a-form-item :label="t('routes.WhitelistVO.DetectionRule')" name="rule">
                <a-select v-model:value="whitelistData.ruleList" mode="multiple" :options="ruleDataList" optionFilterProp="label" />
              </a-form-item>
            </div>
          </a-col>
        </a-row>
      </a-form>
    </a-modal>
    <DetectionRuleModal @register="registerModal" />
  </div>
</template>

<script lang="ts" setup>
import {useRoute, useRouter} from 'vue-router';
import {reactive, ref, toRaw} from 'vue';
import {
  queryById as securityById
} from '/@/views/aggregationriskeventsecurity/AggregationRiskEventSecurity.api';
import {
  queryById as hostById
} from '/@/views/aggregationriskeventhost/AggregationRiskEventHost.api';
import {queryById} from '/@/views/aggregationRiskEventView/RiskEventView.api';
import {detectionRuleListRequest} from '/@/views/detection/DetectionRule.api';
import ThreatHuntingModal
  from '/@/views/aggregationriskeventsecurity/modules/ThreatHuntingModal.vue';
import ThreatHuntingHostModal
  from '/@/views/aggregationriskeventhost/modules/ThreatHuntingHostModal.vue';
import {loadInvestigationTop5} from '/@/views/investigation/InvestigationVO.api';
import {useI18n} from '/@/hooks/web/useI18n';
import InvestigationListModal from '/@/views/investigation/modules/InvestigationListModal.vue';
import {useUserStore} from '/@/store/modules/user';
import {saveOrUpdate2} from '/@/views/risk/InvestigationRiskEventlogs.api';
import {formLayout} from '/@/settings/designSetting';
import DetectionRuleModal from '/@/views/detection/modules/DetectionRuleModal.vue';
import {useModal} from '/@/components/Modal';
import {checkedElRuleIp, checkedElRuleRequired, checkedRule} from '/@/utils/checkedRule';
import {getTabFieldList} from '/@/utils/ckTable';
import {loadDetectionRuleCodeList, saveOrUpdate} from '/@/views/whitelist/WhitelistVO.api';
import {hasHunting, isAdministrator} from '/@/utils/auth';
import JSearchSelect from '/@/components/Form/src/jeecg/components/JSearchSelect.vue';
import TenantSelectList from '/@/components/Tenant/TenantSelectList/TenantSelectList.vue';
import {usePermission} from '/@/hooks/web/usePermission';
import Severity from '/@/components/Severity/Severity.vue';
import RiskEventAssign
  from '/@/views/aggregationRiskEventView/component/riskEvent/RiskEventAssign.vue';

const isModal = ref(false);
  const props = defineProps({
    pId: String,
    pType: String,
    closeDrawer: Function,
    record: Object as any,
    reload: Function,
  });

  const { hasPermission } = usePermission();

  const { t } = useI18n();
  const tp = (name) => {
    return t('routes.riskLogs.' + name);
  };
  const router = useRouter();
  const route = useRoute();
  const type = ref<any>(route.query.type);
  const eventId = ref<string>(route.query.eventId as string);
  const tabIndexStr = ref<string>(route.query.tab as string);

  console.log('props', props);
  if (props.pId) {
    eventId.value = props.pId;
    type.value = props.pType;
    isModal.value = true;
  }
  console.log(eventId.value, type.value);
  const eventViewInfo = ref<any>({});
  const eventInfo = ref<any>({});
  const eventDetectionRuleInfo = ref<any>({});
  const whiteVisible = ref(false);
  const whiteLoading = ref(false);
  const whitelistData = reactive<any>({
    name: '',
    comment: '',
    riskModule: '1',
    riskModuleType: '',
    ruleScope: 1,
    tenant: '',
    ruleList: [],
    ruleJson: {
      filter: [
        {
          field_id: '',
          filter_list: [
            {
              compare: 1,
              val: '',
            },
          ],
        },
      ],
    },
  });
  const ruleDataList = ref<any[]>([]);
  const fieldTypeMap = reactive<any>({});
  const fieldDataList = reactive<any[]>([]);
  const showWhite = ref(false);

  const [registerModal, { openModal }] = useModal();

  const columnsLog = [
    { title: t('common.InsertDate'), dataIndex: 'ck_enter_date', ellipsis: true, width: 150 },
    { title: t('common.EventType'), dataIndex: 'event_type', ellipsis: true, width: 150 },
    { title: t('common.EventName'), dataIndex: 'event_name', ellipsis: true, width: 150 },
    { title: t('common.SrcIP'), dataIndex: 'src_ip', ellipsis: true, width: 150 },
    { title: t('common.DstIP'), dataIndex: 'dst_ip', ellipsis: true, width: 150 },
    { title: t('common.SrcPort'), dataIndex: 'src_port', ellipsis: true, width: 90 },
    { title: t('common.DstPort'), dataIndex: 'dst_port', ellipsis: true, width: 90 },
    { title: t('common.ProxyIP'), dataIndex: 'proxy_ip', ellipsis: true, width: 150 },
    {
      title: t('common.EventLevel'),
      dataIndex: 'event_level',
      ellipsis: true,
      width: 120,
      slots: { customRender: 'eventLevel' },
    },
  ];

  init();

  function init() {
    queryById({ id: eventId.value }).then((data) => {
      eventViewInfo.value = data;
      whitelistData.tenant = data.socTenantId;
      loadInvestigation();
    });

    if (type.value == '1') {
      securityById({ id: eventId.value }).then((data) => {
        eventInfo.value = data;
      });
    } else if (type.value == '2') {
      hostById({ id: eventId.value }).then((data) => {
        eventInfo.value = data;
        detectionRuleListRequest({ id: data.detectionRuleId }).then((res) => {
          eventDetectionRuleInfo.value = res.records[0] ?? {};
          whitelistData.ruleList = [res.records[0].id];
        });
      });
    }
    whitelistData.riskModuleType = toRaw(type.value);
    let data: any = getTabFieldList("0");
    for (let i = 0; i < data.length; i++) {
      if (data[i].fieldValue == 'ck_enter_date') {
        continue;
      }
      fieldDataList.push({
        label: data[i].fieldName,
        value: data[i].classFieldName,
      });
      fieldTypeMap[data[i].classFieldName] = data[i].fieldType;
    }
  }

  function toThreatHunting() {
    router.push({
      path: '/threatHunting/ThreatHuntingIndex',
      query: { val: type.value, eventName: eventViewInfo.value.eventName, eventId: eventId.value },
    });
  }

  const investigationData = ref<any[]>([]);
  const LogId = ref<string>(''); //记录当前点击的日志id
  const LogType = ref<string>(''); //记录当前点击的日志type
  const loadInvestigation = () => {
    loadInvestigationTop5({ socTenantIds: [eventViewInfo.value.socTenantId] }).then((result) => {
      console.log(result);
      let list = result.records;
      investigationData.value = list;
    });
  };

  function addInvestigation(data): void {
    console.log(data);
    let id = '';
    id = eventViewInfo.value.eventId;
    if (eventViewInfo.value.type == '1') {
      LogType.value = '1';
    } else if (eventViewInfo.value.type == '2') {
      LogType.value = '2';
    }
    inveId = data.id;
    LogId.value = id;
    inveVisible.value = true;
  }

  const inveVisible = ref(false);
  let inveId = '';
  const conclusion = ref('');
  const userStore = useUserStore();
  const saveToInve = () => {
    if (addInveFlag) {
      addInveFlag = false;
      let param = {
        eventId: LogId.value,
        conclusion: conclusion.value,
        conclusionBy: userStore.userInfo?.username,
        socTenantId: eventViewInfo.value.socTenantId,
        type: eventViewInfo.value.type,
      };
      sessionStorage.setItem('addInvestigationParam', JSON.stringify(param));
      router.push({
        path: '/investigation/modules/InvestigationNewModal',
      });
      return;
    }

    saveOrUpdate2(
      {
        investigationId: inveId,
        eventId: LogId.value,
        conclusion: conclusion.value,
        conclusionBy: userStore.userInfo?.username,
        typeStr: LogType.value,
      },
      false
    ).then(() => {
      inveVisible.value = false;
      conclusion.value = '';
    });
  };
  const registerRiskLogsModal = ref();

  function showMoreInvestigation() {
    let id = '';
    id = eventViewInfo.value.eventId;
    if (eventViewInfo.value.type == '1') {
      LogType.value = '1';
    } else if (eventViewInfo.value.type == '2') {
      LogType.value = '2';
    }
    LogId.value = id;
    registerRiskLogsModal.value.visible = true;
  }

  let addInveFlag = false;
  const showAddInvestigation = () => {
    LogId.value = eventViewInfo.value.eventId;
    addInveFlag = true;
    inveVisible.value = true;
  };

  function showDetectionRule() {
    let record = eventDetectionRuleInfo.value;
    openModal(true, {
      record,
      isUpdate: true,
      showFooter: true,
    });
  }

  function back() {
    if (isModal.value) {
      props.closeDrawer && props.closeDrawer();
      return;
    }
    let json = sessionStorage.getItem('inRiskEvent_1');
    if (json) {
      sessionStorage.removeItem('inRiskEvent_1');
      sessionStorage.setItem('inRiskEvent_2', json);
    }
    if (tabIndexStr.value) {
      sessionStorage.setItem('tabIndexStr', tabIndexStr.value);
    }
    router.go(-1);
  }

  function fieldChange(data) {
    console.log(data);
    data.filter_list = [
      {
        compare: 1,
        val: '',
      },
    ];
  }

  function addOr(item) {
    console.log(item);
    item.filter_list.push({
      compare: 1,
      val: '',
    });
  }

  function delOr(item, index) {
    item.filter_list.splice(index, 1);
  }

  function addWhere() {
    whitelistData.ruleJson.filter.push({
      field_id: '',
      filter_list: [
        {
          compare: 1,
          val: '',
        },
      ],
    });
  }

  function delWhere(index) {
    whitelistData.ruleJson.filter.splice(index, 1);
  }

  function saveWhite() {
    let checked = checkedRule('rule_required,rule_ipv4');
    if (!checked) {
      return;
    }
    whiteVisible.value = true;
  }

  const whiteForm = ref();

  function saveWhiteInfo() {
    whiteLoading.value = true;
    whiteForm.value
      .validate()
      .then(() => {
        console.log(toRaw(whitelistData));
        let data: any = {
          name: whitelistData.name,
          comment: whitelistData.comment,
          riskModule: whitelistData.riskModule,
          riskModuleType: whitelistData.riskModuleType,
          ruleScope: whitelistData.ruleScope,
          tenant: whitelistData.tenant,
          ruleList: whitelistData.ruleList,
          ruleJson: JSON.stringify(whitelistData.ruleJson),
        };
        //提交表单
        saveOrUpdate(data, false).then(() => {
          //关闭弹窗
          whiteVisible.value = false;
          whiteLoading.value = false;
          //刷新列表
        });
      })
      .catch((err) => {
        console.log('error', err);
        whiteLoading.value = false;
      });
  }

  loadDetectionRuleCodeList().then((data) => {
    for (let i in data) {
      ruleDataList.value.push({
        label: data[i].title,
        value: data[i].id,
      });
    }
  });

  function ruleScopeChange() {
    whitelistData.tenant = '';
  }
</script>

<style lang="less" scoped>
  .background {
    background-color: @dark-bg2;
    padding: 10px 15px;
  }

  :deep(.ant-tag) {
    margin: 5px;
  }

  .r_ft12 {
    /* 12 - Regular */

    font-size: 12px;
    font-weight: normal;
    line-height: 16px;
    letter-spacing: 0px;
    /* Font/白0.4 */
    color: @font-color-4;
  }

  .whitelist {
    .field_where_div {
      display: flex;
      flex-flow: row wrap;
      background: rgba(48, 140, 255, 0.102);
      padding: 8px 0px 0 16px;
    }

    .where_div {
      width: 255px;
      position: relative;
      margin-bottom: 8px;

      .delImg {
        position: absolute;
        right: -8px;
        top: -8px;
        cursor: pointer;
        display: none;
      }
    }

    .where_div:hover {
      .delImg {
        display: block;
      }
    }

    .or_div {
      width: 30px;
      text-align: center;
      line-height: 32px;
    }

    .addImg {
      cursor: pointer;
      position: relative;
      margin-left: 10px;
    }

    .andText {
      margin: 8px 16px;
    }

    .del_where_div {
      width: 36px;
      text-align: center;
      padding: 10px;
      background: rgba(48, 140, 255, 0.102);
      margin-top: -8px;
      cursor: pointer;
    }

    .border_div {
      border: 1px solid @border-color;
      padding: 8px;
      margin-bottom: 8px;
    }
  }
</style>
