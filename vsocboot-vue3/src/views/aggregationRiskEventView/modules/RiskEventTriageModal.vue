<template>
  <a-modal v-model:visible="visible" @ok="handleOk" :title="t('common.triageBtn')" :width="300">
    <div class="padding16 triage_div_m">
      <a-form ref="formRef" :model="model" :layout="formLayout">
        <a-form-item name="triageStatus" :rules="[{ required: true }]" style="margin-bottom: 0!important;">
          <a-radio-group v-model:value="model.triageStatus" name="triageStatus">
            <a-radio value="1" :style="radioStyle">
              <div class="ax-label ax-label-red">
                <i class="ax-com-Danger soc"></i>
                {{ t('routes.RiskEventLogView.true') }}
              </div>
            </a-radio>
            <a-radio value="3" :style="radioStyle">
              <div class="ax-label ax-label-yellow">
                <Icon icon="ant-design:question-circle-outlined"></Icon>
                {{ t('routes.RiskEventLogView.other') }}
              </div>
            </a-radio>
            <a-radio value="2" :style="radioStyle">
              <div class="ax-label ax-label-cyan">
                <i class="ax-com-Success soc"></i>
                {{ t('routes.RiskEventLogView.false') }}
              </div>
            </a-radio>
          </a-radio-group>
        </a-form-item>
      </a-form>
    </div>
  </a-modal>
</template>

<script lang="ts" setup>
import {ref, defineEmits, toRaw, reactive} from 'vue';
import {useI18n} from '/@/hooks/web/useI18n';
import {formLayout} from '/@/settings/designSetting';

const emit = defineEmits(['ok']);
const model = ref({
  triageStatus: null,
  eventId: '',
  type: '',
});
const formRef = ref();
const {t} = useI18n();
const visible = ref(false);

const radioStyle = reactive({
  display: 'flex',
  height: '30px',
  lineHeight: '30px',
});

function handleOk() {
  formRef.value
    .validate()
    .then(() => {
      console.log(toRaw(model.value));
      visible.value = false;
      emit('ok', toRaw(model.value));
    })
    .catch((err) => {
      console.log('error', err);
    });
}

function open(data) {
  console.log(data);
  model.value.eventId = data.eventId;
  model.value.type = data.type;
  model.value.triageStatus = null;
  visible.value = true;
}

defineExpose({
  open,
});
</script>

<style scoped lang="less">
.triage_div_m {
  :deep(.ant-radio) {
    top: 0 !important;
  }

  .true_radio {
    color: @color-red;
  }

  .false_radio {
    color: @color-green;
  }

  .other_radio {
    color: @color-yellow;
  }
}


</style>
