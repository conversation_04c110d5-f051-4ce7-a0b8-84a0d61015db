<template>
  <div @click.stop="assignUser(records)" v-if="type == 'assign_other'">
    {{ t('common.assignBtn') }}
  </div>
  <div @click.stop="assignSelf(records)" v-else-if="type == 'assign_self'">
    {{ t('common.assignBtn') }}
  </div>

  <UserTableModal @register="registerSelUserModal" @select="assignSelectUserOk" />
</template>
<script setup lang="ts">
  import { useI18n } from '/@/hooks/web/useI18n';
  import UserTableModal from '/@/components/Form/src/jeecg/components/userSelect/UserTableModal.vue';
  import { useModal } from '/@/components/Modal';
  import { toRaw } from 'vue';
  import { useUserStore } from '/@/store/modules/user';
  import { message, Modal } from 'ant-design-vue';
  import { batchTenantAssign } from '/@/views/badactors/BadActors.api';
  const emit = defineEmits(['cleanSelect']);
  const { t } = useI18n();
  const userStore = useUserStore();
  const props = defineProps({
    type: String,
    records: Array as any,
    reload: Function,
  });
  console.log(props);

  //Assign 分配代码开始

  const [registerSelUserModal, assignUserModal] = useModal();
  let assignEventData = {};

  /**
   * 弹出选择用户列表
   * @param record
   */
  function assignUser(records) {
    console.log(records);
    if (records.filter((e) => e.status == 1)?.length > 0) {
      return message.error(t('routes.badActors.pleasechoose'));
    }
    let hasMultipleSocTenantIds = new Set(records.map((e) => e.socTenantId)).size > 1;
    if (hasMultipleSocTenantIds) {
      return message.error(t('routes.badActors.pleasechoosesame'));
    }
    assignEventData = toRaw(records);
    let record = records[0];
    assignUserModal.openModal(true, { record });
  }

  /**
   * 分配给自己
   * @param record
   */
  function assignSelf(record) {
    assignEventData = toRaw(record);
    console.log(userStore.getUserInfo);
    Modal.confirm({
      title: t('common.assignSelfTitle'),
      content: t('common.assignSelfContent'),
      onOk: () => {
        let value = [userStore.getUserInfo.id];
        assignSelectUserOk(value);
      },
    });
  }

  /**
   * 选择用户确定
   * @param value
   */
  function assignSelectUserOk(value) {
    if (value && value[0]) {
      if (value && value[0]) {
        // 初始化 riskEventViews 数组
        let events = [] as any;
        // 遍历 props.records 数组
        props.records.forEach((e) => {
          // 直接创建新对象并添加到 riskEventViews 数组中
          events.push({
            id: e.id,
            owner: value[0],
          });
        });
        //保存分配的用户
        batchTenantAssign(events).then(() => {
          emit('cleanSelect');
          props.reload && props.reload();
        });
      }
    }
  }

  //Assign 分配代码结束
</script>

<style scoped lang="less"></style>
