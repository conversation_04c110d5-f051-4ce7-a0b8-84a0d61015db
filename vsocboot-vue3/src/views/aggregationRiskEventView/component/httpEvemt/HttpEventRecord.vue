<template>
  <div @click="showRecord(record)">
    {{ t('common.recordBtn') }}
  </div>
  <RiskEventRecordList ref="recordModalRef"/>
</template>
<script setup lang="ts">
import {useI18n} from "/@/hooks/web/useI18n";
import RiskEventRecordList from "/@/views/aggregationRiskEventView/modules/RiskEventRecordList.vue";
import {ref} from "vue";

const {t} = useI18n();
const props = defineProps({
  record: {
    type: Object as PropType<{
      id: string;
    }>,
    required: true
  },
});
console.log(props)


//record开始

const recordModalRef = ref()

/**
 * 弹出record页
 * @param record
 */
function showRecord(record) {
  const data = {
    eventType: 7,
    eventId: record.id
  }
  recordModalRef.value.open(data)
}


//record结束
</script>


<style scoped lang="less">

</style>
