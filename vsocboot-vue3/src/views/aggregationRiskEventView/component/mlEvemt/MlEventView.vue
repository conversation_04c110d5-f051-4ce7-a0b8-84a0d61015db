<template>
  <span v-if="!icon" :style="style" @click="handleDetail(record)">{{ t('common.viewText') }}</span>
  <div  v-else  class="ax-icon-button"  @click="handleDetail(record)">
    <span class="soc ax-com-See ax-icon"></span>
  </div>
  <MlEventView @register="registerMlEventView" :reload="reload"/>
</template>
<script setup lang="ts">
import {useI18n} from "/@/hooks/web/useI18n";
import {useDrawer} from "/@/components/Drawer";
import MlEventView from "/@/views/mlView/modules/MlEventView.vue";

const {t} = useI18n();


const props = defineProps({
  record: {
    type: Object as PropType<{
      ruleType: string;
      id: string;
    }>,
    required: true
  },
  style: Object,
  icon: String,
  reload:Function
});
console.log(props)


const [registerMlEventView, {openDrawer: openMlEventView}] = useDrawer();

/**
 * 详情
 */
function handleDetail(record: Recordable) {
  sessionStorage.setItem("MlEventModalId", record.id)
  openMlEventView(true, {
    ruleType: record.ruleType,
    record: record
  });

}
</script>


<style scoped lang="less">

</style>
