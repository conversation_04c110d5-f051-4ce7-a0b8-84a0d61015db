<template>
  <div @click="showTriage(record)">
    {{ t('common.triageBtn') }}
  </div>

  <RiskEventTriageModal ref="triageModalRef" @ok="triageResult"/>
</template>
<script setup lang="ts">
import {useI18n} from "/@/hooks/web/useI18n";
import {triage} from "/@/views/aggregationRiskEventView/RiskEventView.api";
import {ref} from "vue";
import RiskEventTriageModal
  from "/@/views/aggregationRiskEventView/modules/RiskEventTriageModal.vue";

const emits = defineEmits(['ok']);
const {t} = useI18n();
const props = defineProps({
  record: {
    type: Object as PropType<{
      type: string;
      eventId: string;
      socTenantId: string;
    }>,
    required: true
  },
  reload: Function
});
console.log(props)
//triage 代码开始

const triageModalRef = ref();

function showTriage(record) {
  triageModalRef.value.open(record);
}

function triageResult(data) {
  triage({
    triageStatus: data.triageStatus,
    eventId: data.eventId,
    type: data.type,
  }).then(() => {
    emits('ok', data.triageStatus);
    props.reload && props.reload();
  });
}

//triage 代码结束
</script>


<style scoped lang="less">

</style>
