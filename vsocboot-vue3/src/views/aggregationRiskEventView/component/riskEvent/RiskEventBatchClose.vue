<template>
  <div v-if="type == 'close'" @click="closeEvent(records)">
    {{ t('common.closeBtn') }}
  </div>
  <div v-else-if="type == 'open'" @click="openEvent(records)">
    {{ t('common.openBtn') }}
  </div>
</template>
<script setup lang="ts">
  import { useI18n } from '/@/hooks/web/useI18n';
  import { close, batchClose } from '/@/views/aggregationRiskEventView/RiskEventView.api';
  import { message, Modal } from 'ant-design-vue';
  import { useUserStore } from '/@/store/modules/user';
  const userStore = useUserStore();
  const { t } = useI18n();
  const props = defineProps({
    type: String,
    records: Array as any,
    reload: Function,
  });
  const emit = defineEmits(['cleanSelect','ok']);
  console.log(props);

  // close 代码开始

  function closeEvent(records) {
    //选择的分配人需要为当前用户
    let hasUserInfo = records.filter((e) => e.owner !== userStore.getUserInfo.id).length > 0;
    if (hasUserInfo) {
      return message.error(t('routes.RiskEventLogView.pleasechooseown'));
    }
    //选择的状态需要一致
    let hasMultipleStatus = new Set(records.map((e) => e.eventStatus)).size > 1;
    if (hasMultipleStatus) {
      return message.error(t('routes.RiskEventLogView.pleasechoosestatus'));
    }
    //状态一致并且都为未关闭，则判断是否都有处置结果
    let flag = false;
    if (!hasMultipleStatus) {
      props.records.forEach((e) => {
        if (e.triageStatus == 0) {
          flag = true;
        }
      });
    }
    if(flag){
      return message.error(t('routes.RiskEventLogView.pleasechoosedisposal'));
    }
    if (records[0].eventStatus == 2) {
      return message.error(t('routes.RiskEventLogView.pleasechooseopen'));
    }
    console.log(records);
    let content = '';
    let title = '';
    let status = 1;
    if (records[0].eventStatus == 1) {
      content = t('common.closeConfirmText');
      title = t('common.closeText');
      status = 2;
    }
    Modal.confirm({
      title: title,
      content: content,
      onOk: () => {
        // 初始化 riskEventViews 数组
        let riskEventViews = [] as any;
        // 遍历 props.records 数组
        props.records.forEach((e) => {
          // 直接创建新对象并添加到 riskEventViews 数组中
          riskEventViews.push({
            eventId: e.eventId,
            eventStatus: status,
            type: e.type,
          });
        });
        batchClose(riskEventViews).then(() => {
          emit('cleanSelect');
          emit('ok');
          console.log('props.records', props.records);
          props.reload && props.reload();
        });
      },
    });
  }

  function openEvent(records) {
    //选择的分配人需要为当前用户
    let hasUserInfo = records.filter((e) => e.owner !== userStore.getUserInfo.id).length > 0;
    if (hasUserInfo) {
      return message.error(t('routes.RiskEventLogView.pleasechooseown'));
    }
    //选择的状态需要一致
    let hasMultipleStatus = new Set(records.map((e) => e.eventStatus)).size > 1;
    if (hasMultipleStatus) {
      return message.error(t('routes.RiskEventLogView.pleasechoosestatus'));
    }
    //状态一致并且都为未关闭，则判断是否都有处置结果
    let flag = false;
    if (!hasMultipleStatus) {
      props.records.forEach((e) => {
        if (e.triageStatus == 0) {
          flag = true;
        }
      });
    }
    if(flag){
      return message.error(t('routes.RiskEventLogView.pleasechoosedisposal'));
    }

    if (records[0].eventStatus == 1) {
      return message.error(t('routes.RiskEventLogView.pleasechooseclose'));
    }
    console.log(records);
    let content = '';
    let title = '';
    let status = 1;
    if (records[0].eventStatus == 2) {
      content = t('common.openConfirmText');
      title = t('common.openText');
      status = 1;
    }
    Modal.confirm({
      title: title,
      content: content,
      onOk: () => {
        // 初始化 riskEventViews 数组
        let riskEventViews = [] as any;
        // 遍历 props.records 数组
        props.records.forEach((e) => {
          // 直接创建新对象并添加到 riskEventViews 数组中
          riskEventViews.push({
            eventId: e.eventId,
            eventStatus: status,
            type: e.type,
          });
        });
        batchClose(riskEventViews).then(() => {
          emit('cleanSelect');
          emit('ok');
          props.reload && props.reload();
        });
      },
    });
  }
</script>

<style scoped lang="less"></style>
