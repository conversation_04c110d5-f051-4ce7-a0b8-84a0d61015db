<template>
  <div @click="assignUser(record)" v-if="type == 'assign_other'">
    {{ t('common.assignBtn') }}
  </div>
  <div @click="assignSelf(record)" v-else-if="type == 'assign_self'">
    {{ t('common.assignBtn') }}
  </div>

  <UserTableModal @register="registerSelUserModal" @select="assignSelectUserOk"/>
</template>
<script setup lang="ts">
import {useI18n} from "/@/hooks/web/useI18n";
import UserTableModal from "/@/components/Form/src/jeecg/components/userSelect/UserTableModal.vue";
import {useModal} from "/@/components/Modal";
import {toRaw} from "vue";
import {useUserStore} from "/@/store/modules/user";
import {Modal} from "ant-design-vue";
import {assign} from "/@/views/aggregationRiskEventView/RiskEventView.api";

const emits = defineEmits(['ok']);
const {t} = useI18n();
const userStore = useUserStore();
const props = defineProps({
  type: String,
  record: {
    type: Object as PropType<{
      type: string;
      eventId: string;
      socTenantId: string;
    }>,
    required: true
  },
  reload: Function
});
console.log(props)
//Assign 分配代码开始

const [registerSelUserModal, assignUserModal] = useModal();
let assignEventData: any = {};

/**
 * 弹出选择用户列表
 * @param record
 */
function assignUser(record) {
  console.log(record);
  assignEventData = toRaw(record);
  assignUserModal.openModal(true, {record});
}

/**
 * 分配给自己
 * @param record
 */
function assignSelf(record) {
  assignEventData = toRaw(record);
  console.log(userStore.getUserInfo);
  Modal.confirm({
    title: t('common.assignSelfTitle'),
    content: t('common.assignSelfContent'),
    onOk: () => {
      let value = [userStore.getUserInfo.id];
      let name = [userStore.getUserInfo.username];
      assignSelectUserOk(value, name);
    },
  });
}

/**
 * 选择用户确定
 * @param value
 */
function assignSelectUserOk(value, name) {
  console.log(value, name)
  if (value && value[0]) {
    //保存分配的用户
    assign({
      eventId: assignEventData.eventId,
      type: assignEventData.type,
      owner: value[0],
    }).then(() => {
      emits('ok', name[0]);
      props.reload && props.reload();
    });
  }
}

//Assign 分配代码结束
</script>


<style scoped lang="less">

</style>
