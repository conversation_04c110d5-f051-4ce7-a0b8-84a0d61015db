<template>
  <div class="order-log risk_event_card" v-bind="$attrs">
    <!--引用表格-->
    <BasicTable @register="registerTable" :row-selection="rowSelection" v-model:expandedRowKeys="expandedRowKeys" class="auto-min-height">
      <template #form-formHeader>
        <TableSearchBtn @search="search" ref="tableSearchBtnRef" source="risk" :advancedQueryFlag="advancedQueryFlag" />
      </template>
      <template #form-formFooter>
        <div class="flex flex-row gap-4px items-center">
          <!--  切换到列表 -->
          <a-tooltip placement="top">
            <template #title>
              <span>{{ t('routes.RiskEventLogView.switchTable') }}</span>
            </template>
            <div class="ax-icon-button" @click="emits('switch')">
              <span class="soc ax-com-Formview ax-icon"></span>
            </div>
          </a-tooltip>
          <!--  批量操作 -->
          <a-dropdown :trigger="['click']" :visible="dropdownVisible">
            <template #overlay>
              <a-menu @click="dropdownVisible = false">
                <a-menu-item v-if="hasPermission('risk:assign_other')">
                  <RiskEventBatchAssign :records="selectedRows" type="assign_other" :reload="reload" @cleanSelect="cleanSelect" />
                </a-menu-item>
                <a-menu-item v-else-if="hasPermission('risk:assign_self')">
                  <RiskEventBatchAssign :records="selectedRows" type="assign_self" :reload="reload" @cleanSelect="cleanSelect" />
                </a-menu-item>
                <a-menu-item v-if="hasPermission('risk:triage')">
                  <RiskEventBatchTriage :reload="reload" :records="selectedRows" @cleanSelect="cleanSelect" @ok="closeDown" />
                </a-menu-item>
                <a-menu-item v-if="hasPermission('risk:close')">
                  <RiskEventBatchClose :reload="reload" :records="selectedRows" type="close" @cleanSelect="cleanSelect" @ok="closeDown" />
                </a-menu-item>
                <a-menu-item v-if="hasPermission('risk:close')">
                  <RiskEventBatchClose :reload="reload" :records="selectedRows" type="open" @cleanSelect="cleanSelect" @ok="closeDown" />
                </a-menu-item>
                <a-sub-menu
                  v-if="
                    hasPermission('ticket:useinternel-2') ||
                    hasPermission('ticket:useSS-2') ||
                    hasPermission('ticket:useinternel-1') ||
                    hasPermission('ticket:useIssued-1')
                  "
                  key="sub2"
                  :title="t('common.ticketBtn')"
                >
                  <EventApply :records="selectedRows" eventType="1" @applyOk="reload" />
                </a-sub-menu>
              </a-menu>
            </template>
            <a-tooltip placement="top">
              <template #title>
                <span>{{ t('common.batch') }}</span>
              </template>
              <div class="ax-icon-button" @click.stop="showBatchBtn">
                <span class="soc ax-com-Batch ax-icon"></span>
              </div>
            </a-tooltip>
          </a-dropdown>
          <!--  导出 -->
          <a-tooltip placement="top">
            <template #title>
              <span>{{ t('common.exportText') }}</span>
            </template>
            <div class="ax-icon-button" @click="exportList" v-if="pageSource != 'ticket'">
              <span class="soc ax-Export ax-icon"></span>
            </div>
          </a-tooltip>

          <!--  白名单 -->
          <a-tooltip placement="top">
            <template #title>
              <span>{{ t('routes.RiskEventLogView.whitelist') }}</span>
            </template>
            <div class="ax-icon-button" v-if="pageSource != 'ticket'" @click="showAddWhite">
              <span class="soc ax-com-Submit ax-icon"></span>
            </div>
          </a-tooltip>

          <!--  全部展开-->
          <a-tooltip placement="top" v-if="expandedAllFlag">
            <template #title>
              <span>{{ t('routes.RiskEventLogView.expend') }}</span>
            </template>
            <div class="ax-icon-button"  @click="expandedAll">
              <span class="soc ax-com-Expand ax-icon"></span>
            </div>
          </a-tooltip>

          <!--  全部取消展开-->
          <a-tooltip placement="top" v-else>
            <template #title>
              <span>{{ t('routes.RiskEventLogView.fold') }}</span>
            </template>
            <div class="ax-icon-button"  @click="expandedAll">
              <span class="soc ax-com-Retract ax-icon"></span>
            </div>
          </a-tooltip>
        </div>
      </template>
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'eventStatus'">
          <div :class="getEventLevelClass(record.eventLevel, record.eventStatus)" class="tdDiv eventStatus">
            <span class="soc ax-com-Warning"></span>
            <div v-if="record.eventStatus == '1'">
              {{ t('common.Unclosed') }}
            </div>
            <div v-else-if="record.eventStatus == '2'">
              {{ t('common.Closed') }}
            </div>
          </div>
        </template>
        <template v-if="column.dataIndex === 'eventName'">
          <div class="tdDiv" style="display: flex; gap: 8px; align-items: center">
            <div class="ax-icon-button" v-if="!record.expandFlag" @click="expandInfo(record)">
              <span class="soc ax-com-Arrow-down ax-icon"></span>
            </div>
            <div class="ax-icon-button" v-if="record.expandFlag" @click="closeInfo(record)">
              <span class="soc ax-com-Arrow-up ax-icon"></span>
            </div>
            <div class="logo">
              <img v-if="record.vendorIcon" :src="render.renderUploadImageSrc(record.vendorIcon)" />
              <img v-else src="./image/default.png" />
            </div>
            <div class="ant-table-cell-ellipsis e_n" @click="handleView(record)">
              {{ record[column.dataIndex] }}
            </div>
            <a-divider class="vertical_div" type="vertical" />
          </div>
        </template>
        <template v-if="column.dataIndex === 'socTenantId_dictText'">
          <div class="tdDiv">
            <div class="ant-table-cell-ellipsis" :title="record[column.dataIndex]">
              {{ record[column.dataIndex] }}
            </div>
          </div>
        </template>
        <template v-if="column.dataIndex === 'ruleType'">
          <div class="tdDiv justify-end">
            <div class="ant-table-cell-ellipsis" :title="EventSource[record[column.dataIndex]]">
              {{ EventSource[record[column.dataIndex]] }}
            </div>
            <a-divider class="vertical_div" type="vertical" />
          </div>
        </template>
        <template v-if="column.dataIndex === 'ticketName'">
          <div class="tdDiv justify-center">
            <Ticket :ticketInfo="record" @applyOk="reload" />
            <a-divider class="vertical_div" type="vertical" />
          </div>
        </template>
        <template v-if="column.dataIndex === 'eventLevel'">
          <div class="tdDiv ant-table-cell-ellipsis justify-start gap-4px">
            <Severity2 :value="record[column.dataIndex]"/>
          </div>
        </template>
        <template v-if="column.dataIndex === 'updateTime'">
          <div class="tdDiv justify-start">
            <div class="ant-table-cell-ellipsis" :title="record[column.dataIndex]">
              {{ record[column.dataIndex] }}
            </div>
          </div>
        </template>
        <template v-if="column.dataIndex === 'triageStatus'">
          <div class="tdDiv justify-end">
            <TriageStatus :status="record[column.dataIndex]" />
            <a-divider class="vertical_div" type="vertical" />
          </div>
        </template>
        <template v-if="column.dataIndex === 'owner_dictText'">
          <div class="tdDiv justify-center">
            <TableUser :value="record[column.dataIndex] ?? record['owner']" />
            <a-divider class="vertical_div" type="vertical" />
          </div>
        </template>
      </template>
      <template #action="{ record }">
        <div class="tdDiv" style="display: flex; gap: 4px" v-if="props.pageSource != 'ticket'">
          <RiskEventView :record="record" :reload="reload" icon="ant-design:eye-outlined" />
          <a-dropdown v-if="showMoreBtn()">
            <div class="ax-icon-button" @click.prevent>
              <span class="soc ax-com-More ax-icon"></span>
            </div>
            <template #overlay>
              <a-menu>
                <!--                有新建调查或参与调查权限显示按钮-->
                <!-- <RiskEventInvestigate :record="record"/> -->

                <a-sub-menu v-if="hasTicketPermission()" key="sub2" :title="t('common.ticketBtn')">
                  <EventApply :record="record" eventType="1" @applyOk="reload" />
                </a-sub-menu>

                <a-menu-item v-if="hasHunting('/threatHunting/Index') && pageSource != 'asset'" :title="t('common.ticketBtn')">
                  <span @click="toThreatHunting(record, router, 'event')"> {{ t('routes.riskLogs.hunting') }}</span>
                </a-menu-item>

                <!-- 有权限且没有关闭 -->
                <a-menu-item v-if="hasPermission('risk:assign_other') && record?.eventStatus !== 2">
                  <RiskEventAssign :record="record" type="assign_other" :reload="reload" />
                </a-menu-item>
                <a-menu-item v-else-if="hasPermission('risk:assign_self') && record?.eventStatus !== 2">
                  <RiskEventAssign :record="record" type="assign_self" :reload="reload" />
                </a-menu-item>

                <!-- 有权限且没有关闭且分配给登录人 -->
                <a-menu-item v-if="hasPermission('risk:triage') && record?.eventStatus !== 2 && record?.owner == userStore.getUserInfo.id">
                  <RiskEventTriage :reload="reload" :record="record" @ok="closeDown" />
                </a-menu-item>

                <!-- 有权限且已验证且分配给登录人且没有关闭 -->
                <a-menu-item
                  v-if="
                    hasPermission('risk:close') &&
                    record?.triageStatus !== 0 &&
                    record?.owner == userStore.getUserInfo.id &&
                    record?.eventStatus !== 2
                  "
                >
                  <RiskEventClose :reload="reload" :record="record" type="close" @ok="closeDown" />
                </a-menu-item>
                <a-menu-item v-if="hasPermission('risk:close') && record?.owner == userStore.getUserInfo.id && record?.eventStatus === 2">
                  <RiskEventClose :reload="reload" :record="record" type="open" @ok="closeDown" />
                </a-menu-item>
                <!-- 有权限且已验证且分配给登录人且没有关闭 -->

                <a-menu-item v-if="hasPermission('risk:record')">
                  <RiskEventRecord :record="record" />
                </a-menu-item>
              </a-menu>
            </template>
          </a-dropdown>
        </div>
      </template>
      <template #expandedRowRender="{ record }">
        <div class="expanded_d">
          <div style="display: flex; flex-wrap: wrap; gap: 4px; margin-left: 32px; padding: 8px 0">
            <div v-for="(item, key) in record?.resultJson" :key="key" class="f_d">
              {{ key + ':' + item }}
            </div>
          </div>
          <a-spin :spinning="!!record.summaryLoading">
            <div v-for="(list, key) in record?.summaryJson" :key="key" class="summary_p">
              <div style="width: 120px">
                {{ key }}
              </div>
              <div style="flex: 1; display: flex; flex-wrap: wrap; gap: 4px">
                <template v-for="(item, index) in list" :key="key + item + index">
                  <div  class="f_d" v-if="item">
                    {{ item }}
                  </div>
                </template>
              </div>
            </div>
          </a-spin>
        </div>
      </template>
    </BasicTable>
  </div>

  <WhitelistVOModal @register="registerModal"/>

<!--  <RiskEventViewInfo @register="registerRiskEventModal" :reload="reload" />-->
  <RiskEventModal ref="RiskEventModalRef" :reload="reload"/>
</template>

<script lang="ts" name="aggregationRiskEventView-riskEventLogView" setup>
  //注册table数据
  import { useListPage } from '/@/hooks/system/useListPage';
  import { loadRiskSummaryData, tableList, getExportUrl } from '/@/views/aggregationRiskEventView/RiskEventView.api';
  import { formLayout } from '/@/settings/designSetting';
  import {
    columnsCard, EventSource,
    getCardSearchFormSchema,
    handleAggFieldsResult,
    hasTicketPermission,
    showMoreBtn,
    toThreatHunting,
  } from '/@/views/aggregationRiskEventView/RiskEventView.data';
  import { useI18n } from '/@/hooks/web/useI18n';
  import BasicTable from '/@/components/Table/src/BasicTable.vue';
  import { handleLastTime } from '/@/utils/searchTableListTimeUtil';
  import { getEventLevelClass } from '/@/utils/valueEnum';
  import { render } from '/@/utils/common/renderUtils';
  import { Ticket } from '/@/views/aggregationRiskEventView/component/ticket/index';
  import Severity2 from '/@/components/Severity/Severity2.vue';
  import { TriageStatus } from '/@/components/Table';
  import TableUser from '/@/components/Table/src/components/TableUser.vue';
  import RiskEventView from '/@/views/aggregationRiskEventView/component/riskEvent/RiskEventView.vue';
  import RiskEventRecord from '/@/views/aggregationRiskEventView/component/riskEvent/RiskEventRecord.vue';
  import RiskEventClose from '/@/views/aggregationRiskEventView/component/riskEvent/RiskEventClose.vue';
  import RiskEventAssign from '/@/views/aggregationRiskEventView/component/riskEvent/RiskEventAssign.vue';
  import EventApply from '/@/views/ticket/view/apply/eventApply.vue';
  import RiskEventTriage from '/@/views/aggregationRiskEventView/component/riskEvent/RiskEventTriage.vue';
  import { usePermission } from '/@/hooks/web/usePermission';
  import { useUserStore } from '/@/store/modules/user';
  import { hasHunting } from '/@/utils/auth';
  import { useRouter } from 'vue-router';
  import { useModal } from '/@/components/Modal';
  import WhitelistVOModal from '/@/views/whitelist/modules/WhitelistVOModal.vue';
  import TableSearchBtn from '/@/views/tableSearch/TableSearchBtn.vue';
  import {defineAsyncComponent, nextTick, onMounted, ref, unref} from 'vue';
  import RiskEventBatchClose from '/@/views/aggregationRiskEventView/component/riskEvent/RiskEventBatchClose.vue';
  import RiskEventBatchAssign from '/@/views/aggregationRiskEventView/component/riskEvent/RiskEventBatchAssign.vue';
  import RiskEventBatchTriage from '/@/views/aggregationRiskEventView/component/riskEvent/RiskEventBatchTriage.vue';
  import { getLastTime } from '/@/utils/ckTable';
  import { useMethods } from '/@/hooks/system/useMethods';
  import { filterObj } from '/@/utils/common/compUtils';
  import { message } from 'ant-design-vue';
  const RiskEventModal = defineAsyncComponent(() => import("/@/views/aggregationRiskEventView/modules/RiskEventModal.vue"));
  
  const RiskEventModalRef = ref();
  
  const router = useRouter();
  const { t } = useI18n();
  const props = defineProps({
    ip: String,
    pageSource: String,
    selectIds: Array,
    socTenantId: String,
  });
  /**
   * 控制展开的行的rowKey值 eventId
   */
  const expandedRowKeys = ref<any>([]);

  const emits = defineEmits(['switch', 'closeDown']);
  const { hasPermission } = usePermission();
  const { handleExportXls } = useMethods();
  const userStore = useUserStore();

  let lastTime: any = getLastTime('riskEventLastTime');
  /*高级查询开始*/

  const tableSearchBtnRef = ref();
  //高级查询数据
  let searchData: any = '';
  //高级查询标识
  const advancedQueryFlag = ref(false);
  const searchCacheKey = 'riskAdvancedQueryData';
  if (sessionStorage.getItem(searchCacheKey)) {
    let data = sessionStorage.getItem(searchCacheKey);
    if (data) {
      data = JSON.parse(data);
      searchData = data;
      advancedQueryFlag.value = true;
      nextTick(() => {
        tableSearchBtnRef.value.setData(searchData);
      });
      lastTime = null;
    }
  }

  /**
   * 高级查询
   */
  function search(data: any) {
    searchData = data;
    if (data) {
      advancedQueryFlag.value = true;
      //把高级查询条件存到缓存中，防止路由跳转后再回来高级查询条件消失
      sessionStorage.setItem(searchCacheKey, JSON.stringify(data));
      getForm().resetFields();
    } else {
      sessionStorage.removeItem(searchCacheKey);
      advancedQueryFlag.value = false;
      getForm().setFieldsValue({ lastTime: getLastTime('riskEventLastTime') });
    }
  }

  /*高级查询结束*/

  let searchFormSchema = getCardSearchFormSchema(props.socTenantId);
  const { tableContext, } = useListPage({
    tableProps: {
      title: 'risk event',
      api: tableList,
      rowKey: 'eventId',
      columns: columnsCard(),
      canResize: false,
      formConfig: {
        model: { lastTime: lastTime },
        schemas: searchFormSchema,
        layout: formLayout,
      },
      showTableSetting: false,
      defSort: {
        column: 'updateTime',
        order: 'desc',
      },
      actionColumn: {
        title: '',
        width: 100,
      },
      beforeFetch: (params) => {
        if (props.ip) {
          params.fromMainIp = props.ip;
        }
        if (props.socTenantId) {
          params.socTenantId = props.socTenantId;
        }
        if (advancedQueryFlag.value) {
          let updateTimeStr = searchData.updateTimeStr;
          if (updateTimeStr) {
            let strs = updateTimeStr.split(',');
            params.updateTime_begin = strs[0];
            params.updateTime_end = strs[1];
          }
          params.advancedQuery = JSON.stringify(searchData.whereList);
        } else {
          params.advancedQuery = '';
          if (!params.lastTime) {
            params.lastTime = getLastTime('riskEventLastTime');
          }
          handleLastTime(params, 'updateTime');
          sessionStorage.setItem('riskEventLastTime', params.lastTime);
        }
        console.log(params);
        return params;
      },
      afterFetch: () => {
        expandedRowKeys.value = [];
      },
    },
  });

  const [registerTable, { reload, getForm, setSelectedRowKeys, getDataSource }, { rowSelection, selectedRows, selectedRowKeys }] = tableContext;
  rowSelection.width = 30;
  rowSelection.center = 'left';

  async function exportList() {
    let exportConfig = {
      url: getExportUrl,
      name: 'Risk Event',
      params: buildExportParams(),
    };
    let { params } = exportConfig;
    let realUrl = getExportUrl;
    if (realUrl) {
      let title = 'Risk Event';
      let paramsForm: any = {};
      try {
        paramsForm = await getForm().validate();
      } catch (e) {
        console.error(e);
      }

      if (!paramsForm?.column) {
        Object.assign(paramsForm, { column: 'updateTime', order: 'desc' });
      }

      if (params) {
        Object.keys(params).map((k) => {
          let temp = (params as object)[k];
          if (temp) {
            paramsForm[k] = unref(temp);
          }
        });
      }
      let selections = selectedRowKeys.value.join(',');
      if (selections) {
        paramsForm['selections'] = selections;
      }
      console.log();
      return handleExportXls(title as string, realUrl, filterObj(paramsForm));
    } else {
      message.warn('没有传递 exportConfig.url 参数');
      return Promise.reject();
    }
  }

  // 新增公共参数构建方法
  function buildExportParams() {
    let params: any = {};

    if (props.ip) {
      params.fromMainIp = props.ip;
    }
    if (props.socTenantId) {
      params.socTenantId = props.socTenantId;
    }
    if (advancedQueryFlag.value) {
      let updateTimeStr = searchData.updateTimeStr;
      if (updateTimeStr) {
        let strs = updateTimeStr.split(',');
        params.updateTime_begin = strs[0];
        params.updateTime_end = strs[1];
      }
      params.advancedQuery = JSON.stringify(searchData.whereList);
    } else {
      params.advancedQuery = '';
      if (!params.lastTime) {
        params.lastTime = getLastTime('riskEventLastTime');
      }
      handleLastTime(params, 'updateTime');
      sessionStorage.setItem('riskEventLastTime', params.lastTime);
    }
    return params;
  }

  function cleanSelect() {
    setSelectedRowKeys([]);
  }

  const dropdownVisible = ref(false);

  function showBatchBtn() {
    if (unref(selectedRows)?.length > 0) {
      dropdownVisible.value = true;
    } else {
      dropdownVisible.value = false;
    }
  }

  onMounted(() => {
    document.addEventListener('click', () => {
      dropdownVisible.value = false;
    });
  });

  /**
   * 查询事件其它信息
   * @param ids
   */
  function loadSummaryInfo(ids) {
    console.log(ids);
    const list = getDataSource();
    loadRiskSummaryData({ ids: ids }).then((data) => {
      console.log(data);
      for (let i = 0; i < list.length; i++) {
        if (data[list[i].eventId]) {
          list[i].summaryJson = data[list[i].eventId];
          list[i].summaryLoading = false;
        }
      }
    });
  }

  const expandedAllFlag = ref(true);

  /**
   * 展开当前列表所有行
   */
  function expandedAll() {
    expandedAllFlag.value = !expandedAllFlag.value;
    const list = getDataSource();
    if (expandedAllFlag.value) {
      //全部关闭
      expandedRowKeys.value = [];
      for (let i = 0; i < list.length; i++) {
        list[i].expandFlag = false;
      }
      return;
    }
    console.log(list);
    expandedRowKeys.value = [];
    const ids: string[] = [];
    for (let i = 0; i < list.length; i++) {
      expandInfo(list[i], false);
      if (!list[i].summaryJson && list[i].summaryField) {
        ids.push(list[i].eventId);
        list[i].summaryLoading = true;
      }
    }
    if (ids.length > 0) {
      loadSummaryInfo(ids.join(','));
    }
  }

  /**
   * 展开
   * @param record
   */
  function expandInfo(record, flag = true) {
    record.expandFlag = true;
    record.resultJson = {};
    if (record.aggregationFields) {
      try {
        record.resultJson = handleAggFieldsResult(record.aggregationFields);
      } catch (err) {}
    }
    expandedRowKeys.value.push(record.eventId);
    //2025-07-21 ml 统计和order 的summary结果已经存到事件表里了
    if(record.summaryField && record.summaryData && (record.ruleType == 1 || record.ruleType == 2)){
      const summaryData = JSON.parse(record.summaryData);
      console.log(summaryData)
      record.summaryJson = summaryData;
      return;
    }
    if (flag && !record.summaryJson && record.summaryField) {
      record.summaryLoading = true;
      loadSummaryInfo(record.eventId);
    }
  }

  /**
   * 关闭
   * @param record
   */
  function closeInfo(record) {
    record.expandFlag = false;
    let index = expandedRowKeys.value.indexOf(record.eventId);
    if (index > -1) {
      expandedRowKeys.value.splice(index, 1);
    }
  }

  const [registerModal, { openModal }] = useModal();

  function showAddWhite() {
    openModal(true, {
      isUpdate: false,
      showFooter: true,
    });
  }


  /**
   * 查看事件
   */
  function handleView(record: Recordable) {
    RiskEventModalRef.value.open({
      eventId: record.eventId,
      record: record,
    });
  }

  const closeDown = () => {
    emits('closeDown');
  };
</script>
<style lang="less" scoped>
  .risk_event_card {
    /deep/.ant-table-tbody > tr > td {
      border-bottom: 0 !important;
    }
    /deep/.ant-table-expanded-row {
      position: relative;
      top: -8px;
    }
    :deep(.ant-table-container) {
      padding: 0 16px;
    }
    .e_n {
      cursor: pointer;
      flex: 1;
      height: 100%;
      display: flex;
      align-items: center;
    }

    :deep(.ant-table-expand-icon-col) {
      width: 0;
      opacity: 0;
    }

    :deep(tr th:first-child) {
      width: 0;
      opacity: 0;
    }

    :deep(tr.ant-table-row.ant-table-row-level-0 td:first-child) {
      width: 0;
      opacity: 0;
    }

    :deep(td.ant-table-cell) {
      //border-bottom: 8px solid @dark-bg1 !important;
      padding: 0 !important;
    }

    :deep(tr.ant-table-measure-row td) {
      border-bottom: 0 !important;
    }

    :deep(tr th:nth-child(2)) {
      padding: 0 !important;
    }

    :deep(tr.ant-table-row.ant-table-row-level-0 td:nth-child(2)) {
      padding: 0 !important;
    }

    .eventStatus {
      border-radius: 8px 0 0 8px;
      padding: 0;
      height: 56px;
      display: flex;
      flex-flow: column;
      align-items: center;
      justify-content: center;
    }

    .logo {
      border-radius: 4px;
      width: 40px;
      height: 40px;
      min-width: 40px;
      display: flex;
      align-items: center;

      img {
        width: 40px;
        height: 40px;
      }
    }

    .vertical_div {
      position: absolute;
      right: -8px;
      top: revert;
      height: 32px;
    }

    .tdDiv {
      background-color: @bg-color;
      height: 56px;
      display: flex;
      align-items: center;
      position: relative;
      padding: 0 16px;
      margin-bottom: 8px;
    }

    .expanded_d {
      //margin-top: -8px;
      background: rgba(255, 255, 255, 0.08);
      margin-left: 110px;
      padding: 16px 0;
      border-radius: 0 0 8px 8px;

      .f_d {
        background: rgba(255, 255, 255, 0.1);
        padding: 4px 8px;
        border-radius: 4px;
      }

      .summary_p {
        display: flex;
        padding: 8px 32px;
        align-items: center;
        gap: 10px;
      }

      .summary_p:nth-child(odd) {
        background: rgba(255, 255, 255, 0.04);
      }
    }
    
    :deep(.ax-search-wrapper .ax-search-input) {
      width: 210px !important;
    }
  }
</style>
