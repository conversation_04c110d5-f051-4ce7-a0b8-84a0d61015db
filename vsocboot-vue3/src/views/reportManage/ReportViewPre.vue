<template>
  <div class="search_transparent">
    <PieLegend :chartData="chartDataSource" height="240px" :rightDiv="true" width="100%"></PieLegend>
  </div>
<!--  <div v-if="secondChart" style="display: none;" class="search_transparent">
    <PieLegend :chartData="chartDataSource" height="240px" width="100%"></PieLegend>
  </div>-->
</template>

<script lang="ts" setup>
//ts语法
import {ref, onMounted, Ref, reactive, defineProps, defineEmits, defineExpose, watch} from 'vue';
import {useI18n} from "/@/hooks/web/useI18n";
let riskColorArr = ref([]);
const chartDataSource = ref([]);
import PieLegend from "/@/components/chart/PieLegend.vue";
import {riskColor} from "/@/components/chart/ChartColor";
import {reportViewChartRequest} from "/@/views/reportManage/ReportView.api";
import BarChart from "/@/components/chart/BarChart.vue";
const {t} = useI18n();

const props = defineProps({
  dataValue: {
    type: Object,
    default: ()=>{}
  },
  dataBase: {
    type: Object,
    default: ()=>{}
  },
  id:{
    type:String
  },
  secondChart: {
    type: Boolean,
    default: false
  },
})

function init(record){
  console.log(record);
  //if(record.tableCode=='tbl_risk_event'){
    initData(record);
  //}

}

function initData(record){
  console.log("initData");
  let group = record.group;
  let count = [];
  let countSource = record.count?record.count:[];
  for(let i=0;i<countSource.length;i++){
    count.push(countSource[i]);
  }

  if(!group || !count){
    return;
  }
  if((count instanceof Array) && count.length==0){
    return;
  }

  let qParam = {};
  for(let i in record.queryParam){
    console.log(i);
    if(i=="statusStr" && record.queryParam[i] && (record.queryParam[i] instanceof Array)){
      qParam['statusStr'] = record.queryParam[i].join(",");
    }else
      qParam[i] = record.queryParam[i];
  }

  console.log(record);
  let data = {
    tableCode:record.tableCode,
    orderNum:record.orderNum,
    group:group,
    count:count.join(","),
    queryParam:qParam,
    orderFields:record.orderFields,
    orderAscDesc:record.orderAscDesc,
    distinctFields:record.distinctFields,
    dateType:record.dateType,
    viewType:4
  };
  reportViewChartRequest(data).then((res)=>{
    console.log(res);
    chartDataSource.value = res.pre;
  });
}

watch(()=>props.dataValue, (info)=>{
  if(!info)return;
  loadLine(info);
}, {deep: true, immediate: true});

function loadLine(record){
  console.log(record);
  let groupFields = record.groupFields;
  let countFields = record.countFields;
  let groupNum = record.groupNum;
  let filterCode = record.filterCode;
  let orderFields = record.orderFields;
  let orderAsc = record.orderAsc;
  let tableCode = record.tableCode;
  let queryParam = {};
  if(filterCode){
    let queryP = filterCode.split(";");
    for(let i=0;i<queryP.length;i++){
      let keyV = queryP[i].split(":");
      if(keyV.length>2){
        let key = queryP[i].substring(0,queryP[i].indexOf(":"));
        let value = queryP[i].substring(queryP[i].indexOf(":")+1);
        queryParam[key] = value;
        continue;
      }else if(keyV[0]=="statusStr"){
        queryParam[keyV[0]] = keyV[1].split(",");
        continue;
      }
      queryParam[keyV[0]] = keyV[1];
    }
  }
  if(!countFields || !groupFields){
    return;
  }
  if((countFields instanceof Array) && countFields.length==0){
    return;
  }

  let data = {
    tableCode:record.tableCode,
    orderNum:groupNum,
    group:groupFields,
    count:countFields,
    queryParam:queryParam,
    orderFields:orderFields,
    orderAscDesc:orderAsc,
    reStartTime:record.reStartTime,
    reEndTime:record.reEndTime,
    timeField:record.timeField,
    distinctFields:record.distinctFields,
    dateType:record.dateType,
    viewType:4
  };

  //if(tableCode=="tbl_risk_event"){
    reportViewChartRequest(data).then((res)=>{
      console.log(res);
      chartDataSource.value = res.pre;
    });
  //}
}

watch(()=>props.dataBase, (info)=>{
  if(!info)return;
  chartDataSource.value = props.dataBase.pre;
}, {deep: true, immediate: true});

defineExpose({
  init
});

</script>

<style lang="less" scoped>

</style>
