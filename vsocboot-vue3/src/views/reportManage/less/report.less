.report_template{
  display: flex;
  flex-direction: column;
  height: 100%;

}
.form-container{
  background: @dark-bg2;
  padding: 16px 32px;
  flex: 1;
  overflow-y: auto;
}
.tab-content-wrapper{
  margin-top:15px;
  /deep/.ant-tabs-nav{
    margin: 0px!important;
  }
}
.inves-formItem {
  width: 10%;
  display: inline-block;
  padding: 0 5px;
}
.modal-top {
  padding: 8px 16px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: @dark-bg2;
  margin-bottom: 8px;

}

[data-theme='dark'] .form-containe {
  background: #1A1B1F !important;
}

.icon_tooltip {
  font-size: 12px;
}

.view_div {
  width: 100%;
  margin-bottom: 20px;
  position: relative;
  &:hover{
    .delBtn{
      display: block;
      cursor: pointer;
    }
  }
  .title{
    padding-bottom: 16px;
  }
  .view_content{
    margin:0px -32px
  }
  .view_content_chart{
    background: @dark-bg1!important;
    margin-bottom: 8px!important;
    padding: 16px;
  }
  /deep/.ant-table,   /deep/.ant-table-thead > tr > th{
    background: @dark-bg3!important;
  }
  /deep/.search_transparent{
    padding-bottom: 8px!important;
  }
}

.close-a {
  position: absolute;
  right: 10px;
  top: 10px;
}

.edit-a {
  position: absolute;
  right: 40px;
  top: 10px;
}

.view_div .close-a, .view_div .edit-a {
  display: none;
}

.view_show .close-a, .view_show .edit-a {
  display: inline-block;
  z-index:9;
  cursor: pointer;
}

/deep/ .ant-card-body{
  position:relative;
}
.addDataBtn{
  padding-left: 0px;
  z-index: 10;
  &:hover{
    background: transparent;
  }
}
.pl-0{
  padding-left: 0px;
}
.optBtn{
  padding: 8px 0px;
  button{
    color: @primary-color!important;
  }
  &.optBtn-editor{
    margin-top:-44px;
  }
  button:hover{
    background: transparent!important;
  }
}
.delBtn{
  display: none;
}
