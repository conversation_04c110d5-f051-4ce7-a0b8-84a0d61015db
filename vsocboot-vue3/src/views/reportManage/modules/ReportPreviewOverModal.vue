<template>
  <div class="report_template">

    <div class="modal-top  ">
      <div class="font16 fcolor">
        <Icon icon="ant-design:left-outlined" :size="20" @click="goOut" style="margin-right: 5px;cursor: pointer;">
        </Icon>{{ title }}<span style="margin-left:10px;">{{ model.name }}</span>
      </div>
      <div>
        <a-button v-if="!model.filePath" @click="downloadWord('word')" style="margin-right: 10px;">
          Generate and Download Word
        </a-button>
        <a-button v-if="model.filePath" @click="downloadWord2" style="margin-right: 10px;">
          Download Word
        </a-button>
        <a-button v-if="!model.filePathPdf" @click="downloadWord('pdf')" style="margin-right: 10px;">
          Generate and Download PDF
        </a-button>
        <a-button v-if="model.filePathPdf" @click="downloadWord3" style="margin-right: 10px;">
          Download PDF
        </a-button>
      </div>
    </div>

    <div class="form-container">
      <div  v-for="(item,index) in viewData"  class="view_div">
        <div class="title font14 fcolor1" v-if="item.viewType!=99">{{ item.name }}</div>
        <div v-if="item.viewType==1"  class="view_content ">
          <ReportViewTable :dataBase="item" :id="item.name"></ReportViewTable>
        </div>
        <div v-if="item.viewType==2"  class="view_content view_content_chart">

          <ReportViewLine :dataBase="item.dataValues" :id="item.name" :secondChart="secondChart"></ReportViewLine>

        </div>
        <div v-if="item.viewType==3"  class="view_content view_content_chart">

          <ReportViewBar :dataBase="item.dataValues" :id="item.name" :secondChart="secondChart" :reversalVerticalHorizontal="item.reversalVerticalHorizontal=='horizontal'?true:false"></ReportViewBar>

        </div>
        <div v-if="item.viewType==4"  class="view_content view_content_chart">
          <ReportViewPre :dataBase="item.dataValues" :id="item.name" :secondChart="secondChart"></ReportViewPre>
        </div>
        <div v-if="item.viewType==99">
          <div v-html="item.dataValues" name="remarks_div"></div>
        </div>
      </div>
    </div>

  </div>
</template>

<script lang="ts" setup>
import {ref, nextTick, defineExpose,reactive} from 'vue';
import {useI18n} from "/@/hooks/web/useI18n"
import ReportViewPre from "/@/views/reportManage/ReportViewPre.vue";
import ReportViewTable from "/@/views/reportManage/ReportViewTable.vue";
import ReportViewLine from "/@/views/reportManage/ReportViewLine.vue";
import ReportViewBar from "/@/views/reportManage/ReportViewBar.vue";
import {
  downloadWordRequest,
  downloadPdfRequest,
  queryPerViewByInfoIdRequest, reportInfoList
} from "/@/views/reportManage/ReportInfo.api";
import {downloadFileBlob} from "/@/utils/common/renderUtils";
import {useRoute, useRouter} from "vue-router";
const router = useRouter();
const route = useRoute();
const {push} = router;

const {t} = useI18n();

const title = ref<string>('Preview');
const visible = ref<boolean>(false);
const model = reactive({});
let loading = ref(false);
let viewData = ref([]);
let secondChart = ref(false);

console.log(route.query)
if (route.query && route.query.id) {
  //infoEdit({id:route.query.id});
  reportInfoList({id:route.query.id}).then((data) => {
    if (data.records.length > 0) {
      let record = data.records[0];
      model['name'] = record.name;
      model['id'] = record.id;
      model['filePath'] = record.filePath;
      model['filePathPdf'] = record.filePathPdf;
      let jsonData = record.jsonData;
      console.log(jsonData);
      if(jsonData){
        let data = JSON.parse(jsonData);
        console.log(data);
        viewData.value = data;
      }
    }
  });
}else{

}

function infoEdit(record){
  queryPerViewByInfoIdRequest({id:record.id}).then((res) => {
    console.log(res);
    let json = [];
    let data = res;
    for (let i = 0; i < data.length; i++) {
      if (data[i].remarks) {
        json[i] = {
          viewType: 99,
          key: "remarks" + (Math.random()*100000000).toFixed(),
          remarks: data[i].remarks,
          text:data[i].content
        };
      } else {
        json[i] = data[i];
      }
    }
    viewData.value = json;
  });
}


/**
 * 取消按钮回调事件
 */
function handleCancel() {
  visible.value = false;
}

function sleep (ms){
  var unixtime_ms = new Date().getTime() ;
  while(new Date().getTime () < unixtime_ms +ms){}
}

async function downloadWord(type){
  secondChart.value = true;
  console.log("1");
  //await nextTick();
  //sleep(5000);
  console.log("3");
  setTimeout(function(){

    let a = document.getElementById("viewsDiv");
    let b = a.getElementsByTagName("canvas");
    let imgData = [];
    for (let i = 0; i < b.length; i++) {
      let dataURL = b[i].toDataURL("image/png");
      imgData.push(dataURL);
    }
    console.log(imgData);
    let sortData = [];
    let chartViewType = [2,3,4];
    let source = viewData.value;
    let chartSource = [];
    for (let i = 0; i < source.length; i++) {
      sortData.push({name: source[i].name, viewType: source[i].viewType});
      if(chartViewType.indexOf(source[i].viewType)>-1)chartSource.push(source[i]);
    }

    let chartData = [];
    for(let i=0;i<chartSource.length;i++){
      let viewType = chartSource[i].viewType;
      let dataValues = chartSource[i].dataValues;
      let data = [];//数据
      let key = [];//图例
      let column = [];//X轴
      if(viewType==4){
        let arr = dataValues.pre;
        for(let j=0;j<arr.length;j++){
          key.push(arr[j].name);
          data.push(arr[j].value);
        }
      }else if(viewType==3 || viewType==2){
        column = dataValues.xAxis;
        for(let j=0;j<dataValues.legend.length;j++){
          key.push(dataValues.legend[j]);
        }
        data = {};
        for(let j in key){
          //data.push(dataValues[key[j]]);
          data[key[j]] = dataValues[key[j]];
        }
      }
      let base = {
        data:data,
        key:key,
        column:column
      };
      chartData.push(base);
    }

    console.log(chartData);

    let remarksData = [];
    let c = document.getElementsByName("remarks_div");
    for (let i = 0; i < c.length; i++) {
      let html = c[i].innerHTML;
      let text = c[i].textContent;
      remarksData.push(html);
    }

    let tableData = [];
    let d = a.getElementsByTagName("table");
    for (let i = 0; i < d.length; i++) {
      let theadData = [];
      let th = d[i].getElementsByTagName("thead")[0].getElementsByTagName("th");
      for (let j = 0; j < th.length; j++) {
        theadData.push(th[j].textContent);
      }

      let tbodyData = [];
      let tr = d[i].getElementsByTagName("tbody")[0].getElementsByTagName("tr");
      for (let j = 0; j < tr.length; j++) {
        let td = tr[j].getElementsByTagName("td");
        let trData = [];
        for (let k = 0; k < td.length; k++) {
          trData.push(td[k].textContent);
        }
        tbodyData.push(trData);
      }

      tableData.push({thead: theadData, tbody: tbodyData});
    }


    let paramData = {
      imgData: imgData,
      chartData: chartData,
      name: model.name,
      sortData: sortData,
      remarksData: remarksData,
      tableData: tableData,
      id: model.id
    };
    console.log(paramData);

    if(type=="word"){
      downloadWordFunc(paramData);
    }else if(type=="pdf"){
      downloadPdfFunc(paramData)
    }
    secondChart.value = false;
  },1000);
  console.log("2");
}

function downloadWord2(){
  console.log(model.filePath,model.name);
  if(!model.filePath){
    return;
  }
  let suffix = model.filePath.substr(model.filePath.lastIndexOf("."));
  downFileFunc(model.filePath,model.name+suffix);
}

function downloadWord3(){
  console.log(model.filePathPdf,model.name);
  if(!model.filePathPdf){
    return;
  }
  let suffix = model.filePathPdf.substr(model.filePathPdf.lastIndexOf("."));
  downFileFunc(model.filePathPdf,model.name+suffix);
}

function downloadWordFunc(paramData){
  downloadWordRequest(paramData).then((res) => {
    console.log(res);
    downFileFunc(res.filePath,res.fileName);
  });
}

function downloadPdfFunc(paramData){
  downloadPdfRequest(paramData).then((res) => {
    console.log(res);
    downFileFunc(res.filePath,res.fileName);
  });
}

function downFileFunc(filePath,fileName){
  downloadFileBlob(filePath,fileName);
}

function goOut(){
  push("/reportManage/ReportInfo");
}

</script>

<style lang="less" scoped>
@import '../less/report.less';

</style>
