<template>
  <BasicModal v-bind="$attrs" @register="registerModal" @cancel="handleCancel" :title="title" @ok="handleSubmit" width="800px" :destroyOnClose="true">

    <a-form
      :model="formState"
      ref="formRef"
      name="basic"
      :layout="formLayout"
      :label-col="{ span: 24 }"
      :wrapper-col="{ span: 24 }"
      autocomplete="off"
      style="padding:15px;"
    >
        <a-form-item
          :label="t('routes.report.report.name')"
          name="name"
          :rules="[{ required: true, message: t('common.inputText') }]">
          <a-input v-model:value="formState.name" />
        </a-form-item>
        <a-form-item
          :label="t('routes.report.report.templateId_dictText')"
          name="templateId"
          :rules="[{ required: true, message: t('common.inputText') }]">
          <JDictSelectTag  :placeholder="t('common.chooseText')" :listHeight="190" showSearch v-model:value="formState.templateId" allowClear dictCode="tbl_report_template,name,id">
          </JDictSelectTag>
        </a-form-item>

        <a-form-item
          :label="t('routes.report.report.startTime')"
          name="startTime"
          :rules="[{ required: true, message: t('common.inputText') }]">
          <custom-com>
            <a-date-picker showTime valueFormat="YYYY-MM-DD HH:mm:ss" format="YYYY-MM-DD HH:mm:ss" v-model:value="formState.startTime"/>
            <div style="display: inline-block;width: 150px;margin-left: 20px;">
              <a-select
                style="width: 100%"
                placeholder="Fast Select"
                @change="timeChange"
                v-model:value="timeValue"
              >
                <a-select-option :value="1">Last 1 Day</a-select-option>
                <a-select-option :value="2">Last 7 Days</a-select-option>
                <a-select-option :value="3">Last 30 Days</a-select-option>
              </a-select>
            </div>
          </custom-com>
        </a-form-item>
        <a-form-item
          :label="t('routes.report.report.endTime')"
          name="endTime"
          :rules="[{ required: true, message: t('common.inputText') }]">
          <a-date-picker showTime valueFormat="YYYY-MM-DD HH:mm:ss" format="YYYY-MM-DD HH:mm:ss" v-model:value="formState.endTime"/>
        </a-form-item>

    </a-form>

  </BasicModal>
</template>

<script lang="ts" setup>
import {ref, computed, unref, reactive, onMounted, toRaw} from 'vue';
import {BasicModal, useModalInner} from '/@/components/Modal';
import {ApiSelect, BasicForm, JDictSelectTag, useForm} from '/@/components/Form/index';
import { formLayout } from '/@/settings/designSetting';
import {useI18n} from "/@/hooks/web/useI18n";
import {saveOrUpdate} from "/@/views/reportManage/ReportInfo.api";
import {useMessage} from "/@/hooks/web/useMessage";
const {createMessage} = useMessage();
const { t } = useI18n();
// Emits声明
const emit = defineEmits(['register','success']);
const isUpdate = ref(true);

//设置标题
const title = computed(() => (!unref(isUpdate) ? t('common.add') : t('common.editText')));
const formState = reactive({});
const formRef = ref();

let timeValue = ref();


const [registerForm, {resetFields, setFieldsValue}] = useForm({
  // labelWidth: 150,
});
//表单赋值
const [registerModal, {setModalProps , closeModal}] = useModalInner(async (data) => {
  //重置表单
  //await resetFields();
  setModalProps({confirmLoading: false,showCancelBtn:data?.showFooter,showOkBtn:data?.showFooter});
  isUpdate.value = !!data?.isUpdate;
  if (unref(isUpdate)) {
    //表单赋值
    console.log(data.record);
    let record = data.record;
    formState['id'] = record.id;
    formState['name'] = record.name;
    formState['templateId'] = record.templateId;
    formState['startTime'] = record.startTime;
    formState['endTime'] = record.endTime;
  }else{
    formState['id'] = null;
    formState['name'] = null;
    formState['templateId'] = null;
    formState['startTime'] = null;
    formState['endTime'] = null;
  }
});

//表单提交事件
async function handleSubmit() {
  formRef.value
    .validate()
    .then(() => {
      console.log(formState);
      let params: any = toRaw(formState)
      for (let key in params) {
        if (!params[key]) {
          params[key] = ""
        }
      }
      console.log('values', params);
      setModalProps({confirmLoading: true});
      saveOrUpdate(params, isUpdate.value).then((data) => {
        if(data.id)
          createMessage.success("Successfully"+(isUpdate.value?' edited':' added'));
        closeModal();
        handleCancel();
        console.log(data);
        emit('success',data.id);
      });
      setModalProps({confirmLoading: false});


    })
    .catch(error => {
      console.log('error', error);
    });
}

function timeChange(value){
  console.log(value);
  let val = 0;
  if (value == 1) {
    val = 1;
  } else if (value == 2) {
    val = 7;
  } else if (value == 3) {
    val = 30;
  }
  let date = new Date();
  let endTime = getTime(date);
  date.setTime(date.getTime() - 1000 * 60 * 60 * 24 * val);
  let startTime = getTime(date);
  console.log(startTime,endTime);
  formState['startTime'] = startTime ? startTime : null;
  formState['endTime'] = endTime ? endTime : null;
}

const resetForm = () => {
  formRef.value.resetFields();
};

function handleCancel(){
  resetForm();
  timeValue.value = null;
}

onMounted(() => {

});

function getTime(date) {
  let year = date.getFullYear();
  let month = date.getMonth() + 1;
  month = month<10? "0"+month:month;
  let day = date.getDate();
  day = day<10? "0"+day:day;
  let hours = date.getHours();
  hours = hours<10? "0"+hours:hours;
  let minutes = date.getMinutes();
  minutes = minutes<10? "0"+minutes:minutes;
  let seconds = date.getSeconds();
  seconds = seconds<10? "0"+seconds:seconds;
  let time = year + "-" + month + "-" + day + " " + hours + ":" + minutes + ":" + seconds;
  console.log(time);
  return time;
}

</script>

<style lang="less" scoped>

</style>
