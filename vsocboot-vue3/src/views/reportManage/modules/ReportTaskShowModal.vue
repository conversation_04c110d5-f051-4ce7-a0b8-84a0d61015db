<template>
  <a-modal :title="title" :width="width" :visible="visible" @ok="handleOk"
           @cancel="handleCancel">
        <div class="padding16">
          <a-form
            :label-col="{ span: 24 }"
            :wrapper-col="{ span: 24 }"
            :layout="formLayout"

          >
            <a-row>
              <a-col :span="8">
                <a-form-item :label="t('routes.report.task.taskName')">
                  {{formState.taskName}}
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item :label="t('routes.report.task.templateId_dictText')">
                  {{formState.templateId_dictText}}
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item :label="t('routes.report.task.createUserName')">
                  {{formState.createUserName}}
                </a-form-item>
              </a-col>
            </a-row>
            <a-row>
              <a-col :span="8">
                <a-form-item :label="t('routes.report.task.lastTime')">
                  {{formState.lastTime}}
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item :label="t('routes.report.task.nextTime')">
                  {{formState.nextTime}}
                </a-form-item>
              </a-col>
            </a-row>
          </a-form>

        </div>
      <div class="font14 fcolor border-bottom" >
        Execution List
      </div>
        <BasicTable @register="registerRiskLogsTable">
          <template #action="{ record }">
            <a-button @click="handlePreview(record)" size="small">{{ t("common.preview") }}</a-button>
          </template>
        </BasicTable>

  </a-modal>
</template>

<script lang="ts" setup>
import {ref, nextTick, defineExpose,reactive} from 'vue';
import {useI18n} from "/@/hooks/web/useI18n"
import {BasicTable, TableAction} from "/@/components/Table";
import {useListPage} from "/@/hooks/system/useListPage";
import {formLayout} from "/@/settings/designSetting";
import {reportInfoList} from "/@/views/reportManage/ReportInfo.api";
import {useRouter} from "vue-router";
const router = useRouter();
const {push} = router;
const {t} = useI18n();

const title = ref<string>('View');
const width = ref<number>(800);
const visible = ref<boolean>(false);
const emit = defineEmits(['ok']);
let loading = ref(false);
const formState = reactive({});


const columns = ref([
  {
    title: t('routes.report.report.startTime'),
    dataIndex: 'startTime',
  },
  {
    title: t('routes.report.report.endTime'),
    dataIndex: 'endTime',
  },
  {
    title: t('routes.report.task.lastTime'),
    dataIndex: 'updateTime',
  }
]);

const {tableContext} = useListPage({
  tableProps: {
    // title: 'Execution List',
    api: reportInfoList,
    useSearchForm:false,
    columns:columns,
    immediate:false,
    canResize: false,
    showActionColumn: true,

  },
})

const [registerRiskLogsTable,{reload}] = tableContext


function init(record){
  console.log("init");
  console.log(record);
  for(let i in record){
    formState[i] = record[i];
  }
  nextTick(() => {
    reload({searchInfo:{taskId:record.id}});
  });
}



/**
 * 取消按钮回调事件
 */
function handleCancel() {
  visible.value = false;
}

function handlePreview(record) {
  push({path: "/reportManage/modules/ReportPreviewOverModal", query: {id: record.id}});
}

defineExpose({
  visible,init
});
</script>

<style lang="less" scoped>
  /deep/.ant-form-item-control-input-content{
    font-size:13px;
    font-weight: bold;

    line-height: 20px;
  }
  /deep/.ant-form-item-label>label {

    font-size:13px;
    color:rgba(255,255,255,0.8);
    line-height: 20px;
  }
  .border-bottom{
    border-bottom: 1px solid @border-color;
    padding-left: 16px;
    padding-bottom: 16px;
  }
</style>
