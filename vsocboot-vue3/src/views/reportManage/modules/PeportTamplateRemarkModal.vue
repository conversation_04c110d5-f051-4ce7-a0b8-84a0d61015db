<template>
  <a-modal :title="title" :width="width" :visible="visible" @ok="handleOk"
           @cancel="handleCancel">
    <a-row :gutter="24">
      <a-row style="line-height: 30px;padding: 20px;">
        <a-button shape="circle" class="w-btn" style="margin-left: 5px;" @click="addCount()">+
        </a-button><span @click="addCount()" style="cursor: pointer;">&nbsp;&nbsp;&nbsp;add statistical</span>
      </a-row>
      <a-col :span="24" style="padding: 20px;">
        <a-form :layout="formLayout"
                ref="closeForm"
                :model="closeBase"
        >
          <JEditor v-model:value="remark" :height="300" :showImageUpload="false" />
        </a-form>
      </a-col>
    </a-row>

    <template #footer>
      <a-button key="back" @click="handleCancel">concel</a-button>
      <a-button key="submit" type="primary" :disabled="isClose" :loading="loading" @click="handleOk">Submit</a-button>
    </template>
    <ReportViewListModal ref="registerReportViewListModal" :typeVs="typeVs" @success="addCountOk"></ReportViewListModal>
  </a-modal>
</template>

<script lang="ts" setup>
  import {ref, nextTick, defineExpose,reactive} from 'vue';
  import {useI18n} from "/@/hooks/web/useI18n"
  import {Modal} from 'ant-design-vue';
  import { formLayout } from '/@/settings/designSetting';
  import ReportViewListModal from './ReportViewListModal.vue'
  import JEditor from '/@/components/Form/src/jeecg/components/JEditor.vue';
  import {reportViewlist} from "/@/views/reportManage/ReportView.api";

  const {t} = useI18n();

  const title = ref<string>('Remark');
  const width = ref<number>(800);
  const visible = ref<boolean>(false);
  const closeBase = reactive({});
  const emit = defineEmits(['ok']);
  const typeVs = ref("2");
  let loading = ref(false);
  let isClose = ref(false);
  let remark = ref("");
  let index = ref("");
  let remark_tow = ref("");
  const templateCountMap = reactive({});



  function queryTemplateCountMap(){
    reportViewlist({typeVs:2}).then((res)=>{
      let data = res.records;
      for(let i=0;i<data.length;i++){
        templateCountMap[data[i].name] = data[i].countValue;
      }
    });
  }
  function add(record){
    console.log("init");
    index.value = "";
    remark.value = "";
    queryTemplateCountMap();
  }

  function edit(data, key){
    index.value = key;
    remark.value = data;
    queryTemplateCountMap();
  }


  /**
   * 确定按钮点击事件
   */
  function handleOk() {
    let text = remark.value;
    for(let i in templateCountMap){
      let replaceText = "${"+ i +"}";
      //text = text.replace(new RegExp(replaceText,"g"),templateCountMap[i]);
      text = text.replaceAll(replaceText,templateCountMap[i]);
    }
    console.log(templateCountMap);
    console.log(text);
    emit("success",text,index.value,remark.value);
    handleCancel();
  }

  /**
   * 取消按钮回调事件
   */
  function handleCancel() {
    remark_tow.value = "";
    visible.value = false;
  }

  const registerReportViewListModal = ref();

  function addCount() {
    registerReportViewListModal.value.visible = true;
    registerReportViewListModal.value.edit(null,null,"2");
  }

  function addCountOk(data){
    console.log(data);
    let text = remark.value;
    console.log(text);
    //text = text.split("</");
    text = text.substr(0,text.lastIndexOf("</"));
    for(let i=0;i<data.length;i++){
      text += "${" + data[i].name + "}";
    }
    remark.value = text;
  }


  defineExpose({
    visible,add,edit
  });
</script>

<style>
</style>
