<template>
  <div>

    <div class="modal-top ">
      <div class="font16 fcolor">
        <Icon icon="ant-design:left-outlined" :size="20" @click="goOut"
              style="margin-right: 5px;cursor: pointer;">
        </Icon>
        {{ title }}
        <!--      {{formData.investigation}}-->
        <!--      <a-input v-model:value="formData.investigation" @focusout="InvestigationChange"/>-->
      </div>
      <div>
        <a-button style="margin-right: 10px" @click="handleCancel">{{ t('common.cancelText') }}
        </a-button>
        <a-button type="primary" @click="handleSubmit">{{ t('common.saveText') }}</a-button>
      </div>

    </div>

    <div class="form-container">
      <a-form
        :model="formState"
        ref="formRef"
        name="basic"
        :layout="formLayout"
        :label-col="{ span: 24 }"
        :wrapper-col="{ span: 24 }"
        autocomplete="off"
      >
        <a-row :gutter="[16,0]">
          <a-col :span="4">
            <a-form-item
              label="Security Posture"
              name="name"
              :rules="[{ required: true, message: t('common.inputText') },{ ...rules.limitationCheckRule(64)[0] }]"
            >
              <a-input v-model:value="formState.name" placeholder="Please enter Security Posture"/>
            </a-form-item>
          </a-col>
          <a-col :span="4">
            <a-form-item
              label="Data Source"
              name="tableCode"
              :rules="[{ required: true, message: t('common.inputText') }]"
            >
              <a-select v-model:value="formState.tableCode" @change="tableCodeChange">
                <a-select-option :value="item.value" v-for="(item,index) in DATA_SOURCE">
                  {{ item.label }}
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="4" style="position:relative;">
            <a-form-item
              label="Displayed Filter"
              name="fieldsName"
            >
              <!--<a-input v-model:value="formState.fieldsName" style="width:calc(100% - 30px);" />-->
              <a-dropdown :trigger="['click']" v-model:visible="dropdownVisible">
                <a-button type="text" @click.prevent style="position:absolute;top: 0px;">
                  <Icon icon="ant-design:plus-outlined"></Icon>
                  {{ t('common.add') }}
                </a-button>
                <template #overlay>
                  <a-menu style="height: 300px;overflow: auto;">
                    <a-menu-item>
                      <a-input allow-clear @change.prevent="changeGroupByData"
                               v-model:value="filterValue" placeholder="Filter Field">
                        <template #prefix>
                          <Icon icon="ant-design:search-outlined"></Icon>
                        </template>
                      </a-input>
                    </a-menu-item>
                    <template v-for="(item,index) in fieldData">
                      <a-menu-item v-if="!item.filter">
                        <a-checkbox @change="gbChange" :checked="item.default" :value="item.name">
                          {{ item.name }}
                        </a-checkbox>
                      </a-menu-item>
                    </template>


                  </a-menu>
                </template>
              </a-dropdown>
            </a-form-item>
          </a-col>
        </a-row>
        <div class="font13 fcolor1" style="padding-bottom: 8px">Filter</div>
        <ReportViewFilterFormModal :queryParam="queryParam" :editAdd="editAdd"
                                   :tableCode="formState.tableCode"
                                   @change="initDownData"></ReportViewFilterFormModal>


        <a-row :gutter="[16]">
          <a-col style="width: 385px;">
            <a-form-item
              label="Chart Type"
              name="viewType"
            >
              <a-radio-group v-model:value="viewType" @change="viewTypeChange">
                <a-radio value="1">Table</a-radio>
                <a-radio value="2">Line Chart</a-radio>
                <a-radio value="3">Bar Chart</a-radio>
                <a-radio value="4" :disabled="queryIsDisabled()">Per Chart</a-radio>
              </a-radio-group>
            </a-form-item>
          </a-col>
          <a-col :span="4">
            <a-form-item
              label="Primary keys"
              name="groupField"
              :rules="requiredForm_">
              <a-select v-model:value="formState.groupField" :mode="viewType==1?'multiple':''"
                        @change="groupCountFieldsChange" allowClear
                        placeholder="Please choose Primary keys">
                <template v-for="(item,index) in fieldData">
                  <a-select-option :value="item.name" v-if="item.default">{{
                      item.name
                    }}
                  </a-select-option>
                </template>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="4">
            <a-form-item
              id="staticDiv"
              label="Statistical fields"
              name="countField"
              style="position:relative;"
              :rules="requiredForm_">
              <a-dropdown :trigger="['click']" v-model:visible="dropdownCountVisible">
                <a-button ref="statisBtn" style="position:absolute;top: 0px;width:1px;">

                </a-button>
                <template #overlay>
                  <a-menu style="max-height: 350px;overflow: auto;position: relative"
                          id="selectDropdown">
                    <a-menu-item :style="searchStyle">
                      <a-input allow-clear @change.prevent="changeStaticGroupByData"
                               v-model:value="filterStaticValue" placeholder="Filter Field">
                        <template #prefix>
                          <Icon icon="ant-design:search-outlined"></Icon>
                        </template>
                      </a-input>
                    </a-menu-item>
                    <a-menu-item style="line-height:24px;">
                      <div style="height: 33px;"></div>
                    </a-menu-item>
                    <template v-for="(item,index) in fieldData">
                      <a-menu-item v-if="item.default && !item.filterStatic"
                                   :style="{width:countWidth}">
                        <a-checkbox @change="countChange" :name="item.name"
                                    v-model:checked="item.count">{{ item.name }}
                        </a-checkbox>
                        <div style="float:right;" v-if="item.count">
                          Duplicate removal statistics
                          <a-checkbox @change="countDistinctChange" :name="item.name"
                                      v-model:checked="item.distinct"
                                      style="margin-left:5px;"></a-checkbox>
                        </div>
                      </a-menu-item>
                    </template>
                  </a-menu>
                </template>
              </a-dropdown>
              <a-select v-model:value="formState.countField" mode="multiple"
                        @change="groupCountFieldsChange"
                        placeholder="Please choose Statistical fields" id="selectDiv"
                        @deselect="CountFieldsDeChange" readonly :open="false"
                        style="width:calc(100% - 2px);" @click="showStatis">
                <template v-for="(item,index) in fieldData">
                  <a-select-option :value="item.name" v-if="item.default">{{
                      item.name
                    }}
                  </a-select-option>
                </template>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="3">

            <a-form-item
              label="Display quantity"
              name="groupNum">
              <a-input-number style="width:100%" v-model:value="formState.groupNum" :min="1"
                              @change="initDownData" placeholder="Please enter Display quantity"/>
            </a-form-item>

          </a-col>
          <a-col :span="2" v-if="dateTypeShow">
            <a-form-item
              label="Date Type"
              name="dateType">
              <a-select v-model:value="formState.dateType" @change="initDownData" allowClear
                        placeholder="Please choose Date Type">
                <a-select-option value="Minute">Minute</a-select-option>
                <a-select-option value="Hour">Hour</a-select-option>
                <a-select-option value="Day">Day</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="3">
            <a-form-item
              label="Sorting Fields"
              name="orderFields">
              <a-select v-model:value="formState.orderFields" @change="initDownData" allowClear
                        placeholder="Please choose Sorting Fields">
                <a-select-option :value="item" v-for="(item,index) in orderFieldData">{{
                    item
                  }}
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="3">
            <a-form-item
              label="Ascending/Descending"
              name="orderFields">
              <a-select v-model:value="formState.orderFieldsAscDesc" @change="initDownData"
                        allowClear placeholder="Please choose">
                <a-select-option value="asc">Asc</a-select-option>
                <a-select-option value="desc">Desc</a-select-option>
              </a-select>
            </a-form-item>


          </a-col>
          <a-col v-if="viewType==3" :span="2">
            <a-form-item
              label="Bar Type"
              name="reversalVerticalHorizontal">
              <a-select v-model:value="formState.reversalVerticalHorizontal"
                        @change="reversalVerticalHorizontalChange"
                        placeholder="Please choose Bar Type">
                <a-select-option value="vertical">Vertical</a-select-option>
                <a-select-option value="horizontal">Horizontal</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>

        </a-row>
      </a-form>
    </div>
    <a-row>
      <a-form
        :model="formState"
        name="basic"
        ref="formRef2"
        :layout="formLayout"
        :label-col="{ span: 24 }"
        :wrapper-col="{ span: 24 }"
        autocomplete="off"
        style="overflow-x: hidden;width:100%;"
      >

      </a-form>
    </a-row>

    <div class="report-content">
      <div class="font16 fcolor padding16">Preview</div>
      <div v-if="viewType==1" id="viewTableDiv" >
        <ReportViewTable ref="reportTable"></ReportViewTable>
      </div>
      <div v-if="viewType==2"  >
        <ReportViewLine ref="reportLine"></ReportViewLine>
      </div>
      <div v-if="viewType==3" >
        <ReportViewBar ref="reportBar"
                       :reversalVerticalHorizontal="reversalVerticalHorizontal"></ReportViewBar>
      </div>
      <div v-if="viewType==4"  >
        <ReportViewPre ref="reportPre"></ReportViewPre>

      </div>


    </div>





  </div>
</template>

<script lang="ts" setup>
import {ref, computed, unref, reactive, onMounted, toRaw, nextTick} from 'vue';
import {
  ApiSelect,
  BasicForm,
  JDictSelectTag,
  useForm,
  JSelectMultiple,
  JCheckbox
} from '/@/components/Form/index';
import {formLayout} from '/@/settings/designSetting';
import ReportViewTable from "/@/views/reportManage/ReportViewTable.vue";
import ReportViewLine from "/@/views/reportManage/ReportViewLine.vue";
import ReportViewBar from "/@/views/reportManage/ReportViewBar.vue";
import ReportViewPre from "/@/views/reportManage/ReportViewPre.vue";
import JRangeDate from "/@/components/Form/src/jeecg/components/JRangeDate.vue";
import ReportViewFilterFormModal from "/@/views/reportManage/modules/ReportViewFilterFormModal.vue";
import {
  saveOrUpdate, reportViewlist, doDuplicateCheckRequest
} from '../ReportView.api.ts';
import {useI18n} from "/@/hooks/web/useI18n";
import {
  EVENT_FIELD,
  DATA_SOURCE,
  INV_FIELD,
  LOGS_FIELD,
  LOGS_HOST_FIELD,
  EVENT_SEC_FIELD,
  EVENT_HOST_FIELD,
  LOGS_NETWORK_FIELD,
  LOGS_OPERATION_FIELD,
  WORKFLOW_FIELD
} from "/@/views/reportManage/Report.data";
import {formSchema, RULE_RISK_TYPE} from "/@/views/rule/aggregation/AggregationRule.data";
import {useRoute, useRouter} from "vue-router";
import dayjs from "dayjs";
import {useMessage} from "/@/hooks/web/useMessage";
import {rules} from "/@/utils/helper/validator";

const router = useRouter();
const route = useRoute();
const {push} = router;
const {t} = useI18n();
// Emits声明
const emit = defineEmits(['register', 'success']);
const isUpdate = ref(true);

const searchStyle = reactive({
  'position': 'fixed',
  'z-index': 9,
  'background-color': '#28282C !important',
  'width': 'auto',
  'border-radius': '5px'
});
let countWidth = ref("auto");

//设置标题
const title = ref(t('common.add'));
const formState = reactive({
  groupNum: 10,
  source: 1,
  tableCode: 'sec',
  reversalVerticalHorizontal: 'vertical'
});
let fieldData = ref([]);
const filterValue = ref("");
const filterStaticValue = ref("");
const dropdownVisible = ref(false);
const dropdownCountVisible = ref(false);
let filterData = ref([]);
let queryParam = reactive({
  startDateV: dayjs().subtract(1, 'day').format('YYYY-MM-DD HH:mm:ss') + ',' + dayjs().format('YYYY-MM-DD HH:mm:ss'),
  startDate: dayjs().subtract(1, 'day').format('YYYY-MM-DD HH:mm:ss') + ',' + dayjs().format('YYYY-MM-DD HH:mm:ss')
});
let orderFieldData = ref([]);
let viewType = ref("1");
const formRef = ref();
const formRef2 = ref();

const reportTable = ref();
const reportLine = ref();
const reportBar = ref();
const reportPre = ref();
const {createMessage} = useMessage();
let reversalVerticalHorizontal = ref(false);
let editAdd = ref('add');

const dateTypeShow = ref(false);

const flexValSec = ((window.innerWidth - (window.innerWidth * (5 / 24))) / 7 - 16 - 10 - 8) + "px";
console.log(flexValSec);
const requiredForm = ref([{required: true, message: t('common.inputText')}]);
let requiredForm_ = ref([]);

for (let i = 0; i < EVENT_SEC_FIELD.length; i++) {
  let base = {};
  for (let j in EVENT_SEC_FIELD[i]) {
    base[j] = EVENT_SEC_FIELD[i][j];
  }
  fieldData.value.push(base);
}

console.log(route.query)
if (route.query && route.query.id) {
  editAdd.value = 'edit';
  title.value = t('common.editText');
  isUpdate.value = true;
  reportViewlist({id: route.query.id}).then((data) => {
    console.log(data)
    if (data.records.length > 0) {
      let record = data.records[0];
      setFieldDataFunc(record.tableCode);//设置 FIELD_DATA

      let fieldsCode = record.fieldsCode;
      let groupFields = record.groupFields;
      let countFields = record.countFields;
      let groupNum = record.groupNum;
      let filterCode = record.filterCode;
      let name = record.name;
      let orderFields = record.orderFields;
      let orderAsc = record.orderAsc;
      let tableCode = record.tableCode;
      let distinctFields = record.distinctFields;
      viewType.value = record.viewType + "";
      formState['id'] = record.id;
      formState['name'] = name;
      formState['tableCode'] = tableCode;
      formState['groupNum'] = groupNum;
      formState['reversalVerticalHorizontal'] = record.reversalVerticalHorizontal;
      if (record.reversalVerticalHorizontal == 'horizontal') {
        reversalVerticalHorizontal.value = true;
      } else {
        formState['reversalVerticalHorizontal'] = 'vertical';
        reversalVerticalHorizontal.value = false;
      }
      if (groupFields) {
        if (record.viewType == 1) {
          formState['groupField'] = groupFields.split(",");
        } else {
          formState['groupField'] = groupFields;
        }
        for (let i = 0; i < groupFields.length; i++) {
          if (!groupFields[i].endsWith("_time") || !groupFields[i].endsWith("_date")) {
            dateTypeShow.value = true;
            formState['dateType'] = record.dateType;
            continue;
          }
        }
      }
      if (countFields) {
        formState['countField'] = countFields.split(",");
        let fieldArr = countFields.split(",");
        let source = fieldData.value;
        for (let i = 0; i < fieldArr.length; i++) {
          for (let j = 0; j < source.length; j++) {
            if (fieldArr[i] == source[j].name) {
              source[j].count = true;
            }
          }
        }
        fieldData.value = source;
      }
      if (groupFields && countFields) {
        let group = formState.groupField;
        let count = formState.countField;
        let list = count;//group.concat(count);
        console.log(list);
        if (group && group.length > 0 && count && count.length > 0) {
          orderFieldData.value = Array.from(new Set(list));
        }
      }
      formState['orderFields'] = orderFields;
      formState['orderFieldsAscDesc'] = orderAsc;

      Object.keys(queryParam).map(item => {
        delete queryParam[item]
      });

      if (filterCode) {
        let queryP = filterCode.split(";");
        for (let i = 0; i < queryP.length; i++) {
          let keyV = queryP[i].split(":");
          if (keyV.length > 2) {
            let key = queryP[i].substring(0, queryP[i].indexOf(":"));
            let value = queryP[i].substring(queryP[i].indexOf(":") + 1);
            queryParam[key] = value;
            continue;
          } else if (keyV[0] == "statusStr") {
            if (keyV[1])
              queryParam[keyV[0]] = keyV[1].split(",");
            continue;
          }
          queryParam[keyV[0]] = keyV[1];
        }
        if (queryParam['startDate']) {
          queryParam['startDateV'] = queryParam['startDate'];
        }
      }

      if (fieldsCode) {
        let fieldArr = fieldsCode.split(",");
        let source = fieldData.value;
        for (let i = 0; i < fieldArr.length; i++) {
          for (let j = 0; j < source.length; j++) {
            if (fieldArr[i] == source[j].name) {
              source[j].default = true;
            }
          }
        }
        fieldData.value = source;
      }

      if (distinctFields) {
        let fieldArr = distinctFields.split(",");
        let source = fieldData.value;
        for (let i = 0; i < fieldArr.length; i++) {
          for (let j = 0; j < source.length; j++) {
            if (fieldArr[i] == source[j].name) {
              source[j].distinct = true;
            }
          }
        }
        fieldData.value = source;
      }
      console.log(fieldData.value);
      initDownData();
    }
  });
} else {
  console.log("add");
  initDownData();
}


const [registerForm, {resetFields, setFieldsValue}] = useForm({
  // labelWidth: 150,
});

onMounted(() => {
  nextTick(() => {
    let selectDiv = document.getElementById("selectDiv");
    let width = window.getComputedStyle(selectDiv.parentNode.parentNode.parentNode.parentNode).width;
    let w = width.substring(0, width.length - 2);
    searchStyle['width'] = parseInt(w) - 1 + "px";
    countWidth.value = parseInt(w) - 9 + "px";
  });
});

const gbChange = (e) => {
  let val = e.target.value;
  let checked = e.target.checked;
  let source = fieldData.value;
  if (source.filter(item => item.default == true).length == 1 && !checked) {
    createMessage.error('Select at least one');
    return;
  }
  for (let i = 0; i < source.length; i++) {
    if (source[i].name == val) {
      source[i].default = checked;
      if (checked == false) {
        source[i].count = false;
        source[i].distinc = false;
      }
    }
  }
  fieldData.value = source;
  if (checked == false) {
    let countFieldArr = formState.countField;
    if (countFieldArr) {
      countFieldArr.splice(countFieldArr.indexOf(val), 1);
      formState["countField"] = countFieldArr;
    }
  }
  initDownData();
}

//表单提交事件
async function handleSubmit() {
  formRef2.value.validate().then(() => {
  }).catch(error => {
    console.log('error', error);
  });
  let flagStar = true;
  await formRef.value.validate();

  // .then(() => {
  console.log(formState);
  let params: any = toRaw(formState)
  for (let key in params) {
    if (!params[key]) {
      params[key] = ""
    }
  }

  await doDuplicateCheckRequest({
    name: params.name,
    id: params.id,
    typeVs: 1
  })
    .then((res) => {
      if (!res.success) {
        createMessage.error(res.message);
        flagStar = false;
      }
    })
    .catch((err) => {
      flagStar = false;
      createMessage.error(err.message);
    });

  if (!flagStar) return;

  if (params.groupField && params.countField) {
    if (params.groupField instanceof Array) {
      params['groupFields'] = params.groupField.join(",");
    } else {
      params['groupFields'] = params.groupField;
    }
    params['countFields'] = params.countField.join(",");
  }
  if (params.orderFieldsAscDesc) {
    params['orderAsc'] = params.orderFieldsAscDesc;
  }
  let fieldDatas = fieldData.value.filter(item => item.default == true);
  let fieldsCode = [];
  let fieldsName = [];
  let distinctFields = [];
  for (let i = 0; i < fieldDatas.length; i++) {
    fieldsCode.push(fieldDatas[i].name);
    fieldsName.push(fieldDatas[i].field);
    if (fieldDatas[i].count == true && fieldDatas[i].distinct == true) {
      distinctFields.push(fieldDatas[i].name);
    }
  }
  let queryP = [];
  for (let i in queryParam) {
    if (!queryParam[i]) {
      continue;
    }
    if (i == 'statusStr') {
      queryP.push(i + ":" + queryParam[i].join(","));
    } else {
      queryP.push(i + ":" + queryParam[i]);
    }
  }

  let fieldsTitle = [];
  if (viewType.value == 1) {//获取table标题

    let a = document.getElementById("viewTableDiv");
    let d = a.getElementsByTagName("table");
    for (let i = 0; i < d.length; i++) {
      let th = d[i].getElementsByTagName("thead")[0].getElementsByTagName("th");
      for (let j = 0; j < th.length; j++) {
        fieldsTitle.push(th[j].textContent);
      }
    }
  }


  params['fieldsCode'] = fieldsCode.join(",");
  params['fieldsName'] = fieldsName.join(",");
  params['fieldsTitle'] = fieldsTitle.join(",");
  params['filterCode'] = queryP.join(";");
  params['viewType'] = viewType.value;
  params['typeVs'] = 1;
  if (params.tableCode == "tbl_risk_event") {
    params['timeField'] = "alarm_time";
  } else if (params.tableCode == "sec") {
    params['timeField'] = "update_time";
  } else if (params.tableCode == "host") {
    params['timeField'] = "update_time";
  } else if (params.tableCode == "inv") {
    params['timeField'] = "creation_time";
  } else if (params.tableCode == "log" || params.tableCode == "hostLog" || params.tableCode == "networkLog" || params.tableCode == "operationLog") {
    params['timeField'] = "ck_enter_date";
  } else if (params.tableCode == "workflow") {
    params['timeField'] = "start_time";
  }
  params['distinctFields'] = distinctFields.join(",");
  console.log('values', params);
  let isUpd = false;
  if (route.query.id) {
    isUpd = true;
  }
  saveOrUpdate(params, isUpd).then((res) => {
    console.log(res);
    handleCancel();
    emit('success');
  });


  // })
  // .catch(error => {
  //   console.log('error', error);
  // });
}

const resetForm = () => {
  for (let i in formState) {
    formState[i] = null;
  }
  formState['groupNum'] = 10;
  formState['source'] = 1;
  formState['tableCode'] = 'sec';
  formRef.value.resetFields();
  formRef2.value.resetFields();
};

function handleCancel(v) {
  resetForm();
  console.log(EVENT_FIELD);
  fieldData.value = EVENT_FIELD;
  filterValue.value = "";
  filterStaticValue.value = "";
  filterData.value = [];
  for (let i in queryParam) {
    //queryParam[i] = null;
  }
  orderFieldData.value = [];
  viewType.value = "1";
  goOut();
}

function changeGroupByData(e) {
  console.log(e);
  let val = filterValue.value;
  console.log("filterValue", val)
  let source = fieldData.value;
  for (let i = 0; i < source.length; i++) {
    if (source[i].name.indexOf(val) == -1) {
      source[i].filter = true;
    }
  }
  if (!val) {
    for (let i = 0; i < source.length; i++) {
      source[i].filter = false;
    }
  }
  fieldData.value = source;
}

function changeStaticGroupByData(e) {
  console.log(e);
  let val = filterStaticValue.value;
  console.log("filterStaticValue", val)
  let source = fieldData.value;
  for (let i = 0; i < source.length; i++) {
    if (source[i].name.indexOf(val) == -1) {
      source[i].filterStatic = true;
    }
  }
  if (!val) {
    for (let i = 0; i < source.length; i++) {
      source[i].filterStatic = false;
    }
  }
  fieldData.value = source;
}

function initTable() {
  let tableCode = formState.tableCode;
  let fieldDataV = fieldData.value;
  let group = formState.groupField;
  let count = formState.countField;
  let orderNum = formState.groupNum;
  let orderFields = formState.orderFields;
  let orderFieldsAscDesc = formState.orderFieldsAscDesc;
  let distinctFields = fieldDataV.filter(item => item.default == true && item.count == true && item.distinct == true);
  //let queryParam = queryParam;
  if (group && !count) {
    //return;
  }
  if (!group && count) {
    //return;
  }
  console.log(fieldDataV);
  let record = {
    tableCode: tableCode,
    fieldData: fieldData,
    group: group,
    count: count,
    orderNum: orderNum,
    orderFields: orderFields,
    orderAscDesc: orderFieldsAscDesc,
    distinctFields: distinctFields.map(item => item.name).join(","),
    queryParam: queryParam,
    dateType: formState.dateType
  };
  console.log(record);
  reportTable.value.init(record);
}

function initDownData() {
  let tableCode = formState.tableCode;
  dateTypeShow.value = false;
  if (formState.groupField) {
    let group = formState.groupField;
    if (!(group instanceof Array)) {
      group = [group];
    }
    for (let i = 0; i < group.length; i++) {
      console.log(group[i].endsWith("_time") || group[i].endsWith("_date"), group[i].endsWith("_time"), group[i].endsWith("_date"));
      if (group[i].endsWith("_time") || group[i].endsWith("_date")) {
        dateTypeShow.value = true;
        continue;
      }
    }
  }
  let viewTypeV = viewType.value;
  console.log("tableCode=" + tableCode);
  console.log("viewType=" + viewTypeV);
  nextTick(() => {
    //if (tableCode=='tbl_risk_event'){
    if (viewTypeV == 1) {
      initTable();
    } else if (viewTypeV == 2) {
      initLine();
    } else if (viewTypeV == 3) {
      initBar();
    } else if (viewTypeV == 4) {
      initPre();
    }
    //}
  });
}

function initLine() {
  let tableCode = formState.tableCode;
  let group = formState.groupField;
  let count = formState.countField;
  let orderNum = formState.groupNum;
  let orderFields = formState.orderFields;
  let orderFieldsAscDesc = formState.orderFieldsAscDesc;
  let distinctFields = fieldData.value.filter(item => item.default == true && item.count == true && item.distinct == true);
  //let queryParam = queryParam;
  if (group && !count) {
    return;
  }
  if (!group && count) {
    return;
  }
  if (!group && !count) {
    return;
  }
  let record = {
    tableCode: tableCode,
    group: group,
    count: count,
    orderNum: orderNum,
    orderFields: orderFields,
    orderAscDesc: orderFieldsAscDesc,
    distinctFields: distinctFields.map(item => item.name).join(","),
    queryParam: queryParam,
    dateType: formState.dateType
  };
  console.log(record);
  reportLine.value.init(record);
}

function initBar() {
  let tableCode = formState.tableCode;
  let group = formState.groupField;
  let count = formState.countField;
  let orderNum = formState.groupNum;
  let orderFields = formState.orderFields;
  let orderFieldsAscDesc = formState.orderFieldsAscDesc;
  let distinctFields = fieldData.value.filter(item => item.default == true && item.count == true && item.distinct == true);
  //let queryParam = queryParam;
  if (group && !count) {
    return;
  }
  if (!group && count) {
    return;
  }
  if (!group && !count) {
    return;
  }
  let record = {
    tableCode: tableCode,
    group: group,
    count: count,
    orderNum: orderNum,
    orderFields: orderFields,
    orderAscDesc: orderFieldsAscDesc,
    distinctFields: distinctFields.map(item => item.name).join(","),
    queryParam: queryParam,
    dateType: formState.dateType
  };
  console.log(record);
  reportBar.value.init(record);
}

function initPre() {
  let tableCode = formState.tableCode;
  let group = formState.groupField;
  let count = formState.countField;
  let orderNum = formState.groupNum;
  let orderFields = formState.orderFields;
  let orderFieldsAscDesc = formState.orderFieldsAscDesc;
  let distinctFields = fieldData.value.filter(item => item.default == true && item.count == true && item.distinct == true);
  //let queryParam = queryParam;
  if (group && !count) {
    return;
  }
  if (!group && count) {
    return;
  }
  if (!group && !count) {
    return;
  }
  let record = {
    tableCode: tableCode,
    group: group,
    count: count,
    orderNum: orderNum,
    orderFields: orderFields,
    orderAscDesc: orderFieldsAscDesc,
    distinctFields: distinctFields.map(item => item.name).join(","),
    queryParam: queryParam,
    dateType: formState.dateType
  };
  console.log(record);
  reportPre.value.init(record);
}

function groupCountFieldsChange(e) {
  console.log(formRef.value.validateFields());
  console.log(e);
  let source = orderFieldData.value;

  let group = formState.groupField;
  let count = formState.countField;
  //console.log(source,group,count);//getFieldsValue
  let list = count;//group.concat(count);
  if (list && list.length > 1 && viewType.value == 4) {
    viewType.value = "1";
    initDownData();
    return;
  }
  console.log(list);
  if (group && group.length > 0 && count && count.length > 0) {
    orderFieldData.value = Array.from(new Set(list));
  } else {
    orderFieldData.value = [];
    formState.orderFields = null;
    formState.orderFieldsAscDesc = null;
  }
  let orderFields = formState.orderFields;
  if (orderFields && list.indexOf(orderFields) == -1) {
    formState.orderFields = null;
  }
  initDownData();
}

function handleChange(arr) {
  console.log(arr);
  /*let str = ''
  if(arr && arr.length>0){
    if(arr[1] && arr[0]){
      str = arr.join(',')
    }
  }*/
  queryParam['startDate'] = arr;
  initDownData();
}

function viewTypeChange(e) {
  let val = e.target.value;
  console.log(val);
  let groupField = formState['groupField'];
  if (val == 1) {
    if (groupField) formState['groupField'] = [groupField];
    //formState['groupField'] = [];
    requiredForm_.value = [];
  } else {
    if (groupField) {
      if (!(groupField instanceof Array)) {
        groupField = [groupField];
      }
      formState['groupField'] = groupField[0];
    }
    //formState['groupField'] = null;
    requiredForm_.value = requiredForm.value;
  }

  initDownData();
}

function queryIsDisabled() {
  console.log(formState.countField);
  if (formState.countField) {
    return formState.countField.length < 2 ? false : true;
  }
  return false;
}

function tableCodeChange(e) {
  console.log(e);
  setFieldDataFunc(e);
  let name = formState.name;
  let id = route.query.id ?? null;
  for (let i in formState) {
    formState[i] = null;
  }
  // for(let i in queryParam){
  //   queryParam[i] = null;
  // }
  Object.keys(queryParam).map(item => {
    delete queryParam[item]
  });
  queryParam['startDateV'] = dayjs().subtract(1, 'day').format('YYYY-MM-DD HH:mm:ss') + ',' + dayjs().format('YYYY-MM-DD HH:mm:ss');
  queryParam['startDate'] = dayjs().subtract(1, 'day').format('YYYY-MM-DD HH:mm:ss') + ',' + dayjs().format('YYYY-MM-DD HH:mm:ss')

  formState['groupNum'] = 10;
  formState['source'] = 1;
  formRef.value.resetFields();
  formRef2.value.resetFields();
  formState['tableCode'] = e;
  formState['name'] = name;
  formState['id'] = id;


  //initDownData();
}

function setFieldDataFunc(e) {
  let source = [];
  if (e == "tbl_risk_event") {
    source = EVENT_FIELD;
  } else if (e == "inv") {
    source = INV_FIELD;
  } else if (e == "log") {
    source = LOGS_FIELD;
  } else if (e == "hostLog") {
    source = LOGS_HOST_FIELD;
  } else if (e == "sec") {
    source = EVENT_SEC_FIELD;
  } else if (e == "host") {
    source = EVENT_HOST_FIELD;
  } else if (e == "networkLog") {
    source = LOGS_NETWORK_FIELD;
  } else if (e == "operationLog") {
    source = LOGS_OPERATION_FIELD;
  } else if (e == "workflow") {
    source = WORKFLOW_FIELD;
  }
  let arr = [];
  for (let i = 0; i < source.length; i++) {
    let base = {};
    for (let j in source[i]) {
      base[j] = source[i][j];
    }
    arr.push(base);
  }
  fieldData.value = arr
}

function countChange(e) {
  let val = e.target.name;
  console.log(e);
  let source = fieldData.value;
  let checked = e.target.checked;
  for (let i = 0; i < source.length; i++) {
    if (source[i].name == val) {
      source[i].count = checked;
      if (checked == false) {
        source[i].distinct = checked;
      }
    }
  }
  fieldData.value = source;
  console.log(fieldData.value);
  let countField = source.filter(item => item.default && item.count == true);
  let count = countField.map(item => item.name);
  console.log(count);
  formState['countField'] = count;
  groupCountFieldsChange(null);
}

function countDistinctChange(e) {
  let val = e.target.name;
  console.log(e);
  let source = fieldData.value;
  let checked = e.target.checked;
  for (let i = 0; i < source.length; i++) {
    if (source[i].name == val) {
      source[i].distinct = checked;
    }
  }
  fieldData.value = source;
  console.log(fieldData.value);
  groupCountFieldsChange(null);
}

function CountFieldsDeChange(a, b) {
  console.log(a, b);
  let source = fieldData.value;
  for (let i = 0; i < source.length; i++) {
    if (source[i].name == a) {
      source[i].count = false;
      source[i].distinct = false;
    }
  }
  fieldData.value = source;
  groupCountFieldsChange(null);
}

const statisBtn = ref()
const showStatis = () => {
  statisBtn.value.$el.click()
}

function dateSelectChange(v) {
  console.log(v);
  let lastDate = queryParam.lastDate;
  if (lastDate && v) {
    setDateSpild(lastDate, v);
  }
}

function lastDateChange(v) {
  console.log(v);
  let dateSelect = queryParam.dateSelect;
  if (v && dateSelect) {
    setDateSpild(v, dateSelect);
  }
}

function setDateSpild(lastDate, dateSelect) {
  console.log(lastDate, dateSelect);
  let date = dayjs().subtract(lastDate, dateSelect).format("YYYY-MM-DD HH:mm:ss");
  let dateE = dayjs().format("YYYY-MM-DD HH:mm:ss");
  console.log(dateE, date);
  queryParam['startDateV'] = [date, dateE];
  handleChange([date, dateE]);
}

function reversalVerticalHorizontalChange(val) {
  console.log(val);
  if (val == 'horizontal') {
    reversalVerticalHorizontal.value = true;
  } else {
    reversalVerticalHorizontal.value = false;
  }
  initDownData();
}

function goOut() {
  push("/reportManage/ReportView");
}
</script>

<style lang="less" scoped>
.form-container {
  padding: 16px 16px 0;
}

.tab-content-wrapper {
  margin-top: 15px;

  /deep/ .ant-tabs-nav {
    margin: 0px !important;
  }
}

.inves-formItem {
  width: 10%;
  display: inline-block;
  padding: 0 5px;
}

.modal-top {
  padding: 8px 16px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-bottom: 1px solid @border-color;
}

[data-theme='dark'] .form-containe {
  background: #1A1B1F !important;
}

/deep/ #staticDiv .ant-dropdown.ant-dropdown-placement-bottomLeft {
  position: absolute;
  left: 10px;
}
</style>

<style scoped lang="less">
/deep/ .ant-dropdown-content {
  background: @dark-bg3 !important;
}

#selectDropdown::-webkit-scrollbar-track {
  background: #28282C !important;
  border-radius: 2px;
}

#selectDropdown::-webkit-scrollbar-thumb {
  background: black !important;
  border-radius: 8px;
}

/deep/ .ant-form-item {
  margin-bottom: 16px !important;
}
.report-content{
  background: @dark-bg2;
  padding-bottom: 16px;
}

</style>
