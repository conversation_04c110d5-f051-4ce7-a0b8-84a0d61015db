<template>
  <BasicModal v-bind="$attrs" @register="registerModal" @cancel="handleCancel" :title="title" @ok="handleSubmit" width="80%" :destroyOnClose="true">

    <a-form
      :model="formState"
      ref="formRef"
      name="basic"
      :layout="formLayout"
      :label-col="{ span: 24 }"
      :wrapper-col="{ span: 24 }"
      autocomplete="off"
      style="overflow-x: hidden;"
    >
      <a-row >
        <a-col :span="6">
          <a-form-item
            label="Socurity Posture"
            name="name"
            :rules="[{ required: true, message: t('common.inputText') }]"
          >
            <a-input v-model:value="formState.name" />
          </a-form-item>
        </a-col>
        <a-col :span="6">
          <a-form-item
            label="Data Source"
            name="tableCode"
            :rules="[{ required: true, message: t('common.inputText') }]"
          >
            <a-select v-model:value="formState.tableCode" @change="tableCodeChange">
              <a-select-option :value="item.value" v-for="(item,index) in DATA_SOURCE">{{ item.label }}</a-select-option>
            </a-select>
          </a-form-item>
        </a-col>
        <a-col :span="6" style="position:relative;">
          <a-form-item
            label="Advanced Filter"
            name="fieldsName"
          >
            <!--<a-input v-model:value="formState.fieldsName" style="width:calc(100% - 30px);" />-->
            <a-dropdown :trigger="['click']" v-model:visible="dropdownVisible">
              <a-button  @click.prevent style="position:absolute;top: 0px;" >
                <Icon icon="ant-design:plus-outlined"></Icon>{{t('common.add')}}
              </a-button>
              <template #overlay>
                <a-menu style="height: 300px;overflow: auto;">
                  <a-menu-item>
                    <a-input allow-clear @change.prevent="changeGroupByData" v-model:value="filterValue">
                      <template #prefix>
                        <Icon icon="ant-design:search-outlined"></Icon>
                      </template>
                    </a-input>
                  </a-menu-item>
                  <template v-for="(item,index) in fieldData">
                    <a-menu-item v-if="!item.filter">
                      <a-checkbox @change="gbChange" :checked="item.default" :value="item.name">{{item.name}}</a-checkbox>
                    </a-menu-item>
                  </template>


                </a-menu>
              </template>
            </a-dropdown>
          </a-form-item>
        </a-col>
      </a-row>

      <a-row >
        <a-row >
          <h1>Filter</h1><br/>
        </a-row>
        <a-form
          :model="queryParam"
          name="basic"
          :layout="formLayout"
          :label-col="{ span: 24 }"
          :wrapper-col="{ span: 24 }"
          style="width:100%;overflow-x:hidden;"
        >
          <a-row :gutter="12" v-if="formState.tableCode=='tbl_risk_event'">
            <a-col :span="3">
              <a-form-item
                :label="t('routes.riskEvent.ruleName')"
                :name="ruleName">
                <a-input v-model:value="queryParam.ruleName" @change="initDownData" />
              </a-form-item>
            </a-col>
            <a-col :span="3">
              <a-form-item
                :label="t('routes.aggregationrule.riskType')"
                :name="riskType">
                <a-select v-model:value="queryParam.riskType" @change="initDownData" allowClear>
                  <a-select-option :value="item.value" v-for="(item,index) in RULE_RISK_TYPE">{{ item.label }}</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :span="3">
              <a-form-item
                :label="t('routes.riskEvent.disposition')"
                :name="disposition">
                <a-select v-model:value="queryParam.disposition" @change="initDownData" allowClear>
                  <a-select-option value="1">Unknown</a-select-option>
                  <a-select-option value="2">False Position</a-select-option>
                  <a-select-option value="3">Ture Position</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :span="3">
              <a-form-item
                :label="t('routes.riskEvent.ruleUrgency')"
                :name="ruleUrgency">
                <a-select v-model:value="queryParam.ruleUrgency" @change="initDownData" allowClear>
                  <a-select-option value="1">Critical</a-select-option>
                  <a-select-option value="2">High</a-select-option>
                  <a-select-option value="3">Medium</a-select-option>
                  <a-select-option value="4">Low</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :span="3">
              <a-form-item
                :label="t('routes.riskEvent.status')"
                :name="statusStr">
                <a-select v-model:value="queryParam.statusStr" mode="multiple" @change="initDownData">
                  <a-select-option value="1">New</a-select-option>
                  <a-select-option value="2">Pending</a-select-option>
                  <a-select-option value="3">Investigating</a-select-option>
                  <a-select-option value="4">Closed</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :span="3">
              <a-form-item
                :label="t('routes.riskEvent.owner')"
                :name="owner">
                <JDictSelectTag  placeholder="请选择" v-model:value="queryParam.owner" allowClear dictCode="sysUserNameDict" @change="initDownData">
                </JDictSelectTag>
              </a-form-item>
            </a-col>
            <a-col :span="6">
              <a-form-item
                :label="t('routes.riskEvent.alarmTime')"
                :name="startDate">
                <a-range-picker v-model:value="queryParam.startDateV" :show-time="true" valueFormat="YYYY-MM-DD HH:mm:ss" @change="handleChange"/>
              </a-form-item>
            </a-col>
          </a-row>
          <a-row :gutter="12" v-if="formState.tableCode=='tbl_investigation'">
            <a-col :span="3">
              <a-form-item
                :label="t('routes.investigation.query.search')"
                :name="investigation">
                <a-input v-model:value="queryParam.investigation" @change="initDownData" />
              </a-form-item>
            </a-col>
            <a-col :span="3">
              <a-form-item
                :label="t('routes.investigation.creator')"
                :name="creator">
                <JDictSelectTag  placeholder="请选择" v-model:value="queryParam.creator" allowClear dictCode="sysUserNameDict" @change="initDownData">
                </JDictSelectTag>
              </a-form-item>
            </a-col>
            <a-col :span="3">
              <a-form-item
                :label="t('routes.investigation.members')"
                :name="members">
                <JSelectMultiple  placeholder="请选择" v-model:value="queryParam.members" allowClear dictCode="sysUserNameDict" @change="initDownData">
                </JSelectMultiple>
              </a-form-item>
            </a-col>
            <a-col :span="6">
              <a-form-item
                :label="t('routes.investigation.query.time')"
                :name="startDate">
                <a-range-picker v-model:value="queryParam.startDateV" :show-time="false" valueFormat="YYYY-MM-DD" style="width:100%;" @change="handleChange"/>
              </a-form-item>
            </a-col>
            <a-col :span="3">
              <a-form-item
                :label="t('routes.investigation.status')"
                :name="statusStr">
                <a-select v-model:value="queryParam.statusStr" mode="multiple" @change="initDownData">
                  <a-select-option value="In Process">In Process</a-select-option>
                  <a-select-option value="Pause">Pause</a-select-option>
                  <a-select-option value="Closed">Closed</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :span="3">
              <a-form-item
                :label="t('routes.investigation.severity')"
                :name="ruleUrgency">
                <a-select v-model:value="queryParam.ruleUrgency" @change="initDownData" allowClear>
                  <a-select-option value="Critical">Critical</a-select-option>
                  <a-select-option value="High">High</a-select-option>
                  <a-select-option value="Medium">Medium</a-select-option>
                  <a-select-option value="Low">Low</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :span="3">
              <a-form-item
                :label="t('routes.investigation.query.isMe')"
                :name="isMe">
                <JCheckbox :options="[{label:'Include ME', value: '1'}]" v-model:value="queryParam.isMe" @change="initDownData" />
              </a-form-item>
            </a-col>
          </a-row>


        </a-form>
      </a-row>

      <a-row >
        <a-col :span="6">
          <a-form-item
            label="Chart Name"
            name="viewType"
          >
            <a-radio-group v-model:value="viewType" @change="viewTypeChange">
              <a-radio value="1">Table</a-radio>
              <a-radio value="2">Line Chart</a-radio>
              <a-radio value="3">Bar Chart</a-radio>
              <a-radio value="4" :disabled="queryIsDisabled()">Per Chart</a-radio>
            </a-radio-group>
          </a-form-item>
        </a-col>
        <a-col :span="4">
          <a-form-item
            label="Primary keys"
            name="groupField"
            :rules="[{ required: true, message: t('common.inputText') }]">
            <a-select v-model:value="formState.groupField" :mode="viewType==1?'multiple':''" @change="groupCountFieldsChange" allowClear>
              <template v-for="(item,index) in fieldData">
                <a-select-option :value="item.name"  v-if="item.default">{{ item.name }}</a-select-option>
              </template>
            </a-select>
          </a-form-item>
        </a-col>
        <a-col :span="4">
          <a-form-item
            label="Statistical fields"
            name="countField"
            :rules="[{ required: true, message: t('common.inputText') }]">
            <a-select v-model:value="formState.countField" mode="multiple" @change="groupCountFieldsChange" @deselect="CountFieldsDeChange" :open="false" style="width:75%;">
              <template v-for="(item,index) in fieldData">
                <a-select-option :value="item.name"  v-if="item.default">{{ item.name }}</a-select-option>
              </template>
            </a-select>
            <a-dropdown :trigger="['click']" v-model:visible="dropdownCountVisible">
              <a-button  @click.prevent style="position:absolute;top: 0px;" >
                <Icon icon="ant-design:plus-outlined"></Icon>{{t('common.add')}}
              </a-button>
              <template #overlay>
                <a-menu style="height: 300px;overflow: auto;">
                  <template v-for="(item,index) in fieldData">
                    <a-menu-item v-if="item.default">
                      <a-checkbox @change="countChange" :name="item.name" v-model:checked="item.count">{{item.name}}</a-checkbox>
                      <div style="float:right;" v-if="item.count">
                        Duplicate removal statistics
                        <a-checkbox @change="countDistinctChange" :name="item.name" v-model:checked="item.distinct" style="margin-left:5px;"></a-checkbox>
                      </div>
                    </a-menu-item>
                  </template>
                </a-menu>
              </template>
            </a-dropdown>
          </a-form-item>
        </a-col>
        <a-col :span="3">
          <a-form-item
            label="Display quantity"
            name="groupNum">
            <a-input-number  v-model:value="formState.groupNum" :min="1" @change="initDownData" />
          </a-form-item>
        </a-col>
        <a-col :span="3">
          <a-form-item
            label="Sorting Fields"
            name="orderFields">
            <a-select v-model:value="formState.orderFields" @change="initDownData" allowClear>
              <a-select-option :value="item" v-for="(item,index) in orderFieldData">{{ item }}</a-select-option>
            </a-select>
          </a-form-item>
        </a-col>
        <a-col :span="3">
          <a-form-item
            label="Ascending/Descending"
            name="orderFields">
            <a-select v-model:value="formState.orderFieldsAscDesc" @change="initDownData" allowClear>
              <a-select-option value="asc">Asc</a-select-option>
              <a-select-option value="desc">Desc</a-select-option>
            </a-select>
          </a-form-item>
        </a-col>

      </a-row>
    </a-form>


    <a-row>
      <a-form
        :model="formState"
        name="basic"
        ref="formRef2"
        :layout="formLayout"
        :label-col="{ span: 24 }"
        :wrapper-col="{ span: 24 }"
        autocomplete="off"
        style="overflow-x: hidden;width:100%;"
      >

      </a-form>
    </a-row>


    <a-row>
      <h3>Preview</h3>
    </a-row>
    <a-row>
      <a-col :span="24" v-if="viewType==1">
        <ReportViewTable ref="reportTable"></ReportViewTable>
      </a-col>
      <a-col :span="24" v-if="viewType==2">
        <ReportViewLine ref="reportLine"></ReportViewLine>
      </a-col>
      <a-col :span="24" v-if="viewType==3">
        <ReportViewBar ref="reportBar"></ReportViewBar>
      </a-col>
      <a-col :span="24" v-if="viewType==4">
        <ReportViewPre ref="reportPre"></ReportViewPre>
      </a-col>
    </a-row>


  </BasicModal>
</template>

<script lang="ts" setup>
import {ref, computed, unref, reactive, onMounted, toRaw , nextTick} from 'vue';
import {BasicModal, useModalInner} from '/@/components/Modal';
import {ApiSelect, BasicForm, JDictSelectTag, useForm,JSelectMultiple,JCheckbox} from '/@/components/Form/index';
import { formLayout } from '/@/settings/designSetting';
import ReportViewTable from "/@/views/reportManage/ReportViewTable.vue";
import ReportViewLine from "/@/views/reportManage/ReportViewLine.vue";
import ReportViewBar from "/@/views/reportManage/ReportViewBar.vue";
import ReportViewPre from "/@/views/reportManage/ReportViewPre.vue";
import {
  saveOrUpdate,
} from '../ReportView.api.ts';
import {useI18n} from "/@/hooks/web/useI18n";
import {EVENT_FIELD, EVENT_FILTER, DATA_SOURCE,INV_FIELD} from "/@/views/reportManage/Report.data";
import {formSchema, RULE_RISK_TYPE} from "/@/views/rule/aggregation/AggregationRule.data";
import {FormInstance} from "ant-design-vue";
const { t } = useI18n();
// Emits声明
const emit = defineEmits(['register','success']);
const isUpdate = ref(true);

//设置标题
const title = computed(() => (!unref(isUpdate) ? t('common.add') : t('common.editText')));
const formState = reactive({groupNum:10,source:1,tableCode:'tbl_risk_event'});
let fieldData = ref([]);
const filterValue = ref("");
const dropdownVisible = ref(false);
const dropdownCountVisible = ref(false);
let filterData = ref([]);
let queryParam = reactive({});
let orderFieldData = ref([]);
let viewType = ref("1");
const formRef = ref();
const formRef2 = ref();

const reportTable = ref();
const reportLine = ref();
const reportBar = ref();
const reportPre = ref();



for(let i=0;i<EVENT_FIELD.length;i++){
  let base = {};
  for(let j in EVENT_FIELD[i]){
    base[j] = EVENT_FIELD[i][j];
  }
  fieldData.value.push(base);
}


const [registerForm, {resetFields, setFieldsValue}] = useForm({
  // labelWidth: 150,
});
//表单赋值
const [registerModal, {setModalProps , closeModal}] = useModalInner(async (data) => {
  //重置表单
  //await resetFields();
  setModalProps({confirmLoading: false,showCancelBtn:data?.showFooter,showOkBtn:data?.showFooter});
  isUpdate.value = !!data?.isUpdate;
  if (unref(isUpdate)) {
    //表单赋值

    console.log(data.record);
    console.log(fieldData);
    let record = data.record;

    setFieldDataFunc(record.tableCode);//设置 FIELD_DATA

    let fieldsCode = record.fieldsCode;
    let groupFields = record.groupFields;
    let countFields = record.countFields;
    let groupNum = record.groupNum;
    let filterCode = record.filterCode;
    let name = record.name;
    let orderFields = record.orderFields;
    let orderAsc = record.orderAsc;
    let tableCode = record.tableCode;
    let distinctFields = record.distinctFields;
    viewType.value = record.viewType+"";
    formState['id'] = record.id;
    formState['name'] = name;
    formState['tableCode'] = tableCode;
    formState['groupNum'] = groupNum;
    if(groupFields){
      if(record.viewType==1){
        formState['groupField'] = groupFields.split(",");
      }else{
        formState['groupField'] = groupFields;
      }
    }
    if(groupFields && countFields){
      let group = formState.groupField;
      let count = formState.countField;
      let list = count;//group.concat(count);
      console.log(list);
      if(group && group.length>0 && count && count.length>0){
        orderFieldData.value = Array.from(new Set(list));
      }
    }
    if(countFields){
      formState['countField'] = countFields.split(",");
      let fieldArr = countFields.split(",");
      let source = fieldData.value;
      for(let i=0;i<fieldArr.length;i++){
        for(let j=0;j<source.length;j++){
          if(fieldArr[i]==source[j].name){
            source[j].count = true;
          }
        }
      }
      fieldData.value = source;
    }
    formState['orderFields'] = orderFields;
    formState['orderFieldsAscDesc'] = orderAsc;

    if(filterCode){
      let queryP = filterCode.split(";");
      for(let i=0;i<queryP.length;i++){
        let keyV = queryP[i].split(":");
        queryParam[keyV[0]] = keyV[1];
      }
    }

    if(fieldsCode){
      let fieldArr = fieldsCode.split(",");
      let source = fieldData.value;
      for(let i=0;i<fieldArr.length;i++){
        for(let j=0;j<source.length;j++){
          if(fieldArr[i]==source[j].name){
            source[j].default = true;
          }
        }
      }
      fieldData.value = source;
    }

    if(distinctFields){
      let fieldArr = distinctFields.split(",");
      let source = fieldData.value;
      for(let i=0;i<fieldArr.length;i++){
        for(let j=0;j<source.length;j++){
          if(fieldArr[i]==source[j].name){
            source[j].distinct = true;
          }
        }
      }
      fieldData.value = source;
    }
    console.log(fieldData.value);




    // await setFieldsValue({
    //   ...data.record,
    // });

  }
  initDownData();
});

const gbChange = (e) => {
  let val = e.target.value;
  let checked = e.target.checked;
  let source = fieldData.value;
  for(let i=0;i<source.length;i++){
    if(source[i].name == val){
      source[i].default = checked;
      if(checked==false){
        source[i].count = false;
        source[i].distinc = false;
      }
    }
  }
  fieldData.value = source;
  if(checked==false){
    let countFieldArr = formState.countField;
    if(countFieldArr){
      countFieldArr.splice(countFieldArr.indexOf(val), 1);
      formState["countField"] = countFieldArr;
    }
  }
}

//表单提交事件
async function handleSubmit() {
  formRef2.value.validate().then(() => {}).catch(error => {
    console.log('error', error);
  });
  formRef.value
    .validate()
    .then(() => {
      console.log(formState);
      let params: any = toRaw(formState)
      for (let key in params) {
        if (!params[key]) {
          params[key] = ""
        }
      }
      if(params.groupField){
        if(params.groupField instanceof Array){
          params['groupFields'] = params.groupField.join(",");
        }else{
          params['groupFields'] = params.groupField;
        }
      }
      if(params.countField){
        params['countFields'] = params.countField.join(",");
      }
      if(params.orderFieldsAscDesc){
        params['orderAsc'] = params.orderFieldsAscDesc;
      }
      let fieldDatas = fieldData.value.filter(item => item.default==true);
      let fieldsCode = [];
      let fieldsName = [];
      let distinctFields = [];
      for(let i=0;i<fieldDatas.length;i++){
        fieldsCode.push(fieldDatas[i].name);
        fieldsName.push(fieldDatas[i].field);
        if(fieldDatas[i].count==true && fieldDatas[i].distinct==true){
          distinctFields.push(fieldDatas[i].name);
        }
      }
      let queryP = [];
      for(let i in queryParam){
        if(!queryParam[i]){
          continue;
        }
        queryP.push(i+":"+queryParam[i]);
      }
      params['fieldsCode'] = fieldsCode.join(",");
      params['fieldsName'] = fieldsName.join(",");
      params['filterCode'] = queryP.join(";");
      params['viewType'] = viewType.value;
      if(params.tableCode=="tbl_risk_event"){
        params['timeField'] = "alarm_time";
      }else if(params.tableCode=="tbl_investigation"){
        params['timeField'] = "creation_time";
      }
      params['distinctFields'] = distinctFields.join(",");
      console.log('values', params);
      setModalProps({confirmLoading: true});
      saveOrUpdate(params, isUpdate.value).then(() => {
        closeModal();
        handleCancel();
        emit('success');
      });
      setModalProps({confirmLoading: false});


    })
    .catch(error => {
      console.log('error', error);
    });
}

const resetForm = () => {
  for(let i in formState){
    formState[i]=null;
  }
  formState['groupNum'] = 10;
  formState['source'] = 1;
  formState['tableCode'] = 'tbl_risk_event';
  formRef.value.resetFields();
  formRef2.value.resetFields();
};

function handleCancel(v){
  resetForm();
  console.log(EVENT_FIELD);
  fieldData.value = EVENT_FIELD;
  filterValue.value = [];
  filterData.value = [];
  for(let i in queryParam){
    queryParam[i] = null;
  }
  orderFieldData.value = [];
  viewType.value = "1";

}

function changeGroupByData(e){
  console.log(e);
  let val = filterValue.value;
  console.log("filterValue",val)
  let source = fieldData.value;
  for(let i=0;i<source.length;i++){
    if(source[i].name.indexOf(val)==-1){
      source[i].filter = true;
    }
  }
  if(!val){
    for(let i=0;i<source.length;i++){
      source[i].filter = false;
    }
  }
  fieldData.value = source;
}


function initTable(){
  let tableCode = formState.tableCode;
  let fieldDataV = fieldData.value;
  let group = formState.groupField;
  let count = formState.countField;
  let orderNum = formState.groupNum;
  let orderFields = formState.orderFields;
  let orderFieldsAscDesc = formState.orderFieldsAscDesc;
  let distinctFields = fieldDataV.filter(item => item.default==true && item.count==true && item.distinct==true );
  //let queryParam = queryParam;
  if(group && !count){
    return;
  }
  if(!group && count){
    return;
  }
  console.log(fieldDataV);
  let record = {
    tableCode:tableCode,
    fieldData:fieldData,
    group:group,
    count:count,
    orderNum:orderNum,
    orderFields:orderFields,
    orderAscDesc:orderFieldsAscDesc,
    distinctFields:distinctFields.map(item => item.name).join(","),
    queryParam:queryParam
  };
  console.log(record);
  reportTable.value.init(record);
}

function initDownData(){
  let tableCode = formState.tableCode;
  let viewTypeV = viewType.value;
  console.log("tableCode="+tableCode);
  console.log("viewType="+viewTypeV);
  nextTick(()=>{
    //if (tableCode=='tbl_risk_event'){
      if(viewTypeV==1){
        initTable();
      }else if(viewTypeV==2){
        initLine();
      }else if(viewTypeV==3){
        initBar();
      }else if(viewTypeV==4){
        initPre();
      }
    //}
  });
}

onMounted(() => {
  console.log(EVENT_FIELD);
  //initDownData();
});

function initLine(){
  let tableCode = formState.tableCode;
  let group = formState.groupField;
  let count = formState.countField;
  let orderNum = formState.groupNum;
  let orderFields = formState.orderFields;
  let orderFieldsAscDesc = formState.orderFieldsAscDesc;
  let distinctFields = fieldData.value.filter(item => item.default==true && item.count==true && item.distinct==true );
  //let queryParam = queryParam;
  if(group && !count){
    return;
  }
  if(!group && count){
    return;
  }
  if(!group && !count){
    return;
  }
  let record = {
    tableCode:tableCode,
    group:group,
    count:count,
    orderNum:orderNum,
    orderFields:orderFields,
    orderAscDesc:orderFieldsAscDesc,
    distinctFields:distinctFields.map(item => item.name).join(","),
    queryParam:queryParam
  };
  console.log(record);
  reportLine.value.init(record);
}

function initBar(){
  let tableCode = formState.tableCode;
  let group = formState.groupField;
  let count = formState.countField;
  let orderNum = formState.groupNum;
  let orderFields = formState.orderFields;
  let orderFieldsAscDesc = formState.orderFieldsAscDesc;
  let distinctFields = fieldData.value.filter(item => item.default==true && item.count==true && item.distinct==true );
  //let queryParam = queryParam;
  if(group && !count){
    return;
  }
  if(!group && count){
    return;
  }
  if(!group && !count){
    return;
  }
  let record = {
    tableCode:tableCode,
    group:group,
    count:count,
    orderNum:orderNum,
    orderFields:orderFields,
    orderAscDesc:orderFieldsAscDesc,
    distinctFields:distinctFields.map(item => item.name).join(","),
    queryParam:queryParam
  };
  console.log(record);
  reportBar.value.init(record);
}

function initPre(){
  let tableCode = formState.tableCode;
  let group = formState.groupField;
  let count = formState.countField;
  let orderNum = formState.groupNum;
  let orderFields = formState.orderFields;
  let orderFieldsAscDesc = formState.orderFieldsAscDesc;
  let distinctFields = fieldData.value.filter(item => item.default==true && item.count==true && item.distinct==true );
  //let queryParam = queryParam;
  if(group && !count){
    return;
  }
  if(!group && count){
    return;
  }
  if(!group && !count){
    return;
  }
  let record = {
    tableCode:tableCode,
    group:group,
    count:count,
    orderNum:orderNum,
    orderFields:orderFields,
    orderAscDesc:orderFieldsAscDesc,
    distinctFields:distinctFields.map(item => item.name).join(","),
    queryParam:queryParam
  };
  console.log(record);
  reportPre.value.init(record);
}

function groupCountFieldsChange(e){
  console.log(e);
  let source = orderFieldData.value;

  let group = formState.groupField;
  let count = formState.countField;
  //console.log(source,group,count);//getFieldsValue
  let list = count;//group.concat(count);
  if(list && list.length>1 && viewType.value == 4){
    viewType.value = "1";
    initDownData();
    return;
  }
  console.log(list);
  if(group && group.length>0 && count && count.length>0){
    orderFieldData.value = Array.from(new Set(list));
  }else{
    orderFieldData.value = [];
    formState.orderFields = null;
    formState.orderFieldsAscDesc = null;
  }
  let orderFields = formState.orderFields;
  if(orderFields && list.indexOf(orderFields)==-1){
    formState.orderFields = null;
  }
  initDownData();
}

function handleChange(arr){
  let str = ''
  if(arr && arr.length>0){
    if(arr[1] && arr[0]){
      str = arr.join(',')
    }
  }
  queryParam['startDate'] = str;
  initDownData();
}

function viewTypeChange(e){
  let val = e.target.value;
  console.log(val);
  if(val==1){
    formState['groupField'] = [];
  }else{
    formState['groupField'] = null;
  }

  initDownData();
}

function queryIsDisabled(){
  console.log(formState.countField);
  if(formState.countField){
    return formState.countField.length<2?false:true;
  }
  return false;
}

function tableCodeChange(e){
  console.log(e);
  let name = formState.name;
  for(let i in formState){
    formState[i]=null;
  }
  formState['groupNum'] = 10;
  formState['source'] = 1;
  formRef.value.resetFields();
  formRef2.value.resetFields();
  formState['tableCode'] = e;
  formState['name'] = name;
  setFieldDataFunc(e);

  initDownData();
}

function setFieldDataFunc(e){
  if(e=="tbl_risk_event"){
    let arr = [];
    for(let i=0;i<EVENT_FIELD.length;i++){
      let base = {};
      for(let j in EVENT_FIELD[i]){
        base[j] = EVENT_FIELD[i][j];
      }
      arr.push(base);
    }
    fieldData.value = arr
  }else if(e=="tbl_investigation"){
    let arr = [];
    for(let i=0;i<INV_FIELD.length;i++){
      let base = {};
      for(let j in INV_FIELD[i]){
        base[j] = INV_FIELD[i][j];
      }
      arr.push(base);
    }
    fieldData.value = arr
    console.log(fieldData.value);
  }
}

function countChange(e){
  let val = e.target.name;
  console.log(e);
  let source = fieldData.value;
  let checked = e.target.checked;
  for(let i=0;i<source.length;i++){
    if(source[i].name == val){
      source[i].count = checked;
      if(checked==false){
        source[i].distinct = checked;
      }
    }
  }
  fieldData.value = source;
  console.log(fieldData.value);
  let countField = source.filter(item => item.default && item.count==true);
  let count = countField.map(item => item.name);
  console.log(count);
  formState['countField'] = count;
  groupCountFieldsChange(null);
}

function countDistinctChange(e){
  let val = e.target.name;
  console.log(e);
  let source = fieldData.value;
  let checked = e.target.checked;
  for(let i=0;i<source.length;i++){
    if(source[i].name == val){
      source[i].distinct = checked;
    }
  }
  fieldData.value = source;
  console.log(fieldData.value);
  groupCountFieldsChange(null);
}

function CountFieldsDeChange(a,b){
  console.log(a,b);
  let source = fieldData.value;
  for(let i=0;i<source.length;i++){
    if(source[i].name == a){
      source[i].count = false;
      source[i].distinct = false;
    }
  }
  fieldData.value = source;
  groupCountFieldsChange(null);
}

</script>

<style lang="less" scoped>

</style>
