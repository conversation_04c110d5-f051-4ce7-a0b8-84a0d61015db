import {defHttp} from '/@/utils/http/axios';
import {Modal} from 'ant-design-vue';

enum Api {
  list = '/report/reportViewInfo/list',
  save='/report/reportViewInfo/add',
  edit='/report/reportViewInfo/edit',
  deleteOne = '/report/reportViewInfo/delete',
  deleteBatch = '/report/reportViewInfo/deleteBatch',
  queryViewByTemplateId = "/report/reportViewInfo/queryViewByTemplateId",
  reportViewList = '/report/reportViewInfo/reportViewList',
  reportViewChart = '/report/reportViewInfo/reportViewChart',
  doDuplicateCheck = '/report/reportViewInfo/doDuplicateCheck',
}
/**
 * 列表接口
 * @param params
 */
export const reportViewlist = (params) =>
  defHttp.get({url: Api.list, params});

/**
 * 删除单个
 * @param params
 * @param handleSuccess
 */
export const deleteOne = (params,handleSuccess) => {
  return defHttp.delete({url: Api.deleteOne, params}, {joinParamsToUrl: true}).then(() => {
    handleSuccess();
  });
}
/**
 * 批量删除
 * @param params
 * @param handleSuccess
 */
export const batchDelete = (params, handleSuccess) => {
  Modal.confirm({
    title: '确认删除',
    content: '是否删除选中数据',
    okText: '确认',
    cancelText: '取消',
    onOk: () => {
      return defHttp.delete({url: Api.deleteBatch, data: params}, {joinParamsToUrl: true}).then(() => {
        handleSuccess();
      });
    }
  });
}
/**
 * 保存或者更新
 * @param params
 * @param isUpdate 是否是更新数据
 */
export const saveOrUpdate = (params, isUpdate) => {
  let url = isUpdate ? Api.edit : Api.save;
  return defHttp.post({url: url, params});
}

export const queryViewByTemplateIdRequest = (params) =>
  defHttp.get({url: Api.queryViewByTemplateId, params});


export const reportViewListRequest = (params) =>
  defHttp.get({url: Api.reportViewList, params});

export const reportViewChartRequest = (params) =>
  defHttp.get({url: Api.reportViewChart, params});


export const doDuplicateCheckRequest = (params) =>
   defHttp.post({url: Api.doDuplicateCheck, params},{ isTransformResponse: false });


