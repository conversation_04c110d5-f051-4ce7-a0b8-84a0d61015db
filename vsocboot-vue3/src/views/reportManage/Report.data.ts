import {useI18n} from "/@/hooks/web/useI18n";
import {RULE_RISK_TYPE} from "/@/views/rule/aggregation/AggregationRule.data";
import {fieldList} from "/@/views/riskLogs/RiskLogs.api";
const { t} = useI18n();
export const VIEW_TYPE:any[] = [{
  label: 'Table',
  value: 1,
  key: '1',
},{
  label: 'Line Chart',
  value: 2,
  key: '2',
},{
  label: 'Bar Chart',
  value: 3,
  key: '3',
},{
  label: 'Pie Chart',
  value: 4,
  key: '4',
// },{
//   label: 'Count',
//   value: 5,
//   key: '5',
}];

export const DATA_SOURCE:any[] = [{
  label: 'Risk Events Security',
  value: 'sec',
  key: '1',
},{
  label: 'Risk Events Host',
  value: 'host',
  key: '2',
},{
//   label: 'Risk Events',
//   value: 'tbl_risk_event',
//   key: '1',
// },{
  label: 'Investigations',
  value: 'inv',
  key: '3',
},{
  label: 'Security Log',
  value: 'log',
  key: '4',
},{
  label: 'Host Log',
  value: 'hostLog',
  key: '5',
},{
  label: 'Network Log',
  value: 'networkLog',
  key: '6',
},{
  label: 'Operation Log',
  value: 'operationLog',
  key: '7',
},{
  label: 'Workflow',
  value: 'workflow',
  key: '8',
}];

// tbl_aggregation_risk_event_security
export const RISK_EVENT_SEC_FIELD = ['event_id','src_ip','dst_ip','dst_port','event_type','event_name','aggregation_key','merge_count','event_status','event_level','owner','create_time','update_time','from_ip','owner_time','close_time','remark'];
export const RISK_EVENT_SEC_TABLE_DEFAULT_FIELD = ['event_name','event_type','src_ip','dst_ip','dst_port','merge_count','event_status','event_level','update_time','owner'];
export const EVENT_SEC_FIELD:any[] = [];
for(let i=0;i<RISK_EVENT_SEC_FIELD.length;i++){
  EVENT_SEC_FIELD.push({
    name:RISK_EVENT_SEC_FIELD[i],
    field: getCamelCase( RISK_EVENT_SEC_FIELD[i] ),
    default:RISK_EVENT_SEC_TABLE_DEFAULT_FIELD.indexOf(RISK_EVENT_SEC_FIELD[i])>-1,
    filter:false
  });
}

// tbl_aggregation_risk_event_host
export const RISK_EVENT_HOST_FIELD = ['event_id','detection_rule_id','detection_rule_title','detection_rule_tags','event_type','from_ip','aggregation_key','merge_count','event_status','owner','create_time','update_time','owner_time','close_time','remark'];
export const RISK_EVENT_HOST_TABLE_DEFAULT_FIELD = ['detection_rule_title','detection_rule_tags','event_type','from_ip','merge_count','event_status','update_time','owner'];
export const EVENT_HOST_FIELD:any[] = [];
for(let i=0;i<RISK_EVENT_HOST_FIELD.length;i++){
  EVENT_HOST_FIELD.push({
    name:RISK_EVENT_HOST_FIELD[i],
    field: getCamelCase( RISK_EVENT_HOST_FIELD[i] ),
    default:RISK_EVENT_HOST_TABLE_DEFAULT_FIELD.indexOf(RISK_EVENT_HOST_FIELD[i])>-1,
    filter:false
  });
}

// act_hi_procinst
export const WORKFLOW_ALL_FIELD = ['process_id','flow_name','flow_type','start_user_id','assignee','start_time','end_time','duration','state','bis_number'];
export const WORKFLOW_TABLE_DEFAULT_FIELD = ['flow_name','flow_type','start_user_id','start_time','end_time','duration','state'];
export const WORKFLOW_FIELD:any[] = [];
for(let i=0;i<WORKFLOW_ALL_FIELD.length;i++){
  WORKFLOW_FIELD.push({
    name:WORKFLOW_ALL_FIELD[i],
    field: getCamelCase( WORKFLOW_ALL_FIELD[i] ),
    default:WORKFLOW_TABLE_DEFAULT_FIELD.indexOf(WORKFLOW_ALL_FIELD[i])>-1,
    filter:false
  });
}

export const EVENT_FIELD:any[] = [
  {
    name:"id",
    field:"id",
    default:false,
    filter:false,
  },
  {
    name:"rule_name",
    field:"ruleName",
    default:true,
    filter:false,
  },
  {
    name:"risk_type",
    field:"riskType",
    default:true,
    filter:false,
  },
  {
    name:"risk_object",
    field:"riskObject",
    default:true,
    filter:false,
  },
  {
    name:"risk_count",
    field:"riskCount",
    default:true,
    filter:false,
  },
  {
    name:"alarm_time",
    field:"alarmTime",
    default:true,
    filter:false,
  },
  {
    name:"disposition",
    field:"disposition",
    default:true,
    filter:false,
  },
  {
    name:"status",
    field:"status",
    default:true,
    filter:false,
  },
  {
    name:"owner",
    field:"owner",
    default:true,
    filter:false,
  },
  {
    name:"create_time",
    field:"createTime",
    default:false,
    filter:false,
  },
  {
    name:"create_by",
    field:"createBy",
    default:false,
    filter:false,
  },
  {
    name:"update_time",
    field:"updateTime",
    default:false,
    filter:false,
  },
  {
    name:"update_by",
    field:"updateBy",
    default:false,
    filter:false,
  },
  {
    name:"rule_id",
    field:"ruleId",
    default:false,
    filter:false,
  },{
    name:"rule_urgency",
    field:"ruleUrgency",
    default:true,
    filter:false,
  },
  {
    name:"rule_risk_groupby",
    field:"ruleRiskGroupby",
    default:false,
    filter:false,
  },
  {
    name:"rule_min_limits",
    field:"ruleMinLimits",
    default:false,
    filter:false,
  },
  {
    name:"rule_max_limits",
    field:"rulemaxLimits",
    default:false,
    filter:false,
  },
  {
    name:"rule_time_threshold",
    field:"ruleTimeThreshold",
    default:false,
    filter:false,
  },
  {
    name:"rule_risk_desc",
    field:"ruleRiskDesc",
    default:false,
    filter:false,
  },
  {
    name:"rule_advanced_rules",
    field:"ruleAdvancedRules",
    default:false,
    filter:false,
  },
  {
    name:"rule_time_threshold_type",
    field:"ruleTimeThresholdType",
    default:false,
    filter:false,
  },
  {
    name:"remark",
    field:"remark",
    default:false,
    filter:false,
  },
  {
    name:"close_time",
    field:"closeTime",
    default:false,
    filter:false,
  },
  {
    name:"owner_time",
    field:"ownerTime",
    default:false,
    filter:false,
  }
];

export const EVENT_FILTER:any[] = [
  {
    label: t('routes.riskEvent.ruleName'),
    field: 'ruleName',
    component: 'Input',

  },
  {
    label: t('routes.aggregationrule.riskType'),
    field: 'riskType',
    component: 'Select',
    componentProps: {
      options: RULE_RISK_TYPE,
      stringToNumber: true,
    }
  },
  {
    label:  t('routes.riskEvent.disposition'),
    field: 'disposition',
    component: 'JSelectInput',

    componentProps: {
      options: [
        {label: 'Unknown', value: '1'},
        {label: 'False Position', value: '2'},
        {label: 'Ture Position', value: '3'}
      ],
    },
  },
  {
    label:  t('routes.riskEvent.ruleUrgency'),
    field: 'ruleUrgency',
    component: 'JSelectInput',

    componentProps: {
      options: [
        {label: 'Critical', value: '1'},
        {label: 'High', value: '2'},
        {label: 'Medium', value: '3'},
        {label: 'Low', value: '4'},
      ],
    },
  },
  {
    label:  t('routes.riskEvent.status'),
    field: 'statusStr',
    component: 'JSelectMultiple',
    colProps: {
      span: 3,
    },
    componentProps: {
      options: [
        {label: 'New', value: '1'},
        {label: 'Pending', value: '2'},
        {label: 'Investigating', value: '3'},
        {label: t('common.Closed'), value: '4'},
      ],
    },
  },
  {
    label: t('routes.riskEvent.owner'),
    field: 'owner',
    component: 'JDictSelectTag',
    componentProps: {
      dictCode: 'sysUserNameDict',
    }
  },
  {
    label: t('routes.riskEvent.alarmTime'),
    field: 'startDate',
    component: 'RangeDate',
    componentProps:{
      datetime:true,
    },
  },
];

export const INV_FIELD:any[] = [
  {
    name:"id",
    field:"id",
    default:false,
    filter:false,
  },
  {
    name:"investigation",
    field:"investigation",
    default:true,
    filter:false,
  },
  {
    name:"risk_events",
    field:"riskEvents",
    default:true,
    filter:false,
  },
  {
    name:"creator",
    field:"creator",
    default:true,
    filter:false,
  },
  {
    name:"status",
    field:"status",
    default:true,
    filter:false,
  },
  {
    name:"creation_time",
    field:"creationTime",
    default:true,
    filter:false,
  },
  {
    name:"severity",
    field:"severity",
    default:true,
    filter:false,
  },
  {
    name:"priority",
    field:"priority",
    default:true,
    filter:false,
  },
  {
    name:"discription",
    field:"discription",
    default:false,
    filter:false,
  },
  {
    name:"category",
    field:"category",
    default:false,
    filter:false,
  },
  {
    name:"closeTime",
    field:"closeTime",
    default:false,
    filter:false,
  },
  {
    name:"conclusion",
    field:"conclusion",
    default:false,
    filter:false,
  },
  {
    name:"tag",
    field:"tag",
    default:false,
    filter:false,
  },
];
export const LOGS_FIELD:any[] = [];

export const LOGS_HOST_FIELD:any[] = [];

export const LOGS_NETWORK_FIELD:any[] = [];

export const LOGS_OPERATION_FIELD:any[] = [];

class Demo{
  private static instance:Array;
  private constructor(public arr) {}
  public static getInstance(){
    if(!this.instance){
      let data = queryLogList();
      console.log(data.then());
      this.instance = data;
    }
    return this.instance;
  }
}
//const dome = Demo.getInstance();
//console.log(dome);
const logTableDefault = ['ck_enter_date','enter_date','event_type','event_name','src_ip','dst_ip','src_port','dst_port','proxy_ip','event_level'];
queryLogList();
async function queryLogList(){
  let source = [];
  await fieldList({fieldSource:"1"}).then((res)=>{
    console.log(res);
    for(let i=0;i<res.length;i++){
      LOGS_FIELD.push({
        name:res[i].fieldValue,
        field: getCamelCase( res[i].fieldValue ),
        default:logTableDefault.indexOf(res[i].fieldValue)>-1,
        filter:false
      });
    }
  });
  return source;
}
const logHostTableDefault = ['ck_enter_date'	,'enter_date'	,'from_ip','host_hostname','host_event_type','host_severity','host_source_name','host_domain'];
queryLogHostList();
async function queryLogHostList(){
  let source = [];
  await fieldList({fieldSource:"2"}).then((res)=>{
    console.log(res);
    for(let i=0;i<res.length;i++){
      LOGS_HOST_FIELD.push({
        name:res[i].fieldValue,
        field: getCamelCase( res[i].fieldValue ),
        default:logHostTableDefault.indexOf(res[i].fieldValue)>-1,
        filter:false
      });
    }
  });
  return source;
}

const logNetworkTableDefault = ['ck_enter_date'	,'enter_date'	,'from_ip','src_ip','dst_ip','src_port','dst_port','action',"action_level"];
queryLogNetworkList();
async function queryLogNetworkList(){
  let source = [];
  await fieldList({fieldSource:"3"}).then((res)=>{
    console.log(res);
    for(let i=0;i<res.length;i++){
      LOGS_NETWORK_FIELD.push({
        name:res[i].fieldValue,
        field: getCamelCase( res[i].fieldValue ),
        default:logNetworkTableDefault.indexOf(res[i].fieldValue)>-1,
        filter:false
      });
    }
  });
  return source;
}

const logOperationTableDefault = ['ck_enter_date'	,'enter_date'	,'from_ip','operation_hostname','operation_module','operation_time','operation_user','operation_result'];
queryLogOperationList();
async function queryLogOperationList(){
  let source = [];
  await fieldList({fieldSource:"4"}).then((res)=>{
    console.log(res);
    for(let i=0;i<res.length;i++){
      LOGS_OPERATION_FIELD.push({
        name:res[i].fieldValue,
        field: getCamelCase( res[i].fieldValue ),
        default:logOperationTableDefault.indexOf(res[i].fieldValue)>-1,
        filter:false
      });
    }
  });
  return source;
}

function getCamelCase( str ) {
  let arr = str.split( '_' );
  return arr.map( function( item, index ) {
    if( index === 0 ){
      return item;
    }else{
      return item.charAt(0).toUpperCase() + item.slice( 1 );
    }
  }).join('');
}


console.log(LOGS_FIELD);
