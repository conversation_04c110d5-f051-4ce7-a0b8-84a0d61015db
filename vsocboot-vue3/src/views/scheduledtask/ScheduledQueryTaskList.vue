<template>
  <div>
    <!--引用表格-->
   <BasicTable @register="registerTable" :rowSelection="rowSelection" :isSearch="isSearch">
     <!--插槽:table标题-->
          <template #form-formFooter>
             <a-dropdown v-if="selectedRowKeys.length > 0">
                 <template #overlay>
                    <a-menu>
                      <a-menu-item key="1" @click="batchHandleDelete">
                        <Icon icon="ant-design:delete-outlined"/>
                        {{t('common.delText')}}
                      </a-menu-item>
                    </a-menu>
                  </template>
                  <a-button>{{t('common.batch')}}
                    <Icon icon="mdi:chevron-down"/>
                  </a-button>
            </a-dropdown>
            <!--
            <a-button  :class="{'btn-checked': isSearch == true}" type="text" @click="isSearch=!isSearch" preIcon="ant-design:filter-outlined"> {{ t('common.filter') }}</a-button>
            -->
            <a-button type="primary" @click="handleAdd" preIcon="ant-design:plus-outlined"> {{t('common.new')}}</a-button>
            <a-button  type="primary" preIcon="ant-design:export-outlined" @click="onExportXls"> {{t('common.exportText')}}</a-button>
            <j-upload-button  type="primary" preIcon="ant-design:import-outlined" @click="onImportXls">{{t('common.importText')}}</j-upload-button>
          </template>

        <template #enabled="{ text }">
          <a-tag :color="text === 1 ? 'success' : 'error'">
            {{ text === 1 ? t('common.yes') : t('common.no') }}
          </a-tag>
        </template>

       <!--操作栏-->
      <template #action="{ record }">
        <TableAction :actions="getTableAction(record)" :dropDownActions="getDropDownAction(record)"/>
      </template>
    </BasicTable>

    <!-- 表单区域 -->
    <ScheduledQueryTaskModal @register="registerModal" @success="handleSuccess"/>
  </div>
</template>

<script lang="ts" name="scheduled_task-scheduledQueryTask" setup>
  import {ref} from 'vue';
  import {BasicTable, TableAction} from '/@/components/Table';
  import {useModal} from '/@/components/Modal';
  import { useListPage } from '/@/hooks/system/useListPage'
  import ScheduledQueryTaskModal from './modules/ScheduledQueryTaskModal.vue'
  import {columns, searchFormSchema} from './ScheduledQueryTask.data';
  import {list, deleteOne, batchDelete, getImportUrl,getExportUrl} from './ScheduledQueryTask.api';
  import {useI18n} from "/@/hooks/web/useI18n";
  import {formLayout} from '/@/settings/designSetting';

  const { t } = useI18n();
  const isSearch = ref<boolean>(true);
  //注册model
  const [registerModal, {openModal}] = useModal();
  //注册table数据
  const { tableContext,onExportXls,onImportXls } = useListPage({
       tableProps:{
            title: t('routes.scheduledtask.ScheduledQueryTaskList.title'),
            api: list,
            rowKey:'taskId',
            columns,
            canResize:false,
            formConfig: {
               labelWidth: 120,
               schemas: searchFormSchema,
               autoSubmitOnEnter:true,
               showAdvancedButton:true,
               layout: formLayout,
             },
            actionColumn: {
                width: 120,
                fixed: 'right',
             },
        },
        exportConfig: {
             name:t('routes.scheduledtask.ScheduledQueryTaskList.title'),
             url: getExportUrl,
           },
           importConfig: {
             url: getImportUrl
           },
   })

   const [registerTable, {reload},{ rowSelection, selectedRowKeys }] = tableContext

   /**
    * 新增事件
    */
   function handleAdd() {
     openModal(true, {
       isUpdate: false,
       showFooter: true,
     });
   }
   /**
    * 编辑事件
    */
  function handleEdit(record: Recordable) {
     openModal(true, {
       record,
       isUpdate: true,
       showFooter: true,
     });
   }
   /**
    * 详情
   */
  function handleDetail(record: Recordable) {
     openModal(true, {
       record,
       isUpdate: true,
       showFooter: false,
     });
   }
   /**
    * 删除事件
    */
   async function handleDelete(record) {
     await deleteOne({id: record.taskId}, reload);
   }
   /**
    * 批量删除事件
    */
   async function batchHandleDelete() {
     await batchDelete({ids: selectedRowKeys.value}, reload);
   }
   /**
    * 成功回调
    */
   function handleSuccess() {
      reload();
   }
   /**
      * 操作栏
      */
   function getTableAction(record){
       return [
         {
           label: t('common.editText'),
           onClick: handleEdit.bind(null, record),
         }
       ]
     }
     /**
       * 下拉操作栏
       */
    function getDropDownAction(record){
      return [
           {
             label: t('common.details'),
             onClick: handleDetail.bind(null, record),
           }, {
             label: t('common.delText'),
             popConfirm: {
               title: t('common.delConfirmText'),
               confirm: handleDelete.bind(null, record),
               placement: 'left'
             }
           }
      ]
    }
</script>
<style scoped>

</style>
