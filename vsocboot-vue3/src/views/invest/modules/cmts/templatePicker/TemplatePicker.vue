<template>
  <div style="display: flex;gap:8px;flex-wrap: wrap;">
    <div class="investigation-container select_template" @click="selectTemplate($event,'0')">
      <div class="ft16-bold f-color-1 mb-[16px]">{{ t('normalInvestigation') }}</div>
      <div class="node-section">
        <div class="template-title">{{ t('node') }}</div>
        <div class="node-buttons">
          <span v-for="name in nodeButtons" :key="name" class="node-button">
            {{ name }}
          </span>
        </div>
      </div>
      <div class="search-template-section">
        <div class="template-title">{{ t('searchTemplate') }}</div>
        <div class="node-buttons">
          <span class="node-button">
            {{ ct('common.none') }}
          </span>
        </div>
      </div>
      <div class="proposal-section">
        <div class="template-title">{{ t('proposal') }}</div>
        <div class="ft12-bold f-color-08">{{ ct('common.none') }}</div>
      </div>
    </div>
    <div class="investigation-container" v-for="item in templateList" :key="item.id"
         @click="selectTemplate($event,item.id)">
      <div class="ft16-bold f-color-1 mb-[16px]">{{ item.templateName }}</div>
      <div class="node-section">
        <div class="template-title">{{ t('node') }}</div>
        <div class="node-buttons">
          <template v-if="item.nodeNames">
            <span v-for="name in item.nodeNames.split(',')" :key="name" class="node-button">
              {{ name }}
            </span>
          </template>
          <template v-else>
            <span class="node-button">
            {{ ct('common.none') }}
          </span>
          </template>

        </div>
      </div>
      <div class="search-template-section">
        <div class="template-title">{{ t('searchTemplate') }}</div>
        <div class="node-buttons">
          <template v-if="item.searchNames && item.searchNames.length > 0">
            <span v-for="name in item.searchNames" :key="name" class="node-button">
              {{ name }}
            </span>
          </template>
          <template v-else>
            <span class="node-button">
              {{ ct('common.none') }}
            </span>
          </template>
        </div>
      </div>
      <div class="proposal-section">
        <div class="template-title">{{ t('proposal') }}</div>
        <div class="node-buttons2">
          <template v-if="item.proposalNames">
            <span v-for="name in item.proposalNames.split(',')" :key="name" class="node-button">
              {{ name }}
            </span>
          </template>
          <template v-else>
            <div class="ft12-bold f-color-08">{{ ct('common.none') }}</div>
          </template>
        </div>
      </div>
    </div>
  </div>
  <div style="margin-top: 10px;">
    <IPagination @handlePageChange="handlePageChange"
                 :pageSizeOptions="['2']" :defaultPageSize="2"
                 :total="total"/>

  </div>


</template>

<script setup lang="ts">
import {ref} from "vue";
import {useI18n} from "/@/hooks/web/useI18n";
import {list} from "/@/views/investigationTemplate/InvestigationTemplate.api";
import IPagination from "/@/components/IPagination/IPagination.vue";

console.log("TemplatePicker.vue")
const {t: ct} = useI18n();
const {t} = useI18n("investInfo.InvestInfo");
const emits = defineEmits(['update:value']);
const nodeButtons = ref(["IP address", "Asset", "Alert", "Process", "User", "Log"]);
const total = ref(0)
/**
 * 模板数据
 */
const templateList = ref<any>([]);
let pageNo = 1;
let pageSize = 2;
let tenantId = "";
/**
 * 加载模板数据
 */
const loadTemplateList = (socTenantId: string) => {
  tenantId = socTenantId;
  list({
    pageSize: pageSize,
    pageNo: pageNo,
    tenantFlag: socTenantId
  }).then(data => {
    const list = data.records;
    total.value = data.total;
    for (let i = 0; i < list.length; i++) {
      if (list[i].searchTemplate) {
        const searchTemplate = JSON.parse(list[i].searchTemplate);
        list[i].searchNames = searchTemplate
          .map(item => item.label) // 提取 name 属性
          .filter(label => label);   // 过滤掉空值
      }
    }
    templateList.value = list;
  })
}


// 分页查询
function handlePageChange(page, size) {
  pageNo = page;
  pageSize = size;
  loadTemplateList(tenantId);
}

/**
 * 选择模板，设置边框选中样式
 * @param e
 */
function selectTemplate(e, id) {
  const list = document.getElementsByClassName("investigation-container");
  for (let i = 0; i < list.length; i++) {
    list[i].className = 'investigation-container';
  }
  if (!e.currentTarget.classList.contains("select_template")) {
    e.currentTarget.classList.add("select_template");
  }
  emits('update:value', id)
}

defineExpose({
  loadTemplateList
})
</script>

<style scoped lang="less">
.investigation-container {
  color: white;
  padding: 20px;
  border-radius: 8px;
  width: 380px;
  height: 310px;
  background: rgba(255, 255, 255, 0.08);
  border: 2px solid transparent;
  cursor: pointer;
}

.select_template {
  border: 2px solid @m-color;
}

.node-section, .search-template-section {
  margin-bottom: 16px;
}

.template-title {
  font-size: 12px;
  font-weight: 600;
  line-height: 16px;

  /* Font/白1 */
  color: #FFFFFF;
  margin-bottom: 8px;
}

.node-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
  height: 52px;
  overflow: auto;
}

.node-buttons2 {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
  height: 24px;
  overflow: auto;
}

.node-button {
  font-size: 12px;
  font-weight: normal;
  line-height: 16px;
  color: rgba(255, 255, 255, 0.8);
  padding: 4px 8px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 4px;
  height: 24px;
}

</style>
