<template>
  <div class="node_tool">
    <div class="img_div" @click="tool(1,$event)">
      <img src="../../../image/n1.png" alt=""/>
    </div>
    <div class="img_div" @click="tool(2,$event)">
      <img src="../../../image/n2.png" alt=""/>
    </div>
    <div class="img_div" @click="tool(3,$event)">
      <img src="../../../image/n3.png" alt=""/>
    </div>
    <div class="img_div" @click="tool(4,$event)">
      <img src="../../../image/n4.png" alt=""/>
    </div>
  </div>
  
  <ViewNodeTool ref="viewNodeToolRef" @select-tool-node="selectToolNode"
                :templateId="info.templateId"/>
  
  <div id="container_parent" :class="{'pl-[240px]':showTool}">
    <div id="container">
      <template v-for="item in initNodeList" :key="item.id">
        <ViewNode :info="item"/>
      </template>
    </div>
  </div>
  
  <!-- 动态组件 -->
  <component :is="currentComponent" ref="selectDataRef" @delete-node="deleteNode"
             @add-node="addNodeData"/>
  
  <NodeEventInfo ref="nodeEventInfoRef" @add-node="addDataNode" @remove-node="removeDataNode"/>
  <NodeIpInfo ref="nodeIpInfoRef" @add-node="addDataNode" @remove-node="removeDataNode"/>

</template>
<script setup lang="ts">
import {nextTick, onBeforeUnmount, onMounted, provide, ref, toRefs, unref} from "vue";
import {
  addContent,
  addNode,
  clearSelect,
  createInstance, delInitNodeList, delSelectNode,
  handleDeleteKey,
  zoomIn,
  zoomInit,
  zoomOut
} from "/@/views/invest/modules/cmts/viewCanvas/ViewCanvas";
import ViewNode from "/@/views/invest/modules/cmts/viewCanvas/cmts/ViewNode.vue";
import {
  AlertNodeId,
  cursor, DATA_TYPE, IpNodeId, LogNodeId,
  Node,
  nodeMap, ProcessNodeId,
  relate, UserNodeId
} from "/@/views/invest/modules/cmts/viewCanvas/cmts/ViewNode";
import ViewNodeTool from "/@/views/invest/modules/cmts/viewCanvas/cmts/ViewNodeTool.vue";
import {buildUUID} from "/@/utils/uuid";
import {
  JsPlumbStoreType,
  useJsPlumbStore
} from "/@/views/invest/modules/cmts/viewCanvas/JsPlumbStore";
import {queryNodeList} from "/@/views/invest/InvestInfoNode.api";


import NodeEventInfo from "/@/views/invest/modules/cmts/viewCanvas/cmts/nodeInfo/NodeEventInfo.vue";
import NodeIpInfo from "/@/views/invest/modules/cmts/viewCanvas/cmts/nodeInfo/NodeIpInfo.vue";

const emits = defineEmits(['saveInfo']);
const jsPlumbStore = useJsPlumbStore()
const {
  prevSelectNodeId,
  selectTemplateNodeInfo,
  investId,
  isUpdate,
  initNodeList,
  instance,
  selectNodeId
} = toRefs(jsPlumbStore) as unknown as JsPlumbStoreType;

const props = defineProps({
  info: Object as any,
})
console.log("ViewCanvas.vue", props)
investId.value = props.info.id;

provide("invest_socTenantId", props.info.socTenantId);

const showTool = ref(false);
/**
 * 工具组件
 */
const viewNodeToolRef = ref();
const currentComponent = ref();
const selectDataRef = ref();

/**
 * 事件节点详情
 */
const nodeEventInfoRef = ref();
/**
 * IP节点
 */
const nodeIpInfoRef = ref();


function showNodeInfo(el) {
  const json = el.dataset.data;
  let data: any;
  if (json) {
    data = JSON.parse(json);
  }
  console.log(data)
  //事件节点
  if (AlertNodeId === data.nodeId) {
    nodeEventInfoRef.value.open(data);
  } else if (IpNodeId === data.nodeId) {
    nodeIpInfoRef.value.open(data);
  }
}

onMounted(() => {
  createInstance("container", showNodeInfo);
  
  const $container = document.getElementById("container");
  //监听画布上的鼠标点击抬起事件，如果鼠标抬起时，有工具选择的节点，则添加该节点到画布上
  if ($container) {
    $container.addEventListener('mouseup', (e: any) => {
      if (e.target?.id != 'container') {
        return;
      }
      
      //点击画布，清除画布选择的节点数据
      clearSelect();
      //清除上一个节点选中信息
      prevSelectNodeId.value = "";
      
      const offsetX = e.offsetX - parseInt($container.style.left || '0') - 25;
      const offsetY = e.offsetY - parseInt($container.style.top || '0') - 25;
      //有选择的模板节点，添加到画布上，且不是操作节点
      if (selectTemplateNodeInfo.value?.id && !notNodeList.includes(selectTemplateNodeInfo.value?.id)) {
        handleAddNode(unref(selectTemplateNodeInfo), {left: offsetX, top: offsetY});
      }
    });
  }
  window.addEventListener('keydown', handleDeleteKey);
  
  loadNodeList(props.info.id);
})

// 组件卸载时移除事件监听
onBeforeUnmount(() => {
  window.removeEventListener('keydown', handleDeleteKey);
});

/**
 * 查询调查节点数据
 * @param id
 */
function loadNodeList(id) {
  queryNodeList({investId: id}).then(data => {
    if (data && data[0]) {
      if (data[0].nodeJson) {
        const list: Node[] = JSON.parse(data[0].nodeJson);
        initNodeList.value = list;
        nextTick(() => {
          for (let i = 0; i < list.length; i++) {
            addNode(list[i].id);
          }
          
          const line = JSON.parse(data[0].lineJson);
          for (let i = 0; i < line.length; i++) {
            addContent(line[i].sourceId, line[i].targetId)
          }
        })
      }
    } else {
      initNodeList.value = [];
    }
  })
}

/**
 * 处理添加节点事件
 * @param nodeInfo
 * @param position
 */
function handleAddNode(nodeInfo: any, position: any) {
  console.log(nodeInfo)
  isUpdate.value = true;
  viewNodeToolRef.value.initSelect();
  const id = buildUUID();
  initNodeList.value.push({
    nodeName: nodeInfo.nodeName,
    nodeId: nodeInfo.id,
    id: id,
    left: position.left + "px",
    top: position.top + "px"
  });
  //手动添加节点，显示节点要绑定的数据弹出页
  if (nodeMap[nodeInfo.id]) {
    currentComponent.value = nodeMap[nodeInfo.id].component;
    nextTick(() => {
      selectDataRef.value.open(id, props.info.socTenantId);
    })
  }
}

/**
 * 手动添加节点，但是没有选择数据，删除添加的节点
 * @param id
 */
function deleteNode(id: string) {
  document.getElementById(id)?.remove();
  delInitNodeList(initNodeList.value, id);
}

/**
 * 手动添加节点，选择绑定数据，把节点注册到画布中
 * @param id
 * @param data
 */
function addNodeData(id: string, data: any) {
  addNode(id);
  const list = initNodeList.value;
  for (let i = 0; i < list.length; i++) {
    if (list[i].id == id) {
      list[i].nodeName = data.name;
      list[i].nodeType = data.type;
      list[i].dataId = data.eventId;
      break;
    }
  }
  nextTick(() => {
    showNodeInfo(document.getElementById(id));
  })
}

/**
 * 工具点击事件处理
 * @param num
 * @param e
 */
function tool(num: number, e: any) {
  const flag = e.currentTarget.classList.contains("tool_active");
  const list = e.currentTarget.parentElement.getElementsByClassName("img_div");
  for (let i = 0; i < list.length; i++) {
    list[i].className = 'img_div';
  }
  if (num == 1) {
    zoomIn();
  } else if (num == 2) {
    zoomOut();
  } else if (num == 3) {
    zoomInit();
  } else if (num == 4) {
    showTool.value = !flag;
    viewNodeToolRef.value.open(!flag, props.info.templateId);
  }
  if (num == 4 && flag) {
    //取消点击
    return;
  }
  e.currentTarget.classList.add("tool_active");
}


const notNodeList = [cursor, relate];

/**
 * 工具内节点点击处理
 * @param data
 */
function selectToolNode(data) {
  selectTemplateNodeInfo.value = data;
}

/**
 * 添加节点
 * @param sourceNode
 * @param data
 * @param dataType
 * @param call
 */
function addDataNode(sourceNode, data, dataType, call) {
  const left: any = document.getElementById(sourceNode.id)?.style.left ?? "0";
  const top: any = document.getElementById(sourceNode.id)?.style.top;
  isUpdate.value = true;
  viewNodeToolRef.value.initSelect();
  const id = buildUUID();
  console.log('addDataNode data', data)
  //ip
  if (dataType == DATA_TYPE[IpNodeId]) {
    initNodeList.value.push({
      nodeName: data.ip,
      nodeId: IpNodeId,
      id: id,
      left: (left.replace("px", "") * 1 + 200) + "px",
      top: top,
      other: JSON.stringify(data)
    });
  }
  //进程
  else if (dataType == DATA_TYPE[ProcessNodeId]) {
    initNodeList.value.push({
      nodeName: data.processName,
      nodeId: ProcessNodeId,
      id: id,
      left: (left.replace("px", "") * 1 + 200) + "px",
      top: top,
      other: JSON.stringify(data)
    });
  }
  //用户
  else if (dataType == DATA_TYPE[UserNodeId]) {
    initNodeList.value.push({
      nodeName: data.userName,
      nodeId: UserNodeId,
      id: id,
      left: (left.replace("px", "") * 1 + 200) + "px",
      top: top,
      other: JSON.stringify(data)
    });
  }
  //logs
  else if (dataType == DATA_TYPE[LogNodeId]) {
    initNodeList.value.push({
      nodeName: data.log_id,
      nodeId: LogNodeId,
      id: id,
      left: (left.replace("px", "") * 1 + 200) + "px",
      top: top,
      other: JSON.stringify(data)
    });
  } else {
    return;
  }
  
  nextTick(() => {
    addNode(id);
    addContent(sourceNode.id, id);
    emits('saveInfo', call);
  });
}

/**
 * 删除节点
 * @param sourceNode
 * @param data
 * @param dataType
 * @param call
 */
function removeDataNode(sourceNode, data, dataType, call) {
  //只知道源节点和要删除节点的ip，查询源节点所有连线
  const connects = instance.value.getConnections({source: sourceNode.id});
  if (dataType == DATA_TYPE[IpNodeId]) {
    for (let i = 0; i < connects.length; i++) {
      const target = connects[i].target;
      if (target.innerText === data.ip) {
        selectNodeId.value = target.id;
        delSelectNode();
        emits('saveInfo', call);
        break;
      }
    }
  } else if (dataType == DATA_TYPE[ProcessNodeId]) {
    for (let i = 0; i < connects.length; i++) {
      const target = connects[i].target;
      const json = target.dataset.data;
      let dataJson: any = {};
      if (json) {
        dataJson = JSON.parse(json);
      }
      if (dataJson.other) {
        dataJson = JSON.parse(dataJson.other);
      }
      if ((dataJson.processName + dataJson.processId) === (data.processName + data.processId)) {
        selectNodeId.value = target.id;
        delSelectNode();
        emits('saveInfo', call);
        break;
      }
    }
  }
}

</script>


<style scoped lang="less">
#container_parent {
  position: relative;
  height: calc(100% - 64px);
  overflow: hidden;
}

#container {
  position: relative;
  width: 100%;
  height: 100%;
  border: 1px dashed @border-color;
}

//端点透明，不显示
:deep(.dot_none) {
  opacity: 0;
}

//线的样式
:deep(.invest_line) {
  cursor: pointer;
}

//线的悬浮样式
:deep(.invest_hover_line) {

}

.node_tool {
  position: absolute;
  display: flex;
  align-items: center;
  gap: 8px;
  z-index: 2;
  
  .img_div {
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    
    img {
      width: 20px;
      height: 20px;
    }
  }
  
  .tool_active {
    background-color: @bg-color;
    border-radius: 4px;
  }
  
  
}

</style>
