<template>
  <div class="tool_div" v-show="visible">
    <div>
      <a-input-search @search="searchNode"/>
    </div>
    <div class="node-item-div" style="margin-top: 16px;">
      <div ref="cursorRef" class="node-item select_tool" @click="selectNode($event,{id:cursor})">
        <img src="../../../../image/node/<EMAIL>" alt=""/>
        <div>{{ t('cursor') }}</div>
      </div>
      <div class="node-item" @click="selectNode($event,{id:relate})">
        <img src="../../../../image/node/<EMAIL>" alt=""/>
        <div>{{ t('relate') }}</div>
      </div>
      <div class="node-item" v-for="(item,index) in templateNodeList" :key="index"
           @click="selectNode($event,item)">
        <img :src="item.src" alt=""/>
        <div>{{ item.nodeName }}</div>
      </div>
    </div>
    <div style="position:absolute;bottom: 16px;width: calc(100% - 32px);">
      <a-button style="width: 100%;">{{ t('newNode') }}</a-button>
    </div>
  </div>
</template>

<script setup lang="ts">

import {ref} from "vue";
import {loadNodeList} from "/@/views/invest/InvestInfo.api";
import {useI18n} from "/@/hooks/web/useI18n";
import {
  cursor,
  relate,
  defaultImg,
  nodeMap
} from "/@/views/invest/modules/cmts/viewCanvas/cmts/ViewNode";

const {t} = useI18n("investInfo.InvestInfo");
const props = defineProps({
  templateId: String
})
console.log("ViewNodeTool.vue")
const emits = defineEmits(['selectToolNode']);

/**
 * 节点数据
 */
const templateNodeList = ref<any>([]);
/**
 * 查询调查可使用的节点数据
 */
loadNodeList({id: props.templateId}).then(data => {
  console.log(data)
  for (let i = 0; i < data.length; i++) {
    //其它节点图标
    let src = defaultImg;
    if (nodeMap[data[i].id]) {
      src = nodeMap[data[i].id].src
    }
    data[i].src = src;
  }
  templateNodeList.value = data;
})

/**
 * 点击选择模板节点
 * @param e
 * @param node
 */
function selectNode(e, node) {
  emits('selectToolNode', node);
  const list = document.getElementsByClassName("node-item");
  for (let i = 0; i < list.length; i++) {
    list[i].className = 'node-item';
  }
  e.currentTarget.classList.add("select_tool")
}

const visible = ref(false);

/**
 * 显示
 * @param flag
 */
function open(flag: boolean) {
  initSelect();
  visible.value = flag;
}

/**
 * 查询节点
 * @param value
 */
function searchNode(value: string) {
  console.log(value)
}

const cursorRef = ref();

/**
 * 初始化选择信息，默认是第一个节点信息
 */
function initSelect() {
  const list = document.getElementsByClassName("node-item");
  for (let i = 0; i < list.length; i++) {
    list[i].className = 'node-item';
  }
  cursorRef.value.classList.add("select_tool");
  emits('selectToolNode', {id: cursor});
}

defineExpose({
  open,
  initSelect
})
</script>


<style scoped lang="less">
.tool_div {
  height: calc(100% - 130px);
  width: 240px;
  position: absolute;
  top: 105px;
  background: #28282C;
  z-index: 11;
  border-radius: 0 8px 8px 0;
  left: 0;
  padding: 16px;

  .node-item-div::-webkit-scrollbar {
    display: none; /* 隐藏滚动条 */
  }

  .node-item-div {

    display: flex;
    flex-flow: wrap;
    gap: 8px;
    overflow: auto;
    max-height: calc(100% - 90px);

    .select_tool {
      border: 1px solid @m-color;
    }

    .node-item {
      width: 100px;
      height: 100px;
      display: flex;
      align-items: center;
      justify-content: center;
      flex-flow: column;
      background: rgba(255, 255, 255, 0.04);
      border-radius: 8px;
      gap: 12px;
      cursor: pointer;

      font-size: 12px;
      font-weight: normal;
      line-height: 16px;
      text-align: center;

      /* Font/白1 */
      color: #FFFFFF;

      img {
        width: 32px;
        height: 32px;
      }
    }
  }

}
</style>
