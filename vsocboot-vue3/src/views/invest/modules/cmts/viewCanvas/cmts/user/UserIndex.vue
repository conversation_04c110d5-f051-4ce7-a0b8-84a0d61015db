<template>
  <div class="invest_process_div">
    <BasicTable @register="registerTable">
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'name'">
          <div class="tdDiv ip">
            <div class="ft14-bold">
              {{ record.userName }}
            </div>
          </div>
        </template>
        <template v-else-if="column.dataIndex === 'other'">
          <div class="tdDiv"
               style="justify-content: right;">
            <div style="display: flex;gap:4px;flex-wrap: wrap;">
              <div
                v-for="(item,index) in record.fieldSourceList" :key="index" class="ip_source_div">
                {{ item }}
              </div>
            </div>
          </div>
          <a-divider class="vertical_div" type="vertical"/>
        </template>
      </template>

      <template #action="{ record }">
        <div class="tdDiv table_action">
          <div class="ax-icon-button" v-if="record.sort == 2">
            <i class="soc ax-com-Forward ax-icon"></i>
          </div>
          <div class="ax-icon-button" v-if="record.sort == 2">
            <i class="soc ax-com-Association ax-icon" @click="removeNode(record)"></i>
          </div>
          <div class="ax-icon-button" v-if="record.sort == 1">
            <i class="soc ax-com-Add ax-icon" @click="addNode(record)"></i>
          </div>
        </div>
      </template>
    </BasicTable>


  </div>
</template>

<script setup lang="ts">
import {BasicTable} from "/@/components/Table";
import {useListPage} from "/@/hooks/system/useListPage";
import {loadInvestUser} from "/@/views/invest/InvestInfo.api";
import {formLayout} from "/@/settings/designSetting";

import {useI18n} from "/@/hooks/web/useI18n";
import {
  DATA_TYPE,
  UserNodeId
} from "/@/views/invest/modules/cmts/viewCanvas/cmts/ViewNode";
import {
  columns,
  searchFormSchema
} from "/@/views/invest/modules/cmts/viewCanvas/cmts/user/UserIndex";
import {inject} from "vue";

const {t: tc} = useI18n("common");
const {t} = useI18n("investInfo.InvestInfo");
const socTenantId = inject("invest_socTenantId");
const props = defineProps({
  params: {
    type: Object,
    default: () => {
      return {}
    }
  },
})
console.log(props.params)

//注册table数据
const {tableContext} = useListPage({
  tableProps: {
    api: loadInvestUser,
    columns: columns,
    canResize: false,
    // immediate: false,
    formConfig: {
      schemas: searchFormSchema(),
      autoSubmitOnEnter: true,
      showAdvancedButton: true,
      layout: formLayout,
    },
    showTableSetting: false,
    beforeFetch: (params) => {
      params = Object.assign(params, props.params);
      params.socTenantId = socTenantId;
      return params
    },
    afterFetch: (data) => {
      console.log(data)
      for (let i = 0; i < data.length; i++) {
        const fieldSource = data[i].fieldSource;
        if (fieldSource) {
          data[i].fieldSourceList = fieldSource.split(",");
        }
      }
    },
    actionColumn: {
      width: 110,
    },
  }
})

const [registerTable, {reload, setLoading}, {}] = tableContext

const emits = defineEmits(['addNode', 'removeNode']);
/**
 * 添加节点
 */
const addNode = (record) => {
  setLoading(true);
  emits('addNode', record, DATA_TYPE[UserNodeId], reload);
}

const removeNode = (record) => {
  setLoading(true);
  emits('removeNode', record, DATA_TYPE[UserNodeId], reload);
}


defineExpose({
  reload
})
</script>

<style scoped lang="less">
.invest_process_div {

  padding: 12px 16px;

  height: 100%;
  overflow: auto;

  :deep(form.soc-basic-form > div.flex) {
    padding-left: 0;
  }

  :deep(.ant-table-thead) {
    display: none;
  }

  :deep(.soc-basic-table-form-container) {
    padding: 0;
  }

  :deep(td.ant-table-cell) {
    border-bottom: 8px solid @dark-bg1 !important;
    padding: 0 !important;
  }

  :deep(tr.ant-table-measure-row td) {
    border-bottom: 0 !important;
  }


  .tdDiv {
    background-color: @bg-color;
    height: 48px;
    display: flex;
    align-items: center;
    position: relative;
    padding: 0 16px;
    gap: 8px;
  }

  .tdDiv.ip {
    border-radius: 8px 0 0 8px;
  }

  .table_action {
    border-radius: 0 8px 8px 0;
    font-size: 20px;
  }

  .vertical_div {
    position: absolute;
    right: -8px;
    height: 24px;
    top: 12px;
  }
  
  .ip_source_div {
    padding: 4px 8px;
    background: rgba(@m-color, 0.2);
    color: @m-color;
    border-radius: 4px;
    font-size: 12px;
    font-weight: normal;
    line-height: 16px;
  }
}

</style>
