<template>
  <div class="ip_address_div">
    <BasicTable @register="registerTable">
      <template #form-formFooter>
        ({{ t('lastUpdateTime') }} : {{ dateTime }})
        <div class="ax-icon-button" @click="refLoad">
          <i class="ax-com-Update soc" style="font-size: 20px;line-height: 20px;"></i>
        </div>
      </template>

      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'ip'">
          <div class="tdDiv ip">
            {{ record.ip }}
          </div>
          <a-divider class="vertical_div" type="vertical"/>
        </template>
        <template v-else-if="column.dataIndex === 'other'">
          <div class="tdDiv"
               style="flex-flow: column;gap: 8px;align-items: start;justify-content: center;">
            <div class="ft12">
              {{ record.country }}
            </div>
            <div style="display: flex;gap:4px;flex-wrap: wrap;">
              <template v-if="record.assetName">
                <div class="asset_source_div">
                  {{ record.assetName }}
                </div>
                <a-divider class="vertical_div2" type="vertical"/>
              </template>
              <div
                v-for="(item,index) in record.fieldSourceList" :key="index" class="ip_source_div">
                {{ item }}
              </div>
            </div>
          </div>
          <a-divider class="vertical_div" type="vertical"/>
        </template>
      </template>

      <template #action="{ record }">
        <div class="tdDiv table_action">
          <div class="ax-icon-button" v-if="record.sort == 2">
            <i class="soc ax-com-Forward ax-icon"></i>
          </div>
          <div class="ax-icon-button">
            <Icon icon="ant-design:eye-outlined" class="ax-icon"/>
          </div>
          <div class="ax-icon-button" v-if="record.sort == 2">
            <i class="soc ax-com-Association ax-icon" @click="removeNode(record)"></i>
          </div>
          <div class="ax-icon-button" v-if="record.sort == 1">
            <i class="soc ax-com-Add ax-icon" @click="addNode(record)"></i>
          </div>
        </div>
      </template>
    </BasicTable>


  </div>
</template>

<script setup lang="ts">
import {BasicTable} from "/@/components/Table";
import {useListPage} from "/@/hooks/system/useListPage";
import {loadInvestIp} from "/@/views/invest/InvestInfo.api";
import {formLayout} from "/@/settings/designSetting";
import {
  columns,
  searchFormSchema
} from "/@/views/invest/modules/cmts/viewCanvas/cmts/IpAddress/IpAddress";
import {DATA_TYPE, IpNodeId} from "/@/views/invest/modules/cmts/viewCanvas/cmts/ViewNode";
import {useI18n} from "/@/hooks/web/useI18n";
import {inject, ref} from "vue";
import dayjs from "dayjs";

const dateTime = ref("");
const socTenantId = inject("invest_socTenantId");
const {t} = useI18n("investInfo.InvestInfo");
const props = defineProps({
  params: {
    type: Object,
    default: () => {
      return {}
    }
  },
})
console.log(props.params);


//注册table数据
const {tableContext} = useListPage({
  tableProps: {
    api: loadInvestIp,
    columns: columns,
    canResize: false,
    // immediate: false,
    formConfig: {
      schemas: searchFormSchema(),
      autoSubmitOnEnter: true,
      showAdvancedButton: true,
      layout: formLayout,
    },
    showTableSetting: false,
    beforeFetch: (params) => {
      params = Object.assign(params, props.params);
      params.socTenantId  = socTenantId;
      console.log(params)
      dateTime.value = dayjs().format("YYYY-MM-DD HH:mm:ss");
      return params
    },
    afterFetch: (data) => {
      console.log(data)
      for (let i = 0; i < data.length; i++) {
        data[i].country = data[i].country.split(",").filter(item => !!item).join(",");
        const fieldSource = data[i].fieldSource;
        if (fieldSource) {
          data[i].fieldSourceList = fieldSource.split(",");
        }
      }
    },
    actionColumn: {
      width: 150,
    },
  }
})

const [registerTable, {reload, setLoading}, {}] = tableContext

const emits = defineEmits(['addNode', 'removeNode', 'refLoad']);
/**
 * 添加节点
 */
const addNode = (record) => {
  setLoading(true);
  emits('addNode', record, DATA_TYPE[IpNodeId], reload);
}

const removeNode = (record) => {
  setLoading(true);
  emits('removeNode', record, DATA_TYPE[IpNodeId], reload);
}

function refLoad() {
  emits('refLoad', reload);
}

defineExpose({
  reload
})
</script>

<style scoped lang="less">
.ip_address_div {

  padding: 12px 16px;

  height: 100%;
  overflow: auto;

  :deep(form.soc-basic-form > div.flex) {
    padding-left: 0;
  }

  :deep(.ant-table-thead) {
    display: none;
  }

  :deep(.soc-basic-table-form-container) {
    padding: 0;
  }

  :deep(td.ant-table-cell) {
    border-bottom: 8px solid @dark-bg1 !important;
    padding: 0 !important;
  }

  :deep(tr.ant-table-measure-row td) {
    border-bottom: 0 !important;
  }


  .tdDiv {
    background-color: @bg-color;
    height: 80px;
    display: flex;
    align-items: center;
    position: relative;
    padding: 0 16px;

  }

  .tdDiv.ip {
    border-radius: 8px 0 0 8px;
  }

  .table_action {
    border-radius: 0 8px 8px 0;
    font-size: 20px;
  }

  .vertical_div {
    position: absolute;
    right: -8px;
    height: 56px;
    top: 12px;
  }

  .vertical_div2 {
    height: 30px;
  }

  .ip_source_div {
    padding: 4px 8px;
    background: rgba(@m-color, 0.2);
    color: @m-color;
    border-radius: 4px;
    font-size: 12px;
    font-weight: normal;
    line-height: 16px;
  }

  .asset_source_div {
    border: 1px solid @m-color;
    padding: 4px 8px;
    color: @m-color;
    border-radius: 4px;
  }
}

</style>
