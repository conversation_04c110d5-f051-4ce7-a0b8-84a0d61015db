import {FormSchema} from "/@/components/Form";
import {BasicColumn} from "/@/components/Table";
import {useI18n} from "/@/hooks/web/useI18n";

const {t} = useI18n('investInfo.InvestInfo');

export const columns: BasicColumn[] = [
  {
    title: '',
    dataIndex: 'name',
  }
];
export const searchFormSchema = (): FormSchema[] => [
  {
    label: '',
    field: 'searchValue',
    component: 'Input',
    componentProps: {
      search: true,
      placeholder: t('processesName'),
    }
  },
  // {
  //   label: '',
  //   field: 'findWay',
  //   component: 'Select',
  //   componentProps: {
  //     options: [
  //       {label: tc('SecurityLog') + ": host_ip", value: '1:hostIp'},
  //       {label: tc('HostLog') + ": host_ip", value: '2:hostIp'},
  //     ],
  //     mode: "multiple",
  //     placeholder: t('findWay'),
  //   },
  //   ifShow: !hide,
  // }
];
