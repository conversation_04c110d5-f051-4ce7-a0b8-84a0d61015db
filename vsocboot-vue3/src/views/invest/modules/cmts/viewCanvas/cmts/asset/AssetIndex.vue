<template>
  <div class="invest_asset_div">
    <BasicTable @register="registerTable">
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'assetName'">
          <div class="tdDiv ip">
            <i v-if="!record?.integrationId" class="ax-zc-server soc" style="font-size: 32px"></i>
            <img
              v-else-if="record?.logo" style="width: 72px; height: 72px"
              :src="render.renderUploadImageSrc(record?.logo)" alt=""/>
            <div style="width: 100%;display: flex;flex-flow: column;gap: 8px;">
              <div class="ft16-bold">
                {{ record.assetName }}
              </div>
              <div class="ft16-bold">
                {{ record.ipv4 }}
              </div>
            </div>
            <div style="display: flex;flex-flow: column;gap: 12px;" class="ft13">
              <template v-if="!['Application'].includes(record?.assetType)">
                <div v-if="record?.assetSafe == 1" class="safeState safe-red">
                  <i class="ax-com-Danger soc"></i>
                  {{ t('routes.asset.attacked') }}
                </div>
                <div v-else class="safeState safe-blue">
                  <div style="display: flex; align-items: center; gap: 4px">
                    <i class="ax-setting-Security soc"></i>
                    {{ t('routes.asset.safe') }}
                  </div>
                </div>
                <div v-if="record?.assetState == 1" class="assetState1">
                  <i class="ax-com-Success soc"></i>
                  {{ t('common.Online') }}
                </div>
                <div v-else-if="record?.assetState == 3" class="assetState3">
                  <i class="ax-com-Warning soc"></i>
                  {{ t('common.Unknown') }}
                </div>
              </template>
            </div>

          </div>
          <a-divider class="vertical_div" type="vertical"/>
        </template>
        <template v-else-if="column.dataIndex === 'other'">
          <div class="tdDiv">
            <div class="ft12">
              {{ record.assetDesc }}
            </div>
          </div>
          <a-divider class="vertical_div" type="vertical"/>
        </template>
      </template>

      <template #action="{ record }">
        <div class="tdDiv table_action">
          <div class="ax-icon-button">
            <Icon icon="ant-design:eye-outlined" class="ax-icon"/>
          </div>
        </div>
      </template>
    </BasicTable>


  </div>
</template>

<script setup lang="ts">
import {BasicTable} from "/@/components/Table";
import {useListPage} from "/@/hooks/system/useListPage";
import {loadInvestAssets} from "/@/views/invest/InvestInfo.api";
import {useI18n} from "/@/hooks/web/useI18n";
import {columns} from "/@/views/invest/modules/cmts/viewCanvas/cmts/asset/AssetIndex";
import {render} from "/@/utils/common/renderUtils";
import {inject} from "vue";
const socTenantId = inject("invest_socTenantId");
const {t} = useI18n();
const props = defineProps({
  params: {
    type: Object,
    default: () => {
      return {}
    }
  },
})
console.log(props)

//注册table数据
const {tableContext} = useListPage({
  tableProps: {
    api: loadInvestAssets,
    columns: columns,
    canResize: false,
    // immediate: false,
    useSearchForm: false,
    showTableSetting: false,
    beforeFetch: (params) => {
      params = Object.assign(params, props.params);
      params.socTenantId = socTenantId;
      return params
    },
    actionColumn: {
      width: 64,
    },
  }
})

const [registerTable, {reload}, {}] = tableContext




defineExpose({
  reload
})
</script>

<style scoped lang="less">
.invest_asset_div {

  padding: 12px 16px;

  height: 100%;
  overflow: auto;

  :deep(form.soc-basic-form > div.flex) {
    padding-left: 0;
  }

  :deep(.ant-table-thead) {
    display: none;
  }

  :deep(.soc-basic-table-form-container) {
    padding: 0;
  }

  :deep(td.ant-table-cell) {
    border-bottom: 8px solid @dark-bg1 !important;
    padding: 0 !important;
  }

  :deep(tr.ant-table-measure-row td) {
    border-bottom: 0 !important;
  }


  .tdDiv {
    background-color: @bg-color;
    height: 96px;
    display: flex;
    align-items: center;
    position: relative;
    padding: 0 16px;
    gap: 16px;

  }

  .tdDiv.ip {
    border-radius: 8px 0 0 8px;
  }

  .table_action {
    border-radius: 0 8px 8px 0;
    font-size: 20px;
  }

  .vertical_div {
    position: absolute;
    right: -8px;
    height: 56px;
    top: 20px;
  }

  .safe-red {
    color: @color-red;
  }

  .safe-blue {
    color: @color-green;
  }

  .assetState3 {
    color: @color-orange;
  }

  .assetState1 {
    color: @color-green;
  }
}

</style>
