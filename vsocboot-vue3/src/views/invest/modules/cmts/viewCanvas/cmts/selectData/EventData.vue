<template>
  <a-drawer
    placement="right"
    :closable="false" :maskClosable="false" width="1200px"
    :visible="visible" :bodyStyle="{'padding':'16px'}"
    @close="close">
    <div style="display: flex;justify-content: right;padding-bottom: 6px;">
      <a-button danger @click="onClose">{{ t('common.cancelText') }}</a-button>
    </div>
    <div>
      <a-tabs class="new_tabs page_div">
        <a-tab-pane :key="1" :tab="tp('RiskEvents')">
          <RiskEvent v-if="socTenantId" :socTenantId="socTenantId" @ok="selectEvent"/>
        </a-tab-pane>
        <a-tab-pane :key="2" :tab="tp('MLStatistic')">
          <MlEvent v-if="socTenantId" :rule-type="1" :socTenantId="socTenantId" @ok="selectEvent"/>
        </a-tab-pane>
        <a-tab-pane :key="3" :tab="tp('MLOrder')">
          <MlEvent v-if="socTenantId" :rule-type="2" :socTenantId="socTenantId" @ok="selectEvent"/>
        </a-tab-pane>
        <a-tab-pane :key="4" :tab="tp('MLContent')">
          <MlEvent v-if="socTenantId" :rule-type="3" :socTenantId="socTenantId" @ok="selectEvent"/>
        </a-tab-pane>
      </a-tabs>
    </div>
  </a-drawer>
</template>

<script setup lang="ts">
import {defineAsyncComponent, ref} from "vue";
import {useI18n} from "/@/hooks/web/useI18n";

const RiskEvent = defineAsyncComponent(() => import("/@/views/invest/modules/cmts/viewCanvas/cmts/selectData/RiskEvent.vue"));
const MlEvent = defineAsyncComponent(() => import('/@/views/invest/modules/cmts/viewCanvas/cmts/selectData/MlEvent.vue'));

const {t} = useI18n();
const tp = (name) => {
  return t('routes.riskEvent.' + name);
};
console.log("EventData.vue")
const visible = ref(false);
const emits = defineEmits(['deleteNode', 'addNode']);
const socTenantId = ref("");
let nodeDomId = "";
/**
 * 关闭页面
 */
const onClose = () => {
  emits('deleteNode', nodeDomId);
  close();
}
const close = () => {
  visible.value = false;
  socTenantId.value = "";
}

/**
 * 打开页面
 */
const open = (id: string, tenantId: string) => {
  nodeDomId = id;
  socTenantId.value = tenantId;
  visible.value = true;
}

function selectEvent(data) {
  console.log(data)
  emits('addNode', nodeDomId, data);
  close();
}

defineExpose({
  open
})


</script>


<style scoped lang="less">

</style>
