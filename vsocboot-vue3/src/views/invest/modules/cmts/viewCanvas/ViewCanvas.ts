import {
  Connection,
  EVENT_CONNECTION_CLICK, EVENT_ELEMENT_CLICK, EVENT_ELEMENT_DBL_CLICK,
  newInstance
} from "@jsplumb/browser-ui";
import {
  hoverStroke,
  hoverStrokeWidth,
  nodeSize, relate, stroke,
  strokeWidth,
  Node, DATA_TYPE, AlertNodeId, IpNodeId, ProcessNodeId, UserNodeId, LogNodeId
} from "/@/views/invest/modules/cmts/viewCanvas/cmts/ViewNode";
import {
  JsPlumbStoreType,
  useJsPlumbStore
} from "/@/views/invest/modules/cmts/viewCanvas/JsPlumbStore";
import {toRefs} from "vue";
import {saveInvestInfo} from "/@/views/invest/InvestInfo.api";

const jsPlumbStore = useJsPlumbStore()

const {
  instanceId,
  instance,
  selectLine,
  selectNodeId,
  prevSelectNodeId,
  selectTemplateNodeInfo,
  isUpdate,
  initNodeList
} = toRefs(jsPlumbStore) as unknown as JsPlumbStoreType;


/**
 * 创建实例
 * @param id
 * @param call
 */
export function createInstance(id: string, call: Function) {
  instanceId.value = id;
  const container: any = document.getElementById(id);
  instance.value = newInstance({
    container: container,
    elementsDraggable: true,
  });

  /**
   * 当用户点击连接时触发
   */
  instance.value.bind(EVENT_CONNECTION_CLICK, (connection: Connection) => {
    clearSelect();
    //点击线清除上一个节点选中信息
    prevSelectNodeId.value = "";
    selectLine.value = connection;
    selectLine.value.setPaintStyle({stroke: hoverStroke, strokeWidth: hoverStrokeWidth});
    selectLine.value.setVisible(true);
  });
  /**
   * 当用户点击被管理的元素时触发。
   */
  instance.value.bind(EVENT_ELEMENT_CLICK, (element: Element) => {
    selectNode(element);
  });
  /**
   * 双击
   */
  instance.value.bind(EVENT_ELEMENT_DBL_CLICK, (element: Element) => {
    call && call(element);
  });

}

/**
 * 添加节点
 * @param id
 */
export function addNode(id: string) {
  const element = document.getElementById(id);

  if (element && instance.value) {
    instance.value.addEndpoint(element, {
      anchor: "Center",//端点在节点中心
      endpoint: {type: "Dot", options: {radius: nodeSize / 2, cssClass: 'dot_none'}},
      maxConnections: -1,//节点连线数量，-1没有限制
    })
  }
}

/**
 * 节点连线
 * @param sourceId
 * @param targetId
 */
export function addContent(sourceId: string, targetId: string) {
  if (!instance.value) {
    return;
  }

  const source = document.getElementById(sourceId);
  const target = document.getElementById(targetId);
  if (!source || !target) {
    return;
  }


  const a1: any = instance.value.selectEndpoints({element: source});
  const a2: any = instance.value.selectEndpoints({element: target});
  // 检查是否已经存在连接
  const existingConnections = instance.value.getConnections();
  let flag = false;
  for (let i = 0; i < existingConnections.length; i++) {
    const connection = existingConnections[i];
    if (
      (connection.sourceId === sourceId && connection.targetId === targetId) ||
      (connection.sourceId === targetId && connection.targetId === sourceId)
    ) {
      // 如果已经存在连接，阻止新的连接
      flag = true;
      break;
    }
  }
  if (flag) {
    return
  }

  instance.value.connect({
    source: a1.entries[0], // 源元素
    target: a2.entries[0], // 目标元素
    endpoint: "Dot",
    anchor: {type: "Perimeter", options: {shape: "Circle", anchorCount: 150}},
    paintStyle: {
      stroke: stroke,
      strokeWidth: strokeWidth,
    },
    hoverPaintStyle: {
      stroke: hoverStroke,
      strokeWidth: hoverStrokeWidth,
    },
    cssClass: 'invest_line',
    overlays: [
      {
        type: "Arrow",
        options: { // 箭头样式
          width: 15, // 箭头宽度
          length: 15, // 箭头长度
          location: -(nodeSize / 2 - 2), // 箭头位置（0: 起点, 1: 终点）
          foldback: 0.8, // 箭头折叠比例
          direction: 1, // 箭头方向（1: 正向, -1: 反向）
        }
      }
    ],
    directed: true
  });
}

/**
 * 放大
 */
export function zoomIn() {
  const div = document.getElementById(instanceId.value);
  if (div && instance.value) {
    const zoom = instance.value.currentZoom;
    const newZoom = zoom + 0.1;
    div.style.transform = "scale(" + newZoom + ")";
    instance.value.setZoom(newZoom);
  }
}

/**
 * 缩小
 */
export function zoomOut() {
  const div = document.getElementById(instanceId.value);
  if (div && instance.value) {
    const zoom = instance.value.currentZoom;
    const newZoom = zoom - 0.1;
    div.style.transform = "scale(" + newZoom + ")";
    instance.value.setZoom(newZoom);
  }
}

/**
 * 还原
 */
export function zoomInit() {
  const div = document.getElementById(instanceId.value);
  if (div && instance.value) {
    div.style.transform = "scale(1)";
    instance.value.setZoom(1);
  }
}

/**
 * 清空选中节点和线
 */
export function clearSelect() {
  selectNodeId.value = "";
  selectLine.value = undefined;

  const list = document.getElementsByClassName("invest_node");
  for (let i = 0; i < list.length; i++) {
    const $div = list[i].getElementsByClassName("nd")[0];
    $div.className = 'nd';
  }
  if (instance.value) {
    const connections = instance.value.getConnections();
    for (let i = 0; i < connections.length; i++) {
      const connection: Connection = connections[i];
      connection.setPaintStyle({stroke: stroke, strokeWidth: strokeWidth});
      connection.setVisible(true);
    }
  }
}

/**
 * 删除选中的线
 */
function delSelectLine() {
  if (selectLine.value) {
    if (instance.value) {
      instance.value.deleteConnection(selectLine.value);
      selectLine.value = undefined;
      isUpdate.value = true;
    }
  }
}

/**
 * 删除选中的节点
 */
export function delSelectNode() {
  if (selectNodeId.value) {
    const element = document.getElementById(selectNodeId.value);
    if (element) {
      //删除节点和所有连线
      instance.value.unmanage(element, true);
      isUpdate.value = true;
      delInitNodeList(initNodeList.value, selectNodeId.value);
    }
    selectNodeId.value = "";
    prevSelectNodeId.value = "";
  }
}

/**
 * 画布点击节点事件处理
 * @param event
 */
function selectNode(event: Element) {
  clearSelect();
  //添加选中样式
  event.childNodes[0]['classList'].add("invest_none_select");
  const id = event.id;
  selectNodeId.value = id;
  //当前是连线模式
  if (selectTemplateNodeInfo.value.id === relate) {
    addContent(prevSelectNodeId.value, id);
    isUpdate.value = true;
  }
  prevSelectNodeId.value = id;
}


/**
 * 键盘删除选中节点和线事件
 */
export function handleDeleteKey(event) {
  if (event.key === 'Delete') {
    //存在，删除选中的节点
    if (selectNodeId.value) {
      delSelectNode();
    } else {
      delSelectLine();
    }
  }
}

/**
 * 移动画布，暂时不用
 */
export function moveCanvas() {
  // 画布整体移动
  const diagramParent = document.getElementById("container_parent");
  const diagramContainer = document.getElementById(instanceId.value);
  let isDragging = false;
  let offsetX = 0;
  let offsetY = 0;
  if (diagramParent && diagramContainer) {
    diagramParent.addEventListener('mousedown', (e: any) => {
      if (e.target && e.target.id == instanceId.value || e.target && e.target.id == "container_parent") {
        isDragging = true;
        offsetX = e.clientX - parseInt(diagramContainer.style.left || '0');
        offsetY = e.clientY - parseInt(diagramContainer.style.top || '0');
      }
    });

    diagramParent.addEventListener('mousemove', (e) => {
      if (isDragging) {
        diagramContainer.style.left = `${e.clientX - offsetX}px`;
        diagramContainer.style.top = `${e.clientY - offsetY}px`;
      }
    });

    diagramParent.addEventListener('mouseup', () => {
      isDragging = false;
    });
  }
}

/**
 * 保存调查信息
 * @param data
 * @param call
 */
export function saveInfo(data: any, call: Function) {
  //获取所有连线信息
  const connections = instance.value.getConnections();
  const line: any = [];
  const dataList: any = [];

  const nodeDataMap = {};
  //获取所有节点信息
  const map = instance.value.getManagedElements();
  const node: any = [];
  for (const key in map) {
    const el: any = map[key].el;
    const id = el.attributes.id.value;
    const left = el.style.left;
    const top = el.style.top;
    const json = el.dataset.data;
    let data: any = {};
    if (json) {
      data = JSON.parse(json);
    }
    nodeDataMap[id] = data;
    node.push({
      id,
      left,
      top,
      nodeId: data.nodeId,
      nodeName: data.nodeName,
      nodeType: data.nodeType,
      dataId: data.dataId,
      other: data.other,
      queryJson: el.dataset?.queryJson
    });
  }
  for (let i = 0; i < connections.length; i++) {
    const connection: Connection = connections[i];
    const sourceId = connection.sourceId;
    const targetId = connection.targetId;
    line.push({
      sourceId: sourceId,
      targetId: targetId
    });
    const {dataType, eventType, dataValue, dataJson} = handleDataValue(nodeDataMap[targetId]);
    dataList.push({
      nodeId: sourceId,
      dataType: dataType,
      eventType: eventType,
      dataValue: dataValue,
      dataJson: dataJson
    });
  }
  data.lineJson = JSON.stringify(line);
  data.nodeJson = JSON.stringify(node);
  data.dataList = dataList;

  saveInvestInfo(data).then(data => {
    console.log(data)
    isUpdate.value = false;
    call && call();
  })

}

function handleDataValue(data) {
  const dataType = DATA_TYPE[data.nodeId] ?? 7;
  let dataValue = "";
  let eventType = undefined;
  let dataJson = "";
  if (dataType === DATA_TYPE[AlertNodeId]) {
    //事件
    dataValue = data.dataId;
    eventType = data.nodeType;
  } else if (dataType === DATA_TYPE[IpNodeId]) {
    //IP
    dataValue = data.nodeName;
    dataJson = data.other;
  } else if (dataType === DATA_TYPE[ProcessNodeId]) {
    //进程
    dataValue = data.nodeName;
    if (data.other) {
      const other = JSON.parse(data.other);
      dataValue = other.processName + other.processId;
    }
    dataJson = data.other;
  } else if (dataType === DATA_TYPE[UserNodeId]) {
    //进程
    dataValue = data.nodeName;
    dataJson = data.other;
  } else if (dataType === DATA_TYPE[LogNodeId]) {
    //进程
    dataValue = data.nodeName;
    dataJson = data.other;
  }
  return {dataType, eventType, dataValue, dataJson};
}

/**
 * 删除节点，把初始化节点数组里的数据也删除
 *
 * @param list
 * @param id
 */
export function delInitNodeList(list: Node[], id: string) {
  let index = -1;
  for (let i = 0; i < list.length; i++) {
    if (list[i].id == id) {
      index = i;
      break;
    }
  }
  if (index > -1) {
    list.splice(index, 1);
  }
  return list;
}
