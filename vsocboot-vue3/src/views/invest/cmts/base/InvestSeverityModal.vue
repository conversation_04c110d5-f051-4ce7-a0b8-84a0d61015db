<template>
  <a-modal v-model:visible="visible" :title="t('common.editText')" @ok="handleOk">
    <div class="p-[16px]">
      <a-select :options="INVEST_SEVERITY_SELECT" v-model:value="severity">
      </a-select>
    </div>
  </a-modal>
</template>

<script setup lang="ts">
import {ref} from "vue";
import {useI18n} from "/@/hooks/web/useI18n";
import {INVEST_SEVERITY_SELECT} from "/@/utils/valueEnum";
import {message} from "ant-design-vue";

const emits = defineEmits(['updateSeverity']);
const {t} = useI18n();
const visible = ref(false);

const severity = ref("");

let dataId = "";

const handleOk = () => {
  if (severity.value) {
    emits('updateSeverity', severity.value, dataId);
    visible.value = false;
  } else {
    message.warning(t('investInfo.InvestInfo.severityTip1'))
  }
}

const openSeverity = (data: any) => {
  dataId = data.id;
  severity.value = data.severity;
  visible.value = true;
}

defineExpose({
  openSeverity
})
</script>
<style scoped lang="less">

</style>
