<template>
  <div class="cmts_invest_template ant-table-cell-ellipsis" :title="text" v-bind="$attrs">
    <!-- 0表示没有使用模板 -->
    <template v-if="!!text && text != '0'">{{ text }}</template>
    <template v-else>{{ t('normalInvestigation') }}</template>
  </div>
</template>

<script setup lang="ts">
import {useI18n} from "/@/hooks/web/useI18n";

const {t} = useI18n('investInfo.InvestInfo');
defineProps({
  text: String
});
</script>

<style scoped lang="less">
.cmts_invest_template {
  padding: 4px 8px;
  background: rgba(48, 140, 255, 0.2);
  border-radius: 4px;
}
</style>
