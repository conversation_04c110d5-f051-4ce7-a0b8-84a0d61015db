import {defHttp} from '/@/utils/http/axios';
import {Modal} from 'ant-design-vue';

enum Api {
  list = '/riskml/riskMl/list',
  save='/riskml/riskMl/add',
  edit='/riskml/riskMl/edit',
  deleteOne = '/riskml/riskMl/delete',
  deleteBatch = '/riskml/riskMl/deleteBatch',
  importExcel = '/riskml/riskMl/importExcel',
  exportXls = '/riskml/riskMl/exportXls',
  query24MonthData = '/riskml/riskMl/query24MonthData',
  invMlAdd = '/riskml/investigationRiskEventml/add',
  invMlEdit = '/riskml/investigationRiskEventml/edit',
  invMLSaveBatch = '/riskml/investigationRiskEventml/addBatch',
  querySrcIpById = '/riskml/riskMl/querySrcIpById',
  queryDstIpById = '/riskml/riskMl/queryDstIpById',
  queryMlInvList = '/riskml/investigationRiskEventml/queryMlInvList',
  queryLogIdByMlId = '/riskml/riskMl/queryLogIdByMlId',
}
/**
 * 导出api
 * @param params
 */
export const getExportUrl = Api.exportXls;
/**
 * 导入api
 */
export const getImportUrl = Api.importExcel;
/**
 * 列表接口
 * @param params
 */
export const eventMLList = (params) =>
  defHttp.get({url: Api.list, params});

/**
 * 删除单个
 * @param params
 * @param handleSuccess
 */
export const deleteOne = (params,handleSuccess) => {
  return defHttp.delete({url: Api.deleteOne, params}, {joinParamsToUrl: true}).then(() => {
    handleSuccess();
  });
}
/**
 * 批量删除
 * @param params
 * @param handleSuccess
 */
export const batchDelete = (params, handleSuccess) => {
  Modal.confirm({
    title: '确认删除',
    content: '是否删除选中数据',
    okText: '确认',
    cancelText: '取消',
    onOk: () => {
      return defHttp.delete({url: Api.deleteBatch, data: params}, {joinParamsToUrl: true}).then(() => {
        handleSuccess();
      });
    }
  });
}
/**
 * 保存或者更新
 * @param params
 * @param isUpdate 是否是更新数据
 */
export const saveOrUpdate = (params, isUpdate) => {
  let url = isUpdate ? Api.edit : Api.save;
  return defHttp.post({url: url, params});
}

export const query24MonthDataRequest = (params) =>
  defHttp.get({url: Api.query24MonthData, params});

export const invMlSaveOrUpdate = (params, isUpdate) => {
  let url = isUpdate ? Api.invMlEdit : Api.invMlAdd;
  return defHttp.post({url: url, params});
}

export const invMLSaveBatchRequest = (params) => {
  return defHttp.post({url: Api.invMLSaveBatch, params});
}

export const querySrcIpByIdRequest = (params) => {
  return defHttp.get({url: Api.querySrcIpById, params});
}


export const queryDstIpByIdRequest = (params) => {
  return defHttp.get({url: Api.queryDstIpById, params});
}

export const queryMlInvListRequest = (params) => {
  return defHttp.get({url: Api.queryMlInvList, params});
}

export const queryLogIdByMlIdRequest = (params) => {
  return defHttp.get({url: Api.queryLogIdByMlId, params});
}
