import {BasicColumn} from '/@/components/Table';
import {FormSchema} from '/@/components/Table';
import { render } from '/@/utils/common/renderUtils';
import {
  RULE_RISK_TYPE,
  RULE_URGENCY_LEVEL
} from "/@/views/rule/aggregation/AggregationRule.data";
import {useI18n} from "/@/hooks/web/useI18n";
const {t} = useI18n();
export const columns: BasicColumn[] = [
   {
    title: t('routes.riskEvent.ruleName'),
    dataIndex: 'ruleName'
   },
   {
    title: 'Risk Type',
    dataIndex: 'ruleRiskType',
     customRender: ({text}) => {
       return render.renderDictNative(text, RULE_RISK_TYPE);
     },
   },
   {
    title: 'Urgency',
    dataIndex: 'urgency',
     customRender: ({text}) => {
       return render.renderDictNative(text, RULE_URGENCY_LEVEL);
     }
   },
    {
      title: 'Bad Actor Number',
      dataIndex: 'badActor'
    },
   {
    title: 'Risk Object Number',
    dataIndex: 'riskObj'
   },
   {
     title: 'Status',
     dataIndex: 'mlStatus',
     customRender: ({text}) => {
       if(text=='1'){
         return "New";
       }else if(text=='2'){
         return "Investigating";
       }else if(text=='3'){
         return "Closed";
       }
     }
   },
    {
      title: 'Alarm Time',
      dataIndex: 'createTime'
    },
];

export const searchFormSchema: FormSchema[] = [
  {
    label: t('routes.riskEvent.ruleName'),
    field: 'ruleNameStr',
    component: 'Input',
  },
  {
    label: t('routes.aggregationrule.riskType'),
    field: 'ruleRiskType',
    component: 'Select',
    componentProps: {
      options: RULE_RISK_TYPE,
      stringToNumber: true,
    }
  },
  {
    label:  t('routes.riskEvent.ruleUrgency'),
    field: 'urgency',
    component: 'Select',
    componentProps: {
      options: [
        {label: 'Critical', value: '1'},
        {label: 'High', value: '2'},
        {label: 'Medium', value: '3'},
        {label: 'Low', value: '4'},
      ],
    },
  },
  {
    label: "Bad Actor",
    field: 'srcIp',
    component: 'Input',
  },
  {
    label: "Risk Object",
    field: 'dstIp',
    component: 'Input',
  },
  {
    label:  t('routes.riskEvent.status'),
    field: 'statusStr',
    component: 'JSelectMultiple',
    colProps: {
      span: 3,
    },
    componentProps: {
      options: [
        {label: 'New', value: '1'},
        {label: 'Investigating', value: '2'},
        {label: t('common.Closed'), value: '3'},
      ],
    },
  },
  {
    label: t('routes.riskEvent.alarmTime'),
    field: 'startDate',
    component: 'RangeDate',
    componentProps:{
      datetime:true,
    },
    colProps: {
      lg: 12, // ≥992px
      xl: 6, // ≥1200px
      xxl: 6, // ≥1600px
    },
  },
];

export const formSchema: FormSchema[] = [
  // TODO 主键隐藏字段，目前写死为ID
  {label: '', field: 'id', component: 'Input', show: false},
  {
    label: 'ruleId',
    field: 'ruleId',
    component: 'Input',
  },
  {
    label: 'ruleName',
    field: 'ruleName',
    component: 'Input',
  },
  {
    label: '风险类型 1:Network Risk,2:Threat Risk,3:Access Risk,4:Audit Risk,',
    field: 'ruleRiskType',
    component: 'Input',
  },
  {
    label: '紧急程度 1:Critical,2:High,3:Middle,4:Low,',
    field: 'urgency',
    component: 'Input',
  },
  {
    label: 'riskObj',
    field: 'riskObj',
    component: 'Input',
  },
  {
    label: 'badActor',
    field: 'badActor',
    component: 'Input',
  },
  {
    label: '1默认',
    field: 'mlStatus',
    component: 'Input',
  },
  {
    label: '原始规则json',
    field: 'ruleJson',
    component: 'Input',
  },
];
