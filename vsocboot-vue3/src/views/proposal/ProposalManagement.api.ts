import { defHttp } from '/@/utils/http/axios';
import { Modal } from 'ant-design-vue';
import { useI18n } from '/@/hooks/web/useI18n';
const { t } = useI18n();

enum Api {
  list = '/proposalManagement/proposalManagement/list',
  save = '/proposalManagement/proposalManagement/add',
  queryById = '/proposalManagement/proposalManagement/queryById',
  edit = '/proposalManagement/proposalManagement/edit',
  deleteOne = '/proposalManagement/proposalManagement/delete',
  deleteBatch = '/proposalManagement/proposalManagement/deleteBatch',
  importExcel = '/proposalManagement/proposalManagement/importExcel',
  exportXls = '/proposalManagement/proposalManagement/exportXls',
  queryRuleList = '/mlRule/mlRuleVO/queryRuleList',
  queryTenantListByRule = '/mlRule/mlRuleVO/queryTenantListByRule',
  tenantList = '/tenant/tenant/queryList',
  queryInvestigationList = '/investigationtemplate/investigationTemplate/queryListByTenant',
  queryTenantById = '/proposalManagement/proposalManagement/queryTenantById',
  queryTenantByRiskName = '/proposalManagement/proposalManagement/queryTenantByRiskName',
  queryRuleIfBindProposal = '/proposalManagement/proposalManagement/queryRuleIfBindProposal',
  queryRiskEventIfBindProposal = '/proposalManagement/proposalManagement/queryRiskEventIfBindProposal',
}
/**
 * 导出api
 * @param params
 */
export const getExportUrl = Api.exportXls;
/**
 * 导入api
 */
export const getImportUrl = Api.importExcel;
/**
 * 列表接口
 * @param params
 */
export const list = (params) => defHttp.get({ url: Api.list, params });

export const queryById = (params) => defHttp.get({ url: Api.queryById, params });

export const queryRuleList = (params) => defHttp.get({ url: Api.queryRuleList, params });

export const queryTenantListByRule = (params) => defHttp.get({ url: Api.queryTenantListByRule, params });

export const queryTenantList = (params) => defHttp.get({ url: Api.tenantList, params });

export const queryInvestigationList = (params) => defHttp.get({ url: Api.queryInvestigationList, params });

export const queryTenantById = (params) => defHttp.get({ url: Api.queryTenantById, params });

export const queryTenantByRiskName = (params) => defHttp.get({ url: Api.queryTenantByRiskName, params });

export const queryRuleIfBindProposal = (params) => defHttp.get({ url: Api.queryRuleIfBindProposal, params });

export const queryRiskEventIfBindProposal = (params) => defHttp.get({ url: Api.queryRiskEventIfBindProposal, params });

/**
 * 删除单个
 * @param params
 * @param handleSuccess
 */
export const deleteOne = (params, handleSuccess) => {
  Modal.confirm({
    title: t('common.delText'),
    content: t('common.delConfirmText'),
    okText: t('common.okText'),
    cancelText: t('common.cancelText'),
    wrapClassName: 'delete_confirm',
    onOk: () => {
      return defHttp
        .delete({ url: Api.deleteOne, params }, { joinParamsToUrl: true })
        .then(() => {
          handleSuccess();
        })
        .catch(() => {
          handleSuccess();
        });
    },
  });
};

/**
 * 批量删除
 * @param params
 * @param handleSuccess
 */
export const batchDelete = (params, handleSuccess) => {
  Modal.confirm({
    title: t('common.delConfirmText'),
    content: t('common.delContent'),
    okText: t('common.okText'),
    cancelText: t('common.cancelText'),
    wrapClassName: 'delete_confirm',
    onOk: () => {
      return defHttp.delete({ url: Api.deleteBatch, data: params }, { joinParamsToUrl: true }).then(() => {
        handleSuccess();
      });
    },
  });
};
/**
 * 保存或者更新
 * @param params
 * @param isUpdate 是否是更新数据
 */
export const saveOrUpdate = (params, isUpdate) => {
  const url = isUpdate ? Api.edit : Api.save;
  return defHttp.post({ url: url, params });
};

export const importExcel = (file, params, isReturn?: Function) => {
  return defHttp.uploadFile({ url: Api.importExcel }, { file: file, data: params }, { success: isReturn });
};
