<template>
  <div class="overflow-y-auto h-[100%] page_div">
      <ProposalManagementList />
  </div>
</template>

<script setup lang="ts">
  import ProposalManagementList from '/@/views/proposal/ProposalManagementList.vue';
</script>

<style scoped lang="less">
  .layout {
    height: calc(100vh - 52px);
    padding: 0px 16px 0;

    :deep(.ant-tabs-nav) {
      margin-bottom: 0;
    }
  }
</style>
