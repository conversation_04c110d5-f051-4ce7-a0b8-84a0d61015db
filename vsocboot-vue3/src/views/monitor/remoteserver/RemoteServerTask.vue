<template>
  <div class="p-4">
    <template v-for="(remoteServer,index) in remoteServerList" :key="remoteServer.appId">
      <RemoteServerCard :server="remoteServer"/>
    </template>
  </div>
</template>
<script lang="ts" name="remote-server-app" setup>
import {onMounted, ref, unref} from 'vue';
import RemoteServerCard from './modules/RemoteServerCard.vue'
import {queryTaskServerList} from "/@/views/monitor/remoteserver/remoteserver.api";


const remoteServerList: any = ref([]);
const getServerList = () => {
  queryTaskServerList().then((res) => {
    res.forEach((item)=>{
      let server: any = {
        "appId": item.id,
        "serverName": item.appServerName,
        "serverIP": item.appServerIp,
        "serverPort": item.appServerPort,
        "serverDesc": item.appServerDesc
      };
      remoteServerList.value.push(server);
    });

  });
}
onMounted(() => {
  getServerList();
});
</script>

<style lang="less" scoped>

</style>
