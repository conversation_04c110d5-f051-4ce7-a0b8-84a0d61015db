import { defHttp } from '/@/utils/http/axios';

enum Api {
  cpuCount = '/actuator/metrics/system.cpu.count',
  cpuUsage = '/actuator/metrics/system.cpu.usage',
  processStartTime = '/actuator/metrics/process.start.time',
  processUptime = '/actuator/metrics/process.uptime',
  processCpuUsage = '/actuator/metrics/process.cpu.usage',

  jvmMemoryMax = '/actuator/metrics/jvm.memory.max',
  jvmMemoryCommitted = '/actuator/metrics/jvm.memory.committed',
  jvmMemoryUsed = '/actuator/metrics/jvm.memory.used',
  jvmBufferMemoryUsed = '/actuator/metrics/jvm.buffer.memory.used',
  jvmBufferCount = '/actuator/metrics/jvm.buffer.count',
  jvmThreadsDaemon = '/actuator/metrics/jvm.threads.daemon',
  jvmThreadsLive = '/actuator/metrics/jvm.threads.live',
  jvmThreadsPeak = '/actuator/metrics/jvm.threads.peak',
  jvmClassesLoaded = '/actuator/metrics/jvm.classes.loaded',
  jvmClassesUnloaded = '/actuator/metrics/jvm.classes.unloaded',
  jvmGcMemoryAllocated = '/actuator/metrics/jvm.gc.memory.allocated',
  jvmGcMemoryPromoted = '/actuator/metrics/jvm.gc.memory.promoted',
  jvmGcMaxDataSize = '/actuator/metrics/jvm.gc.max.data.size',
  jvmGcLiveDataSize = '/actuator/metrics/jvm.gc.live.data.size',
  jvmGcPause = '/actuator/metrics/jvm.gc.pause',

  tomcatSessionsCreated = '/actuator/metrics/tomcat.sessions.created',
  tomcatSessionsExpired = '/actuator/metrics/tomcat.sessions.expired',
  tomcatSessionsActiveCurrent = '/actuator/metrics/tomcat.sessions.active.current',
  tomcatSessionsActiveMax = '/actuator/metrics/tomcat.sessions.active.max',
  tomcatSessionsRejected = '/actuator/metrics/tomcat.sessions.rejected',
}

/**
 * 查询cpu数量
 */
export const getCpuCount = () => {
  return defHttp.get({ url: Api.cpuCount }, { isTransformResponse: false });
};

/**
 * 查询系统 CPU 使用率
 */
export const getCpuUsage = () => {
  return defHttp.get({ url: Api.cpuUsage }, { isTransformResponse: false });
};

/**
 * 查询应用启动时间点
 */
export const getProcessStartTime = () => {
  return defHttp.get({ url: Api.processStartTime }, { isTransformResponse: false });
};

/**
 * 查询应用已运行时间
 */
export const getProcessUptime = () => {
  return defHttp.get({ url: Api.processUptime }, { isTransformResponse: false });
};

/**
 * 查询当前应用 CPU 使用率
 */
export const getProcessCpuUsage = () => {
  return defHttp.get({ url: Api.processCpuUsage }, { isTransformResponse: false });
};

/**
 * 查询JVM 最大内存
 */
export const getJvmMemoryMax = () => {
  return defHttp.get({ url: Api.jvmMemoryMax }, { isTransformResponse: false });
};

/**
 * JVM 可用内存
 */
export const getJvmMemoryCommitted = () => {
  return defHttp.get({ url: Api.jvmMemoryCommitted }, { isTransformResponse: false });
};

/**
 * JVM 已用内存
 */
export const getJvmMemoryUsed = () => {
  return defHttp.get({ url: Api.jvmMemoryUsed }, { isTransformResponse: false });
};

/**
 * JVM 缓冲区已用内存
 */
export const getJvmBufferMemoryUsed = () => {
  return defHttp.get({ url: Api.jvmBufferMemoryUsed }, { isTransformResponse: false });
};

/**
 *JVM 当前缓冲区数量
 */
export const getJvmBufferCount = () => {
  return defHttp.get({ url: Api.jvmBufferCount }, { isTransformResponse: false });
};

/**
 **JVM 守护线程数量
 */
export const getJvmThreadsDaemon = () => {
  return defHttp.get({ url: Api.jvmThreadsDaemon }, { isTransformResponse: false });
};

/**
 *JVM 当前活跃线程数量
 */
export const getJvmThreadsLive = () => {
  return defHttp.get({ url: Api.jvmThreadsLive }, { isTransformResponse: false });
};

/**
 *JVM 峰值线程数量
 */
export const getJvmThreadsPeak = () => {
  return defHttp.get({ url: Api.jvmThreadsPeak }, { isTransformResponse: false });
};

/**
 *JVM 已加载 Class 数量
 */
export const getJvmClassesLoaded = () => {
  return defHttp.get({ url: Api.jvmClassesLoaded }, { isTransformResponse: false });
};

/**
 *JVM 未加载 Class 数量
 */
export const getJvmClassesUnloaded = () => {
  return defHttp.get({ url: Api.jvmClassesUnloaded }, { isTransformResponse: false });
};

/**
 **GC 时, 年轻代分配的内存空间
 */
export const getJvmGcMemoryAllocated = () => {
  return defHttp.get({ url: Api.jvmGcMemoryAllocated }, { isTransformResponse: false });
};

/**
 *GC 时, 老年代分配的内存空间
 */
export const getJvmGcMemoryPromoted = () => {
  return defHttp.get({ url: Api.jvmGcMemoryPromoted }, { isTransformResponse: false });
};

/**
 *GC 时, 老年代的最大内存空间
 */
export const getJvmGcMaxDataSize = () => {
  return defHttp.get({ url: Api.jvmGcMaxDataSize }, { isTransformResponse: false });
};

/**
 *FullGC 时, 老年代的内存空间
 */
export const getJvmGcLiveDataSize = () => {
  return defHttp.get({ url: Api.jvmGcLiveDataSize }, { isTransformResponse: false });
};

/**
 *系统启动以来GC 次数
 */
export const getJvmGcPause = () => {
  return defHttp.get({ url: Api.jvmGcPause }, { isTransformResponse: false });
};

/**
 *tomcat 已创建 session 数
 */
export const getTomcatSessionsCreated = () => {
  return defHttp.get({ url: Api.tomcatSessionsCreated }, { isTransformResponse: false });
};

/**
 *tomcat 已过期 session 数
 */
export const getTomcatSessionsExpired = () => {
  return defHttp.get({ url: Api.tomcatSessionsExpired }, { isTransformResponse: false });
};

/**
 *tomcat 当前活跃 session 数
 */
export const getTomcatSessionsActiveCurrent = () => {
  return defHttp.get({ url: Api.tomcatSessionsActiveCurrent }, { isTransformResponse: false });
};

/**
 *tomcat 活跃 session 数峰值
 */
export const getTomcatSessionsActiveMax = () => {
  return defHttp.get({ url: Api.tomcatSessionsActiveMax }, { isTransformResponse: false });
};

/**
 *超过session 最大配置后，拒绝的 session 个数
 */
export const getTomcatSessionsRejected = () => {
  return defHttp.get({ url: Api.tomcatSessionsRejected }, { isTransformResponse: false });
};

export const getMoreInfo = (infoType) => {
  if (infoType == '1') {
    return {};
  }
  if (infoType == '2') {
    return { 'jvm.gc.pause': ['.count', '.totalTime'] };
  }
  if (infoType == '3') {
    return {
      'tomcat.global.request': ['.count', '.totalTime'],
      'tomcat.servlet.request': ['.count', '.totalTime'],
    };
  }
};

export const getTextInfo = (infoType) => {
  if (infoType == '1') {
    return {
      'system.cpu.count': { color: 'green', text: 'The number of CPU cores on this machine.', unit: 'core' },
      'system.cpu.usage': { color: 'green', text: 'The usage of cpus.', unit: '%', valueType: 'Number' },
      'process.start.time': { color: 'purple', text: 'Application Startup Time', unit: '', valueType: 'Date' },
      'process.uptime': { color: 'purple', text: 'Application Uptime', unit: 'Second' },
      'process.cpu.usage': { color: 'purple', text: 'Current Application CPU Usage', unit: '%', valueType: 'Number' },
    };
  }
  if (infoType == '2') {
    return {
      'jvm.memory.max': { color: 'purple', text: 'Maximum amount of memory available for memory management, in bytes.', unit: 'MB', valueType: 'RAM' },
      'jvm.memory.committed': { color: 'purple', text: 'Amount of memory available to the JVM, in bytes.', unit: 'MB', valueType: 'RAM' },
      'jvm.memory.used': { color: 'purple', text: 'Amount of used memory, in bytes.', unit: 'MB', valueType: 'RAM' },
      'jvm.buffer.memory.used': { color: 'cyan', text: 'An estimate of the memory that the JVM is using for this buffer pool, in bytes.', unit: 'MB', valueType: 'RAM' },
      'jvm.buffer.count': { color: 'cyan', text: 'An estimate of the number of buffers in the pool.', unit: 'Piece' },
      'jvm.threads.daemon': { color: 'green', text: 'Number of live daemon threads.', unit: 'Piece' },
      'jvm.threads.live': { color: 'green', text: 'Number of live threads, including both daemon and nondaemon threads.', unit: 'Piece' },
      'jvm.threads.peak': { color: 'green', text: 'Peak live thread count since the JVM started or peak was reset.', unit: 'Piece' },
      'jvm.classes.loaded': { color: 'orange', text: 'Number of loaded classes.', unit: 'Piece' },
      'jvm.classes.unloaded': { color: 'orange', text: 'Total number of unloaded classes since the process started.', unit: 'Piece' },
      'jvm.gc.memory.allocated': { color: 'pink', text: 'Increase in the size of the young heap memory pool after one garbage collection and before the next.', unit: 'MB', valueType: 'RAM' },
      'jvm.gc.memory.promoted': { color: 'pink', text: 'Count of positive increases in the size of the old generation memory pool from before to after garbage collection.', unit: 'MB', valueType: 'RAM' },
      'jvm.gc.max.data.size': { color: 'pink', text: 'Maximum size of long-lived heap memory pool, in bytes.', unit: 'MB', valueType: 'RAM' },
      'jvm.gc.live.data.size': { color: 'pink', text: 'Size of long-lived heap memory pool after reclamation, in bytes.', unit: 'MB', valueType: 'RAM' },
      'jvm.gc.pause.count': { color: 'blue', text: 'Count of garbage collection pause.', unit: 'Times' },
      'jvm.gc.pause.totalTime': { color: 'blue', text: 'Time spent in garbage collection pause, in seconds. ', unit: 'Second' },
    };
  }
  if (infoType == '3') {
    return {
      'tomcat.sessions.created': { color: 'green', text: 'Number of sessions that have been created.', unit: 'Piece' },
      'tomcat.sessions.expired': { color: 'green', text: 'Number of sessions that have expired.', unit: 'Piece' },
      'tomcat.sessions.active.current': { color: 'green', text: 'Tomcat Session Active Count.', unit: 'Piece' },
      'tomcat.sessions.active.max': { color: 'green', text: 'Maximum number of sessions that have been active at the same time.', unit: 'Piece' },
      'tomcat.sessions.rejected': { color: 'green', text: 'Number of sessions that were not created because the maximum number of active sessions reached.', unit: 'Piece' },
      'tomcat.global.sent': { color: 'purple', text: 'Amount of data Tomcat web server sent.', unit: 'bytes' },
      'tomcat.global.request.max': { color: 'purple', text: 'Maximum time of Tomcat web server to process a request.', unit: 'Second' },
      'tomcat.global.request.count': { color: 'purple', text: 'Count of Tomcat web server processed requests.', unit: 'Times' },
      'tomcat.global.request.totalTime': { color: 'purple', text: 'Total count of Tomcat web server processed requests.', unit: 'Second' },
      'tomcat.servlet.request.max': { color: 'cyan', text: 'Maximum time of servlet to process a request.', unit: 'Second' },
      'tomcat.servlet.request.count': { color: 'cyan', text: 'Count of servlet processed requests.', unit: 'Times' },
      'tomcat.servlet.request.totalTime': { color: 'cyan', text: 'Total count of servlet processed requests.', unit: 'Second' },
      'tomcat.threads.current': { color: 'pink', text: 'Number of current threads.', unit: 'Piece' },
      'tomcat.threads.config.max': { color: 'pink', text: 'Maximum number of threads configured for tomcat.', unit: 'Piece' },
    };
  }
};

/**
 * 查询cpu数量
 * @param params
 */
export const getServerInfo = (infoType) => {
  if (infoType == '1') {
    return Promise.all([getCpuCount(), getCpuUsage(), getProcessStartTime(), getProcessUptime(), getProcessCpuUsage()]);
  }
  if (infoType == '2') {
    return Promise.all([
      getJvmMemoryMax(),
      getJvmMemoryCommitted(),
      getJvmMemoryUsed(),
      getJvmBufferCount(),
      getJvmBufferMemoryUsed(),
      getJvmThreadsDaemon(),
      getJvmThreadsLive(),
      getJvmThreadsPeak(),
      getJvmClassesLoaded(),
      getJvmClassesUnloaded(),
      getJvmGcLiveDataSize(),
      getJvmGcMaxDataSize(),
      getJvmGcMemoryAllocated(),
      getJvmGcMemoryPromoted(),
      // getJvmGcPause(),
    ]);
  }
  if (infoType == '3') {
    return Promise.all([
      getTomcatSessionsActiveCurrent(),
      getTomcatSessionsActiveMax(),
      getTomcatSessionsCreated(),
      getTomcatSessionsExpired(),
      getTomcatSessionsRejected(),
    ]);
  }
};
