import { BasicColumn, FormSchema } from '/@/components/Table';

import { useI18n } from '/@/hooks/web/useI18n';
import {JInputTypeEnum} from "/@/enums/jeecgEnum";
const { t } = useI18n();
export const columns: BasicColumn[] = [
  {
    title: t('routes.monitor.logContent'),
    dataIndex: 'logContent',
    width: 150,
  },
  {
    title: t('routes.monitor.userName'),
    dataIndex: 'userid',
    width: 80,
  },
  // {
  //   title: 'User Name',
  //   dataIndex: 'username',
  //   width: 80,
  // },
  {
    title: 'IP',
    dataIndex: 'ip',
    width: 80,
  },
  {
    title: t('routes.monitor.costMillis'),
    dataIndex: 'costTime',
    width: 80,
  },
  {
    title: t('routes.monitor.createTime'),
    dataIndex: 'createTime',
    sorter: true,
    width: 80,
  },
  {
    title: t('routes.monitor.logType'),
    dataIndex: 'logType_dictText',
    width: 80,
  },
];

/**
 * 操作日志需要操作类型
 */
export const operationLogColumn: BasicColumn[] = [
  ...columns,
  {
    title: t('routes.monitor.operateType'),
    dataIndex: 'operateType_dictText',
    width: 80,
  },
];

export const searchFormSchema: FormSchema[] = [
  {
    field: 'keyWord',
    label: '',
    component: 'JInput',
    componentProps: {
      search:true,
      type:JInputTypeEnum.JINPUT_QUERY_EQ,
      placeholder: t('routes.monitor.keyword'),
    },
    colProps: {
      lg: 4, // ≥992px
      xl: 4, // ≥1200px
      xxl: 4, // ≥1600px
    },
  },
  {
    field: 'params',
    label: '',
    component: 'JInput',
    componentProps: {
      type:JInputTypeEnum.JINPUT_QUERY_EQ,
      search:true,
      placeholder: t('routes.monitor.params'),
    },
    colProps: {
      lg: 4, // ≥992px
      xl: 4, // ≥1200px
      xxl: 4, // ≥1600px
    },
  },
  {
    field: 'userid',
    label: '',
    component: 'JDictSelectTag',
    colProps: {
      lg: 4, // ≥992px
      xl: 4, // ≥1200px
      xxl: 4, // ≥1600px
    },
    componentProps: {
      showSearch: true,
      dictCode: 'sysUserNameDict',
      placeholder: t('routes.monitor.userName'),
    },
  },
  {
    label: '',
    field: 'costTimeNum',
    component: 'InputNumber',
    componentProps: {
      style: {
        width: '100%',
      },
      placeholder: t('routes.monitor.costMillis'),
    },
    colProps: {
      lg: 4, // ≥992px
      xl: 4, // ≥1200px
      xxl: 4, // ≥1600px
    },
  },
  {
    field: 'fieldTime',
    component: 'RangePicker',
    label: '',
    componentProps: {
      valueType: 'Date',
      placeholder: [t('routes.monitor.createTime'), t('routes.monitor.createTime')],
    },
    colProps: {
      span: 8,
    },
  },
];
