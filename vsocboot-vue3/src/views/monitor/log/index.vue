<template>
  <div class="page_div">
    <a-tabs defaultActiveKey="1" size="small">
      <a-tab-pane tab="Login Log" key="1">
        <div>
          <BasicTable @register="registerTable" :searchInfo="{ logType: '1' }" :columns="columns" />
        </div>
      </a-tab-pane>
      <a-tab-pane tab="Operate Log" key="2">
        <BasicTable @register="registerTable2" :searchInfo="{ logType: '2' }" :columns="operationLogColumn">
          <template #expandedRowRender="{ record }">
            <div class="badge-style">
              <div style="display: flex; width: 100%">
                <a-badge status="success" style="vertical-align: middle; flex-shrink: 0" />
                <span style="vertical-align: middle; white-space: normal; word-wrap: break-word">Request Method:{{ record?.method }}</span>
              </div>
              <div style="display: flex; width: 100%">
                <a-badge status="processing" style="vertical-align: middle; flex-shrink: 0" />
                <span style="vertical-align: middle; white-space: normal; word-wrap: break-word">Request Parameter:{{ record?.requestParam }}</span>
              </div>
            </div>
          </template>
        </BasicTable>
      </a-tab-pane>
    </a-tabs>
  </div>
</template>
<script lang="ts" name="monitor-log" setup>
  import { BasicTable } from '/@/components/Table';
  import { getLogList } from './log.api';
  import { columns, searchFormSchema, operationLogColumn } from './log.data';
  import { useListPage } from '/@/hooks/system/useListPage';
  import { TABLE_CACHE_KEY } from '/@/utils/valueEnum';
  import PageTitle from '/@/components/Menu/src/components/PageTitle.vue';
  import { formLayout } from '/@/settings/designSetting';

  // 列表页面公共参数、方法
  const { tableContext } = useListPage({
    designScope: 'user-list',
    tableProps: {
      title: 'Log List',
      api: getLogList,
      showActionColumn: false,
      rowSelection: {
        columnWidth: 20,
      },
      formConfig: {
        schemas: searchFormSchema,
        fieldMapToTime: [['fieldTime', ['createTime_begin', 'createTime_end'], 'YYYY-MM-DD']],
        layout: formLayout,
      },
      tableSetting: {
        cacheKey: TABLE_CACHE_KEY.loginlog,
      },
    },
  });

  const [registerTable, {}] = tableContext;

  const { tableContext: tableContext2 } = useListPage({
    designScope: 'user-list',
    tableProps: {
      title: 'Log List',
      api: getLogList,
      showActionColumn: false,
      expandIconColumnIndex: 0,
      formConfig: {
        schemas: searchFormSchema,
        fieldMapToTime: [['fieldTime', ['createTime_begin', 'createTime_end'], 'YYYY-MM-DD']],
      },
      tableSetting: {
        cacheKey: TABLE_CACHE_KEY.operationlog,
      },
    },
  });
  const [registerTable2, {}] = tableContext2;
</script>

<style scoped lang="less">
  /deep/ .soc-basic-table-header__tableTitle_right {
    display: contents;
  }

  :deep(.ant-tabs-nav) {
    margin-bottom: 0 !important;
  }

  .badge-style {
    background: @dark-bg2;
    width: 100%;
    padding: 16px;
  }
</style>
