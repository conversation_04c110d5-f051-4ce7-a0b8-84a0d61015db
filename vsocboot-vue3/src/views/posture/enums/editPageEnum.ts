export enum AddTypeEnum {
  DASHBOARD = 'dashboard',
  CHART = 'chart',
}
export enum TooltipEnum {
  Y = 'Y',
  X = 'X',
}

export enum FieldTypeEnum {
  Field = 'field',
  FieldName = 'name',
  TIME = 'time',
  Display = 'display',
  CONDITION = 'condition',
  Count = 'count',
  FieldValue = 'fieldValue',
  Column = 'column',
}


export enum TimeEnum {
  MIN = 0,
  HOUR = 1,
  DAY = 2,
  WEEK = 3,
  MONTH = 4,
  YEAR = 5,
}
//统计范围
export enum RangeEnum{
  HOUR = 1,
  DAY = 2,
  WEEK = 3,
  MONTH = 4,
  HARFYEAR = 6,
  YEAR = 5
}


//统计范围
export enum ChartFromEnum{
  NEW = 1,
  REPORT = 2
}
/**
 * setting group
 */
export enum GroupByTypeEnum{
  FIELD = '1',
  DESIGNATED = '2',
  UNCONDITIONAL = '3',
}
/**
 * setting group
 */
export enum GroupTypeEnum{
  FIELD = '1',
  DESIGNATED = '2',
  TIME = '3',
  SYSTEM = '4',
}
/**
 * setting value
 */
export enum ValueTypeEnum{
  FIELD = '1',
  DESIGNATED = '2',
  VOLUME = '3',
  FIELDVALUE = '4',
  SYSTEM = '5',
}
/**
 * Evaluatio value
 */
export enum EvaluationEnum{
  MAX = 1,
  MIN = 2,
  MEAN = 3,
}


/**
 * sort value
 */
export enum SortEnum{
  MAJORITY = '1',
  MINORITY = '2',
  RANDOM = '3',
}

/**
 * style value
 */
export enum StyleTypeEnum{
  DEFAULT = '1',
  THRESHOLD = '2',
  CONTINUOUS = '3',
  COLORS = '4',
}

/**
 * style value
 */
export enum SpecifyingEnum{
  YES = 1,
  NO = 2,
}


/**
 * dashboard private
 */
export enum E_DashboardPrivate{
  PRIVATE = 1,
  PUBLIC = 2,
}


/**
 * dashboard 来源
 */
export enum E_ChartFrom{
  NEW = 1,
  REPORT = 2,
}
