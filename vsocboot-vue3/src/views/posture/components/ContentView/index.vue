<template>
  <div id="dashboard-show_wrapper" class="dashboard-screens" >
    <div :style="styleObject" class="screen-adapter-style"  v-if="chartList.length > 0">

      <Vue3DraggableResizable
        :key="item.keyId"
        v-for="(item,index) in chartList"
        :prevent-deactivation="true"
        :initW="item.width"
        :initH="item.height"
        v-model:x="item.x"
        v-model:y="item.y"
        v-model:w="item.width"
        v-model:h="item.height"
        :draggable="false"
        :resizable="false" >
        <ContentBox v-model:value="chartList[index]" state="view">
          <template #content>
            <div
              class="box-child"
              :key="child.keyId + k"
              v-for="(child,k) in item.children"
              :style="{width:child.width + 'px',height:child.height + 'px', left : child.x + 'px',top:child.y + 'px'}">
              <template v-if="child.id || child.fillData?.configData">
                <SChart :value="child.fillData"  :keyId="child.keyId" />
              </template>

            </div>
          </template>
        </ContentBox>
      </Vue3DraggableResizable>

    </div>
  </div>
</template>

<script setup lang="ts">
import {computed, defineExpose, reactive, ref} from "vue";
import {ContentBox} from "/@/views/posture/components/ContentBox/index";
import {queryChartList, queryModuleList} from "/@/views/posture/modules/Dashboard.api";
import {SChart} from "/@/views/reportChart/chart/index";
import {IModule} from "/@/views/posture/ts/dashboardModule";
import Vue3DraggableResizable from "vue3-draggable-resizable";
import {isAdministrator} from "/@/utils/auth";

const height = ref(889);
const width = ref(1608 + (isAdministrator() ? 0 : 200));
const chartList = ref<IModule[]>([]);
const screen = reactive({
  width: width,
  height: height,
  scale: 1,
});
window.addEventListener('resize', () => changeStyle());

let styleObject = computed(() => {
  return {
    transform: `scale(${screen.scale})`,
    // WebkitTransform: `scale(${screen.scale}) translate(-50%, -50%)`,
    width: screen.width + "px",
    height: screen.height + "px",
  };
})


/**
 * 渲染保存过的大屏
 * @param dashboardId
 */
async function initContent(dashboardId){
  //默认为空
  chartList.value = [];

  //dashboard为空，不查询
  if(!dashboardId) return;

  //获取module
  let result = await  queryModuleList({dashboardId});

  //module为空,返回
  if(result.length == 0)   return;

  await caculateScreen(result);

  //获取chart
  result.forEach(item=>{
    queryChartList({moduleId : item.id}).then(data=>{
      item.children = data;
      chartList.value.push(item);
    })
  })


}

/**
 * 计算已用屏幕大小
 * @param result
 */
async function caculateScreen(result){
  let maxH = 0;
  let maxW = 0;
  result.forEach(item=>{
    if(item.height + item.y > maxH){
      maxH = item.height + item.y;
    }
    if(item.width + item.x > maxW){
      maxW = item.width + item.x;
    }
  })

  height.value = maxH ;
  width.value = maxW;
  changeStyle()
}


function changeStyle() {
  // console.log('11changeStyle1111')
  // const element = document.getElementById('dashboard-show_wrapper');
  // let w = element?.offsetWidth ?? screen.width;
  // let h = element?.offsetHeight ?? screen.height;
  //
  // const ww = w / screen.width;
  // const wh = h / screen.height;
  //
  // console.log('ww:',ww)
  // console.log('wh:',wh)
  // if(ww > wh && wh < 0.1){
  //   screen.scale = ww;
  // }
  // ww > wh 什么情况下是true？当屏幕宽度大于高度的时候，我们取高度的比例，反之取宽度的比例
  screen.scale = 1;
}
defineExpose({
  initContent,
})
</script>
<style lang="less" scoped>
.dashboard-screens{
  position: relative;
  width: 100%;
  height: 100%;
  overflow-y: auto;
  user-select: none;
  padding-bottom: 0px;
  scrollbar-color: rgba(144, 146, 152, 0.3) transparent;
  scrollbar-width: thin;
  .box-child{
    height: 100%;
    position: absolute;
    width:100%
  }
}
.postion-ab{
  position: absolute;
  z-index: 3;
}
.child-img{
  width:100%;
  height: 100%;
  img{
    width:100%;
    height: 100%;

  }
}

.screen-adapter-style{
  transform-origin: 0 0;
  position: absolute;
  //left: 50%;
  //top: 50%;
  transition: 0.3s; // 增大该值可以比较明显地看到自适应过程
  background: transparent; // 可以调整背景色
}


</style>

