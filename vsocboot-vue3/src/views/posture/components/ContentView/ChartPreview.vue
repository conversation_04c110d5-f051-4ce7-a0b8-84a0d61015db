<template>
  <ContentBox  state="view" v-model:value="moduleData" >
    <template #content>
      <SChart @legend="setLegend" :value="fillData" v-if="isValidate" :isPreview="true" :keyId="moduleData?.keyId"/>
    </template>
  </ContentBox>
</template>
<script setup lang="ts">
import {ContentBox} from "/@/views/posture/components/ContentBox/index";
import {inject, ref, Ref, unref, watch, watchEffect} from "vue";
import {IBaseData, IModuleChartFill, IModuleData} from "/@/views/reportChart/ts/IModule";
import {doValidate, getChartData, getSettingData} from "/@/views/reportChart/chart/chartUtils";
import {ChartNOEnum} from "/@/views/posture/enums/chartEnum";
import {RangeEnum} from "/@/views/posture/enums/editPageEnum";

import {DO_TIME_DATASOURCE, TIME_DEFAULT_USED_FIELD} from "/@/views/reportChart/ts/dataSource";
import {SChart} from "/@/views/reportChart/chart";
import {CONFIG_ERROR} from "/@/views/reports/chart/ts/Setting";
import {useMessage} from "/@/hooks/web/useMessage";
import dayjs from "dayjs";

const {createMessage} = useMessage();
const moduleData = inject('module',ref<IModuleData>({}));
const dynamicLegendData = inject('dynamicLegendData',ref([]));
const tenantId = inject<Ref<string>>('tenantId',ref(''));
const statisticalRange = inject<Ref<number>>('statisticalRange',ref(RangeEnum.DAY));
const fillData = ref<IModuleChartFill>({})
const isValidate = ref(false);
watchEffect(()=>{
  const n = unref(moduleData) as IModuleData;
  isValidate.value = doValidate(n)
  if(isValidate.value === true){
    fillData.value.configData = JSON.parse(JSON.stringify(getSettingData(n)));
    fillData.value.chartData = JSON.parse(JSON.stringify(getChartData(n)));
    fillData.value.chartData!.socTenantId = tenantId.value;
    fillData.value.chartData!.baseInfo = getBaseInfo();
    fillData.value.configData!.chartStyle = n.chartStyle;
  }
})




/**
 * base info
 * timeField 是默认查询时间字段（badactor和asset查询所有）
 * rangeValue 时间范围
 * statisticalType 固定值为2，实时查询
 */
function getBaseInfo() {
  const rangesValueOption = {
    [RangeEnum.DAY]: [dayjs().subtract(24, 'hour').format('YYYY-MM-DD HH:mm:ss'), dayjs().format('YYYY-MM-DD HH:mm:ss')],
    [RangeEnum.WEEK]: [dayjs().subtract(1, 'week').format('YYYY-MM-DD HH:mm:ss'), dayjs().format('YYYY-MM-DD HH:mm:ss')],
    [RangeEnum.MONTH]: [dayjs().subtract(1, 'month').format('YYYY-MM-DD HH:mm:ss'), dayjs().format('YYYY-MM-DD HH:mm:ss')],
    [RangeEnum.HARFYEAR]: [dayjs().subtract(6, 'month').format('YYYY-MM-DD HH:mm:ss'), dayjs().format('YYYY-MM-DD HH:mm:ss')],
    [RangeEnum.YEAR]: [dayjs().subtract(1, 'year').format('YYYY-MM-DD HH:mm:ss'), dayjs().format('YYYY-MM-DD HH:mm:ss')]
  };
  let baseInfo: IBaseData = {
    statisticalType: 2,
    socTenantIds: tenantId.value ,
    timeField: moduleData.value.timeFieldToUse ?? TIME_DEFAULT_USED_FIELD[moduleData.value.dataSource as string + (moduleData.value.logType ?? '')],
    rangeValue: rangesValueOption[statisticalRange.value].join(',')
  }
  //热力图特殊处理
  if (!DO_TIME_DATASOURCE.includes(moduleData.value.dataSource as string) && moduleData.value.chartType == ChartNOEnum.BASIC_HEAT) {
    baseInfo.rangeType = statisticalRange.value;
    baseInfo.rangeValue = 1;
    if (baseInfo.rangeType == RangeEnum.HARFYEAR) {
      baseInfo.rangeValue = 6;
    }
    if (baseInfo.rangeType == RangeEnum.YEAR) {
      baseInfo.rangeType = RangeEnum.YEAR - 1;
    }
  }

  return baseInfo;
}

/**
 * module chart data
 */
function getMoudleChartData(){
  if(!isValidate.value){
    createMessage.warning(CONFIG_ERROR);
    return;
  }
  return fillData.value;
}
function setLegend(legend){
  if(moduleData.value.chartType == ChartNOEnum.RADAR){
    dynamicLegendData.value = legend.data;
  }
}
defineExpose({
  getMoudleChartData
})
</script>


<style scoped lang="less">

</style>
