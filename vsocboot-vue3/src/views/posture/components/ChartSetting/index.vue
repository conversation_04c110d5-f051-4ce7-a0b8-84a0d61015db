<template>
  <div class="chart-style-setting">
    <div class="chart-style-setting_show">
      <!-- dashboard name edit start-->
      <div class="chart-dashboard-name pl16 width400">
        <DashboardName/>
      </div>
      <!-- dashboard name edit end-->

      <!-- preview start-->
      <div class="chart-preview pl16">
        <div class="font14 fcolor1">{{ t('routes.posture.preview') }}</div>
        <div
          :style="{
            width:'400px',
            height:moduleData.chartType == ChartNOEnum.NUM ? '140px':'280px'
          }">
          <ChartPreview ref="chartPreview"/>
        </div>
      </div>
      <!-- preview edit-->

      <!-- other style select-->
      <div class="chart-select-style">
        <div class="font14 fcolor">{{ t('routes.posture.selectStyle') }}</div>
        <div class="chart-select-style_wrapper">
          <component :is="chartStyle[moduleData.chartType as number]"/>
        </div>
      </div>
      <!-- other style end-->

    </div>
    <div class="chart-style-setting_config">
      <TabGroup :tabs="!hasStyle ? postureConfigOptionNoStyle:postureConfigOption"/>
    </div>

  </div>

</template>

<script setup lang="ts" name="dashboard">
import {DashboardName} from "/@/views/reportChart/components";
import {ChartPreview} from "/@/views/posture/components/ContentView";
import {postureConfigOption, postureConfigOptionNoStyle} from "/@/views/posture/ts/chartSetting";
import {TabGroup} from "/@/views/reportChart/TabGroup";
import {inject, provide, ref} from "vue";
import {IModuleChartFill, IModuleData} from "/@/views/reportChart/ts/IModule";
import {ChartNOEnum} from "/@/views/reportChart/enums/chartEnum";
import {BarStyle} from "/@/views/reportChart/chart/components/bar/index";
import {StackBarStyle} from "/@/views/reportChart/chart/components/barstack/index";
import {PieStyle} from "/@/views/reportChart/chart/components/pie/index";
import {NumberStyle} from "/@/views/reportChart/chart/components/number/index";
import {GridStyle} from "/@/views/reportChart/chart/components/grid/index";
import {StatisticalListStyle} from "/@/views/reportChart/chart/components/statisticalList/index";
import {LineStyle} from "/@/views/reportChart/chart/components/line/index";
import {RadarStyle} from "/@/views/reportChart/chart/components/radar/index";
import {useI18n} from "/@/hooks/web/useI18n";

const {t} = useI18n();
const chartPreview = ref();
const moduleData = inject('module',ref<IModuleData>({}));
const dynamicLegendData = ref([]);
const hasStyle = ref(moduleData.value.chartType != ChartNOEnum.NUM);

provide('dynamicLegendData',dynamicLegendData)
function getFillData(){
  return chartPreview.value.getMoudleChartData() as IModuleChartFill;

}
/**
 * chart
 */
const chartStyle = {
  [ChartNOEnum.BASIC_COLUMN]:BarStyle,
  [ChartNOEnum.STACKED_BAR]:StackBarStyle,
  [ChartNOEnum.BASIC_PIE]:PieStyle,
  [ChartNOEnum.BASIC_LINE]:LineStyle,
  [ChartNOEnum.BASIC_LIST]:GridStyle,
  [ChartNOEnum.STATISTICAL_LIST]:StatisticalListStyle,
  [ChartNOEnum.NUM]:NumberStyle,
  [ChartNOEnum.RADAR]:RadarStyle,
}

defineExpose({
  getFillData
})
</script>

<style lang="less" scoped>
.chart-style-setting {
  padding-top: 16px;
  display: flex;
  flex-direction: row;
  min-height: 100%;

  .chart-style-setting_show {
    width: 432px;
    display: flex;
    flex-direction: column;
    gap: 24px;
    .width400 {
      width: 416px;
    }


  }
  .pl16 {
    padding-left: 16px;
  }
  .pt16{
    padding-top: 16px;
  }
  .pb16{
    padding-bottom: 16px;
  }
  .chart-select-style{
    padding: 0 16px 16px 16px;
    flex: 1;
    flex-shrink: 0;
    display: flex;
    flex-direction: column;
    gap: 0;
    overflow-y: auto;
    min-height: 200px;
    position: relative;
    .chart-select-style_wrapper{
      height:100%;
      position: absolute;
      top: 40px;
      width:calc(100% - 32px);


    }
  }
  .chart-style-setting_config {
    flex: 1;
    padding-right: 16px;
  }

  .chart-preview {
    border-bottom: 1px solid @border-color;
    display: flex;
    flex-direction: column;
    gap: 8px;
    padding-bottom: 24px;
  }
}

</style>
