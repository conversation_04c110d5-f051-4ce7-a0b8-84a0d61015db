<template>
  <a-button
    v-if="!isUpdate && currentDashboardId"
    @click="editDashboard">
    <span class="soc ax-com-Edit ax-icon"></span>
    {{ t('routes.posture.editPosture') }}
  </a-button>
  <!--  开启大屏编辑模式按钮 end-->

  <!--   大屏编辑模式 操作按钮 start-->
  <template v-if="isUpdate">
    <a-button @click="addChart"><span class="soc ax-com-Add ax-icon"></span>{{ t('routes.posture.addAChart') }}</a-button>
    <a-button @click="doQuit">{{ t('common.cancelText') }}</a-button>
    <a-button @click="doSave" type="primary">{{ t('common.saveText') }}</a-button>
  </template>

</template>

<script setup lang="ts" name="dashboard">
import {inject, Ref, ref} from "vue";
import {StateEnum} from "/@/views/posture/enums/menuEnum";
import {useI18n} from "/@/hooks/web/useI18n";

const {t} = useI18n();
const emit = defineEmits(['addChart']);
const isUpdate = ref(false);
const currentDashboardId = inject<Ref>('currentDashboardId',ref(''))
const dashboardState = inject<Ref>('dashboardState', ref(StateEnum.SHOW))

/**
 * 添加dashboard chart
 */
function addChart() {
  emit('addChart');
}

/**
 * 退出编辑
 */
function doQuit() {
  isUpdate.value = false;
  dashboardState.value = StateEnum.SHOW
}

/**
 * 保存
 */
function doSave() {
  isUpdate.value = false;
  dashboardState.value = StateEnum.SAVE
}

/**
 * 开始编辑
 */
function editDashboard() {
  //没有dashboard 不能编辑
  if (!currentDashboardId.value) return;
  //开始编辑
  isUpdate.value = true;
  dashboardState.value = StateEnum.EDIT

}

</script>

<style lang="less" scoped>


</style>
