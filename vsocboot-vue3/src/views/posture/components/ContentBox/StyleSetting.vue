<template>
  <SettingBox>
    <template #title> {{ tp('Setting') }}</template>
    <template #content>
      <!--   Table header color
      ======================================================================-->
      <template v-if="CHART_SUBCLASS_TYPE[chartType]=='table'">
        <div class="setting-content_row">
          <span>{{tp('HeaderColor')}} </span>
          <ColorGroup v-model:value="styleSettingData.headerColor"/>
        </div>
      </template>
      <!--  echart color
     ======================================================================-->
      <template v-else>
        <!--   color 选项
         ======================================================================-->
        <div class="setting-content_row">
          <a-radio-group v-model:value="styleSettingData.type">
            <a-radio
              :value="StyleTypeEnum.DEFAULT"
              :disabled="DATA_VISUALMAP_CHART.includes(CHART_SUBCLASS_TYPE[chartType])">
              {{ tp('DefaultColor') }}
            </a-radio>
            <a-radio :value="StyleTypeEnum.THRESHOLD">{{tp('ThresholdColor')}}</a-radio>
            <a-radio
              :value="StyleTypeEnum.CONTINUOUS"
              :disabled="CHART_SUBCLASS_TYPE[chartType]==ChartTypeEnum.GAUGE">
              {{tp('ContinuousColor')}}
            </a-radio>
            <a-radio :value="StyleTypeEnum.COLORS"> {{tp('Color')}} </a-radio>
          </a-radio-group>
        </div>

        <!--   default color
         ======================================================================-->
        <div class="style-bg" v-if="styleSettingData.type == StyleTypeEnum.DEFAULT">
          <!--  饼图等自动添加默认颜色-->
          <template v-if="VIEW_NOT_COLOR_CHART.includes(CHART_SUBCLASS_TYPE[chartType])">
            <div class="setting-content_row" :key="index" v-for="(item,index) in styleSettingData.arrayColors">
              <Icon
                class="cursor fcolor3 icon-delete"
                icon="ant-design:minus-circle-filled"
                @click="delColors(index)"
                size="14"/>
              <ColorGroup v-model:value="styleSettingData.arrayColors[index]"/>
            </div>
            <div class="addBtn">
              <a-button type="primary" ghost @click="addDefaultColor">{{tp('AddColor')}}</a-button>
            </div>
          </template>
          <div class="setting-content_row" :key="'d-' + j" v-for="(item,j) in legendOption" v-else>
            <div class="flex1 ellipsis font12">{{ item.label }}</div>
            <ColorGroup v-model:value="item.pureColor"/>
          </div>

        </div>
        <!--   threshold color
        ======================================================================-->
        <div class="style-bg" v-if="styleSettingData.type == StyleTypeEnum.THRESHOLD">
          <div
            class="setting-content_row"
            :key="'t-' + index"
            v-for="(item,index) in styleSettingData.thresholdColors">

            <!--  delete value -->
            <Icon
              class="cursor fcolor3 icon-delete"
              icon="ant-design:minus-circle-filled"
              @click="delColors(index)"
              size="14"/>

            <!--  start value -->
            <a-input-number v-model:value="item.start" class="flex1"/>
            <!--  end value -->

            <template v-if="CHART_SUBCLASS_TYPE[chartType]!=ChartTypeEnum.GAUGE">
              <Icon
                class="cursor fcolor3"
                icon="ant-design:minus-outlined"
                style="width:14px"
                size="14"/>
              <a-input-number v-model:value="item.end" class="flex1"/>
            </template>

            <!--  color value -->
            <ColorGroup v-model:value="item.pureColor"/>
          </div>
          <div class="addBtn">
            <a-button type="primary" ghost @click="addThreshold">{{tp('AddThreshold')}}</a-button>
          </div>
        </div>

        <!--   continuours color
        ================================================================================-->
        <template v-if="styleSettingData.type == StyleTypeEnum.CONTINUOUS">
          <div class="setting-content_row">
            <span> {{tp('Min')}} </span>
            <a-input-number v-model:value="styleSettingData.min" class="flex1"/>
            <span class="ml-8"> {{tp('Max')}} </span>
            <a-input-number v-model:value="styleSettingData.max" class="flex1"/>
          </div>

          <div
            :key="'r-' + index"
            class="setting-content_row"
             v-for="(item,index) in styleSettingData.rangeColors">
            <Icon
              class="cursor fcolor3 icon-delete"
              icon="ant-design:minus-circle-filled"
              @click="delColors(index)"
              size="14"/>
            <ColorGroup v-model:value="styleSettingData.rangeColors[index]"/>
          </div>
          <div class="addBtn">
            <a-button type="primary" ghost @click="addColor">{{tp('AddColor')}}</a-button>
          </div>
        </template>

        <!--   最新 color
        ================================================================================-->
        <template v-if="styleSettingData.type == StyleTypeEnum.COLORS">
          <div class="setting-content_row">
            <span> {{tp('Min')}} </span>
            <a-input-number v-model:value="styleSettingData.min" class="flex1"/>
            <span class="ml-8">{{tp('Max')}} </span>
            <a-input-number v-model:value="styleSettingData.max" class="flex1"/>
          </div>

          <div class="setting-content_row" :key="'c'+index" v-for="(item,index) in styleSettingData.rangeColors">
            <Icon
              class="cursor fcolor3 icon-delete"
              icon="ant-design:minus-circle-filled"
              @click="delColors(index)"
              size="14"/>
            <ColorGroup v-model:value="styleSettingData.rangeColors[index]"/>
          </div>
          <div class="addBtn">
            <a-button type="primary" ghost @click="addColor">{{tp('AddColor')}}</a-button>
          </div>
        </template>

      </template>


    </template>
  </SettingBox>


</template>

<script setup lang="ts">
import {ColorGroup, SettingBox} from "/@/views/posture/components/ContentBox/index";
import "vue3-colorpicker/style.css";
import {
  CHART_SUBCLASS_TYPE,
  DATA_VISUALMAP_CHART,
  VIEW_NOT_COLOR_CHART
} from "/@/views/reports/chart/ts/ChartType";
import {inject,Ref,ref} from "vue";
import {getChartColor,} from "/@/views/reports/chart/ts/Setting";
import {StyleTypeEnum} from "/@/views/posture/enums/editPageEnum";
import {ChartTypeEnum} from "/@/views/posture/enums/chartEnum";
import {ILegend} from "/@/views/posture/ts/dashboardModule";
import {tp} from "/@/views/posture/ts/i18Utils";


const styleSettingData: any = inject('styleSettingData')
const chartType = inject('chartType') as number
const legendOption = inject<Ref<ILegend[]>>('legendOption')


/**
 * add threshold
 */
function addThreshold() {
  if (!styleSettingData.value.thresholdColors)
    styleSettingData.value.thresholdColors = [];
  let i = styleSettingData.value.thresholdColors.length;
  styleSettingData.value.thresholdColors.push({
    start: null,
    end: null,
    pureColor: getChartColor(i)
  });
}

/**
 * add color
 */
function addColor() {
  if (!styleSettingData.value.rangeColors)
    styleSettingData.value.rangeColors = [];
  let i = styleSettingData.value.rangeColors.length;
  styleSettingData.value.rangeColors.push(getChartColor(i));
}

/**
 * add color
 */
function addDefaultColor() {
  if (!styleSettingData.value.arrayColors)
    styleSettingData.value.arrayColors = [];
  let i = styleSettingData.value.arrayColors.length ?? 0;
  styleSettingData.value.arrayColors.push(getChartColor(i));
}

/**
 * delete threshold
 * @param index
 */
function delColors(index) {
  if (styleSettingData.value.type == StyleTypeEnum.DEFAULT) {
    styleSettingData.value.arrayColors.splice(index, 1)
  } else if (styleSettingData.value.type == StyleTypeEnum.THRESHOLD) {
    styleSettingData.value.thresholdColors.splice(index, 1)
  } else if (styleSettingData.value.type == StyleTypeEnum.CONTINUOUS) {
    styleSettingData.value.rangeColors.splice(index, 1)
  }else if (styleSettingData.value.type == StyleTypeEnum.COLORS) {
    styleSettingData.value.rangeColors.splice(index, 1)
  }

}
</script>

<style lang="less" scoped>

.setting-content_row {
  display: flex;
  flex-direction: row;
  gap: 16px;
  align-items: center;
  padding: 4px 16px;
}

.style-bg {
  display: flex;
  flex-direction: column;
  gap: 12px;
  background: #1A1B1F;
  padding: 16px 0;
  max-height: 330px;
  overflow-y: auto;
}

.flex1 {
  flex: 1;
}

.addBtn {
  padding-left: 16px;
}

.icon-delete:hover {
  background: rgba(255, 255, 255, 0.08);
  color: #F75555;
}

.ml-8 {
  margin-left: 8px;
}

</style>
