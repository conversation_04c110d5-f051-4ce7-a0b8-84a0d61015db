<template>
  <div class="color-group">
    <ColorPicker
      lang="En"
      theme="black"
      v-model:pureColor="colorValue"
      :disableAlpha="true"
      shape="square"
      format="hex6"
      useType="pure"/>
    <a-input v-model:value="colorValue"  class="flex1"/>
  </div>
</template>

<script setup lang="ts">


import {ColorPicker} from "vue3-colorpicker";
import {ref, watch, watchEffect} from "vue";

const emit = defineEmits(['update:value']);
const props = defineProps({
  value : String,
})
const colorValue = ref<string>();
watch(()=>colorValue.value,(n)=>{
  emit('update:value',n)
})

watchEffect(()=>{
  colorValue.value = props.value;
})
</script>

<style lang="less" scoped>
.color-group{
  border: 1px solid @border-color;
  width:120px;
  border-radius: 4px;
  display: flex;
  flex-direction: row;
  align-items: center;
  /deep/.ant-input{
    border: 0px;
    flex:1;
    border-radius: 0;
    border-left: 1px solid @border-color!important;
  }
  /deep/.ant-input:focus{
    box-shadow: none!important;
  }

  /deep/.vc-color-wrap{
    border-radius: 4px;
    left: 4px;
    margin-right: 8px;
    box-shadow: none;
    width: 24px;
    &.transparent{
      background-image: none;
    }
    .current-color{
      border-radius: 4px;
    }
  }

}
</style>
