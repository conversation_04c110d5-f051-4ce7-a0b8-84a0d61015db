<template>
  <div class="setting-content_row p-relative"  :key="'row' + index" v-for="(item,index) in dataList">
    <div class="setting-content_label" >
      <span>{{index + 1 }}#</span>
      <Icon  class="cursor fcolor3" icon="ant-design:minus-circle-filled" @click="doDel(index)"  size="14"/>
    </div>

    <!-- table Column field-->
    <template  v-if="props.type == FieldTypeEnum.Column">
      <div class="setting-content_row_select">
        <a-select v-model:value="dataList[index]"   show-search>
          <a-select-option
            :value="f.value"
            :key="f.value"
            v-for="f in fieldDataOption"
            :disabled="dataList.map(v=> v.fieldValue).includes(f.value)"> {{f.label}}</a-select-option>
        </a-select>
      </div>
    </template>

    <!--yAxis 、 xAxis field-->
    <template  v-if="props.type == FieldTypeEnum.Field">
      <div class="setting-content_row_select">
        <a-select v-model:value="item.fieldValue" @change="setItemLabel(item)"  show-search>
          <a-select-option
            :value="f.value"
            :key="f.value"
            v-for="f in fieldDataOption"
            :disabled="dataList.map(v=> v.fieldValue).includes(f.value)"> {{f.label}}</a-select-option>
        </a-select>
      </div>
    </template>

    <!--yAxis 、 xAxis field-->
    <template  v-if="props.type == FieldTypeEnum.FieldName">
      <div class="setting-content_row_select">
        <a-select v-model:value="item.name" @change="setItemLabel(item)"  show-search>
          <a-select-option
            :value="f.value"
            :key="f.value"
            v-for="f in fieldDataOption"
            :disabled="dataList.map(v=> v.fieldValue).includes(f.value)"> {{f.label}}</a-select-option>
        </a-select>
      </div>
    </template>


    <!--yAxis 、 xAxis statistic-->
    <template v-else-if="props.type == FieldTypeEnum.CONDITION">
      <a-input
        v-model:value="item.label"
        :placeholder="'Condition#' + (index+1)"
        :style="{width: source== DatasourceEnum.LOG ? '110px' : '370px','flex-shrink': 0}"/>

      <!--  Log表-->
      <div class="flex1" v-if="source == DatasourceEnum.LOG">
        <a-input v-model:value="item.echo"  @click="item.isFieldSearch = true"/>
        <FieldSearch
          ref="fieldSearchRef"
          v-if="item.isFieldSearch"
          v-model:value="dataList[index]"/>
      </div>
      <!--  其他表-->
      <div v-else>
        <Icon
          :class="['cursor',{'primaryColor' : item.advancedQueryFlag == true}]"
          icon="ant-design:file-search-outlined"
          :size="22"
          @click="openSearch(item)"/>
      </div>
    </template>


    <!--xAxis field value-->
    <template v-else-if="props.type == FieldTypeEnum.Count">
      <a-input
        v-model:value="item.label"
        :placeholder="'Condition#' + (index+1)"
        style="width:100px" />
      <a-select
        v-model:value="item.fieldValue"
        @change="setFieldValueItemLabel(item)"
        :options="numberDataOption"
        class="flex1"
        show-search/>
      <a-select
        v-model:value="item.type"
        :options="EVALUATION_OPTION"
        style="width: 150px"
        show-search/>
    </template>
  </div>
  <!--  add field -->
  <div class="addBtn">
    <a-button type="primary"  ghost @click="doAdd" >
      <span class="soc ax-com-Add ax-icon"></span> {{tp('Add')}} {{props.from == TooltipEnum.X ? 'Field' : CHART_BTN_NAME[chartType]}}
    </a-button>
  </div>
  <!--高级查询
  =======================================================================-->
  <TableSearchModel ref="tableSearchModelRef" :isReport="true" @search="saveSearch" :source="QUERY_TABLE_NAME[tableName]" />
</template>

<script setup lang="ts">
import FieldSearch from "/@/views/reports/components/FieldSearch.vue";


import {inject, ref, watch, watchEffect} from "vue";
import {FieldTypeEnum, TooltipEnum} from "/@/views/posture/enums/editPageEnum";
import {DatasourceEnum} from "/@/views/reports/enums/dataSourceEnum";
import {getTabFieldList} from "/@/utils/ckTable";
import {FIELD_TYPE_NO, QUERY_TABLE_NAME} from "/@/views/reports/chart/ts/Source";
import TableSearchModel from "/@/views/tableSearch/TableSearchModel.vue";
import {CHART_BTN_NAME} from "/@/views/reports/chart/ts/ChartType";
import {EVALUATION_OPTION} from "/@/views/reports/chart/ts/Setting";
import {tp} from "/@/views/posture/ts/i18Utils";

const emit = defineEmits(['del','update:value']);
const dataList:any = ref([]);
const chartType = inject('chartType')
const tableSearchModelRef = ref();
const searchItem = ref({});
const source = inject('source');
const tableName = inject('tableName');
const fieldDataOption = inject('fieldDataOption');
const numberDataOption = inject('numberDataOption');
const props = defineProps({
  from: String,
  type : String,
  value : Array,
  valueType : Number
})

watchEffect(()=>{
  dataList.value = props.value;
})

watch(()=>dataList.value,(n)=>{
  // console.log('dataList change=======',n)
  emit('update:value',n)
},{deep : true})

watch(()=>tableName.value,()=>{
  dataList.value = [];
})

watch(()=>props.type,()=>{
  dataList.value = [];
})



/**
 * delete data
 * @param index
 */
function doDel(index){
  dataList.value.splice(index,1)
}

/**
 * add data
 */
function doAdd(){
  let data:any = {
    label : '' ,
    fieldValue : ''
  }
  if(props.type == FieldTypeEnum.Count){
    data.type = "1";//Maximum value
  }
  if(props.valueType == 2){
    data = {name : ''};
  }
  dataList.value.push(data);
}

/**
 * 打开查询
 */
function openSearch(item) {
  searchItem.value = item;
  let list = getTabFieldList(FIELD_TYPE_NO[tableName.value]);
  tableSearchModelRef.value.init(list, item.str);
}

/**
 * 保存查询
 * @param value
 */
function saveSearch(value) {
  searchItem.value.advancedQueryFlag = true;
  searchItem.value.str = JSON.stringify(value);
}

/**
 * 设置label
 * @param item
 */
function setFieldValueItemLabel(item){
  item.label = numberDataOption.value.filter(data=>data.value == item.fieldValue)[0].label;
}

/**
 * 设置label
 * @param item
 */
function setItemLabel(item) {
  console.log('-----------------change item label---------------',item)
  if(props.valueType == 2 ){
    item.label = item.name;
  }
  else if(props.type == FieldTypeEnum.Field){
    item.label = fieldDataOption.value.filter(data=>data.value == item.fieldValue)[0].label;
  }

}
</script>

<style lang="less" scoped>
.setting-content_row{
  position: relative;
  display: flex;
  flex-direction: row;
  gap: 8px;
  align-items: center;
  padding: 4px 16px;

  &:hover{
    background: rgba(255, 255, 255, 0.08);
    .setting-content_label span:last-child{
      color: #F75555;
    }
  }
  .setting-content_label{
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    flex:0 0 40px;
    align-items: center;
  }

  &.setting-content_row_display{
    span {
      width:120px;
    }
  }
  .setting-content_row_select{
    flex: 1;
    /deep/ .ant-select{
      width:100%!important;
    }
  }
}
.addBtn{
  padding-left: 62px;
  border: 0px!important;
}
.flex1{
  flex: 1;
}
</style>
