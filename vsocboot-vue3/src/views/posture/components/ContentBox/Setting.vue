<template>
  <div class="column-wrapper">

    <!-- table column
   =========================================================================-->
    <template v-if="chartType == 16">
      <SettingBox>
        <template #title> {{tp('DataSetting')}}</template>
        <template #content>
          <FieldBox v-model:value="columns" :type="FieldTypeEnum.Column"/>
        </template>
      </SettingBox>
    </template>


    <!-- field value
    =========================================================================-->
    <template v-if="CHART_SUBCLASS_GROUP_NAME[chartType]">
      <y-setting/>
    </template>


    <!-- statistic value
      =========================================================================-->
    <template v-if="CHART_SUBCLASS_VALUE_NAME[chartType]">
      <x-setting/>
    </template>

    <!--  Display setting
   =========================================================================-->
    <template v-if="groupAxisData.dataType != GroupTypeEnum.TIME && CHART_DISPLAY_SETTING[chartType]">
      <DisplaySetting/>
    </template>

    <!--  style setting
    =========================================================================-->
    <StyleSetting/>

    <!--  advanced setting
     =========================================================================-->
    <AdvancedSetting/>
  </div>

</template>

<script setup lang="ts">
import {
  CHART_DISPLAY_SETTING,
  CHART_SUBCLASS_GROUP_NAME,
  CHART_SUBCLASS_VALUE_NAME
} from "/@/views/reports/chart/ts/ChartType";
import {
  AdvancedSetting,
  DisplaySetting,
  FieldBox,
  SettingBox,
  StyleSetting,
  XSetting,
  YSetting
} from "/@/views/posture/components/ContentBox/index";
import {defineExpose, inject, provide, Ref, watch} from "vue";
import {useModule} from "/@/views/posture/hooks/useModulehook";
import {FieldTypeEnum, GroupTypeEnum} from "/@/views/posture/enums/editPageEnum";
import {tp} from "/@/views/posture/ts/i18Utils";

const {
  groupAxisData,
  initSettingData,
  getSettingData,
  doValidate,
  displaySettingData,
  settingData,
  getLegendOption,
  valueAxisData,
  styleSettingData,
  legendOption,
  columns,
  advancedSettingData
} = useModule();
const chartType = inject<Ref<number|string>>('chartType');
provide('displaySettingData', displaySettingData)
provide('styleSettingData', styleSettingData)
provide('legendOption', legendOption)
provide('columns', columns)
provide('advancedSettingData', advancedSettingData)
provide('groupAxisData', groupAxisData)
provide('valueAxisData', valueAxisData)

watch(() => valueAxisData.value, (n) => {
  if (n) {
    getLegendOption(chartType.value)
  }
}, {deep: true})
// watch(()=>displaySettingData.value,(n)=>{
//   console.log('displaySettingData.value change=====',displaySettingData.value)
// })
// watch(()=>styleSettingData.value,(n)=>{
//   console.log('styleSettingData.value change=====',styleSettingData.value)
// })



/**
 * 获取配置数据
 */
function getSettingConfigData() {
  let validate = doValidate(chartType.value);
  if (validate) {
    return getSettingData(chartType.value);
  }

  return validate;
}

defineExpose({
  initSettingData, settingData, getSettingConfigData
});

</script>

<style lang="less" scoped>
.setting-content {
  display: flex;
  flex-direction: column;
  gap: 12px;
  padding: 12px 0px;
}
</style>
