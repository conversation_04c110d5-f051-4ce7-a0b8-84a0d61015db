<template>

  <TimeFieldUsed v-model:value="timeInterval.timeField"/>

  <!--  统计范围-->
  <div class="setting-content_row">
    <div class="font13">{{ t('routes.posture.statisticalRange') }}</div>
  </div>
  <div class="setting-content_row">
    <TimeInterval
      :type="CHART_SUBCLASS_TYPE[chartType] == ChartTypeEnum.HEATMAP ? 100 : 101"
      :disabled="CHART_SUBCLASS_TYPE[chartType] == ChartTypeEnum.HEATMAP"
      v-model:value="timeInterval"
      :state="1"/>
  </div>

  <!--  统计范围-->
  <div class="setting-content_row">
    <div class="font13">{{ t('routes.posture.timeInterval') }}</div>
  </div>
  <div class="setting-content_row">
    <div>The current statistical range is
      {{ timeInterval.rangeValue }} {{ TIME_INTERVAL_OPTION_MAP[timeInterval.rangeType as number] }}
      , the time interval on X axis should be less than {{ timeInterval.rangeValue }}
      {{ TIME_INTERVAL_OPTION_MAP[timeInterval.rangeType as number] }}
    </div>
  </div>
  <!--  时间间隔-->
  <div class="setting-content_row">
    <TimeInterval
      :type="timeInterval.rangeType"
       v-model:value="timeInterval"
       :state="1"
       numberName="value"
      selectName="type"/>
  </div>


</template>

<script setup lang="ts">
import {TimeFieldUsed, TimeInterval} from "/@/views/posture/components/ContentBox/index";
import {inject, ref, watch} from "vue";
import {CHART_SUBCLASS_TYPE} from "/@/views/reports/chart/ts/ChartType";
import {ITimeInterval} from "/@/views/posture/ts/dashboardModule";
import {ChartTypeEnum} from "/@/views/posture/enums/chartEnum";
import {TIME_INTERVAL_OPTION_MAP} from "../../../reports/chart/ts/Setting";
import {useI18n} from "/@/hooks/web/useI18n";

const {t} = useI18n();
const emit = defineEmits(['update:value']);
const chartType = inject<string>('chartType') as string
const timeInterval = ref<ITimeInterval>({rangeType : 1});
const props = defineProps({
  value: Object,
});
timeInterval.value = props.value as ITimeInterval;


watch(() => timeInterval.value, (n) => {
  emit('update:value', n);
}, {immediate: true, deep: true})

function refreshTime(value) {
  console.log('refreshTime', value)
  timeInterval.value = value;
}

defineExpose({
  refreshTime
});
</script>

<style lang="less" scoped>

.setting-content_row {
  display: flex;
  flex-direction: row;
  gap: 16px;
  align-items: center;
  padding: 4px 16px;

}

.flex1 {
  flex: 1
}
</style>
