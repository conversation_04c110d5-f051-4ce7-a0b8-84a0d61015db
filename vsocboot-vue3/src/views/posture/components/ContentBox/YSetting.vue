<template>

  <SettingBox
    :isContentShow="!(groupAxisData.dataType == GroupTypeEnum.TIME && !DO_TIME_DATASOURCE.includes(source))">
    <template #title> {{ tp2(CHART_SUBCLASS_GROUP_NAME[chartType]) }}</template>
    <template #head>
      <a-select
        v-model:value="groupAxisData.dataType"
        :disabled="DATA_TYPE_TIME_CHART.includes(CHART_SUBCLASS_TYPE[chartType])"
        @change="changeGroupType"
        :options="GROUP_DATA_TYPE_OPTION"
        style="width: 220px"/>
      <Tooltip :type="TooltipEnum.Y"/>
    </template>
    <template #content>
      <template v-if="groupAxisData.dataType == GroupTypeEnum.FIELD">
        <FieldBox
          v-model:value="groupAxisData.fieldGroup"
          :from="TooltipEnum.Y"
          v-model:type="FieldTypeEnum.Field"/>
      </template>
      <template v-else-if="groupAxisData.dataType == GroupTypeEnum.DESIGNATED">
        <FieldBox
          v-model:value="groupAxisData.conditionGroup"
          :from="TooltipEnum.Y"
          v-model:type="FieldTypeEnum.CONDITION"/>
      </template>
      <template
        v-else-if="groupAxisData.dataType == GroupTypeEnum.TIME && DO_TIME_DATASOURCE.includes(source)">
        <FieldTime v-model:value="groupAxisData.timeInterval" ref="timeRef"/>
      </template>
    </template>
  </SettingBox>

</template>

<script setup lang="ts">

import {
  FieldBox,
  FieldTime,
  SettingBox,
  Tooltip
} from "/@/views/posture/components/ContentBox/index";
import {
  CHART_SUBCLASS_GROUP_NAME,
  CHART_SUBCLASS_TYPE,
  DATA_TYPE_TIME_CHART
} from "/@/views/reports/chart/ts/ChartType";
import {GROUP_DATA_TYPE_OPTION} from "/@/views/reports/chart/ts/Setting";
import {FieldTypeEnum, GroupTypeEnum, TooltipEnum} from "/@/views/posture/enums/editPageEnum";
import {inject, nextTick, ref, Ref} from "vue";
import {DO_TIME_DATASOURCE} from "/@/views/reports/chart/ts/Source";
import {tp2} from "/@/views/posture/ts/i18Utils";
import {IAxis} from "/@/views/posture/ts/dashboardModule";


const source = inject<Ref<string>>('source', ref(''));
const groupAxisData = inject<Ref<IAxis>>('groupAxisData', ref({}))
const timeRef = ref();
const chartType = inject('chartType') as number


/**
 * 改变统计项
 */
function changeGroupType(dataType: string) {
  const data: any = {dataType};
  if (dataType == GroupTypeEnum.FIELD) {
    data.fieldGroup = [];
  } else if (dataType == GroupTypeEnum.DESIGNATED) {
    data.conditionGroup = [];
  } else if (dataType == GroupTypeEnum.TIME) {
    data.timeInterval = {};
    if (DO_TIME_DATASOURCE.includes(source.value)) {
      data.timeInterval = {type: 0, rangeType: 1, rangeValue: 1, value: 1};
      nextTick(() => {
        timeRef.value.refreshTime(data.timeInterval);
      })
    }
  }
  groupAxisData.value = data;
}

</script>

<style scoped>

</style>
