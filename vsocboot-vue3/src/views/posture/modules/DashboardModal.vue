<template>
  <a-modal
    :title="title"
    width="1000px"
    :visible="visible"
    @ok="handleOk"
    @cancel="handleCancel"
    :maskClosable="false"
    :destroyOnClose="true">
    <template #closeIcon>
      <div class="ax-icon-button ax-icon-large">
        <span class="soc ax-com-Close ax-icon"></span>
      </div>
    </template>

    <div class="pt-16px">
      <a-form
        ref="formRef"
        autocomplete="off"
        :model="form"
        :layout="formLayout">
        <div class="px-16px flex flex-row gap-8px">
          <div class="flex-1">
            <a-form-item :label="t('routes.posture.dashboardName')" required name="dashboardName">
              <a-input v-model:value="form.dashboardName" maxlength="128"/>
            </a-form-item>
          </div>
          <div class="flex-1">
            <a-form-item :label="t('routes.posture.private')" name="dashboardPrivate"  required>
            <a-select v-model:value="form.dashboardPrivate" :options="dashboardPrivateOption"/>
          </a-form-item>
          </div>
        </div>
        <div class="split-line"></div>
        <div class="px-16px pt-16px flex flex-row flex-wrap gap-8px">
          <a-form-item :label="t('routes.posture.selectMainStyle')" required name="dashboardMode">
            <div class="themeItem" v-for="item in ThemeOption" :key="'type-'+item.type">
              <img :src="item.img" :alt="item.type + 'image'"/>
              <div class="themeItem_check">
                <a-checkbox :value="item.type" :checked="form.dashboardMode===item.type" @change="setMode"/>
              </div>
            </div>
          </a-form-item>
        </div>
      </a-form>
    </div>

  </a-modal>
</template>

<script lang="ts" setup>
import {ref} from 'vue';
import {formLayout} from '/@/settings/designSetting';
import {queryDashboardInfo, saveOrUpdate} from "/@/views/posture/modules/Dashboard.api";
import {Dashboard, IDict} from "/@/views/posture/ts/dashboardModule";
import {E_DashboardPrivate} from "/@/views/posture/enums/editPageEnum";
import {tp} from "/@/views/posture/ts/i18Utils";
import {E_Theme} from "/@/views/posture/enums/theme";
import {ThemeOption} from "/@/views/posture/ts/themeSetting";
import {useI18n} from "/@/hooks/web/useI18n";

const {t} = useI18n();
const emit = defineEmits(['reload']);
const dashboardPrivateOption:IDict[] = [{
  label: tp('Private'),
  value: E_DashboardPrivate.PRIVATE
},{
  label: tp('Public'),
  value: E_DashboardPrivate.PUBLIC
}]
const defaultFormData = {dashboardPrivate : E_DashboardPrivate.PRIVATE,dashboardMode : E_Theme.DEFAULT};
const form = ref<Dashboard>({...defaultFormData});
const visible = ref(false);
const formRef = ref();
const title = ref(t('routes.posture.createNewDashboard'));

let tenantId = '';
//打开窗口
function open(data){
  tenantId = data.tenantId;
  if (data.id) {//修改
    title.value = t('routes.posture.updateDashboard');
    queryDashboardInfo({id : data.id}).then(result => {
      form.value = result;
    })
  }
  visible.value = true;
}
function handleCancel(){
  visible.value = false;
  form.value = {...defaultFormData};
}
async function handleOk(){
  await formRef.value.validate();
  form.value.socTenantId = tenantId;
  saveOrUpdate(form.value,!!form.value.id).then(()=>{
    emit('reload',form.value.id)
    handleCancel();
  })
}

function setMode(e){
  form.value.dashboardMode = '';
  if(e.target.checked){
    form.value.dashboardMode = e.target.value;
  }
  // form.value.dashboardMode = value;
}
defineExpose({
  open
})
</script>

<style lang="less" scoped>
@import "../less/common";
.themeItem{
  width: 308px;
  height: 173.25px;
  border-radius: 8px;
  border: 1px solid #308CFF;
  position: relative;
  img {
    width: 100%;
    height: 100%;
  }
  .themeItem_check{
    position: absolute;
    right: 8px;
    top: 8px;
    z-index: 2;
  }
}
.mode-box{
  border: 1px solid @border-color;
  border-radius: 4px;
  width : 200px;
  &.mode-box_active{
    border-color: @primary-color!important;
  }
  .flex-column{
    padding: 10px 12px;
  }
  .mode-box_img{
    position: relative;
    top: 8px;
    right: 8px;
    width: 48px;
    height: 48px;
    border-radius: 4px;
    background: rgba(255, 255, 255, 0.08);
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
  }
}
</style>
