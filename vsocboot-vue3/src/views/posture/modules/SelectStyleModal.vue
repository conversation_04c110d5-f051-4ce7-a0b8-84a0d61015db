<template>
  <a-modal :title="title" width="1200px" :visible="visible"
           @ok="handleOk" @cancel="handleCancel" :maskClosable="false"
           :destroyOnClose="true">
    <div class="padding16 ">
      <div class="style_head">
        <div class="font13 fcolor">{{ t('routes.posture.addMethod') }}</div>
        <a-select v-model:value="method" style="width: 220px" >
          <a-select-option :value="1">{{ t('routes.posture.createANewView') }}</a-select-option>
          <a-select-option :value="2">{{ t('routes.posture.importFromReport') }}</a-select-option>
        </a-select>
      </div>
      <div class="style_content">
        <ReportStyle ref="styleRef" v-if="method == 1"></ReportStyle>
        <ReportCardList ref="cardRef" v-else-if="method == 2"></ReportCardList>
      </div>

    </div>
  </a-modal>
</template>

<script lang="ts" setup>
import {defineExpose, ref} from 'vue';
import ReportStyle from "/@/views/reports/components/ReportStyle.vue";
import {AddTypeEnum} from "/@/views/posture/enums/editPageEnum";
import ReportCardList from "/@/views/posture/modules/ReportCardList.vue";
import {useI18n} from "/@/hooks/web/useI18n"; // Emits声明
const {t} = useI18n();
// Emits声明
const emit = defineEmits(['ok']);
const method = ref(1);
const visible = ref(false);
const styleRef = ref();
const cardRef = ref();
const title = ref('Select Style');
// 1 dashboard 整体 2 dashboard 子集
let selectType = AddTypeEnum.DASHBOARD;
function open(type){
  visible.value = true;
  selectType = type;
}
function handleCancel(){
  visible.value = false;
  method.value = 1;
}
async function handleOk(){
  let values = false;
  if( method.value == 1){
    values = await styleRef.value.getData();
  }else{
    values = await cardRef.value.getReportId();
  }
  if (values === false) {
    return false;
  }

  emit('ok',values,selectType,method.value)
  handleCancel();
}


defineExpose({
  open
})
</script>

<style lang="less" scoped>
.style_head{
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 8px;
  margin-bottom: 16px;
  padding: 0px 8px;
}
.style_content{
  height:500px;
  overflow-y: auto;
  overflow-x: hidden;
}

</style>
