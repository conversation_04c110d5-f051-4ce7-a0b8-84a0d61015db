import {RangeEnum, TimeEnum} from "/@/views/posture/enums/editPageEnum";

/**
 *   Last 24hour（刷新频率5min）
 *   Last 1 week（刷新频率30min）
 *   Last 30 days （刷新频率1day）
 *   Last 6 months （刷新频率1week）
 *   Last 1 year （刷新频率1month）
 */
export const FreshTime = {
  [RangeEnum.DAY]: 5,
  [RangeEnum.WEEK]: 30,
  [RangeEnum.MONTH]: 24 * 60,
  [RangeEnum.HARFYEAR]: 7 * 24 * 60,
  [RangeEnum.YEAR]: 7 * 24 * 60,
}
/**
 * 任务的刷新频率
 */
export const TaskFreshTime = {
   [TimeEnum.MIN]: 1,
  [TimeEnum.HOUR]: 60,
  [TimeEnum.DAY]: 24 * 60,
  [TimeEnum.WEEK]: 7 * 24 * 60,
  [TimeEnum.MONTH]: 30 * 24 * 60,
  [TimeEnum.YEAR]: 365 * 24 * 60,
}
//字典
export interface IDict{
  label?: string,
  value?: string|number,
}

export interface Dashboard{
  id?:string,
  socTenantId?:string,
  dashboardName?:string,
  dashboardMode?:number|string,
  dashboardPrivate?:number,
}

/**
 * 基础 module
 */
export interface IModuleBase {
  id?: string;
  //dashboard id
  dashboardId?: string;
  // name
  name?: string;
  // module key
  keyId: string;
  // width
  width?: number;
  //height
  height?: number;
  //x坐标
  x: number;
  //y坐标
  y: number;
}


/**
 * 拖拽 module
 */
export interface IDragModule extends IModuleBase {

  //draggable
  draggable?: boolean;
  //resizable
  resizable?: boolean;
  //选中样式
  selectStyleName?: string;
  //处理样式
  selectHandleName?: string;
  //是否active
  active?: boolean;
  //是否选中
  select?: boolean;
  //css层级
  zIndex?: number;

}

/**
 * module 其他变量
 */
export interface IModuleOher {
  //selected
  selected?: boolean;
  parentAble?: boolean;
  ctrl?: boolean;
}

export interface IModule extends IDragModule, IModuleOher {
  //children
  children: IModuleChart[];
  key:string;
  name:string;
  width?:number;
  height?:number;
}

export interface IModuleChartBase {
  //图形类型
  chartType: number;
  //表
  dataSource?: string;
  //分表
  logType?: string|number;
  //时间字段
  timeField?: string;

}

export interface IModuleChart extends IDragModule, IModuleOher, IModuleChartBase {
  //报表id
  reportId?: string;
  //上一级id
  parentId: string;
  //moduleId
  moduleId?: string;
  //图表快照
  chartImg?: string;
  //图表配置
  fillData?: IModuleChartFill;
  //特殊图表高度
  height?:number;
}


export interface IModuleChartFill {
  //报表id
  configData?: object;

}
//坐标轴统计项
export interface IAxisGroupItem {
  label?: string,
  fieldValue?: string,
  pureColor?: string,
  value?: number,
  str?: string,//clickhouse sql条件
  echo?: string,//clickhouse 回显sql
  type?: number,//countGroup 用"1 max 2 min,3mean,4 median"
}
//旭日图专用
export interface IFloorItem {
  label?: string,
  value?: Array<string>,
}

//时间间隔
export interface ITimeInterval {
  rangeValue?: number,
  rangeType?: number,
  value?: number,
  type?: number,
  timeField?: string
}
//坐标轴
export interface IAxis {
  dataType?: string,
  valueType?: number,
  fieldGroup?: IAxisGroupItem[],
  conditionGroup?: IAxisGroupItem[],
  countGroup?: IAxisGroupItem[],
  timeInterval?:ITimeInterval,
  floor?: IFloorItem[],//旭日图用
}

//坐标轴
export interface IDisplaySetting {
  maximum?: number,
  sortType?: string,//"1 majority first,2 mainority first,3Random",
  sortBy?: number,//"1 统计字段数组下标，-1表示汇总排序，把统计的各项值加一起排序",
  isNull?: boolean,
}

//图例
export interface ILegend{
  label?: string,
  pureColor?: string,
}
