import {DataSetting,StyleSetting,AdvancedSetting} from "/@/views/reportChart/components";
interface ITab {
  name: string,
  key: string,
  component: string,
}
/**
 * chart
 */
export const postureConfigOption = [{
  name : 'Data setting',
  component : DataSetting
},{
  name : 'Style setting',
  component : StyleSetting
},{
  name : 'Advanced setting',
  component : AdvancedSetting
}]
/**
 * chart
 */
export const postureConfigOptionNoStyle = [{
  name : 'Data setting',
  component : DataSetting
},{
  name : 'Advanced setting',
  component : AdvancedSetting
}]
