import {BGEnum, E_ColorPickerType} from "/@/views/posture/enums/styleEnum";

/**
 * 样式文件
 */



/**
 * 背景
 */
export interface BackgroundStyle {
  bgType : number;
  opacity : number;
  bgValue : string;
  colorType : string;
}

/**
 * 背景类型
 */
export const BG_OPTION = [
  {
    label: 'Picture',
    value: BGEnum.PICTURE,
  },
  {
    label: 'Color',
    value: BGEnum.COLOR,
  }
]

/**
 * 背景颜色类型
 */
export const BG_COLOR_TYPE_OPTION = [
  {
    label: 'Color',
    value: E_ColorPickerType.PURE,
  },
  {
    label: 'Range',
    value: E_ColorPickerType.GRADIENT,
  }
]
