<template>
  <div class="mt-16px">
    <div class="font14 fcolor mb-16px"> {{ tp('summaryField') }}</div>

    <div class="flex flex-row gap-8px flex-wrap" style="">
      <template v-for="(item, index) in summaryField" :key="'s_' + item">
        <div style="width: 148px; position: relative">
          <JSearchSelect
            v-model:value="item.value"
            :dict-options="fieldOptions"
            @change="summaryFieldChange($event, index)"
            :ifDisable="item.disable"
          />
          <span
            class="soc ax-com-Fault ax-icon"
            style="cursor: pointer; position: absolute; right: -8px; top: -8px"
            @click="delSummaryField(index)"
            v-if="!item.disable"
          ></span>
        </div>
      </template>
      <div class="ax-icon-button" @click="addSummaryField">
        <span class="soc ax-com-Add ax-icon"></span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { defineExpose, defineProps, nextTick, ref, watch } from 'vue';
  import JSearchSelect from '/@/components/Form/src/jeecg/components/JSearchSelect.vue';
  import { getTabFieldList } from '/@/utils/ckTable';
  import { useI18n } from '/@/hooks/web/useI18n';
  import { message } from 'ant-design-vue';

  const { t } = useI18n();

  function tp(name) {
    return t('routes.MlRuleVO.' + name);
  }
  const summaryField = ref<any>([]);
  const fieldOptions = ref<any>([]);

  const props = defineProps({
    data: Array as any,
    ifEdit: Boolean as any,
    summaryList: Array as any,
  });

  /**
   * 加载可显示摘要字段
   */
  function reloadFieldOptions(type) {
    const list: any[] = [];
    const data = getTabFieldList(type);
    const map: any = {};
    summaryField.value = [];
    for (let i = 0; i < data.length; i++) {
      if (
        data[i].fieldValue != 'ck_enter_date' &&
        data[i].fieldValue != 'enter_date' &&
        data[i].fieldValue != 'syslog' &&
        data[i].fieldValue != 'log_id' &&
        data[i].fieldValue != 'receive_millis'
      ) {
        list.push({
          label: data[i].fieldValue,
          text: data[i].fieldValue,
          value: data[i].fieldValue,
          disable: false,
        });
        map[data[i].fieldValue] = 1;
      }
    }
    fieldOptions.value = list;
    //判断之前选中的值在下拉选中是否还存在，不存在则删除
    const li: any = [];
    for (let i = 0; i < summaryField.value.length; i++) {
      if (map[summaryField.value[i].value]) {
        if (summaryField.value[i].value !== 'user_name') {
          li.push(summaryField.value[i]);
        }
      }
    }
    summaryField.value = li;
    console.log('summaryField.value00', summaryField.value);

    let fieldsArr = [] as any;
    fieldsArr = ['src_ip', 'dst_ip'];
    console.log('fieldOptions.value1', fieldOptions.value);
    if (props.ifEdit && props.summaryList.length > 0) {
      props.summaryList.forEach((e) => {
        summaryField.value.push({
          label: e,
          text: e,
          value: e,
          disable: fieldsArr.find((item) => item === e) ? true : false,
        });
      });
    } else {
      fieldsArr.forEach((field) => {
        let obj = fieldOptions.value.find((item) => item.value === field);
        let find = summaryField.value.find((item) => item.value === field);

        if (obj && !find) {
          obj.disable = true;
          summaryField.value.push(obj);
        }
      });
    }

    console.log('summaryField.value', summaryField.value);
  }

  /**
   * 添加摘要字段
   */
  function addSummaryField() {
    summaryField.value.push({
      label: '',
      text: '',
      value: '',
      disable: false,
    });
  }

  /**
   * 摘要字段改变判断
   * @param value
   * @param index
   */
  function summaryFieldChange(value, index) {
    let index_ = -1;
    summaryField.value[index].label = value;
    summaryField.value[index].text = value;
    summaryField.value[index].disable = false;
    for (let i = 0; i < summaryField.value.length; i++) {
      if (summaryField.value[i].value === value && index != i) {
        index_ = i;
        break;
      }
    }
    console.log(value, index, index_);
    //其它下拉选已经选了该字段，不能重复选中
    if (index_ > -1) {
      message.warning(value + ' already exists');
      //change 事件还没有触发v-model:value绑定值，所以写在nextTick方法里
      nextTick(() => {
        summaryField.value[index].value = '';
      });
    }
    console.log(summaryField.value);
  }

  /**
   * 摘要字段删除
   * @param index
   */
  function delSummaryField(index) {
    summaryField.value.splice(index, 1);
  }

  function getSummaryData() {
    let arr = summaryField.value.map((item) => {
      return item.value;
    });
    return JSON.parse(JSON.stringify(arr));
  }

  defineExpose({ getSummaryData, reloadFieldOptions });
</script>

<style scoped lang="less">
  .flex {
    display: flex;
    flex-flow: wrap;
  }

  .label {
    line-height: 32px;
  }

  .cursor {
    cursor: pointer;
  }

  .border {
    border: 1px solid @border-color;
  }

  .condition_title {
    display: flex;
    align-items: center;
    width: 120px;
    padding: 10px;
  }

  .content {
    padding: 10px;
    border-left: 0px !important;
    width: calc(100% - 180px);
  }

  .delete {
    display: flex;
    align-items: center;
    width: 60px;
    padding: 10px;
  }

  .border_top {
    border-top: 1px solid @border-color;
  }

  .tabs_div {
    padding: 0px 10px;
    cursor: pointer;
  }

  .active {
    background-color: @m-text-color;
  }
</style>
