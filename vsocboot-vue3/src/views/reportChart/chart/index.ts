import SChart from './index.vue'
import type {EChartsOption} from "echarts";
import {TooltipOption} from "echarts/types/dist/shared";
import {LegendComponentOption} from "echarts/types/dist/echarts";
import {IGrid} from "/@/views/reportChart/chart/components/ts/grid";

export {SChart}

export interface ChartOption extends EChartsOption {
  tooltip: TooltipOption | TooltipOption[],
  xAxis?: any;
  yAxis?: any;
  series: any,
  radar: any,
  legend?: LegendComponentOption,
  grid?: IGrid
}


