import {
  CHART_SUBCLASS_GROUP_NAME,
  CHART_SUBCLASS_VALUE_NAME
} from "/@/views/reportChart/ts/ChartType";
import {ChartNOEnum} from "/@/views/reportChart/enums/chartEnum";
import {GroupTypeEnum, SpecifyingEnum, ValueTypeEnum} from "/@/views/posture/enums/editPageEnum";

/**
 * 校验
 * @param data {
 *       chartType : chartType.value,
 *       groupAxisData:groupAxisData.value,
 *       valueAxisData:valueAxisData.value,
 *       displaySettingData:displaySettingData.value,
 *       styleSettingData:styleSettingData.value,
 *       legendOption:legendOption.value,
 *       columns : columns.value
*       }
 */
export function doValidateChartConfig(data) {
  const type = data.chartType;
  let isGroupPass = true;
  let isValuePass = true;
  let isColumnPass = true;
  //统计项
  if (CHART_SUBCLASS_GROUP_NAME[type]) {
    isGroupPass = validateGroup(data.groupAxisData, type);
  }
  //统计值
  if (CHART_SUBCLASS_VALUE_NAME[type]) {
    isValuePass = validateValue(data.valueAxisData, type);
  }
  //table 列
  if (type == ChartNOEnum.BASIC_LIST && (!data.columns || data.columns.length == 0 ||
    data.columns.filter(item => item == '').length > 0)) {
    isColumnPass = false;
  }
  // console.log('validate result=>', {isGroupPass, isValuePass, isColumnPass})
  return isGroupPass && isValuePass && isColumnPass;
}

/**
 * dataType：1 Field statistics（Deduplication,2Field value statistics,3Log volume ,4Field value
 * @param data
 */
export function validateValue(data, chartType) {
  // console.log('validateValue')
  // console.log(data, chartType)
  // console.log('validateValue')
  if (!data) {
    return false
  }
  const dataType = data.dataType;
  const valueType = data.valueType;
  let isPass = true;
  if (chartType == ChartNOEnum.DONUT) {//Sunburst
    if (data.floor.length == 0
      || data.floor.filter(item => !item.name).length > 0
      || data.floor.filter(item => item.value.length == 0).length > 0
    ) {
      isPass = false;
    }
  } else if (dataType == ValueTypeEnum.FIELD &&
    (!data.fieldGroup || data.fieldGroup.length == 0 ||
      data.fieldGroup.filter(item => !item.fieldValue || JSON.stringify(item) == '{}').length > 0)) {
    isPass = false;
  } else if (dataType == ValueTypeEnum.DESIGNATED) {

    if(valueType == 1 && (!data.conditionGroup || data.conditionGroup.length == 0 ||
      data.conditionGroup.filter(item => !item.str || JSON.stringify(item) == '{}').length > 0)){
      isPass = false;
    } else if(valueType == 2 && (!data.conditionGroup || data.conditionGroup.length == 0 ||
      data.conditionGroup.filter(item => !item.name || JSON.stringify(item) == '{}').length > 0)){
      isPass = false;
    }

  } else if (dataType == ValueTypeEnum.FIELDVALUE &&
    (!data.countGroup || data.countGroup.length == 0 ||
      data.countGroup.filter(item => !item.fieldValue || JSON.stringify(item) == '{}').length > 0)) {
    isPass = false;
  }
  return isPass;
}

/**
 * dataType : 1 field 2 field(designated value) 3 Time
 * @param data
 */
function validateGroup(data, chartType) {
  if (!data) {
    return false
  }
  const dataType = data.dataType;
  let isPass = true;
  if (dataType == GroupTypeEnum.FIELD &&
    (!data.fieldGroup || data.fieldGroup.length == 0 ||
      data.fieldGroup.filter(item => !item.fieldValue || JSON.stringify(item) == '{}').length > 0)) {
    isPass = false;
  } else if (dataType == GroupTypeEnum.DESIGNATED &&
    (!data.conditionGroup || data.conditionGroup.length == 0 ||
      data.conditionGroup.filter(item => !item.str || JSON.stringify(item) == '{}').length > 0)) {
    isPass = false;
  }

  return isPass;
}


