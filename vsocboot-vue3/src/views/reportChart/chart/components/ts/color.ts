import {THEME_CHART_COLOR} from "/@/views/reportChart/ts/chartColor";
import {IPieces, IThresholdColor} from "/@/views/reportChart/ts/IModule";

export function getColor(styleConfig, theme) {
  // console.log('configData.styleSettingData',styleConfig)
  const color = styleConfig?.colors || [];
  // console.log('getColor',color)
  if (color.length > 0) {
    return color
  } else {
    return THEME_CHART_COLOR[theme]
  }
}

/**
 * threshold color
 * @param styleConfig
 */
export function getPiecesColor(styleConfig) {

  const min = styleConfig.min as IThresholdColor;
  const max = styleConfig.max as IThresholdColor;
  const thresholdColors = styleConfig.thresholdColors as IThresholdColor[];
  const pieces = [] as IPieces[];
  if (min) {//(-Infinity,min]
    pieces.push({lt: min.start, color: min.pureColor});
  }
  //中间范围（item.start,item.end]
  thresholdColors.forEach(item=>{
    pieces.push({gte: item.start,lte:item.end, color: item.pureColor});
  })
  if (max) {//(max, Infinity]
    pieces.push({gt: max.end, color: max.pureColor});
  }
  return {
    type: "piecewise",
    show: false,
    pieces: pieces,
  };
}
