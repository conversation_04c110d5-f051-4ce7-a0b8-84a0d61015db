import {legendlColor} from "/@/views/reportChart/chart/components/ts/common";

export const BAR_LEGEND = {
  type: 'scroll',
  show: true,
  orient: 'horizontal',
  itemWidth:8,
  itemHeight:8,
  width: '100%',
  bottom: 0,
  itemGap:40,
  pageTextStyle: {
    color: legendlColor
  },
  data: [] as string[],
  formatter: function (name) {
    return name;
  },
  tooltip: {
    show: false
  }
};

export const COLUMN_LEGEND = {
  type: 'scroll',
  show: true,
  orient: 'vertical',
  right: 10,
  top: 20,
  bottom: 20,
  itemWidth:8,
  itemHeight:8,

  data: [],
  formatter: function (name) {
    return name;
  },
  tooltip: {
    show: false
  }
};

export const DEFAULT_LEGEND = {
  type: 'scroll',
  show: true,
  orient: 'horizontal',
  itemWidth:8,
  itemHeight:8,
  width: '100%',
  bottom: -4,
  itemGap:40,
  pageTextStyle: {
    color: legendlColor
  },
  data: [] as string[],
  formatter: function (name) {
    return name;
  },
  tooltip: {
    show: false
  }
};

export const NO_LEGEND = {
  show: false,
};
