import dayjs from "dayjs";

/**
 * 日期显示格式
 */
export enum E_TIME_FORMAT {
  'Y-M-D H:M:S' = "YYYY-MM-DD HH:mm:ss",
  'Y-M-D H:M' = "YYYY-MM-DD HH:mm",
  'Y-M-D' =  "YYYY-MM-DD",
  'D H' = "DD HH",
  'H:M:S' = "HH:mm:ss",
  'H:M' = "HH:mm",
  'Y/M/D H:M:S' = "YYYY/MM-DD HH:mm:ss",
  'Y.M.D H:M:S' = "YYYY.MM.DD HH:mm:ss",

}

export const timeFormatOption = [
  {label: '2024-01-01 12:00:00', value: E_TIME_FORMAT["Y-M-D H:M:S"]},
  {label: '2024-01-01 12:00', value: E_TIME_FORMAT["Y-M-D H:M"]},
  {label: '2024-01-01', value: E_TIME_FORMAT["Y-M-D"]},
  {label: '12:00:00', value: E_TIME_FORMAT["H:M:S"]},
  {label: '01 00', value: E_TIME_FORMAT["D H"]},
  {label: '00:00', value: E_TIME_FORMAT["H:M"]},
  {label: '2024/01/01 12:00:00', value: E_TIME_FORMAT["Y/M/D H:M:S"]},
  {label: '2024.01.01 12:00:00', value: E_TIME_FORMAT["Y.M.D H:M:S"]},
];

/**
 * bug:只要datestring是数字，就会解析成时间，校验通过
 * @param dateString
 */
function isValidDate(dateString: string): boolean {
  return dayjs(dateString).isValid();
}
export const getTimeLabel = (formatStr:string,labelData:string[])=>{
  const list:string[] = [];
  labelData.forEach(item => {
    if(isValidDate(item)){
      try {
        list.push(dayjs(item as string).format(formatStr))
      } catch (e) {
        list.push(item)
      }
    }else{
      list.push(item)
    }
  })
  return list;
}
