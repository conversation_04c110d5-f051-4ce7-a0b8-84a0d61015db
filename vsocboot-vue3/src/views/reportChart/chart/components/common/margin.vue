<template>
  <div class="data-setting-row">
    <div class="data-setting-col gap4 flex1">
      <div class="style-title-font13"> Margin Up</div>
      <a-input-number v-model:value="styleData.top"/>
    </div>
    <div class="data-setting-col gap4 flex1">
      <div class="style-title-font13"> Margin Down</div>
      <a-input-number v-model:value="styleData.bottom"/>
    </div>
    <div class="data-setting-col gap4 flex1">
      <div class="style-title-font13"> Margin Right</div>
      <a-input-number v-model:value="styleData.right"/>
    </div>
    <div class="data-setting-col gap4 flex1">
      <div class="style-title-font13"> Margin Left</div>
      <a-input-number v-model:value="styleData.left"/>
    </div>
  </div>
</template>
<script setup lang="ts">
import {ref, watch} from "vue";
// Emits声明
const emit = defineEmits(['update:value']);
const styleData = ref();
const props = defineProps({
  value:{
    type:Object,
    default:{
      top:4,
      left:4,
      bottom:4,
      right:4
    }
  }
})
watch(()=>props.value,(n)=>{
  styleData.value = n;
},{deep:true,immediate:true})

watch(()=>styleData.value,(n)=>{
  emit('update:value',n)
},{deep:true})
</script>
<style scoped lang="less">
@import "../../../less/common.less";
</style>
