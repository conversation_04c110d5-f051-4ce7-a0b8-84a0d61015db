<template>
  <div class="data-setting-col gap16">
    <!-- show base style start -->
    <div class="data-setting-col gap4">
      <div class="style-title-font14">Baisc Style</div>
      <a-select :options="barTypeOption" v-model:value="chartStyle.baseStyle"  />
    </div>
    <!-- show base style end -->

    <!-- show legend start -->
    <div class="data-setting-row item-center height32">
      <a-switch v-model:checked="chartStyle.legend" size="small"/>
      <div class="style-title-font13">Show legend</div>
    </div>
    <!-- show legend end -->

    <!-- show top border start -->
    <div class="data-setting-row item-center height32">
      <a-switch v-model:checked="chartStyle.topBorder" size="small"/>
      <div class="style-title-font13">Show Top Border</div>
    </div>
    <!-- show top border end -->

    <!-- show bar width start -->
    <BarWidth v-model:value="chartStyle"/>
    <!-- show bar width end -->

    <!-- show axis start -->
    <ShowX v-model:value="chartStyle"/>
    <ShowY v-model:value="chartStyle"/>
    <!-- show axis end -->

    <!-- show number start -->
    <ShowNumber v-model:value="chartStyle"/>
    <!-- show number end -->

    <!-- rotate start -->
    <Rotate  v-model:value="chartStyle"/>
    <!-- rotate end -->

    <!-- unit format label start -->
    <UnitFormat v-model:value="chartStyle"/>
    <!-- unit format end -->

    <!-- time label start -->
    <TimeLabel v-model:value="chartStyle"/>
    <!-- time label end -->

    <!-- margin start -->
    <Margin  v-model:value="chartStyle.marginStyle"/>
    <!-- margin end -->
  </div>
</template>
<script setup lang="ts">
import {inject, ref, watch, watchEffect} from "vue";
import {
  DEFAULT_OPTION,
  E_BAR_SUBTYPE
} from "/@/views/reportChart/chart/components/barstack/barUtils";
import {IModuleData} from "/@/views/reportChart/ts/IModule";
import {
  BarWidth,
  Margin,
  Rotate,
  ShowNumber,
  ShowX,
  ShowY, TimeLabel, UnitFormat
} from "/@/views/reportChart/chart/components/common";

const moduleData = inject('module',ref<IModuleData>({}));
const barTypeOption = ref([{
  label:'Bar',
  value:E_BAR_SUBTYPE.BAR
},{
  label:'Column',
  value:E_BAR_SUBTYPE.COLUMN
}])
const chartStyle = ref(JSON.parse(JSON.stringify(DEFAULT_OPTION)));
watchEffect(()=>{
  if( moduleData.value.chartStyle && JSON.stringify( moduleData.value.chartStyle) != '{}'){
    chartStyle.value = moduleData.value.chartStyle;
  }
})
watch(()=>chartStyle.value,(n)=>{
  moduleData.value.chartStyle = n;

},{deep:true,immediate:true})


</script>
<style scoped lang="less">
@import "../../../less/common.less";

</style>
