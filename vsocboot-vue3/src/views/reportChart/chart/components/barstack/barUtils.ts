import {IChartStyle} from "/@/views/reportChart/ts/IModule";
import {
  defaultUnitFormat,
  E_BAR_WIDTH_TYPE,
  gridConfig,
  rotateNumber
} from "/@/views/reportChart/chart/components/ts/common";
import {E_NUMBER_TEXT_ALIGN} from "/@/views/reportChart/chart/components/number/numberUtils";
import {E_TIME_FORMAT} from "/@/views/reportChart/chart/components/ts/time";
import {E_UNIT_TIME} from "/@/views/reportChart/enums/timeEnum";

/**
 * 图类型：默认
 */
export enum E_BAR_SUBTYPE {
  COLUMN = 2,//
  BAR = 1,//
}


/**
 * 默认配置项
 */
export const DEFAULT_OPTION = {
  legend: true,///有legend
  number: true,//显示数值
  numberAlign: E_NUMBER_TEXT_ALIGN.INSIDE,//数值显示位置
  showX: true,//显示x轴
  showXLine: true,//显示x轴线
  showY: true,//显示y轴
  showYLine: true,//显示y轴线
  topBorder: false,//显示圆弧
  rotate: false,//旋转
  rotateValue: rotateNumber,
  baseStyle: E_BAR_SUBTYPE.BAR,//默认是柱状
  showTimeFormat:false,
  format: E_TIME_FORMAT["Y-M-D H:M:S"],
  marginStyle: JSON.parse(JSON.stringify(gridConfig)),
  barWidthUnit: E_BAR_WIDTH_TYPE.PX,
  barWidthValue : 40,
  showBarWidth:false,
  showUnitConversion:false,
  unitFormat:defaultUnitFormat,
} as IChartStyle


