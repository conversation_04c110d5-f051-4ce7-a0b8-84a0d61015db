<template>
  <div class="num-chart">
    <!-- 图标 start-->
    <span
      :class="ICON[numData.icon ?? '']"
      class="num_icon2"
      :style="{'color':numData.color}"></span>
    <!-- 图标 end-->
    <div class="num-chart_content">
      <!-- number value start-->
      <div
        class="num_count"
        :style="{'font-size':numData.style.fontSize + 'px','line-height':numData.style.fontSize + 'px','text-align': numData.style.textAlign}">
        {{ numData.count }}</div>
      <!-- number value end -->

      <!-- number rate start-->
      <div class="num_rate ft16" :style="{'color':numData.style.color}">
        <img :src="ICON_IMG.rate" alt=""/>
        {{ numData.rate }}%
      </div>
      <!-- number rate end-->
    </div>
  </div>
</template>
<script setup lang="ts">

import {
  DEFAULT_OPTION,
  ICON,
  ICON_IMG
} from "/@/views/reportChart/chart/components/number/numberUtils";
import {ref, watch} from "vue";
const numData = ref();
const chartStyle = ref(JSON.parse(JSON.stringify(DEFAULT_OPTION)));
const props = defineProps({
  value:Object
});
watch(() => props.value, (n) => {

  numData.value = n;
  if (n && !n?.style) {
    numData.value.style = chartStyle.value.style;
  }
  if(n?.rate == 0){
    numData.value.style.color = numData.value.style.equal;
  } else if(n?.rate < 0){
    numData.value.style.color = numData.value.style.decrease;
  }else if(n?.rate > 0){
    numData.value.style.color = numData.value.style.increase;
  }
  console.log('numData.value',numData.value)
}, {deep: true, immediate: true})
</script>
<style scoped lang="less">
.num-chart{
  margin: 16px 20px 12px 20px;
  display: flex;
  flex-direction: row;
  width: 100%;
  height:68px;
  gap: 20px;
  align-items: center;
  .num-chart_content{
    display: flex;
    flex-direction: column;
    flex: 1;
    gap: 8px;
    .num_count{
      font-weight: bold;
    }
  }
}
.num_icon2 {
  width: 48px;
  height: 48px;
  line-height: 1;
  font-size: 48px;
}

.num_rate {
  color: @critical-color;
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 4px;
  img {
    width: 16px;
    height: 16px;
  }
}
</style>
