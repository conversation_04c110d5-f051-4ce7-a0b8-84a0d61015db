<template>
  <div class="data-setting-col gap16">



    <!-- show legend start -->
    <div class="data-setting-row item-center height32">
      <a-switch v-model:checked="chartStyle.legend" size="small"/>
      <div class="style-title-font13">Show legend</div>
    </div>
    <!-- show legend end -->

    <!-- show label start -->
    <div class="data-setting-row item-center height32">
      <a-switch v-model:checked="chartStyle.number" size="small"/>
      <div class="style-title-font13">Show Numerical Values</div>
    </div>
    <!-- show label end -->

    <!-- show Label start -->
    <div class="data-setting-row item-center height32">
        <a-switch v-model:checked="chartStyle.labelShow" size="small"/>
        <div class="style-title-font13">Show Label Name</div>
    </div>

    <!-- unit format label start -->
    <UnitFormat v-model:value="chartStyle"/>
    <!-- unit format end -->

    <!-- show Label end -->
    <div class="data-setting-col height32">
      <div class="style-title-font14">Area Conversion</div>
      <a-select :options="areaOption" v-model:value="chartStyle.areaStyle"/>
    </div>



  </div>
</template>
<script setup lang="ts">
import {inject, ref, watch, watchEffect} from "vue";
import {IModuleData} from "/@/views/reportChart/ts/IModule";
import {
  DEFAULT_OPTION,
  E_RADAR_SUBTYPE
} from "/@/views/reportChart/chart/components/radar/radarUtils";
import {UnitFormat} from "/@/views/reportChart/chart/components/common";
const moduleData = inject('module',ref<IModuleData>({}));
const areaOption = ref([{
  label: 'None',
  value: E_RADAR_SUBTYPE.AREA_NONE
}, {
  label: 'Translucent Area',
  value: E_RADAR_SUBTYPE.AREA_TRANSLUCENT
}, {
  label: 'Area',
  value: E_RADAR_SUBTYPE.AREA
}])
const chartStyle = ref(JSON.parse(JSON.stringify(DEFAULT_OPTION)));
watchEffect(()=>{
  if( moduleData.value.chartStyle && JSON.stringify( moduleData.value.chartStyle) != '{}'){
    chartStyle.value = moduleData.value.chartStyle;
  }
})
watch(()=>chartStyle.value,(n)=>{
  moduleData.value.chartStyle = n;

},{deep:true,immediate:true})


</script>
<style scoped lang="less">
@import "../../../less/common.less";

</style>
