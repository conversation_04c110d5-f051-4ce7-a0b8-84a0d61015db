import {ChartOption} from "/@/views/reportChart/chart/index";
import {IChartTypeShow} from "/@/views/reportChart/ts/chartResult";
import {TooltipOption} from "echarts/types/dist/shared";
import {animationDuration} from "/@/views/reportChart/chart/components/ts/common";
import {getColor} from "/@/views/reportChart/chart/components/ts/color";
import {CHART_TOOLTIP_ITEM} from "/@/views/reportChart/chart/components/ts/tooltip";
import {
  E_RADAR_SUBTYPE,
  getAreaStyle
} from "/@/views/reportChart/chart/components/radar/radarUtils";
import {getUnitConvarsionValue} from "/@/views/reportChart/chart/chartUtils";


/**
 * 获取option
 * 坐标轴“字符串”的高度和宽度都是echart自动计算，无需再改变grid 的left 和 bottom
 * @param param
 */
export function getRadarChartOption(param: IChartTypeShow) {
  const {data, configData, theme, chartStyle} = param;
  const { showUnitConversion, unitFormat,legend, number, areaStyle, labelShow} = chartStyle
  //数据处理
  const legendData: string[] = [];

  const option: ChartOption = {
    animationDuration: animationDuration,//动画执行时间
    tooltip: CHART_TOOLTIP_ITEM as TooltipOption,
    radar: {},
    series: [{
      type: 'radar',
      emphasis: {
        lineStyle: {
          width: 4
        }
      },
      data: []
    }],
  }

  //chart color
  const {color} = getColor(configData, theme)
  option.color = color;

  //数据处理
  option.radar = JSON.parse(JSON.stringify(data.radar));
  console.log('radar data.radar', data.radar)
  const seriesData: any = data.seriesDatas ? JSON.parse(JSON.stringify(data.seriesDatas[0].data)) : [];

  for (const i in seriesData) {
    legendData.push(seriesData[i]?.name as string);
    //区域图
    if (areaStyle != E_RADAR_SUBTYPE.AREA_NONE) {
      seriesData[i].areaStyle = {opacity: getAreaStyle(areaStyle as number)};
    }else{
      delete  seriesData[i]['areaStyle']
    }
    //是否显示值
    seriesData[i].label = {
      show: !!number
    }
    // 时间单位转换
    if(showUnitConversion){
      seriesData[i].value = seriesData[i].value.map(item=>{
        if(item > 0){
          return getUnitConvarsionValue(item, unitFormat)
        }
        return item;
      })
    }
  }
  // 时间单位转换
  if(showUnitConversion) {
    option.radar.indicator = option.radar.indicator.map(item => {
      item.max = getUnitConvarsionValue(item.max, unitFormat)
      return item;
    })
  }

  //是否显示名称
  option.radar.axisName = {
    show: !!labelShow,
  }
  //图例
  option.legend = {
    show: false,
    data: legendData,
    orient: "vertical",
    right: "10",
    top: "10",
  }

  if (legend) {
    option.legend.show = true;
    option.radar.center = ["35%", "60%"];
  }else{
    option.radar.center = ["50%", "60%"];
  }
  option.series[0].data = seriesData;

  return option;
}
