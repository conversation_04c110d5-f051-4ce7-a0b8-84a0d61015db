import {IChartStyle} from "/@/views/reportChart/ts/IModule";
import {E_UNIT_TIME} from "/@/views/reportChart/enums/timeEnum";
import {defaultUnitFormat} from "/@/views/reportChart/chart/components/ts/common";

/**
 * 图类型：默认
 */
export enum E_RADAR_SUBTYPE {
  AREA_NONE = 1,//区域默认
  AREA_TRANSLUCENT = 2,//区域浅色
  AREA = 3,//区域深色
}


/**
 * 默认配置项
 */
export const DEFAULT_OPTION = {
  legend: false,///有legend
  number: false,//显示数值
  labelShow: true,//显示名称
  areaStyle: E_RADAR_SUBTYPE.AREA_NONE,
  showUnitConversion: false,
  unitFormat: defaultUnitFormat,
} as IChartStyle

//浅色area
const areaStyleLight = 0.1;
//深色area
const areaStyleDark = 0.7;

export function getAreaStyle(styleNo: number) {
  if (E_RADAR_SUBTYPE.AREA_TRANSLUCENT == styleNo) {
    return areaStyleLight;
  } else if (E_RADAR_SUBTYPE.AREA == styleNo) {
    return areaStyleDark;
  } else {
    return 0;
  }
}
