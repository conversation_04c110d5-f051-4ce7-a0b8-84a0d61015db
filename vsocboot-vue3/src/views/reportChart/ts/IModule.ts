import {RangeEnum} from "/@/views/posture/enums/editPageEnum";

export interface IBaseData {
  //查询范围
  rangeType?: number | string;
  //查询范围
  rangeValue?: number | string;
  //时间字段
  timeField?: string;
  //统计类型
  statisticalType?: number;
  //租户
  socTenantIds?: string;
}

//坐标轴统计项
export interface IAxisGroupItem {
  label?: string;
  fieldValue?: string;
  name?: string;
  pureColor?: string;
  value?: number;
  str?: string;//clickhouse sql条件
  echo?: string;//clickhouse 回显sql
  type?: number;//countGroup 用"1 max 2 min;3mean;4 median"
  advancedQueryFlag?: boolean;//是否是高级查询
}

//系统值
export interface ISystemDataItem {
  fieldValue?: string;
  systemValue?: string;
  label?: string;//显示名称
  echo?: string;//回显sql
  str?:string;//查询条件
}

//旭日图专用
export interface IFloorItem {
  label?: string;
  value?: Array<string>;
}

//坐标轴
export interface IAxis {
  dataType?: string;
  valueType?: number;
  fieldGroup?: IAxisGroupItem[];
  conditionGroup?: IAxisGroupItem[];
  systemData?: ISystemDataItem;
  countGroup?: IAxisGroupItem[];
  timeInterval?: ITimeInterval;
  floor?: IFloorItem[];//旭日图用
}

//时间间隔
export interface ITimeInterval {
  rangeValue?: number;
  rangeType?: number;
  value?: number;
  type?: number;
  timeField?: string
}

//展示样式
export interface IDisplaySetting {
  maximum?: number;
  sortType?: string;//"1 majority first;2 mainority first;3Random";
  sortBy?: number;//"1 统计字段数组下标，-1表示汇总排序，把统计的各项值加一起排序";
  isNull?: boolean;
}

export interface IChartData {
  baseInfo?: IBaseData;
  //图形类型
  chartType: number;
  chartStyleNo?: number;
  //表
  datasource?: string;
  //分表
  logType?: string;
  //租户ID
  socTenantId?: string;
  //图表配置
  xAxis?: IAxis;
  yAxis?: IAxis;
  columns?: string[];
  //样式配置
  displaySetting?: IDisplaySetting,
  searchInfo?: string,
  searchVal: string
}

export interface IConfigData {
  chartStyleNo?: number | string;
  chartStyle?: IChartStyle;
  chartType?: number;
  groupAxisData?: IAxis;
  valueAxisData?: IAxis;
  displaySettingData?: IDisplaySetting;
  styleSettingData?: IStyle;
  legendOption?: IOptionItem;
  columns?: Array<string>;
  advancedSettingData?: IAdvanced
}


export interface IModuleChartFill {
  //报表id
  configData?: IConfigData;
  chartData?: IChartData;

}

export interface IFieldOption {
  classFieldName?: string;
  label?: string;
  value?: string;
  type?: string;

}

export interface IThresholdColor {
  start?: number;
  end?: number;
  pureColor?: string;
}

export interface IPieces {
  lte?: number;
  gt?: number;
  gte?: number;
  lt?: number;
  color?: string;
}

export interface IStyle {
  type: string;
  min?: number | IThresholdColor;
  max?: number | IThresholdColor
  colors?: string[];
  thresholdColors?: IThresholdColor[];
}

export interface IOptionItem {
  label?: string;
  value?: string | number;
}

export interface IAdvanced {
  executionFrequencyType: number;
  refreshTimeType: number;
  refreshTime: number;
  statisticalRange: number;
  statisticalRangeType: number;
  type: number;
  executionFrequency: number;
  firstExecutionTime?: string;
  timeInternal: {
    [RangeEnum.DAY]: { type: number; value: number };
    [RangeEnum.WEEK]: { type: number; value: number };
    [RangeEnum.MONTH]: { type: number; value: number };
    [RangeEnum.HARFYEAR]: { type: number; value: number };
    [RangeEnum.YEAR]: { type: number; value: number }
  }
}

export interface IMarginStyle {
  top: number;
  bottom: number;
  left: number;
  right: number;
}

export interface IChartStyle {
  legend?: boolean;
  number?: boolean;
  numberAlign?: string;
  showTimeFormat?: boolean;
  showBarWidth?: boolean;
  barWidthUnit?: string;
  barWidthValue?: number;
  showX?: boolean;
  showXLine?: boolean;
  showY?: boolean;
  showYLine?: boolean;
  topBorder?: boolean;
  rotate?: boolean;
  rotateValue?: number;
  baseStyle?: number;
  labelLine?: boolean;
  labelPosition?: string;
  labelShow?: boolean;
  color?: string;
  areaStyle?: number,
  format?: string
  border?: string;
  headerColor?: boolean;
  showUnitConversion?: boolean;
  unitFormat?: number;
  marginStyle?: IMarginStyle;
  radius?: number[];
}

export interface IModuleData {
  //图形类型
  chartType?: number;
  //图表来源
  chartFrom?: number;
  //图表样式
  chartStyleNo?: number;
  //表
  dataSource?: string;
  //分表
  logType?: string|number;
  //dashboardName
  name?: string;
  //配置
  fillData?: IModuleChartFill;
  //日志查询字符串
  searchVal?: string;
  //其他表高级查询{whereList:[]}
  searchInfo?: string;
  systemFilter?: string;
  //是否是高级查询：true 是
  advancedQueryFlag?: boolean;
  //field字段
  fieldDataOption?: IFieldOption[];
  //number字段
  numberDataOption?: IOptionItem[];
  //时间字段
  timeFieldOption?: IOptionItem[];
  //字段数据
  yData?: IAxis;
  xData?: IAxis;
  //table 列
  column?: Array<string>;
  //排序等设置
  displaySetting?: IDisplaySetting;
  //Y轴为时间
  timeInterval?: ITimeInterval;
  //图例
  legend?: Array<string>;
  styleSetting?: IStyle;
  //图表
  colors?: Array<string>;
  //高级设置
  advancedSetting?: IAdvanced;
  //图例
  legendOption?: IOptionItem[];
  baseInfo?: IBaseData;
  chartStyle?: IChartStyle;
  keyId?: string;
  timeFieldToUse?:string;

}


//时间间隔
export interface ITimeInterval {
  rangeValue?: number;
  rangeType?: number;
  value?: number;
  type?: number;
  timeField?: string
}


//图例
export interface ILegend {
  label?: string;
  pureColor?: string;
}

//table column
export interface IGridColumn {
  title: string;
  dataIndex: string;
  ellipsis: boolean;
  width?: number;
}


//number
export interface INum {
  count: number;
  rate: number;
  icon: string;
  color: string;
  style: any;
}


//grid
export interface ITable {
  dataSource: any;
  columnData: IGridColumn[];
}
