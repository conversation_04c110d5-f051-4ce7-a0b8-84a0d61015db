import {E_TIME} from "/@/views/reportChart/enums/timeEnum";
import {useI18n} from "/@/hooks/web/useI18n";
const {t} = useI18n();

/**
 * 时间LABEL
 */
export const TIME_OPTIN_LABEL = {
  [E_TIME.MIN] : t('common.Min'),
  [E_TIME.HOUR] : t('common.Hour'),
  [E_TIME.DAY] : t('common.Day'),
  [E_TIME.WEEK] : t('common.Week'),
  [E_TIME.MONTH] : t('common.Month'),
  [E_TIME.YEAR] : t('common.Year'),

}



/**
 * 时间间隔对应选项
 */
export const INTERVAL_SELECT_MAP = {
  [E_TIME.HOUR]: [E_TIME.MIN],
  [E_TIME.DAY]: [E_TIME.MIN,E_TIME.HOUR],
  [E_TIME.WEEK]: [E_TIME.HOUR,E_TIME.DAY],
  [E_TIME.MONTH]: [E_TIME.HOUR,E_TIME.DAY,E_TIME.WEEK],
  [E_TIME.YEAR]: [E_TIME.DAY,E_TIME.WEEK,E_TIME.MONTH],
  [E_TIME.HALFYEAR]: [E_TIME.DAY,E_TIME.WEEK,E_TIME.MONTH],
  [E_TIME.NO_HOUR]: [E_TIME.DAY,E_TIME.WEEK,E_TIME.MONTH,E_TIME.YEAR],
  [E_TIME.NO_MINUTE]: [E_TIME.HOUR,E_TIME.DAY,E_TIME.WEEK,E_TIME.MONTH,E_TIME.YEAR],
  [E_TIME.ALL]: [E_TIME.MIN,E_TIME.HOUR,E_TIME.DAY,E_TIME.WEEK,E_TIME.MONTH,E_TIME.YEAR],
  [E_TIME.CRON]: [E_TIME.MIN,E_TIME.HOUR,E_TIME.DAY,E_TIME.WEEK,E_TIME.MONTH],
};


/**
 * 时间间隔默认值
 */
export const INTERVAL_DEFAULT_TYPE = {
  [E_TIME.HOUR]: E_TIME.MIN,
  [E_TIME.DAY]: E_TIME.MIN,
  [E_TIME.WEEK]: E_TIME.HOUR,
  [E_TIME.MONTH]: E_TIME.DAY,
  [E_TIME.YEAR]: E_TIME.MONTH,
  [E_TIME.NO_HOUR]: E_TIME.DAY,
  [E_TIME.NO_MINUTE]: E_TIME.HOUR,
  [E_TIME.HALFYEAR]: E_TIME.DAY,
  [E_TIME.ALL]: E_TIME.MIN,
  [E_TIME.CRON]: E_TIME.HOUR,
};

/**
 * CRON表达式每项的最大值
 */
export const CRON_MAX = {
  [E_TIME.MIN]: 59,
  [E_TIME.HOUR]: 23,
  [E_TIME.DAY]: 31,
  [E_TIME.WEEK]: 7,
  [E_TIME.MONTH]: 12,
};
/**
 * 时间LABEL
 */
export const TIME_OPTIN_MAP = {
  [E_TIME.MIN] : '',
  [E_TIME.HOUR] : t('common.Hour'),
  [E_TIME.DAY] : t('common.Day'),
  [E_TIME.WEEK] : t('common.Week'),
  [E_TIME.MONTH] : t('common.Month'),
  [E_TIME.YEAR] : t('common.Year'),

}
