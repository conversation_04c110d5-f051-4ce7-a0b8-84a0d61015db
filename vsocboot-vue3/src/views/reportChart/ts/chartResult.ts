import {IChartStyle, IConfigData} from "/@/views/reportChart/ts/IModule";

export interface ISeriesData{
  name?:string | number;
  value?:string|number;
}


export interface ISeries{
  data:ISeriesData[];
  type:string;
}

export interface IChartResult {
  seriesDatas?:ISeries[];
  xaxisData?: any;
  yaxisData?: any;
  radiusAxis?: any;
  radar?: any;
  visualMap?: any;
  calendar?: any;
  dataList?: any;
  data?:any;
}

export interface IChartTypeShow{
  data:IChartResult,
  configData:IConfigData,
  theme:string|number,
  chartType:string|number,
  chartStyleNo:string|number
  chartStyle:IChartStyle
}
