.data-setting-row{
  display: flex;
  flex-direction: row;
  gap: 8px;
  box-sizing: border-box;
  button[role='switch']{
    top: 0!important;
  }
}
.data-setting-col{
  display: flex;
  flex-direction: column;
  gap: 8px;
}
.flex1{
  flex: 1;
}
.flex2{
  flex: 2;
}
.flex3{
  flex: 3;
}
.flex4{
  flex: 4;
}
.border-bottom{
  border-bottom: 1px solid @border-color;
}
.item-center{
  align-items: center;
}
.space-between{
  justify-content: space-between;
}
.pb-16{
  padding-bottom: 16px;
}
.pl-32{
  padding-left: 32px;
}
.pl-16{
  padding-left: 16px;
}
.pr-16{
  padding-right: 16px;
}
.padding16{
  padding: 16px;
}
.gap16{
  gap: 16px;
}
.gap12{
  gap: 12px;
}
.gap8{
  gap: 8px;
}
.gap4{
  gap: 4px;
}
.setting-content_delNo{
  display: flex;
  flex-direction: row;
  width: 50px;
  align-items: center;
  justify-content: space-between;
  &:hover{
    .delIcon{
      color: #F75555;
    }
  }
}

.addBtn {
  border: 0!important;
 /deep/button > span{

    font-size: 12px;
    font-weight: normal;
    line-height: 16px;
    color: @font-color-1;
  }
}

.pr{
  position: relative;
}

.pt-8{
  padding-top: 8px;
}
.col-label{
  color: @font-color-default;
}
.height32{
  height: 32px;
}
.text-center{
  text-align: center;
}
.chart-select-style_wrapper{
  .style-title-font13{

    font-size: 13px;
    font-weight: 600;
    line-height: 24px;
    color: rgba(255, 255, 255, 0.8);
  }
  .style-title-font14{

    font-size: 14px;
    font-weight: 600;
    line-height: 24px;
    color: rgba(255, 255, 255, 0.8);
  }
}

#sortColumn{
  .drag{
    display: flex;
    flex-direction: column;
    gap: 8px;
  }
}
/deep/.ant-input{
  height: 32px!important;
}
