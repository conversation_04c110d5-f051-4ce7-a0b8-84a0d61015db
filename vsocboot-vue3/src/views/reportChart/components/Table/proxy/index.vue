<template>
  <div class="overflow-y-auto h-[100%]  ">
    <BasicTable @register="registerTable" :rowSelection="rowSelection as any">
      <template #userInfo="{ text }">
        <img v-if="!!text" :src="render.renderUploadImageSrc(text)" class="tenantImage"/>
      </template>
    </BasicTable>
  </div>


</template>
<script lang="ts" name="system-tenant" setup>
import {BasicTable} from '/@/components/Table';
import {getProxyList} from './proxy.api';
import {columns, searchFormSchema} from './proxy.data';
import {useListPage} from '/@/hooks/system/useListPage';
import {render} from '/@/utils/common/renderUtils';
import {TABLE_CACHE_KEY} from '/@/utils/valueEnum';


// 列表页面公共参数、方法
const {tableContext} = useListPage({
  designScope: 'tenant-template',
  tableProps: {
    api: getProxyList,
    columns: columns,
    title: '1',
    formConfig: {
      schemas: searchFormSchema,
      fieldMapToTime: [['fieldTime', ['beginDate', 'endDate'], 'YYYY-MM-DD HH:mm:ss']],
    },
    showActionColumn: false,
    tableSetting: {
      cacheKey: TABLE_CACHE_KEY.systemtenant,
    },
  },

});
const [registerTable, {reload}, {rowSelection, selectedRows}] = tableContext;

function getChecked(){
  if(!selectedRows ||  selectedRows.value.length == 0){
    return [];
  }
  const ipv4Array = [];
  selectedRows.value.forEach(item=>{
    if(item.proxyIp){
      ipv4Array.push(item.proxyIp);
    }
  })
  return ipv4Array;
}

defineExpose({
  getChecked
})


</script>
<style scoped lang="less">
:deep(.ant-table-body) {
  overflow-y: hidden !important;
}

.tenantImage {
  width: 25px;
  height: 25px;
  border-radius: 6px;
  image-rendering: -moz-crisp-edges;
  image-rendering: -o-crisp-edges;
  image-rendering: crisp-edges;
  -ms-interpolation-mode: nearest-neighbor;
}
/deep/.searchForm{
  padding-left: 16px!important;
}
</style>
