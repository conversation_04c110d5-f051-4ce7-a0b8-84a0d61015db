import {BasicColumn, FormSchema} from '/@/components/Table';
import { JInputTypeEnum } from '/@/enums/jeecgEnum';
import {useI18n} from '/@/hooks/web/useI18n';

const {t} = useI18n();

function tp(name) {
  return t('routes.LogSourceManager.' + name);
}


export const columns: BasicColumn[] = [
  {
    title: tp('ProxyName'),
    dataIndex: 'proxyName',
    align: 'left',
  },
  {
    title: tp('ProxyIP'),
    dataIndex: 'proxyIp',
  },

  {
    title: tp('ProxyPort'),
    dataIndex: 'proxyPort',
  },
  {
    title: t('routes.tenant.createBy'),
    dataIndex: 'createBy',
    width: 170,
  },
  {
    title: t('routes.tenant.createTime'),
    dataIndex: 'createTime',
    width: 170,
  },
];


export const ASSET_STATE_MAP = {
  1: tp('common.Online'), 2: tp('common.Offline'), 3: tp('common.Unknown')
}

export const searchFormSchema: FormSchema[] = [
  {
    field: 'proxyName',
    label: '',
    component: 'JInput',
    componentProps: {
      search: true,
      placeholder:   tp('ProxyName'),
    },
    colProps: {span: 8},
  },
  {
    field: 'proxyIp',
    label: '',
    component: 'JInput',
    componentProps: {
      search: true,
      placeholder:   tp('ProxyIP'),
    },
    colProps: {span: 8},
  },
  {
    field: 'proxyPort',
    label: '',
    component: 'JInput',
    componentProps: {
      search: true,
      type:JInputTypeEnum.JINPUT_QUERY_EQ,
      placeholder:   tp('ProxyPort'),
    },
    colProps: {span: 8},
  },
];
