import {BasicColumn, FormSchema} from '/@/components/Table';
import {useI18n} from '/@/hooks/web/useI18n';
import {getTenantMode, isAdministrator} from '/@/utils/auth';
import {createVNode} from 'vue';
import {tenantTypeMap} from '/@/utils/ckTable';

const { t } = useI18n();


/**
 * 是否可以修改状态
 * @param data
 */
export function getUpdStatus(data) {
  //没有开启租户
  if (!getTenantMode()) {
    return true;
  }
  const isAdmin = isAdministrator();
  //tenantType== 1 MSSP管理员创建 ; tenantType==2 租户创建
  //ruleScope==1私有的；ruleScope==2共享的

  //管理员创建的，管理员可以修改
  if (data?.tenantType == 1 && isAdmin) {
    return true;
  }
  //管理员创建的私有的，私有租户可以修改,列表能看到就可以修改
  if (data?.tenantType == 1 && data?.ruleScope == 1) {
    return true;
  }
  //租户自己创建的，只有租户自己可以修改
  if (data?.tenantType == 2 && !isAdmin) {
    return true;
  }
  //共享的，租户可以修改状态
  if (data?.ruleScope == 2 && !isAdmin) {
    return true;
  }
  return false;
}

export const getColumns = (): BasicColumn[] => [
  {
    title: t('routes.MlRuleVO.ruleName'),
    dataIndex: 'ruleName',
  },
  {
    title: t('routes.MlRuleVO.urgency'),
    dataIndex: 'urgency',
    customRender: ({ value }) => {
      const map = { 1: t('common.Critical'), 2: t('common.High'), 3: t('common.Middle'), 4: t('common.Low'), 5: t('common.Information') };
      return map[value];
    },
  },
  // {
  //   title: t('routes.MlRuleVO.timeVal'),
  //   dataIndex: 'timeVal',
  //   customRender: ({value, record}) => {
  //     const map: any = {"1": "min", "2": "hour"}
  //     const data: any = record
  //     return value + map[data?.timeType]
  //   }
  // },
  {
    title: t('routes.MlRuleVO.ruleScope'),
    dataIndex: 'ruleScope',
    customRender: ({ value }) => {
      if (value == 1) {
        return t('common.exclusiveRule');
      } else if (value == 2) {
        return t('common.shareableRule');
      }
    },
    ifShow: isAdministrator() && getTenantMode(),
  },
  {
    title: t('routes.MlRuleVO.tenant'),
    dataIndex: 'tenant',
    customRender: ({ text }) => {
      if (text) {
        const array = text.split(',');
        const nodes: any = [];
        for (const i in array) {
          nodes.push(createVNode('span', { class: 'tenantSpan ant-table-cell-ellipsis',}, [array[i]]));
        }
        return createVNode('div', {class:'ant-table-cell-ellipsis',title:text,style:'display: flex;flex-wrap: wrap;max-height: 55px;'}, [nodes]);
      }
    },
    ifShow: isAdministrator() && getTenantMode(),
  },

  // {
  //   title: t('routes.MlRuleVO.datasetNum'),
  //   dataIndex: 'datasetNum'
  // },
  // {
  //   title: t('routes.MlRuleVO.statisticNum'),
  //   dataIndex: 'statisticNum'
  // },
  // {
  //   title: t('routes.MlRuleVO.datasetComparisonTriggers'),
  //   dataIndex: 'datasetComparisonTriggers',
  //   customRender: ({value}) => {
  //     if (value == 1) {
  //       return "Yes"
  //     } else {
  //       return "None"
  //     }
  //   },
  //   width: 200
  // },
  // {
  //   title: t('routes.MlRuleVO.statisticTriggers'),
  //   dataIndex: 'statisticTriggers',
  //   customRender: ({value}) => {
  //     if (value == 1) {
  //       return "Yes"
  //     } else {
  //       return "None"
  //     }
  //   }
  // },
  {
    title: t('routes.MlRuleVO.createUser'),
    dataIndex: 'createBy',
  },
  {
    title: t('routes.MlRuleVO.tenantType'),
    dataIndex: 'tenantType',
    customRender: ({ text }) => {
      return tenantTypeMap(t)[text];
    },
    ifShow: getTenantMode(),
  },
  {
    title: t('routes.MlRuleVO.createTime'),
    dataIndex: 'createTime',
  },
  {
    title: t('routes.MlRuleVO.status'),
    dataIndex: 'status',
    slots: { customRender: 'switchStatus' },
  },
];

export const searchFormSchema: FormSchema[] = [
  {
    label: '',
    field: 'ruleName',
    component: 'Input',
    componentProps:{
      search: true,
      placeholder:t('routes.MlRuleVO.ruleName'),
    }
  },
  // {
  //   label: '',
  //   field: 'ruleType',
  //   component: 'Select',
  //   componentProps: {
  //     options: [
  //       { label: 'Statistic', value: 1 },
  //       { label: 'Order', value: 2 },
  //       { label: 'Content', value: 3 },
  //     ],
  //   },
  // },
  {
    label: '',
    field: 'urgency',
    component: 'Select',
    componentProps: {
      options: [
        { label: t('common.Critical'), value: '1' },
        { label: t('common.High'), value: '2' },
        { label: t('common.Middle'), value: '3' },
        { label: t('common.Low'), value: '4' },
        { label: t('common.Information'), value: '5' },
      ],
      placeholder:t('routes.MlRuleVO.urgency'),
    },
  },
  // {
  //   label: t('routes.MlRuleVO.datasetComparisonTriggers'),
  //   field: 'datasetComparisonTriggers',
  //   component: 'Select',
  //   componentProps: {
  //     options: [
  //       {label: 'Yes', value: '1'},
  //       {label: 'None', value: '2'},
  //     ],
  //   }
  // },
  // {
  //   label: t('routes.MlRuleVO.statisticTriggers'),
  //   field: 'statisticTriggers',
  //   component: 'Select',
  //   componentProps: {
  //     options: [
  //       {label: 'Yes', value: '1'},
  //       {label: 'None', value: '2'},
  //     ],
  //   }
  // },
  {
    label: '',
    field: 'createBy',
    component: 'JDictSelectTag',
    componentProps: {
      dictCode: 'sysUserNameDict',
      showSearch: true,
      placeholder:t('routes.MlRuleVO.createUser'),
    },
  },
  {
    label: '',
    field: 'startDate',
    component: 'RangeDate',
    componentProps: {
      datetime: true,
    },
    colProps: {
      lg: 12, // ≥992px
      xl: 6, // ≥1200px
      xxl: 5, // ≥1600px
    },
  },
  {
    label: '',
    field: 'status',
    component: 'Select',
    componentProps: {
      options: [
        { label: t('common.Active'), value: '1' },
        { label: t('common.Disabled'), value: '2' },
      ],
      placeholder:t('routes.MlRuleVO.status'),
    },
  },
  // {
  //   label: "",
  //   field: '-',
  //   component: 'Input',
  //   slot: "search",
  //   colProps: {
  //     lg: 8, // ≥992px
  //     xl: 6, // ≥1200px
  //     xxl: 4, // ≥1600px
  //   }
  // },
];


