import {defHttp} from '/@/utils/http/axios';
import {Modal} from 'ant-design-vue';
import {useI18n} from "/@/hooks/web/useI18n";

const {t} = useI18n();
` `

enum Api {
  list = '/tenant/tenant/list',
  save = '/tenant/tenant/add',
  edit = '/tenant/tenant/edit',
  get = '/tenant/tenant/queryById',
  delete = '/tenant/tenant/delete',
  deleteBatch = '/tenant/tenant/deleteBatch',
  queryList = '/tenant/tenant/queryList',
  generateLicense = '/tenant/licenseRecord/add',


  recycleBinPageList = '/sys/tenant/recycleBinPageList',
  deleteLogicDeleted = '/sys/tenant/deleteLogicDeleted',
  revertTenantLogic = '/sys/tenant/revertTenantLogic',
}

/**
 * 查询租户列表
 * @param params
 */
export const getTenantList = (params) => {
  return defHttp.get({url: Api.list, params});
};

/**
 * 保存或者更新租户
 * @param params
 */
export const saveOrUpdateTenant = (params, isUpdate) => {
  const url = isUpdate ? Api.edit : Api.save;
  return defHttp.post({url: url, params});
};

/**
 * 查询租户详情
 * @param params
 */
export const getTenantById = (params) => {
  return defHttp.get({url: Api.get, params});
};
export const queryList = (params?) => {
  return defHttp.get({url: Api.queryList, params});
};


/**
 * 删除租户
 * @param params
 */
export const deleteTenant = (params, handleSuccess) => {
  return defHttp.delete({url: Api.delete, data: params}, {joinParamsToUrl: true}).then(() => {

  }).finally(()=>{
    handleSuccess();
  });
};

/**
 * 批量删除租户
 * @param params
 */
export const batchDeleteTenant = (params, handleSuccess) => {
  Modal.confirm({
    title: t('common.delConfirmText'),
    content: t('common.delContent'),
    okText: t('common.delText'),
    cancelText: t('common.cancelText'),
    wrapClassName: 'delete_confirm',
    onOk: () => {
      return defHttp.delete({
        url: Api.deleteBatch,
        data: params
      }, {joinParamsToUrl: true}).then(() => {

      }).finally(()=>{
        handleSuccess();
      });
    },
  });
};


/**
 * 获取租户回收站的列表
 * @param params
 */
export const recycleBinPageList = (params) => {
  return defHttp.get({url: Api.recycleBinPageList, params});
};

/**
 * 租户彻底删除
 * @param params
 */
export const deleteLogicDeleted = (params, handleSuccess) => {
  return defHttp.delete({url: Api.deleteLogicDeleted, params}, {joinParamsToUrl: true}).then(() => {
    handleSuccess();
  }).catch(() => {
    handleSuccess();
  });
};

/**
 * 租户还原
 * @param params
 */
export const revertTenantLogic = (params, handleSuccess) => {
  return defHttp.put({url: Api.revertTenantLogic, params}, {joinParamsToUrl: true}).then(() => {
    handleSuccess();
  })
};
/**
 * 生成license
 * @param params
 */
export const generateLicense = (params) => {
  return defHttp.post({url: Api.generateLicense, params});
};


