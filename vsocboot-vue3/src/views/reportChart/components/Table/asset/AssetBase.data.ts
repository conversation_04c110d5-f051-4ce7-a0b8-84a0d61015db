import {BasicColumn, FormSchema} from '/@/components/Table';
import {ASSET_LEVEL, ASSET_STATE} from "/@/views/asset/AssetBase.option";
import {isAdministrator} from "/@/utils/auth";
import {useI18n} from "/@/hooks/web/useI18n";
import {createVNode} from "vue";
import { JInputTypeEnum } from '/@/enums/jeecgEnum';

const {t} = useI18n();
function tp(name) {
  return t("routes.assetBase." + name);
}
export const assetColumns = (assetType): BasicColumn[] => {
  let col = [] as BasicColumn[];
  if (assetType == 'Host') {
    col = [
      {
        title: t('routes.assetBase.hostName'),
        dataIndex: 'assetName',
        slots: {customRender: 'assetName'},
        sorter:true,
      },
      {
        title: t('routes.assetBase.os'),
        dataIndex: 'os',
        sorter:true,
      },
      {
        title: t('routes.assetBase.ipv4'),
        dataIndex: 'ipv4',
        sorter:true,
        customRender: ({record}) => {
          const data: any = record
          if (!data.mainIp) {
            return '';
          }
          const obj = JSON.parse(data.mainIp);
          if (obj?.ipv4) {
            return obj?.ipv4;
          }
        },
      },


      {
        title: t('routes.assetBase.assetLevel'),
        dataIndex: 'assetLevel',
        sorter:true,
        slots: {customRender: 'level'},
      },
      {
        title: t('routes.assetBase.logsource'),
        dataIndex: 'logSourceId',
        sorter:true,
        width: 150,
        customRender: ({text}) => {
          if (text != null) {
            return 'Yes';
          } else{
            return 'No';
          }
        },
      },
      {
        title: t('routes.assetBase.safeStatus'),
        dataIndex: 'assetSafe',
        sorter:true,
        slots: {customRender: 'safeState'},
        width:120
      },
      {
        title: t('routes.assetBase.onlineStatus'),
        dataIndex: 'assetState',
        sorter:true,
        slots: {customRender: 'onlineState'},
        width:120
      },
      {
        title: t('routes.assetBase.tag'),
        dataIndex: 'tag',
        sorter:true,
        customRender: ({record}) => {
          const data: any = record;
          if (data.tagNames) {
            const array = data.tagNames.split(",");
            const nodes: any = []
            for (const i in array) {
              nodes.push(createVNode('div', {class: 'tag-item',title:array[i]}, [array[i]]))
            }
            return createVNode('div', {class: 'tags'}, [nodes]);
          }

        },
      },
      {
        title: t('routes.assetBase.assetGroup'),
        dataIndex: 'assetGroup_dictText',
        sorter:true,
      },
      {
        title: t('routes.assetBase.addingmethod'),
        dataIndex: 'assetSource',
        sorter:true,
        defaultHidden: true,
        width: 150,
        customRender: ({text}) => {
          if (text == 1) {
            return t('routes.assetBase.Manually');
          } else if (text == 2){
            return t('routes.assetBase.MDPS');
          }else if (text == 3){
            return t('routes.assetBase.Assetdiscovery');
          }else if (text == 4){
            return t('routes.assetBase.Import');
          }
        },
      },
      {
        title: t('routes.assetBase.lossourcestatus'),
        dataIndex: 'logSourceStatus',
        sorter:true,
        defaultHidden: true,
        width: 150,
        customRender: ({text}) => {
          if (text == 1) {
            return t('routes.assetBase.normal');
          } else if (text == 2){
            return t('routes.assetBase.abnormal');
          }
        },
      },
      {
        title: t('routes.assetBase.logapi'),
        dataIndex: 'logApiId',
        defaultHidden: true,
        sorter:true,
        width: 150,
        customRender: ({text}) => {
          if (text != null) {
            return 'Yes';
          } else{
            return 'No';
          }
        },
      },
      {
        title: t('routes.assetBase.proxyconfigured'),
        dataIndex: 'proxyName',
        sorter:true,
        defaultHidden: true,
      },
      {
        title: t('routes.assetBase.deviceManufacturer'),
        dataIndex: 'deviceManufacturer',
        sorter:true,
        defaultHidden: true,
      },
      {
        title: t('routes.assetBase.osversion'),
        dataIndex: 'osVersion',
        sorter:true,
        defaultHidden: true,
      },
      {
        title: t('routes.assetBase.deviceVersion'),
        dataIndex: 'deviceVersion',
        sorter:true,
        defaultHidden: true,
      },
      {
        title: t('routes.assetBase.deviceId'),
        dataIndex: 'deviceId',
        sorter:true,
        defaultHidden: true,
      },
      {
        title: t('routes.assetBase.cpuArchitecture'),
        dataIndex: 'cpuArchitecture',
        sorter:true,
        defaultHidden: true,
      },
      {
        title: t('routes.assetBase.kernelVersion'),
        dataIndex: 'kernelVersion',
        sorter:true,
        defaultHidden: true,
      },
    ]
    if (isAdministrator()) {
      col.splice(2, 0, {
        title: t('common.tenant'),
        dataIndex: 'socTenantId_dictText',
        sorter:true,
      })
    }
  }
  else if (assetType == 'Cloud') {
    col = [
      {
        title: t('routes.assetBase.cloudservername'),
        dataIndex: 'assetName',
        slots: {customRender: 'assetName'},
        sorter:true,
      },
      {
        title: t('routes.assetBase.cloudType'),
        dataIndex: 'cloudType',
        sorter:true,
        customRender: ({record}) => {
          const data: any = record
          if (data.cloudType == '1') {
            return t('routes.assetBase.publicCloud');
          }else{
            return t('routes.assetBase.privateCloud');
          }

        },
      },
      {
        title: t('routes.assetBase.cloudprovider'),
        dataIndex: 'cloudProvider',
        sorter:true,
      },

      {
        title: t('routes.assetBase.ipv4'),
        dataIndex: 'ipv4',
        sorter:true,
        customRender: ({record}) => {
          const data: any = record
          if (!data.mainIp) {
            return '';
          }
          const obj = JSON.parse(data.mainIp);
          if (obj?.ipv4) {
            return obj?.ipv4;
          }
        },
      },

      {
        title: t('routes.assetBase.assetLevel'),
        dataIndex: 'assetLevel',
        sorter:true,
        slots: {customRender: 'level'},
      },
      {
        title: t('routes.assetBase.logsource'),
        dataIndex: 'logSourceId',
        sorter:true,
        width: 150,
        customRender: ({text}) => {
          if (text != null) {
            return 'Yes';
          } else{
            return 'No';
          }
        },
      },
      {
        title: t('routes.assetBase.safeStatus'),
        dataIndex: 'assetSafe',
        sorter:true,
        slots: {customRender: 'safeState'},
        width:120
      },
      {
        title: t('routes.assetBase.onlineStatus'),
        dataIndex: 'assetState',
        sorter:true,
        slots: {customRender: 'onlineState'},
        width:120
      },

      {
        title: t('routes.assetBase.logapi'),
        dataIndex: 'logApiId',
        sorter:true,
        defaultHidden: true,
        width: 150,
        customRender: ({text}) => {
          if (text != null) {
            return 'Yes';
          } else{
            return 'No';
          }
        },
      },
      {
        title: t('routes.assetBase.proxyconfigured'),
        dataIndex: 'proxyName',
        sorter:true,
        defaultHidden: true,
      },
      {
        title: t('routes.assetBase.tag'),
        dataIndex: 'tag',
        sorter:true,
        defaultHidden: true,
        customRender: ({record}) => {
          const data: any = record;
          if (data.tagNames) {
            const array = data.tagNames.split(",");
            const nodes: any = []
            for (const i in array) {
              nodes.push(createVNode('div', {class: 'tag-item',title:array[i]}, [array[i]]))
            }
            return createVNode('div', {class: 'tags'}, [nodes]);
          }

        },
      },
      {
        title: t('routes.assetBase.assetGroup'),
        dataIndex: 'assetGroup_dictText',
        sorter:true,
        defaultHidden: true,
      },
      {
        title: t('routes.assetBase.des'),
        dataIndex: 'assetDesc',
        sorter:true,
        defaultHidden: true,
      },
      {
        title: t('routes.assetBase.os'),
        dataIndex: 'os',
        sorter:true,
        defaultHidden: true,
      },
      {
        title: t('routes.assetBase.osversion'),
        dataIndex: 'osVersion',
        sorter:true,
        defaultHidden: true,
      },
      {
        title: t('routes.assetBase.cpuSpecification'),
        dataIndex: 'cpuSpecification',
        sorter:true,
        defaultHidden: true,
      },
      {
        title: t('routes.assetBase.memorysp'),
        dataIndex: 'memorySpecification',
        sorter:true,
        defaultHidden: true,
      },
      {
        title: t('routes.assetBase.hs'),
        dataIndex: 'hardSpecification',
        sorter:true,
        defaultHidden: true,
      },

      {
        title: t('routes.assetBase.CloudID'),
        dataIndex: 'cloudId',
        sorter:true,
        defaultHidden: true,
      },
      {
        title: t('routes.assetBase.zone'),
        dataIndex: 'availabilityZone',
        sorter:true,
        defaultHidden: true,
      },
      {
        title: t('routes.assetBase.ExpirationTime'),
        dataIndex: 'expirationTime',
        sorter:true,
        defaultHidden: true,
      },
    ]
    if (isAdministrator()) {
      col.splice(4, 0, {
        title: t('common.tenant'),
        dataIndex: 'socTenantId_dictText',
        sorter:true,
      })
    }
  }
  else if (assetType == 'Virtual') {
    col = [
      {
        title: t('routes.assetBase.asssetName'),
        dataIndex: 'assetName',
        slots: {customRender: 'assetName'},
        sorter:true,
      },
      {
        title: t('routes.assetBase.os'),
        dataIndex: 'os',
        sorter:true,
      },
      {
        title: t('routes.assetBase.ipv4'),
        dataIndex: 'ipv4',
        sorter:true,
        customRender: ({record}) => {
          const data: any = record
          if (!data.mainIp) {
            return '';
          }
          const obj = JSON.parse(data.mainIp);
          if (obj?.ipv4) {
            return obj?.ipv4;
          }
        },
      },

      {
        title: t('routes.assetBase.assetLevel'),
        dataIndex: 'assetLevel',
        sorter:true,
        slots: {customRender: 'level'},
      },
      {
        title: t('routes.assetBase.logsource'),
        dataIndex: 'logSourceId',
        sorter:true,
        width: 150,
        customRender: ({text}) => {
          if (text != null) {
            return 'Yes';
          } else{
            return 'No';
          }
        },
      },
      {
        title: t('routes.assetBase.safeStatus'),
        dataIndex: 'assetSafe',
        sorter:true,
        slots: {customRender: 'safeState'},
        width:120
      },
      {
        title: t('routes.assetBase.onlineStatus'),
        dataIndex: 'assetState',
        sorter:true,
        slots: {customRender: 'onlineState'},
        width:120
      },
      {
        title: t('routes.assetBase.logapi'),
        dataIndex: 'logApiId',
        sorter:true,
        defaultHidden: true,
        width: 150,
        customRender: ({text}) => {
          if (text != null) {
            return 'Yes';
          } else{
            return 'No';
          }
        },
      },
      {
        title: t('routes.assetBase.proxyconfigured'),
        dataIndex: 'proxyName',
        sorter:true,
        defaultHidden: true,
      },
      {
        title: t('routes.assetBase.addingmethod'),
        dataIndex: 'assetSource',
        sorter:true,
        defaultHidden: true,
        width: 150,
        customRender: ({text}) => {
          if (text == 1) {
            return t('routes.assetBase.Manually');
          } else if (text == 2){
            return t('routes.assetBase.MDPS');
          }else if (text == 3){
            return t('routes.assetBase.Assetdiscovery');
          }else if (text == 4){
            return t('routes.assetBase.Import');
          }
        },
      },
      {
        title: t('routes.assetBase.lossourcestatus'),
        dataIndex: 'logSourceStatus',
        sorter:true,
        defaultHidden: true,
        width: 150,
        customRender: ({text}) => {
          if (text == 1) {
            return t('routes.assetBase.normal');
          } else if (text == 2){
            return t('routes.assetBase.abnormal');
          }
        },
      },
      {
        title: t('routes.assetBase.tag'),
        dataIndex: 'tag',
        defaultHidden: true,
        sorter:true,
        customRender: ({record}) => {
          const data: any = record;
          if (data.tagNames) {
            const array = data.tagNames.split(",");
            const nodes: any = []
            for (const i in array) {
              nodes.push(createVNode('div', {class: 'tag-item',title:array[i]}, [array[i]]))
            }
            return createVNode('div', {class: 'tags'}, [nodes]);
          }

        },
      },
      {
        title: t('routes.assetBase.assetGroup'),
        dataIndex: 'assetGroup_dictText',
        sorter:true,
        defaultHidden: true,
      },
      {
        title: t('routes.assetBase.osversion'),
        dataIndex: 'osVersion',
        sorter:true,
        defaultHidden: true,
      },
      {
        title: t('routes.assetBase.cpuSpecification'),
        dataIndex: 'cpuSpecification',
        sorter:true,
        defaultHidden: true,
      },
      {
        title: t('routes.assetBase.hs'),
        dataIndex: 'hardSpecification',
        sorter:true,
        defaultHidden: true,
      },
    ]
    if (isAdministrator()) {
      col.splice(2, 0, {
        title: t('common.tenant'),
        dataIndex: 'socTenantId_dictText',
        sorter:true,
      })
    }
  }
  else if (assetType == 'Network' || assetType == 'Cybersecurity' || assetType == 'IOT') {
    col = [
      {
        title: t('routes.assetBase.asssetName'),
        dataIndex: 'assetName',
        slots: {customRender: 'assetName'},
        sorter:true,
      },
      {
        title: t('routes.assetBase.deviceType'),
        dataIndex: 'deviceType',
        sorter:true,
      },
      {
        title: t('routes.assetBase.ipv4'),
        dataIndex: 'ipv4',
        sorter:true,
        customRender: ({record}) => {
          const data: any = record
          if (!data.mainIp) {
            return '';
          }
          const obj = JSON.parse(data.mainIp);
          if (obj?.ipv4) {
            return obj?.ipv4;
          }
        },
      },
      {
        title: t('routes.assetBase.accessibleUrl'),
        dataIndex: 'accessibleUrl',
        sorter:true,
      },

      {
        title: t('routes.assetBase.assetLevel'),
        dataIndex: 'assetLevel',
        slots: {customRender: 'level'},
        sorter:true,
      },
      {
        title: t('routes.assetBase.logsource'),
        dataIndex: 'logSourceId',
        sorter:true,
        width: 150,
        customRender: ({text}) => {
          if (text != null) {
            return 'Yes';
          } else{
            return 'No';
          }
        },
      },
      {
        title: t('routes.assetBase.safeStatus'),
        dataIndex: 'assetSafe',
        slots: {customRender: 'safeState'},
        sorter:true,
        width:120
      },
      {
        title: t('routes.assetBase.onlineStatus'),
        dataIndex: 'assetState',
        sorter:true,
        slots: {customRender: 'onlineState'},
        width:120
      },
      {
        title: t('routes.assetBase.addingmethod'),
        dataIndex: 'assetSource',
        sorter:true,
        defaultHidden: true,
        width: 150,
        customRender: ({text}) => {
          if (text == 1) {
            return t('routes.assetBase.Manually');
          } else if (text == 2){
            return t('routes.assetBase.MDPS');
          }else if (text == 3){
            return t('routes.assetBase.Assetdiscovery');
          }else if (text == 4){
            return t('routes.assetBase.Import');
          }
        },
      },
      {
        title: t('routes.assetBase.lossourcestatus'),
        dataIndex: 'logSourceStatus',
        sorter:true,
        defaultHidden: true,
        width: 150,
        customRender: ({text}) => {
          if (text == 1) {
            return t('routes.assetBase.normal');
          } else if (text == 2){
            return t('routes.assetBase.abnormal');
          }
        },
      },
      {
        title: t('routes.assetBase.logapi'),
        dataIndex: 'logApiId',
        sorter:true,
        defaultHidden: true,
        width: 150,
        customRender: ({text}) => {
          if (text != null) {
            return 'Yes';
          } else{
            return 'No';
          }
        },
      },
      {
        title: t('routes.assetBase.proxyconfigured'),
        dataIndex: 'proxyName',
        sorter:true,
        defaultHidden: true,
      },
      {
        title: t('routes.assetBase.tag'),
        dataIndex: 'tag',
        defaultHidden: true,
        sorter:true,
        customRender: ({record}) => {
          const data: any = record;
          if (data.tagNames) {
            const array = data.tagNames.split(",");
            const nodes: any = []
            for (const i in array) {
              nodes.push(createVNode('div', {class: 'tag-item',title:array[i]}, [array[i]]))
            }
            return createVNode('div', {class: 'tags'}, [nodes]);
          }

        },
      },
      {
        title: t('routes.assetBase.assetGroup'),
        dataIndex: 'assetGroup_dictText',
        defaultHidden: true,
        sorter:true,
      },
      {
        title: t('routes.assetBase.deviceManufacturer'),
        dataIndex: 'deviceManufacturer',
        defaultHidden: true,
        sorter:true,
      },
      {
        title: t('routes.assetBase.deviceModal'),
        dataIndex: 'deviceModal',
        defaultHidden: true,
        sorter:true,
      },
      {
        title: t('routes.assetBase.deviceVersion'),
        dataIndex: 'deviceVersion',
        defaultHidden: true,
        sorter:true,
      },
      {
        title: t('routes.assetBase.deviceId'),
        dataIndex: 'deviceId',
        defaultHidden: true,
        sorter:true,
      },
      {
        title: t('routes.assetBase.location'),
        dataIndex: 'location',
        defaultHidden: true,
        sorter:true,
      },
    ]
    if (isAdministrator()) {
      col.splice(1, 0, {
        title: t('common.tenant'),
        dataIndex: 'socTenantId_dictText',
        sorter:true,
      })
    }
  }else if (assetType == 'Application') {
    col = [
      {
        title: t('routes.assetBase.applicationName'),
        dataIndex: 'assetName',
        slots: {customRender: 'assetName'},
        sorter:true,
      },

      {
        title: t('routes.assetBase.ipv4'),
        dataIndex: 'ipv4',
        sorter:true,
        customRender: ({record}) => {
          const data: any = record
          if (!data.mainIp) {
            return '';
          }
          const obj = JSON.parse(data.mainIp);
          if (obj?.ipv4) {
            return obj?.ipv4;
          }
        },
      },
      {
        title: t('routes.assetBase.accessibleUrl'),
        dataIndex: 'accessibleUrl',
        sorter:true,
      },

      {
        title: t('routes.assetBase.assetLevel'),
        dataIndex: 'assetLevel',
        slots: {customRender: 'level'},
        sorter:true,
      },
      {
        title: t('routes.assetBase.logsource'),
        dataIndex: 'logSourceId',
        sorter:true,
        width: 150,
        customRender: ({text}) => {
          if (text != null) {
            return 'Yes';
          } else{
            return 'No';
          }
        },
      },
      {
        title: t('routes.assetBase.addingmethod'),
        dataIndex: 'assetSource',
        sorter:true,
        defaultHidden: true,
        width: 150,
        customRender: ({text}) => {
          if (text == 1) {
            return t('routes.assetBase.Manually');
          } else if (text == 2){
            return t('routes.assetBase.MDPS');
          }else if (text == 3){
            return t('routes.assetBase.Assetdiscovery');
          }else if (text == 4){
            return t('routes.assetBase.Import');
          }
        },
      },
      {
        title: t('routes.assetBase.logapi'),
        dataIndex: 'logApiId',
        sorter:true,
        defaultHidden: true,
        width: 150,
        customRender: ({text}) => {
          if (text != null) {
            return 'Yes';
          } else{
            return 'No';
          }
        },
      },
      {
        title: t('routes.assetBase.proxyconfigured'),
        dataIndex: 'proxyName',
        sorter:true,
        defaultHidden: true,
      },
      {
        title: t('routes.assetBase.tag'),
        dataIndex: 'tag',
        sorter:true,
        defaultHidden: true,
        customRender: ({record}) => {
          const data: any = record;
          if (data.tagNames) {
            const array = data.tagNames.split(",");
            const nodes: any = []
            for (const i in array) {
              nodes.push(createVNode('div', {class: 'tag-item',title:array[i]}, [array[i]]))
            }
            return createVNode('div', {class: 'tags'}, [nodes]);
          }

        },
      },
      {
        title: t('routes.assetBase.lossourcestatus'),
        dataIndex: 'logSourceStatus',
        sorter:true,
        defaultHidden: true,
        width: 150,
        customRender: ({text}) => {
          if (text == 1) {
            return t('routes.assetBase.normal');
          } else if (text == 2){
            return t('routes.assetBase.abnormal');
          }
        },
      },
      {
        title: t('routes.assetBase.assetGroup'),
        dataIndex: 'assetGroup_dictText',
        sorter:true,
        defaultHidden: true,
      },
    ]
    if (isAdministrator()) {
      col.splice(1, 0, {
        title: t('common.tenant'),
        sorter:true,
        dataIndex: 'socTenantId_dictText',
      })
    }
  }else if (assetType == 'all') {
    col = [
      {
        title: t('routes.assetBase.asssetName'),
        dataIndex: 'assetName',
        slots: {customRender: 'assetName'},
        sorter:true,
      },
      {
        title: t('routes.assetBase.assetType'),
        dataIndex: 'assetType',
        sorter:true,
        customRender: ({text}) => {
          if (text != null) {
            return tp(text)
          }
        },
      },
      {
        title: t('routes.assetBase.ipv4'),
        dataIndex: 'ipv4',
        sorter:true,
        customRender: ({record}) => {
          const data: any = record
          if (!data.mainIp) {
            return '';
          }
          const obj = JSON.parse(data.mainIp);
          if (obj?.ipv4) {
            return obj?.ipv4;
          }
        },
      },

      {
        title: t('routes.assetBase.integrationPlugin'),
        dataIndex: 'pluginName',
        sorter:true,
      },
      {
        title: t('routes.assetBase.assetLevel'),
        dataIndex: 'assetLevel',
        sorter:true,
        slots: {customRender: 'level'},
      },
      {
        title: t('routes.assetBase.logsource'),
        dataIndex: 'logSourceId',
        sorter:true,
        width: 150,
        customRender: ({text}) => {
          if (text != null) {
            return 'Yes';
          } else{
            return 'No';
          }
        },
      },
      {
        title: t('routes.assetBase.safeStatus'),
        dataIndex: 'assetSafe',
        sorter:true,
        slots: {customRender: 'safeState'},
        width:120
      },
      {
        title: t('routes.assetBase.onlineStatus'),
        dataIndex: 'assetState',
        sorter:true,
        slots: {customRender: 'onlineState'},
        width:120
      },
      {
        title: t('routes.assetBase.tag'),
        dataIndex: 'tag',
        defaultHidden: true,
        sorter:true,
        customRender: ({record}) => {
          const data: any = record;
          if (data.tagNames) {
            const array = data.tagNames.split(",");
            const nodes: any = []
            for (const i in array) {
              nodes.push(createVNode('div', {class: 'tag-item',title:array[i]}, [array[i]]))
            }
            return createVNode('div', {class: 'tags'}, [nodes]);
          }

        },
      },
      {
        title: t('routes.assetBase.addingmethod'),
        dataIndex: 'assetSource',
        sorter:true,
        defaultHidden: true,
        width: 150,
        customRender: ({text}) => {
          if (text == 1) {
            return t('routes.assetBase.Manually');
          } else if (text == 2){
            return t('routes.assetBase.MDPS');
          }else if (text == 3){
            return t('routes.assetBase.Assetdiscovery');
          }else if (text == 4){
            return t('routes.assetBase.Import');
          }
        },
      },
      {
        title: t('routes.assetBase.lossourcestatus'),
        dataIndex: 'logSourceStatus',
        sorter:true,
        defaultHidden: true,
        width: 150,
        customRender: ({text}) => {
          if (text == 1) {
            return t('routes.assetBase.normal');
          } else if (text == 2){
            return t('routes.assetBase.abnormal');
          }
        },
      },
      {
        title: t('routes.assetBase.assetGroup'),
        dataIndex: 'assetGroup_dictText',
        sorter:true,
        defaultHidden: true,
      },
      {
        title: t('routes.assetBase.logapi'),
        dataIndex: 'logApiId',
        sorter:true,
        defaultHidden: true,
        width: 150,
        customRender: ({text}) => {
          if (text != null) {
            return 'Yes';
          } else{
            return 'No';
          }
        },
      },
      {
        title: t('routes.assetBase.proxyconfigured'),
        dataIndex: 'proxyName',
        sorter:true,
        defaultHidden: true,
      },
    ]
    if (isAdministrator()) {
      col.splice(2, 0, {
        title: t('common.tenant'),
        sorter:true,
        dataIndex: 'socTenantId_dictText',
      })
    }
  }




  return col;
  
};

export const searchFormSchema: FormSchema[] = [
  {
    label: '',
    field: 'search',
    component: 'JInput',
    componentProps: {
      search: true,
      type:JInputTypeEnum.JINPUT_QUERY_EQ,
      placeholder: t('routes.asset.search'),
      
    },
    colProps: {
      lg: 12, // ≥992px
      xl: 6, // ≥1200px
      xxl: 4, // ≥1600px
    }
  },
  {
    label: '',
    field: 'socTenantId',
    component: 'JSearchSelect',
    componentProps: {
      dict: "tenantDict",
      placeholder: t('routes.asset.allTenant'),
    },
    ifShow: isAdministrator()
  },
  {
    label: '',
    field: 'assetGroupId',
    component: 'TreeSelect',
    componentProps: {
      dict: "assetGroupId",
      placeholder: t('routes.asset.allGroup'),
    },
    slot:'group',
    // ifShow: isAdministrator()
  },
  {
    label: '',
    field: 'assetState',
    component: 'Select',
    componentProps: {
      options: ASSET_STATE,
      stringToNumber: true,
      placeholder: t('routes.asset.allstatus'),
    }
  },


  {
    label: '',
    field: 'assetLevel',
    component: 'Select',
    componentProps: {
      options: ASSET_LEVEL,
      stringToNumber: true,
      placeholder: t('routes.asset.alllevel'),
    }
  },

];




