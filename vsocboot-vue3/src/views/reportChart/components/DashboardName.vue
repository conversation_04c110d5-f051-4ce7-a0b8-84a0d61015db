
<template>
  <a-form
    ref="formRef"
    autocomplete="off"
    :model="moduleData"
    :layout="formLayout">
    <!-- 名称-->
    <div class="font12 fcolor1 dashboardName">Dashboard name</div>
    <a-form-item name="name" label="" required label-align="left">
      <a-input v-model:value="moduleData.name"/>
    </a-form-item>
  </a-form>
</template>
<script setup lang="ts">
import {formLayout} from "/@/settings/designSetting";
import {inject, ref, Ref} from "vue";
import {IModuleData} from "/@/views/reportChart/ts/IModule";
const moduleData = inject('module',ref<IModuleData>({}));
</script>
<style scoped lang="less">
  /deep/.ant-form-item{
    margin-bottom: 0!important;
  }
  .dashboardName{
    padding-bottom: 8px;
    font-weight: bold;
  }
</style>
