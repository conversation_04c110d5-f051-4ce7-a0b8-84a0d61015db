<template>
  <!-- 高级查询按钮start-->
  <a-button type="primary" @click="openQuery">{{ tp(buttonName) }}</a-button>
  <!-- 高级查询按钮 end-->

  <!--  非log高级查询
     ===================================================-->
  <TableSearchModel
    v-if="moduleData?.dataSource != DatasourceEnum.LOG"
    ref="tableSearchModelRef"
    :isReport="true"
    @search="saveSearch"
    :systemFilter="systemFilter && moduleData?.dataSource == DatasourceEnum.RISK"
    :source="TableSearchEnum[moduleData?.dataSource + (moduleData?.logType ?? '')]"/>

  <!--  log高级查询
  ===================================================-->
  <LogSearchModal
    v-else
    ref="logSearchModelRef"
    :tableSource="moduleData.logType as number"
    :systemFilter="systemFilter"
    @search="saveSearch"/>
</template>

<script setup lang="ts">

import TableSearchModel from "/@/views/tableSearch/TableSearchModel.vue";
import LogSearchModal from "/@/views/posture/modules/LogSearchModal.vue";
import {inject, ref,} from "vue";
import {IModuleData} from "/@/views/reportChart/ts/IModule";
import {DatasourceEnum, TableSearchEnum,} from "/@/views/reportChart/enums/dataSourceEnum";
import {useMessage} from "/@/hooks/web/useMessage";
import {propTypes} from "/@/utils/propTypes";
import {tp} from "/@/views/reportChart/ts/i18Utils";
import {getTabFieldList} from "/@/utils/ckTable";
import {FIELD_TYPE_NO} from "/@/views/reportChart/ts/dataSource";

const {createMessage} = useMessage()
defineProps({
  buttonName: propTypes.string.def('Filter'),
  systemFilter:{
    type: Boolean,
    default:false
  }
});
const moduleData = inject('module', ref<IModuleData>({}));
const logSearchModelRef = ref();
const tableSearchModelRef = ref();

/**
 * 查询
 */
function openQuery() {
  if (!moduleData.value.dataSource) {
    createMessage.warning('Please select dataSource')
    return;
  }
  if (moduleData.value.dataSource == DatasourceEnum.LOG) {
    let str = moduleData.value.searchVal;
    logSearchModelRef.value.open(str ?? "", moduleData.value.searchInfo)
  } else {
    const s = moduleData.value.dataSource + (moduleData.value?.logType ?? '');
    let list = getTabFieldList(FIELD_TYPE_NO[s])
    if (!moduleData.value.searchInfo || JSON.stringify(moduleData.value.searchInfo) == '{}') {
      tableSearchModelRef.value.init(list, "");
    } else {
      tableSearchModelRef.value.init(list, moduleData.value.searchInfo);
    }
  }
}

/**
 * 保存查询条件
 * @param data
 */
function saveSearch(where, data) {
  console.log('saveSearch data-------',{where, data})
  moduleData.value.searchInfo = "";
  moduleData.value.searchVal = "";
  moduleData.value.advancedQueryFlag = false;

  if (moduleData.value.dataSource == DatasourceEnum.LOG) {
    moduleData.value.searchInfo = where;
    moduleData.value.searchVal = data;
  } else {
    if(where){
      const info = {
        advancedQueryFlag:true,
        whereList: where.whereList,
      }
      if(where?.advanceSystemData){
        info.advanceSystemData = where.advanceSystemData;
        if( moduleData.value.dataSource == DatasourceEnum.RISK){
          moduleData.value.systemFilter = JSON.stringify(where.advanceSystemData);
        }
      }else{
        if( moduleData.value.dataSource == DatasourceEnum.RISK){
          moduleData.value.systemFilter = "";
        }
      }
      moduleData.value.searchInfo = JSON.stringify(info);
      moduleData.value.searchVal = data;
      moduleData.value.advancedQueryFlag = true;

    }

  }
  console.log('moduleData.value', moduleData.value)

}
</script>
<style scoped lang="less">
@import "../less/common.less";
</style>
