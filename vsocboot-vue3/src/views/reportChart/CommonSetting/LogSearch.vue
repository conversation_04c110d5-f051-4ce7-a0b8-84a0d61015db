<template>
  <div class="search-row">
    <Search
      ref="huntingSearch"
      :autoSearch="true"
      v-model:value="easySearch"
      :searchStr="axisItem?.echo"
      :width="400"
      @searchList="searchList"/>
  </div>
</template>

<script lang="ts" name="reports-ReportSearchLog" setup>
import {defineProps, inject, ref, watch} from "vue";
import {getTabFieldList} from "/@/utils/ckTable";
import Search from "/@/views/clickhouse/Search.vue";
import {IModuleData} from "/@/views/reportChart/ts/IModule";
const emit = defineEmits(['update:echo','update:str']);
const moduleData = inject('module', ref<IModuleData>({}));
const props = defineProps({
  echo: String,
  str: String,
})

const sortMap = ref({});
const echoValue = ref<string>('');
const strValue = ref<string>('');
const huntingSearch = ref();
const easySearch = ref({
  sourceData: [],
  sourceType: {},
  selectData: []
});


watch(() => props.echo, () => {
  loadFieldData();
  huntingSearch.value?.clearValue();
  echoValue.value = props.echo as string;
}, {immediate: true, deep: true})


function searchList() {
  let queryStr = huntingSearch.value.getContentValue();
  if (queryStr.length > 0) {
    let code = huntingSearch.value.validateSearchCode();
    if (!code) {
      echoValue.value = queryStr;
      queryStr = huntingSearch.value.getParsingContentValue();
      strValue.value = "and ( " + queryStr + " )";
    }
  }


  emit('update:echo', echoValue.value)
  emit('update:str', strValue.value)
}

/**
 * 加载表字段
 */
function loadFieldData() {
  let data = getTabFieldList(moduleData.value.logType)
  let selectData: any = [];
  let fieldDataOption: any = [];
  let FieldTypeMap = {}
  for (let i = 0; i < data.length; i++) {
    sortMap.value[data[i].fieldName] = data[i].fieldLabel;
    fieldDataOption.push({
      classFieldName: data[i]?.classFieldName,//下拉选对应option名称
      label: data[i].fieldName,
      value: data[i].fieldValue,
      type: data[i].fieldType.toLocaleLowerCase()
    });
    if (data[i].fieldValue == "ck_enter_date") {
      continue
    }
    selectData.push({
      label: data[i].fieldLabel,
      value: data[i].fieldValue,
      type: data[i].fieldType
    });

    FieldTypeMap[data[i].fieldValue] = data[i].fieldType.toLocaleLowerCase();
  }
  easySearch.value.sourceData = data;
  easySearch.value.selectData = selectData;
  easySearch.value.sourceType = FieldTypeMap;

}


</script>

<style scoped>
.search-row {
  display: flex;
  flex-direction: row;
  align-items: center;

}
</style>
