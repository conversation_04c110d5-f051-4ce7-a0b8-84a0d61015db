<template>
  <div class="s-block">
    <div class="s-block_icon" v-if="imgVisible">
      <slot name="img"></slot>
    </div>
    <div class="s-block_content_wrapper">
      <div class="s-block_header">
        <div class="s-title">
          <slot name="title"></slot>
        </div>
        <slot name="head-right"></slot>
      </div>
      <div class="s-block_content">
        <slot name="content"></slot>
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
defineProps({
  imgVisible: {
    type:<PERSON>olean,
    default:true
  }
})
</script>
<style scoped lang="less">
  .s-block{
    display: flex;
    flex-direction: row;
    gap: 12px;
    box-sizing: border-box;
    align-items: flex-start;
    padding: 0 16px 16px 16px;
    .s-block_icon{
      width: 24px;
      height: 32px;
      display: flex;
      flex-direction: row;
      align-items: center;

    }
    .s-block_content_wrapper{
      flex: 1;
      display: flex;
      flex-direction: column;
      gap: 8px;
      .s-block_header{
        display: flex;
        flex-direction: row;
        align-items: center;
        justify-content: space-between;
        .s-title{

          font-size: 14px;
          font-weight: 600;
          line-height: 24px;
          color: #FFFFFF;
        }
      }

      .s-block_content{
        display: flex;
        flex-direction: column;
        gap: 8px;
      }
    }
  }

</style>
