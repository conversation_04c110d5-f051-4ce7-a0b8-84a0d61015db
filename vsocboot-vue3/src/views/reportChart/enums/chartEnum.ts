/**
 * 图形编号
 */
export enum ChartNOEnum {
  BASIC_BAR = 1,
  BASIC_COLUMN = 2,
  STACKED_BAR = 3,
  STACKED_COLUMN = 4,
  RADIAL_BAR = 5,//饼型柱图
  BASIC_LINE = 6,
  BIAXIAL_LINE = 7,//双轴线图
  BASIC_AREA = 8,
  STACKED_AREA = 9,
  STREAMGRAPH = 10,//河流图
  BASIC_PIE = 11,
  DOUGHNUT = 12,//环形图
  SEMI_CIRCLE = 13,//半圆
  NIGHTINGALE = 14,//南丁格尔图
  DONUT = 15,//旭日图
  BASIC_LIST = 16,//TABLE
  STATISTICAL_LIST = 17,//统计TABLE
  BASIC_HEAT = 18,//热力图
  FUNNEL = 19,//漏斗图
  WORD_CLOUD = 21,//字符图
  SPEEDOMETER = 22,//仪表盘
  RADAR = 23,//雷达图
  NUM = 24,//数字
}

/**
 * 类型名称
 */
export enum ChartTypeEnum {
  BAR = 'Bar',
  COLUMN = 'Column',
  RADIUBAR = 'RadiuBar',
  LINE = 'Line',
  XAXIS = 'XAxisChart',
  AREA = 'Area',
  THEMERIVER = 'ThemeRiver',
  PIE = 'Pie',
  SUNBURST = 'Sunburst',
  HEATMAP = 'HeatMap',
  FUNNEL = 'Funnel',
  WORLD = 'WorldChart',
  GAUGE = 'Gauge',
  RADAR = 'Radar',
  TABLE = 'table',
  NUM = 'number',
}

/**
 * 配置组件类型
 */
export enum CHART_COMPOTENT_NAME {
  XAXIS = 'XAxis',
  YAXIS = 'YAxis',
  COLUMN = 'Column',
}


