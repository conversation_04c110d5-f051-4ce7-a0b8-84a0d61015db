import {useI18n} from "/@/hooks/web/useI18n";

const {t} = useI18n();


export enum DatasourceEnum {
  LOG = 'Log',
  RISK = 'Risk event',
  ML = 'ML Event',
  BAD = 'Bad actor',
  TICKET = 'History ticket',
  ASSET = 'Asset',
  MLStatistic = 'Event by statistic',
  MLOrder = 'Event by order',
  MLContent = 'Event by content',

}

export const ML_RULE_Type = {
  [DatasourceEnum.MLStatistic] :1,
  [DatasourceEnum.MLOrder]: 2,
  [DatasourceEnum.MLContent] : 3
}
export const DatasourceNameEnum = {
  [DatasourceEnum.LOG] : t('routes.report.report.Log'),
  [DatasourceEnum.RISK] :t('routes.report.report.Risk_event'),
  [DatasourceEnum.ML] : t('routes.report.report.ML_Event'),
  [DatasourceEnum.BAD] : t('routes.report.report.Bad_actor'),
  [DatasourceEnum.TICKET] : t('routes.report.report.History_ticket'),
  [DatasourceEnum.ASSET] : t('routes.report.report.Asset'),
  [DatasourceEnum.MLStatistic] : t('routes.riskEvent.MLStatistic'),
  [DatasourceEnum.MLOrder]: t('routes.riskEvent.MLOrder'),
  [DatasourceEnum.MLContent] : t('routes.riskEvent.MLContent')

}
export enum TableSourceEnum {
  LOG = 'Log',
  LOG0 = 'Log0',
  LOG1 = 'Log1',
  LOG2 = 'Log2',
  LOG3 = 'Log3',
  LOG4 = 'Log4',
  LOG20 = 'Log20',
  LOG21 = 'Log21',
  LOG22 = 'Log22',
  LOG23 = 'Log23',
  RISK = 'Risk event',
  ML = 'ML Event',
  BAD = 'Bad actor',
  BAD1 = 'Bad actor1',
  BAD2 = 'Bad actor2',
  BAD3 = 'Bad actor3',
  TICKET = 'History ticket',
  ASSET = 'Asset',
  CE = 'Correlation Event',
  MLStatistic = 'Event by statistic',
  MLOrder = 'Event by order',
  MLContent = 'Event by content',
}




export const TableSearchEnum = {
  'Asset' : 'asset',
  'Bad actor1' : 'badmssp',
  'Bad actor2' : 'badActors',
  'Bad actor3' : 'badActorsHis',
  'ML Event' : 'ml',
  'Risk event' : 'risk',
  'History ticket' : 'workHistory',
  [TableSourceEnum.MLStatistic]: 'ml',
  [TableSourceEnum.MLOrder]:'ml',
  [TableSourceEnum.MLContent] :'ml'
}
