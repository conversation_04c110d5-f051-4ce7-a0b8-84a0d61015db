<template>
  <div v-if="DO_TIME_DATASOURCE.includes(moduleData.dataSource as string)" class="time-setting-wrapper">
    <!--  时间字段 start-->
    <div class="data-setting-row">
      <div class="timeLabel">{{tp('TimeField')}}</div>
      <a-select v-model:value="timeIntervalData.timeField" class="flex1" :options="moduleData.timeFieldOption"/>
    </div>
    <!--  时间字段 end-->

    <!--  时间统计范围 start-->
    <div class="data-setting-row">
      <div class="timeLabel">{{tp('StatisticalRange')}}</div>
      <TimeSelect
        :type="CHART_SUBCLASS_TYPE[moduleData.chartType as number] == ChartTypeEnum.HEATMAP ? E_TIME.NO_HOUR : E_TIME.NO_MINUTE"
        :disabled="CHART_SUBCLASS_TYPE[moduleData.chartType as number] == ChartTypeEnum.HEATMAP"
        v-model:value="timeIntervalData"
        :state="1"
        valueName="rangeValue"
        typeName="rangeType"/>
    </div>
    <!--  时间统计范围 end-->


    <!--  时间统计间隔 start-->
    <div class="data-setting-row">
      <div class="timeLabel">{{tp('TimeInterval')}}</div>
      <TimeSelect
        :type="moduleData.timeInterval?.rangeType"
        v-model:value="timeIntervalData"
        :state="1"
        valueName="value"
        typeName="type"/>
    </div>
    <!--  时间统计间隔 end-->
  </div>


</template>
<script setup lang="ts">

import {CHART_SUBCLASS_TYPE} from "/@/views/reports/chart/ts/ChartType";
import {ChartTypeEnum} from "/@/views/posture/enums/chartEnum";
import {inject, ref, watch, watchEffect} from "vue";
import {IModuleData} from "/@/views/reportChart/ts/IModule";
import {tp} from "/@/views/reportChart/ts/i18Utils";
import {DO_TIME_DATASOURCE} from "/@/views/reportChart/ts/dataSource";
import {E_TIME} from "/@/views/reportChart/enums/timeEnum";
import {TimeSelect} from "/@/views/reportChart/CommonSetting";
import {initTimeInterval} from "/@/views/reportChart/ts/initData";
/**
 * timeInterval
 * 1、asset和badactor需要配置 统计范围
 * 2、其他表查询时间同【整体页面显示的查询时间】（baseinfo里的rangeValue）
 */

const moduleData = inject('module',ref<IModuleData>({}));
const timeIntervalData = ref();
//修改，默认赋值
watch(()=>moduleData.value.yData?.timeInterval,(n)=>{
  console.log('moduleData.value.yData?.timeInterval==',n)
  if(n){
    timeIntervalData.value = moduleData.value.yData?.timeInterval;
  }else{
    timeIntervalData.value = initTimeInterval(moduleData.value.dataSource as string + (moduleData.value.logType ?? ''))
  }

},{deep:true,immediate:true})

watch(()=>timeIntervalData.value,(n)=>{
  moduleData.value.timeInterval = n;
},{deep:true})

</script>
<style scoped lang="less">
@import "../less/common";
.time-setting-wrapper{
  display: flex;
  flex-direction: column;
  gap: 12px;
  justify-content: center;
  .timeLabel{
    width:130px;
    flex-shrink: 0;
  }
}
</style>
