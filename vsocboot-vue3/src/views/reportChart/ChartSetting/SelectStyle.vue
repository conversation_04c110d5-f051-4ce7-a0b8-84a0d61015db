<template>
  <div class="style-group">
    <div
      class="select-group_item"

      :key="moduleData.chartType + '_' + item.id"
      v-for="(item) in CHART_SELECT_STYLE_MAP[CHART_SUBCLASS_TYPE[moduleData.chartType as number]]">
      <img :src="item.img" :alt="item.img"  :class="{'style-sub_checked':moduleData.chartStyleNo==item.id }"/>
      <div class="select-group_item_checkbox">
        <a-checkbox :value="item.id" :checked="moduleData.chartStyleNo==item.id" @change="checkChart"/>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import {inject, ref, Ref} from "vue";
import {IModuleData} from "/@/views/reportChart/ts/IModule";
import {CHART_SELECT_STYLE_MAP} from "/@/views/reportChart/chart/chartUtils";
import {CHART_SUBCLASS_TYPE} from "/@/views/reportChart/ts/ChartType";
const moduleData = inject('module',ref<IModuleData>({}));
function checkChart(e) {
  if (e.target.checked) {
    moduleData.value.chartStyleNo = e.target.value;
  } else {
    moduleData.value.chartStyleNo = 0;
  }
}
</script>



<style scoped lang="less">
.style-group{
  display: flex;
  flex-direction: row;
  gap: 16px;
  align-content: flex-start;
  flex-wrap: wrap;
  flex: 1;

  .select-group_item{
    width: 200px;
    height: 140px;
    border-radius: 8px;
    box-sizing: border-box;
    position: relative;
    img{
      width: 100%;
      height: 100%;
      &.style-sub_checked {
        border: 2px solid #308CFF;
      }
    }

    .select-group_item_checkbox{
      position: absolute;
      top: 4px;
      right: 4px;
    }
  }
}
</style>
