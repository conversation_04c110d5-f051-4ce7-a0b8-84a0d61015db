<template>
  <!-- 高级查询按钮start-->
  <a-button type="primary" @click="openQuery">{{ tp(buttonName) }}</a-button>
  <!-- 高级查询按钮 end-->

  <!--  系统表高级查询
   ===================================================-->
  <SystemTableSearchModel
    ref="tableSearchModelRef"
    :isReport="true"
    @search="saveSearch"
    :source="value!.systemValue"/>


</template>

<script setup lang="ts">

import SystemTableSearchModel from "/@/views/tableSearch/SystemTableSearchModel.vue";
import {ref,} from "vue";
import {useMessage} from "/@/hooks/web/useMessage";
import {propTypes} from "/@/utils/propTypes";
import {tp} from "/@/views/reportChart/ts/i18Utils";
import {getTabFieldList} from "/@/utils/ckTable";
import {FIELD_TYPE_NO} from "/@/views/reportChart/ts/dataSource";

const emit = defineEmits(['update:value']);
const {createMessage} = useMessage()
const props = defineProps({
  buttonName: propTypes.string.def('Filter'),
  value:Object
});
const tableSearchModelRef = ref();


/**
 * 查询
 */
function openQuery() {
  if (!props.value?.systemValue) {
    createMessage.warning('Please select system data')
    return;
  }

  let list = getTabFieldList(FIELD_TYPE_NO[props.value!.systemValue])
  if (!props.value?.str) {
    tableSearchModelRef.value.init(list, "");
  } else {
    tableSearchModelRef.value.init(list, props.value?.str);
  }
}

/**
 * 保存查询条件
 * @param data
 */
function saveSearch(where, data) {
  if(where){
    props.value!.str = JSON.stringify({advancedQueryFlag:true,whereList: where.whereList});
    props.value!.echo = data;
  }
  emit('update:value',props.value)
}
</script>
<style scoped lang="less">
@import "../less/common.less";
</style>
