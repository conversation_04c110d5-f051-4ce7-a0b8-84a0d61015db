<template>
  <div
    class="chart-config-wrapper"
    :key="'C_' + index"
     v-for="(item,index) in configComponent">
    <component :is="item"/>
    <div class="split" v-if="index < configComponent.length - 1"></div>
  </div>
</template>
<script setup lang="ts">

import {inject, ref} from "vue";
import {IModuleData} from "/@/views/reportChart/ts/IModule";
import {ChartNOEnum} from "/@/views/reportChart/enums/chartEnum";
import {ColumnSetting, XSetting, YSetting} from "/@/views/reportChart/ChartSetting/index";


const moduleData = inject('module',ref<IModuleData>({}));

//图形对应配置组件
const CHART_SETTING = {
  [ChartNOEnum.BASIC_BAR]: [YSetting,XSetting],
  [ChartNOEnum.BASIC_COLUMN]: [YSetting,XSetting],
  [ChartNOEnum.STACKED_BAR]: [YSetting,XSetting],
  [ChartNOEnum.STACKED_COLUMN]:  [YSetting,XSetting],
  [ChartNOEnum.RADIAL_BAR]:  [YSetting,XSetting],
  [ChartNOEnum.BASIC_LINE]:  [YSetting,XSetting],
  [ChartNOEnum.BIAXIAL_LINE]:  [YSetting,XSetting],
  [ChartNOEnum.BASIC_AREA]:  [YSetting,XSetting],
  [ChartNOEnum.STACKED_AREA]: [YSetting,XSetting],
  [ChartNOEnum.STREAMGRAPH]:  [YSetting,XSetting],
  [ChartNOEnum.BASIC_PIE]: [XSetting],
  [ChartNOEnum.DOUGHNUT]: [XSetting],
  [ChartNOEnum.SEMI_CIRCLE]: [XSetting],
  [ChartNOEnum.NIGHTINGALE]:[XSetting],
  [ChartNOEnum.DONUT]: [XSetting],
  [ChartNOEnum.BASIC_HEAT]: [XSetting],
  [ChartNOEnum.FUNNEL]: [XSetting],
  [ChartNOEnum.WORD_CLOUD]: [XSetting],
  [ChartNOEnum.SPEEDOMETER]: [XSetting],
  [ChartNOEnum.RADAR]: [YSetting,XSetting],
  [ChartNOEnum.BASIC_LIST]: [ColumnSetting],
  [ChartNOEnum.STATISTICAL_LIST]: [YSetting,XSetting],
  [ChartNOEnum.NUM]: [XSetting],
}
const configComponent = ref(CHART_SETTING[moduleData.value?.chartType as number]);

</script>
<style scoped lang="less">
.split{
  border-bottom: 1px solid @border-color;
  margin-bottom: 16px;
}
</style>
