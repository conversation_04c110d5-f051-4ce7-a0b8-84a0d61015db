<template>
 <s-block>
   <template #img>
     <img :src="yaxisImg" alt="xaxis" class="xaxis"/>
   </template>
   <template #title>
     {{ tp2(CHART_SUBCLASS_VALUE_NAME[chartType]) }}
   </template>
   <template #head-right>
     <XSettingType/>
   </template>
   <template #content>
     <component :is="fieldComponent[moduleData?.xData?.dataType as string]" type="xData"/>
   </template>
 </s-block>
</template>

<script setup lang="ts">
import {SBlock} from "/@/views/reportChart/CommonSetting";
import {tp2} from "/@/views/posture/ts/i18Utils";
import {CHART_SUBCLASS_VALUE_NAME} from "/@/views/reportChart/ts/ChartType";
import {inject, onBeforeUnmount, ref, Ref, watchEffect} from "vue";
import {IAxis, IConfigData, IModuleData} from "/@/views/reportChart/ts/IModule";
import {
  FieldCondition,
  FieldSpecifying,
  FieldValue,
  FieldValueCondition,
  FloorBox,
  XSettingType,
  SystemData
} from "/@/views/reportChart/ChartSetting/index";
import yaxisImg from "/@/assets/images/posture/yaxis.png";
import {ValueTypeEnum} from "/@/views/posture/enums/editPageEnum";
import {initValueAxisData} from "/@/views/reportChart/ts/initData";
import {CHART_SUBCLASS_TYPE, DATA_SPECIFYING_CHART} from "/@/views/reportChart/ts/ChartType";
import {ChartNOEnum, ChartTypeEnum} from "/@/views/reportChart/enums/chartEnum";

const moduleData = inject('module',ref<IModuleData>({}));
const valueAxisData = JSON.parse(JSON.stringify(initValueAxisData(ValueTypeEnum.FIELD)))  as IAxis;
moduleData.value.xData = valueAxisData;
const chartType = ref( moduleData.value.chartType as number)
watchEffect(()=>{
  if(moduleData.value.fillData && moduleData.value.fillData?.configData){//修改
    const configData = moduleData.value.fillData.configData as IConfigData;
    moduleData.value.xData = configData.valueAxisData;
  }
  if( moduleData.value.chartType == ChartNOEnum.BASIC_COLUMN || moduleData.value.chartType == ChartNOEnum.STACKED_BAR){
    chartType.value = moduleData.value.chartStyle?.baseStyle as number;
  }
})

const fieldComponent = {
  [ValueTypeEnum.FIELD]:CHART_SUBCLASS_TYPE[moduleData.value.chartType as number] == ChartTypeEnum.SUNBURST? FloorBox : FieldValue,
  [ValueTypeEnum.DESIGNATED]:DATA_SPECIFYING_CHART.includes(CHART_SUBCLASS_TYPE[moduleData.value.chartType as number]) ? FieldSpecifying : FieldCondition,
  [ValueTypeEnum.FIELDVALUE]:FieldValueCondition,
  [ValueTypeEnum.SYSTEM]:SystemData,
};

</script>

<style lang="less" scoped>
@import '../less/common.less';
.xaxis{
  width: 24px;
  height: 24px;
  transform: rotate(180deg);
  border-top: 8px solid #308CFF;
}
</style>
