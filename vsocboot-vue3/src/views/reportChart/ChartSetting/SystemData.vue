<template>
  <div class="data-setting-row items-center">
    <span class="w-100px">Log Field</span>
    <div class="flex flex-row flex-1 flex-shrink-0">
      <a-select v-model:value="systemData.fieldValue"
                :options="getFieldSystemOptions(moduleData.dataSource as string,tenantId)" @change="changeField"
                show-search/>
    </div>
  </div>
  <div class="data-setting-row items-center">
    <span class="w-100px">System data</span>
    <div class="flex flex-row flex-1 flex-shrink-0 gap8">
      <a-select v-model:value="systemData.systemValue"
                @change="changeSystemValue"
                :options="getSystemOptions(systemData.fieldValue,moduleData.dataSource as string)" show-search/>
      <SystemDataFilter v-model:value="systemData"/>
    </div>
  </div>
  <div class="data-setting-col setting-bg" v-if="isPreview">
    <div
      class="data-setting-row item-center pr"
      v-for="(item,index) in tableData"
      :key="'system' + item.label + index">
      <div class="setting-content_delNo">
        <span>{{ index + 1 }}#</span>
      </div>
      <a-input
        v-model:value="item.label"
        class="input-default"
        style="width: 150px"/>

      <div class="flex1">
        <a-input v-model:value="item.echo"  class="input-default" disabled/>
      </div>
    </div>
  </div>
  <div class="data-setting-row item-center" v-if="systemData.systemValue && systemData.fieldValue">
    <a-button type="primary"  @click="doYPreview"> {{ !isPreview ? t('common.preview') : t('common.FoldUp')}}</a-button>
  </div>
</template>
<script setup lang="ts">
import {inject, Ref, ref, watch} from "vue";
import {IModuleData, ISystemDataItem} from "/@/views/reportChart/ts/IModule";
import {SystemDataFilter} from "./index"
import {
  fieldSystemOptions,
  getFieldSystemOptions,
  getSystemOptions,
} from "/@/views/reportChart/ts/systemData";
import {useI18n} from "/@/hooks/web/useI18n";
import {defHttp} from "/@/utils/http/axios";

const props = defineProps({
  type: String,
})
const {t} = useI18n();
const moduleData = inject('module', ref<IModuleData>({}));
const tableData = ref([]);
const isPreview = ref(false);
const tenantId = inject<Ref>('tenantId',ref(''));
const systemData = ref(moduleData.value?.yData?.systemData ?? {
  fieldValue: '',
  systemValue: '',
  echo: '',//回显sql
  str: '',//查询条件
}) as ISystemDataItem;

watch(() => systemData.value, (n) => {
  moduleData.value[props.type as string]!.systemData = n as ISystemDataItem;
  isPreview.value = false;
}, {deep: true})

function changeField() {
  systemData.value.systemValue = '';
  systemData.value.echo = '';
  systemData.value.str = '';
  isPreview.value = false;
}
function changeSystemValue(){
  systemData.value.echo = '';
  systemData.value.str = '';
}
function doYPreview() {
  tableData.value = [];

  if(isPreview.value){
    isPreview.value = false;
    return;
  }

  defHttp.post({
    url: '/reports/reportData/getDataSetting', params: {
      datasource: moduleData.value.dataSource,
      yAxis: moduleData.value.yData,
      logType: moduleData.value.logType,
      socTenantId:tenantId.value
    }
  }).then((result) => {
    console.log('doYPreview-------', result)
    if (result && result.length > 0) {
      isPreview.value = true;
    }
    tableData.value = result;
  });
}
</script>
<style lang="less" scoped>
@import '../less/common.less';

.setting-bg {
  max-height: 212px;
  overflow-y: auto;
  background: rgba(255, 255, 255, 0.1);
  padding: 12px;
  border-radius: 4px;
}
</style>
