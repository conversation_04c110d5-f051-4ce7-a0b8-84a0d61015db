<template>
  <s-block :imgVisible="false">
    <template #title> Used color</template>
    <template #content>
        <div class="data-setting-col" >
          <ColorGroup
            :key="'used_'+ index"
            v-model:value="chartColors[(index - 1) % chartColors.length]"
            v-for="(index) in usedNum"/>
        </div>
    </template>
  </s-block>
  <s-block :imgVisible="false">
    <template #title> Unused color</template>
    <template #content>
      <div class="data-setting-col" v-if="untUsedNum > 0">
        <template  :key="'unUsed_'+ index"  v-for="(item,index) in chartColors">
          <ColorGroup
            v-model:value="chartColors[index % chartColors.length]"
            v-if="index > usedNum - 1"/>
        </template>
      </div>
    </template>
  </s-block>
</template>
<script setup lang="ts">
import {ColorGroup, SBlock} from "/@/views/reportChart/CommonSetting";
import {inject, ref, Ref, watch, watchEffect} from "vue";
import {IModuleData, IOptionItem} from "/@/views/reportChart/ts/IModule";
import {E_Theme} from "/@/views/posture/enums/theme";
import {THEME_CHART_COLOR} from "/@/views/reportChart/ts/chartColor";
import {StyleTypeEnum, ValueTypeEnum} from "/@/views/posture/enums/editPageEnum";
import {getLegendOption} from "/@/views/reportChart/chart/chartUtils";
import {ChartNOEnum} from "/@/views/posture/enums/chartEnum";
const dynamicLegendData = inject('dynamicLegendData',ref([]));
const moduleData = inject('module', ref<IModuleData>({}));
const currentTheme = inject<Ref>('currentTheme', ref(E_Theme.DEFAULT));
const chartColors = ref();
const usedNum = ref(0);
const untUsedNum = ref(0);
watch(()=>moduleData.value.styleSetting?.colors,(n)=>{
  if(n){
    chartColors.value = n;
  }else{
    chartColors.value = JSON.parse(JSON.stringify(THEME_CHART_COLOR[currentTheme.value]));
  }

},{deep:true,immediate:true})
watchEffect(() => {
  console.log('moduleData.value?.legendOption',moduleData.value?.legendOption)
  if(moduleData.value.chartType ==  ChartNOEnum.RADAR){
    usedNum.value = dynamicLegendData.value.length;
  }else{
    usedNum.value = moduleData.value?.legendOption?.length || getLegendOption(moduleData.value).length
  }
  if(moduleData.value.xData?.dataType == ValueTypeEnum.VOLUME){
    usedNum.value = 1;
  }
  untUsedNum.value = chartColors.value.length - usedNum.value;
})
watch(() => chartColors.value, (n) => {
  console.log('chartColors.value',chartColors.value)
  moduleData.value.styleSetting = {
    type: StyleTypeEnum.DEFAULT,
    colors: n
  }
},{deep:true,immediate:true})
</script>
<style scoped lang="less">
@import "../less/common";

</style>
