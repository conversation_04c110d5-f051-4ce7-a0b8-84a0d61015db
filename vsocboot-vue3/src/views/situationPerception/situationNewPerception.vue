<template>
  <a-row class="top-div">
    <a-col :span="6">
      <span class="top-div__text" @click="back">
        <Icon icon="ant-design:left-outlined"/>
        {{ InvestigationName }}
      </span>
    </a-col>
    <a-col :span="12" style="text-align: center;"/>
    <a-col :span="6" style="text-align: right;">
      <a-button @click="close" type="primary" style="margin-left: 5px;"
                v-if="hasPermission('investigation:close')">
        <span v-if="InvestigationData.status != 'Closed'">
          {{ t('common.closeText') }}
        </span>
        <span v-else>
          {{ t('common.reopenText') }}
        </span>
      </a-button>
    </a-col>
  </a-row>
  <a-row style="height: calc(100vh - 45px);">
    <a-col :span="20" style="height: 100%;max-width: 85.4%;flex: 0 0 85.4%;">
      <a-tabs style="height: 100%;" v-model:activeKey="activeKey">
        <a-tab-pane key="1" :tab="tp('Overview')">
          <a-row style="height: 100%;">
            <a-col :span="6" style="max-width: 14.6%;flex: 0 0 14.6%;height: calc(100vh - 45px)"
                   class="content-left">
              <div style="height: 50%;margin-right: 3px;padding: 5px 10px;"
                   class="content-div__background">
                <h2 style="position: relative;">
                  {{ tp('RiskObject') }}
                  <div style="position: absolute;top: 0px;right: 0px;">
                    <a-checkbox v-model:checked="riskObjectAllChecked"
                                @change="riskObjectAllChange"/>
                  </div>
                </h2>
                <div style="margin-bottom: 2px">
                  <a-input-search v-model:value="riskObjectSearch"/>
                </div>
                <div style="height: calc(50vh - 140px);overflow: auto;">
                  <div v-for="item in riskObject" :key="item.ip">
                    <div class="risk_object" v-if="item?.ip?.indexOf(riskObjectSearch) > -1">
                      <span>
                        <a-checkbox v-model:checked="item.checked" @change="riskObjectChange">
                          {{ item?.ip }}
                        </a-checkbox>
                      </span>
                      <span class="ip_img_div">
                        <img class="ip_img" src="../../assets/images/investigation/u77.svg"
                             v-if="item.type.indexOf('2') > -1 || item.type.indexOf('5') > -1"/>
                        <img class="ip_img" src="../../assets/images/investigation/u63.svg" v-else/>

                        <img class="ip_img" src="../../assets/images/investigation/u62.svg"
                             v-if="item.type.indexOf('4') > -1"/>
                        <img class="ip_img" src="../../assets/images/investigation/u70.svg" v-else/>
                      </span>
                    </div>
                  </div>
                </div>
              </div>
              <div
                style="height: calc(50% - 3px);margin-right: 3px;margin-top: 3px;padding: 5px 10px;"
                class="content-div__background">
                <h2 style="position: relative;">
                  {{ tp('RiskSourceIP') }}
                  <div style="position: absolute;top: 0px;right: 0px;">
                    <a-checkbox v-model:checked="riskSourceAllChecked"
                                @change="riskSourceAllChange"/>
                  </div>
                </h2>
                <div style="margin-bottom: 2px">
                  <a-input-search v-model:value="riskSourceSearch"/>
                </div>
                <div style="height: calc(50vh - 140px);overflow: auto;">
                  <div v-for="item in riskSource" :key="item.ip">
                    <div class="risk_object" v-if="item?.ip?.indexOf(riskSourceSearch) > -1">
                      <span>
                        <a-checkbox v-model:checked="item.checked" @change="riskObjectChange">
                          {{ item?.ip }}
                        </a-checkbox>
                      </span>
                      <span class="ip_img_div">
                        <img class="ip_img" src="../../assets/images/investigation/u77.svg"
                             v-if="item.type.indexOf('2') > -1 || item.type.indexOf('5') > -1"/>
                        <img class="ip_img" src="../../assets/images/investigation/u63.svg" v-else/>

                        <img class="ip_img" src="../../assets/images/investigation/u62.svg"
                             v-if="item.type.indexOf('4') > -1"/>
                        <img class="ip_img" src="../../assets/images/investigation/u70.svg" v-else/>
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            </a-col>
            <a-col :span="18" style="max-width: 85.4%;flex: 0 0 85.4%;height: calc(100vh - 105px)"
                   class="content-center">
              <div ref="echart1" style="height: calc(100vh - 105px);width: 72.93vw;"></div>
            </a-col>
          </a-row>
        </a-tab-pane>
        <a-tab-pane key="2" :tab="tp('riskEvents')">
          <div style="padding: 10px">
            <InRiskEventCard :eventData="eventData" :eventId="eventId" :queryData="queryData1"
                             @callDel="callDel"/>
            <a-button type="primary" @click="showRiskEvent" style="margin-top: 5px">
              {{ tp('AddRiskEvent') }}
            </a-button>
          </div>
        </a-tab-pane>
        <a-tab-pane key="3" :tab="tp('ml')">
          <div style="padding: 10px">
            <InMlCard :mlData="mlData" :mlId="mlId" :queryData="queryData2" @callDel="callDel"/>
            <a-button type="primary" @click="showMl" style="margin-top: 5px">
              {{ tp('AddML') }}
            </a-button>
          </div>
        </a-tab-pane>
        <a-tab-pane key="4" :tab="tp('badActor')">
          <div style="padding: 10px">
            <InBadActorCard :badActorData="badActorData" :badActorId="badActorId"
                            :queryData="queryData3" @callDel="callDel"/>

            <a-button type="primary" @click="showBadActor" style="margin-top: 5px">
              {{ tp('AddBadActor') }}
            </a-button>
          </div>
        </a-tab-pane>
        <a-tab-pane key="5" :tab="tp('suspiciousProcesses')">
          <div style="padding: 10px">

            <InProcessesCard :processesData="processesData" :processesId="processesId"
                             :queryData="queryData4" @callDel="callDel"/>
            <a-button type="primary" @click="showProcesses" style="margin-top: 5px">
              {{ tp('AddSuspiciousProcess') }}
            </a-button>
          </div>
        </a-tab-pane>
        <a-tab-pane key="6" :tab="tp('huntingResult')">
          <div style="padding: 10px">
          <InThreatHuntingCard :sourceData="threatHuntingData" :invId="InvestigationId"
                               :socTenantId="InvestigationData.socTenantId"
                               :queryData="queryData5" @callDel="callDel"/>
          </div>
        </a-tab-pane>

      </a-tabs>
    </a-col>

    <a-col :span="4" style="max-width: 14.56%;flex: 0 0 14.6%;" class="content-right">
      <div style="height: 100%;margin-left: 3px;"
           class="content-div__background">
        <situationPerceptionRight :id="InvestigationId" :statusFlag="statusFlag"
        />
      </div>
    </a-col>
  </a-row>
  <img class="ip_img img_1" style="display: none"
       src="../../assets/images/investigation/u77.svg"/>
  <img class="ip_img img_2" style="display: none"
       src="../../assets/images/investigation/u63.svg"/>
  <img class="ip_img img_3" style="display: none"
       src="../../assets/images/investigation/u62.svg"/>
  <img class="ip_img img_4" style="display: none"
       src="../../assets/images/investigation/u70.svg"/>


  <closeModules @register="registerCloseModule" @saveClose="saveClose"/>
  <InRiskEvent ref="riskEventRef" @addRiskEvent="addRiskEvent"/>
  <InMl ref="mlRef" @addMl="addMl"/>
  <InBadActor ref="badActorRef" @addBadActor="addBadActor"/>
  <InProcesses ref="processesRef" @addProcesses="addProcesses"/>
</template>

<script lang="ts" setup>
import {useRoute, useRouter} from 'vue-router';
import {useI18n} from "/@/hooks/web/useI18n";
import {Ref, ref, toRaw, watch} from 'vue'
import {useModal} from '/@/components/Modal';
import closeModules from './modules/closeModules.vue';
import InBadActorCard from "/@/views/investigation/modules/InBadActorCard.vue";
import InRiskEventCard from "/@/views/investigation/modules/InRiskEventCard.vue";
import InProcessesCard from "/@/views/investigation/modules/InProcessesCard.vue";
import InMlCard from "/@/views/investigation/modules/InMlCard.vue";
import situationPerceptionRight from './situationPerceptionRight.vue'
import {useUserStore} from "/@/store/modules/user";
import InBadActor from "/@/views/investigation/modules/InBadActor.vue";
import InMl from "/@/views/investigation/modules/InMl.vue";
import InRiskEvent from "/@/views/investigation/modules/InRiskEvent.vue";
import InProcesses from "/@/views/investigation/modules/InProcesses.vue";
import InThreatHuntingCard from '/@/views/investigation/modules/InThreatHuntingCard.vue'
import {
  queryById as hostById
} from "/@/views/aggregationriskeventhost/AggregationRiskEventHost.api";
import {loadInvestigationInfo, saveOrUpdate} from "/@/views/investigation/InvestigationVO.api";
import {
  listDstIpAll,
  listSrcDstIpAll,
  listSrcIpAll
} from "/@/views/risk/InvestigationRiskEventlogs.api";
import {useECharts} from "/@/hooks/web/useECharts";
import {EChartsOption} from "echarts";
import {delEvent} from "/@/views/situationPerception/situationPerception.api";
import {usePermission} from "/@/hooks/web/usePermission";

const {hasPermission} = usePermission();
const [registerCloseModule, {openModal}] = useModal();
const userStore = useUserStore();
const eventData = ref<any[]>([]);
const mlData = ref<any[]>([]);
const badActorData = ref<any[]>([]);
const processesData = ref<any[]>([]);
const threatHuntingData = ref<any[]>([]);
const {t} = useI18n();

function tp(name) {
  return t('routes.investigation.' + name);
}

const route = useRoute();
const router = useRouter();
const InvestigationId: string = route.query.id as string;
const InvestigationName = route.query.name;
const queryData1 = ref({})
const queryData2 = ref({})
const queryData3 = ref({})
const queryData4 = ref({})
const queryData5 = ref({})
const activeKey = ref("1")

const statusFlag = ref(true);
const InvestigationData = ref<any>({});

const eventId = ref<any>({})
const mlId = ref<any>({})
const badActorId = ref<any>({})
const processesId = ref<any>({})
const riskObjectSearch = ref("")
const riskSourceSearch = ref("")
let riskObjectFlag = false
let riskSourceFlag = false

/**
 * 加载调查数据
 */
loadInvestigationInfo({id: InvestigationId}).then((data) => {
  InvestigationData.value = data;
  console.log(data)
  if (data.status == 'Closed') {
    statusFlag.value = false
  }
  let riskEventlogs = data.riskEventlogs
  for (let i in riskEventlogs) {
    let type = riskEventlogs[i].type
    // 1:tbl_aggregation_risk_event_security,2:tbl_aggregation_risk_event_host,3:tbl_ml_event,4:tbl_bad_actors,5:tbl_suspicious_processes
    if (type == 1 || type == 2) {
      eventId.value[riskEventlogs[i].eventId] = riskEventlogs[i]
    } else if (type == 3) {
      mlId.value[riskEventlogs[i].eventId] = riskEventlogs[i]
    } else if (type == 4) {
      badActorId.value[riskEventlogs[i].eventId] = riskEventlogs[i]
    } else if (type == 5) {
      processesId.value[riskEventlogs[i].eventId] = riskEventlogs[i]
    }
  }

  let inRiskEvent_2Json = sessionStorage.getItem("inRiskEvent_2")
  if (inRiskEvent_2Json) {
    console.log(inRiskEvent_2Json)
    let json = JSON.parse(inRiskEvent_2Json)
    activeKey.value = json.type
    if (json.type == "2") {
      queryData1.value = json.data;
    } else if (json.type == "3") {
      queryData2.value = json.data;
    } else if (json.type == "4") {
      queryData3.value = json.data;
    } else if (json.type == "5") {
      queryData4.value = json.data;
    } else if (json.type == "6") {
      queryData5.value = json.data;
    }
    sessionStorage.removeItem("inRiskEvent_2")
  }
})
loadOverview()


const back = () => {
  // router.back()
  router.push({
    path: "/investigation/InvestigationVOList",
  });
}


const close = () => {
  openModal(true, {id: InvestigationData.value.id, status: InvestigationData.value.status});
}
const saveClose = (status) => {
  InvestigationData.value.status = status
  if (status == 'Close') {
    statusFlag.value = false
  } else {
    statusFlag.value = true
  }
}

/**
 * 删除行
 * @param type
 * @param data
 */
function callDel(type, data) {
  console.log(type, data)
  let eventId = ""
  if (type == 1) {
    if (data[0].type == '1') {
      type = 1
    } else if (data[0].type == '2') {
      type = 2
    }
    eventId = data[0].eventId
  } else {
    eventId = data[0].id
  }
  delEvent({type: type + "", eventId: eventId, id: InvestigationId}).then(() => {
    loadOverview()
  })

}

const riskEventRef = ref()

function showRiskEvent() {
  let ids: any = [];
  for (let i in eventData.value) {
    ids.push(eventData.value[i].eventId)
  }
  riskEventRef.value.open(ids.join(","), InvestigationData.value.socTenantId)
}

async function addRiskEvent(list, conclusion) {
  for (let i in list) {
    list[i].conclusion = conclusion
    list[i].conclusionBy = userStore.userInfo?.username
    list[i].avatar = userStore.userInfo?.avatar
    await loadEventInfo(list[i])
  }
  eventData.value = eventData.value.concat(list)
  saveRiskEvent()
}

function saveRiskEvent() {
  console.log(toRaw(eventData.value))
  let params: any = {
    id: InvestigationId
  }
  let riskEventId: any = []
  let riskEventType: any = []
  let riskEventConclusion: any = []
  let riskEventConclusionBy: any = []
  for (let i in eventData.value) {
    riskEventId.push(eventData.value[i].eventId)
    riskEventType.push(eventData.value[i].type)
    riskEventConclusion.push(eventData.value[i].conclusion ? eventData.value[i].conclusion : '-')
    riskEventConclusionBy.push(eventData.value[i].conclusionBy)
  }
  params.riskEventId = riskEventId.join(",")
  params.riskEventType = riskEventType.join(",")
  params.riskEventConclusion = riskEventConclusion.join(",")
  params.riskEventConclusionBy = riskEventConclusionBy.join(",")
  saveOrUpdate(params, true).then(() => {
    loadOverview()
  });
}


async function loadEventInfo(data) {
  if (data.type == "2") {
    await hostById({id: data.eventId}).then((data2) => {
      data.info = data2
    })
  }
}

const mlRef = ref()

function showMl() {
  let ids: any = [];
  for (let i in mlData.value) {
    ids.push(mlData.value[i].id)
  }
  mlRef.value.open(ids.join(","), InvestigationData.value.socTenantId)
}

function addMl(list, conclusion) {
  for (let i in list) {
    list[i].conclusion = conclusion
    list[i].conclusionBy = userStore.userInfo?.username
    list[i].avatar = userStore.userInfo?.avatar
  }
  mlData.value = mlData.value.concat(list)

  saveMl()
}

function saveMl() {
  let params: any = {
    id: InvestigationId
  }
  let mlId: any = []
  let mlConclusion: any = []
  let mlConclusionBy: any = []
  for (let i in mlData.value) {
    mlId.push(mlData.value[i].id)
    mlConclusion.push(mlData.value[i].conclusion ? mlData.value[i].conclusion : '-')
    mlConclusionBy.push(mlData.value[i].conclusionBy)
  }
  params.mlId = mlId.join(",")
  params.mlConclusion = mlConclusion.join(",")
  params.mlConclusionBy = mlConclusionBy.join(",")
  saveOrUpdate(params, true).then(() => {
    loadOverview()
  });
}

const badActorRef = ref()

function showBadActor() {
  let ids: any = [];
  for (let i in badActorData.value) {
    ids.push(badActorData.value[i].id)
  }
  badActorRef.value.open(ids.join(","), InvestigationData.value.socTenantId)
}

function addBadActor(list, conclusion) {
  for (let i in list) {
    list[i].conclusion = conclusion
    list[i].conclusionBy = userStore.userInfo?.username
    list[i].avatar = userStore.userInfo?.avatar
  }
  badActorData.value = badActorData.value.concat(list)
  saveBadActor()
}

function saveBadActor() {
  let params: any = {
    id: InvestigationId
  }
  let badActorId: any = []
  let badActorConclusion: any = []
  let badActorConclusionBy: any = []
  for (let i in badActorData.value) {
    badActorId.push(badActorData.value[i].id)
    badActorConclusion.push(badActorData.value[i].conclusion ? badActorData.value[i].conclusion :
      '-')
    badActorConclusionBy.push(badActorData.value[i].conclusionBy)
  }
  params.badActorId = badActorId.join(",")
  params.badActorConclusion = badActorConclusion.join(",")
  params.badActorConclusionBy = badActorConclusionBy.join(",")

  saveOrUpdate(params, true).then(() => {
    loadOverview()
  });
}

const processesRef = ref()

function showProcesses() {
  let ids: any = [];
  for (let i in processesData.value) {
    ids.push(processesData.value[i].id)
  }
  processesRef.value.open(ids.join(","), InvestigationData.value.socTenantId)
}

function addProcesses(list, conclusion) {
  for (let i in list) {
    list[i].conclusion = conclusion
    list[i].conclusionBy = userStore.userInfo?.username
    list[i].avatar = userStore.userInfo?.avatar
  }
  processesData.value = processesData.value.concat(list)

  saveProcesses()
}

function saveProcesses() {
  let params: any = {
    id: InvestigationId
  }
  let processesId: any = []
  let processesConclusion: any = []
  let processesConclusionBy: any = []
  for (let i in processesData.value) {
    processesId.push(processesData.value[i].id)
    processesConclusion.push(processesData.value[i].conclusion ? processesData.value[i].conclusion : '-')
    processesConclusionBy.push(processesData.value[i].conclusionBy)
  }
  params.processesId = processesId.join(",")
  params.processesConclusion = processesConclusion.join(",")
  params.processesConclusionBy = processesConclusionBy.join(",")

  saveOrUpdate(params, true).then(() => {
    loadOverview()
  });
}


const riskObject = ref<any[]>([])

function listDstIpAllData() {
  riskObjectFlag = false
  listDstIpAll({investigationId: InvestigationId}).then((data) => {
    console.log(data)
    for (let i in data) {
      data[i].checked = true
    }
    riskObject.value = data
    riskObjectFlag = true
    // refEchart()
    loadEchart1Data()
  })
}

function riskObjectChange() {
  console.log(toRaw((riskObject.value)))
  console.log(toRaw((riskSource.value)))
  refEchart()
}

const riskObjectAllChecked = ref(true)

function riskObjectAllChange() {
  for (let i in riskObject.value) {
    riskObject.value[i].checked = riskObjectAllChecked.value
  }
  riskObjectChange()
}


const riskSourceAllChecked = ref(true)

function riskSourceAllChange() {
  for (let i in riskSource.value) {
    riskSource.value[i].checked = riskSourceAllChecked.value
  }
  riskObjectChange()
}

const riskSource = ref<any[]>([])


function listSrcIpAllData() {
  riskSourceFlag = false
  listSrcIpAll({investigationId: InvestigationId}).then((data) => {
    console.log(data)
    for (let i in data) {
      data[i].checked = true
    }
    riskSource.value = data
    riskSourceFlag = true
    // refEchart()
    loadEchart1Data()
  })
}

const echart1 = ref<HTMLDivElement | null>(null);
const {setOptions: setOptions1} = useECharts(echart1 as Ref<HTMLDivElement>);

/**
 * 加载调查显示图形数据，源IP和目的IP
 */
function loadOverview() {
  listSrcIpAllData()
  listDstIpAllData()
}

const tooltipItem = ref<any>({})

function refEchart() {
  if (linksFlag && riskSourceFlag && riskObjectFlag) {
    // linksFlag = false
    // riskSourceFlag = false
    // riskObjectFlag = false

    let nodes: any = [];
    let set = {}
    for (let i in riskObject.value) {
      if (riskObject.value[i].checked) {
        set[riskObject.value[i].ip] = riskObject.value[i].type.split(",")
      }
    }
    for (let i in riskSource.value) {
      if (riskSource.value[i].checked) {
        if (set[riskSource.value[i].ip]) {
          set[riskSource.value[i].ip].push(...riskSource.value[i].type.split(","))
        } else {
          set[riskSource.value[i].ip] = riskSource.value[i].type.split(",")
        }
      }
    }
    let source = {}
    let target = {}
    for (let i in links) {
      source[links[i].source] = 1
      target[links[i].target] = 1
    }

    for (let ip in set) {
      nodes.push({
        name: ip,
        type: set[ip],
        source: source[ip],
        target: target[ip]
      })
    }
    let img1 = document.getElementsByClassName("img_1")
    let img2 = document.getElementsByClassName("img_2")
    let img3 = document.getElementsByClassName("img_3")
    let img4 = document.getElementsByClassName("img_4")

    console.log(nodes, links)
    let option: EChartsOption = {
      tooltip: {
        triggerOn: 'click',
        enterable: true,
        formatter: function (params) {
          console.log(params)
          if (params.dataType == 'edge') {
            return params.name
          }
          // 1:tbl_aggregation_risk_event_security,2:tbl_aggregation_risk_event_host,3:tbl_risk_ml,4:tbl_bad_actors,5:tbl_suspicious_processes
          let data = params.data
          let type = data.type
          let html = ""
          if (type.indexOf("1") > -1 || type.indexOf("2") > -1) {
            html += "<div id='riskEvent'><a>Risk Event</a></div>"
          }
          if (type.indexOf("4") > -1) {
            html += "<div id='badActors'><a>Bad Actors</a></div>"
          }
          tooltipItem.value = params
          console.log(html)
          return html
        }
      },
      series: [
        {
          name: '',
          type: 'graph',
          layout: 'force',
          data: nodes,
          links: links,
          roam: true,
          // draggable: true,
          symbolSize: 50,
          zoom: 1.5,
          edgeSymbol: ['none', 'arrow'],
          edgeSymbolSize: [10, 10],
          label: {
            show: true,
            align: 'center',
            formatter: function (params) {
              let d: any = params.data
              let a = ''
              let b = ''
              if (d.type.indexOf('2') > -1 || d.type.indexOf('5') > -1) {
                a = '{b1|}'
              } else {
                a = '{b2|}'
              }
              if (d.type.indexOf('4') > -1) {
                b = '{b3|}'
              } else {
                b = '{b4|}'
              }
              return '{a|' + d.name + '}\n' + a + b
            },
            rich: {
              a: {
                color: '#ffffff'
              },
              b1: {
                backgroundColor: {
                  image: img1[0] as any
                },
                height: 20,
                width: 20
              },
              b2: {
                backgroundColor: {
                  image: img2[0] as any
                },
                height: 20,
                width: 20
              },
              b3: {
                backgroundColor: {
                  image: img3[0] as any
                },
                height: 20,
                width: 20
              },
              b4: {
                backgroundColor: {
                  image: img4[0] as any
                },
                height: 20,
                width: 20
              },
            },
          },
          itemStyle: {
            color: "#1A1B1F"
          },
          force: {
            repulsion: 300,
            edgeLength: 60
          }
        }
      ]
    };
    setOptions1(option)
  }
}

let links: any = [];
let linksFlag = false

function loadEchart1Data() {
  console.log('riskObjectFlag', riskObjectFlag, 'riskObjectFlag', riskObjectFlag)
  if (riskObjectFlag && riskObjectFlag) {
    let src = riskSource.value
    let dst = riskObject.value
    let srcIp: any = []
    let dstIp: any = []
    for (let i in src) {
      srcIp.push(src[i].ip)
    }
    for (let i in dst) {
      dstIp.push(dst[i].ip)
    }
    listSrcDstIpAll({
      investigationId: InvestigationId,
      srcIp: srcIp.join(','),
      dstIp: dstIp.join(',')
    }).then((data) => {
      console.log(data)
      links = [];
      for (let i in data) {
        links.push({
          source: data[i].src_ip,
          target: data[i].dst_ip
        })
      }
      linksFlag = true
      refEchart()
    })
  }

}


watch(() => tooltipItem.value, () => {
  console.log(toRaw(tooltipItem.value))
  let tooltipButton1 = document.getElementById("riskEvent")
  let tooltipButton2 = document.getElementById("ml")
  let tooltipButton3 = document.getElementById("badActors")
  let tooltipButton4 = document.getElementById("processes")

  let ip = tooltipItem.value?.data?.name
  let source = tooltipItem.value?.data?.source
  let target = tooltipItem.value?.data?.target
  let type = tooltipItem.value?.data?.type
  if (tooltipButton1) {
    tooltipButton1.onclick = function () {
      if (source) {
        queryData1.value = {srcIp: ip}
      } else if (target) {
        queryData1.value = {dstIp: ip}
      }
      if (type.indexOf("1") > -1) {
        queryData1.value['riskType'] = '1'
      } else if (type.indexOf("2") > -1) {
        queryData1.value['riskType'] = '2'
      }

      activeKey.value = "2"
    }
  }
  if (tooltipButton2) {
    tooltipButton2.onclick = function () {
      if (source) {
        queryData2.value = {srcIp: ip}
      } else if (target) {
        queryData2.value = {dstIp: ip}
      }
      activeKey.value = "3"
    }
  }
  if (tooltipButton3) {
    tooltipButton3.onclick = function () {
      if (source) {
        queryData3.value = {ip: ip}
      } else if (target) {
        queryData3.value = {ip: ip}
      }
      activeKey.value = "4"
    }
  }
  if (tooltipButton4) {
    tooltipButton4.onclick = function () {
      if (source) {
        queryData4.value = {ip: ip}
      } else if (target) {
        queryData4.value = {ip: ip}
      }
      activeKey.value = "5"
    }
  }

})


</script>

<style lang="less" scoped>
.top-div {
  background-color: @dark-bg2;
  padding: 5px;
}

.top-div__text {
  line-height: 32px;
  cursor: pointer;
}

.top-div__text.tabs {
  margin: 0 10px;
  padding: 12px;
  cursor: pointer;
}

.active {
  background-color: @dark-bg1;
}

.content-div__background {
  background-color: @dark-bg2;
}

:deep(.ant-tabs-content) {
  height: 100%;
}

:deep(.ant-tabs-nav) {
  margin-bottom: 0;
}

:deep(.ant-tabs-nav-wrap) {
  padding-left: 20px;
}

.content-left {

}

.content-center {

}

.content-right {

}

.border-right {
  border-right: 1px solid @border-color;
}

.border-bottom {
  border-bottom: 1px solid @border-color;
}

.background-span {
  background-color: @m-text-bg;
  color: @m-text-color;
  padding: 0 5px;
  margin: 5px;
  display: inline-block;
  border-radius: 5px;
}

:deep(.ant-collapse > .ant-collapse-item > .ant-collapse-header .ant-collapse-extra) {
  margin-left: 0;
  width: 100%;
}

:deep(.ant-collapse > .ant-collapse-item > .ant-collapse-header) {
  padding: 0;
  padding-right: 40px;
}

.ip_img {
  width: 20px;
  height: 20px;
}

.risk_object {
  display: flex;

  .ip_img_div {
    display: flex;
  }
}

.risk_object > span:first-child {
  display: inline-block;
  width: calc(100% - 50px);
}

:deep(.ant-tabs-nav) {
  position: absolute;
  top: -30px;
  transform: translate(-50%, 0px);
  margin-left: 50%;
}

:deep(.ant-tabs-top > .ant-tabs-nav::before) {
  border: 0;
}
</style>
