import {defHttp} from '/@/utils/http/axios';
import {Modal} from 'ant-design-vue';
import {useI18n} from "/@/hooks/web/useI18n";
const {t} = useI18n();
enum Api {
  list = '/mdps/MDPSManagement/list',
  save='/mdps/MDPSManagement/add',
  edit='/mdps/MDPSManagement/edit',
  deleteOne = '/mdps/MDPSManagement/delete',
  deleteBatch = '/mdps/MDPSManagement/deleteBatch',
  importExcel = '/mdps/MDPSManagement/importExcel',
  exportXls = '/mdps/MDPSManagement/exportXls',
}
/**
 * 导出api
 * @param params
 */
export const getExportUrl = Api.exportXls;
/**
 * 导入api
 */
export const getImportUrl = Api.importExcel;
/**
 * 列表接口
 * @param params
 */
export const list = (params) =>
  defHttp.get({url: Api.list, params});

/**
 * 删除单个
 * @param params
 * @param handleSuccess
 */
export const deleteOne = (params,handleSuccess) => {
  Modal.confirm({
    title: t('common.delText'),
    content: t('common.delConfirmText') + "?",
    okText: t('common.delText'),
    cancelText: t('common.cancelText'),
    wrapClassName: 'delete_confirm',
    onOk: () => {
      return defHttp.delete({url: Api.deleteOne, params}, {joinParamsToUrl: true}).then(() => {
        handleSuccess();
      });
    }
  });


}
/**
 * 批量删除
 * @param params
 * @param handleSuccess
 */
export const batchDelete = (params, handleSuccess) => {
  Modal.confirm({
    title: t('common.delConfirmText'),
    content: t('common.delContent'),
    okText: t('common.delText'),
    cancelText: t('common.cancelText'),
    wrapClassName: 'delete_confirm',
    onOk: () => {
      return defHttp.delete({url: Api.deleteBatch, data: params}, {joinParamsToUrl: true}).then(() => {
        handleSuccess();
      });
    }
  });
}
/**
 * 保存或者更新
 * @param params
 * @param isUpdate 是否是更新数据
 */
export const saveOrUpdate = (params, isUpdate) => {
  let url = isUpdate ? Api.edit : Api.save;
  return defHttp.post({url: url, params});
}
