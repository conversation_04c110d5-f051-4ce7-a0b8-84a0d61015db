<template>
  <a-modal v-model:visible="visible" :title="t('common.Apply')" width="1200px" :footer="null"
           :destroy-on-close="true"
           :confirm-loading="loading">
    <template #closeIcon>
      <div class="ax-icon-button ax-icon-large">
        <span class="soc ax-com-Close ax-icon"></span>
      </div>
    </template>
    <template  v-if="!hideTenantTab">
      <step :stepName="stepList" >
        <template #actions>
          <a-button @click="doBack" v-if="current > 0" size="small" class="ax-btn-pre">
            <span class="soc ax-com-Arrow-left ax-icon"></span>
            {{t('common.back') }}
          </a-button>

          <a-button   @click="doNext" size="small" class="ax-btn-suf"
                      v-if="current < stepList.length - 1">
            {{ t('common.nextText') }}
            <span class="soc ax-com-Arrow-right ax-icon"></span>
          </a-button>
          <a-button type="primary" @click="doSubmitApply" size="small"
                    v-if="(current == stepList.length - 1)">
            {{ t('common.Apply') }}
          </a-button>

        </template>
        <template #content>
          <div v-if="current == 0">
            <RuleTenantList v-model:value="socTenantId" v-if="!loading"/>
          </div>
          <div v-if="current == 1">
            <TenantLogSource
              @ok="setApply" :checkedKeyData="applyData"
              :socTenant-id="socTenantId"/>
          </div>
        </template>
      </step>
    </template>
    <template v-else>
      <div style="position:absolute;right: 16px; margin-top:16px">
        <a-button
          type="primary"
          @click="doSubmitApply">
          {{ t('common.Apply') }}
        </a-button>
      </div>
      <TenantLogSource
        @ok="setApply" :checkedKeyData="applyData"
        :socTenantId="socTenantId"/>
    </template>



  </a-modal>
</template>

<script setup lang="ts">
import {provide, ref, unref} from "vue";
import {useI18n} from "/@/hooks/web/useI18n";
import RuleTenantList from "/@/views/parseRule/modules/RuleTenantList.vue";

import TenantLogSource from "/@/views/parseRule/TenantLogSource.vue";
import {getTenantMode} from "/@/utils/auth";
import {Step} from "/@/components/Step/index";

const {t} = useI18n();

function tp(name, params?) {
  return t('routes.parserule.' + name, params);
}

const stepList = ref([tp('BatchPublic'), tp('BulkApply')]);

const current = ref(0);
provide('current', current);
const socTenantId = ref("");
const applyData = ref<any[]>([]);
const visible = ref(false);
const loading = ref(false);
const hideTenantTab = ref(false);
let runCallback;


const doBack = () => {
  current.value--;
}
const doNext = () => {
  current.value++;
}

function setApply(data) {
  applyData.value = data;
}

const doSubmitApply = () => {
  console.log(socTenantId.value)
  console.log(applyData.value)
  runCallback && runCallback(unref(socTenantId), unref(applyData));
  visible.value = false;
}

const showModal = (tenantId: string, callback: Function) => {
  if (tenantId || !getTenantMode()) {
    current.value = 1;
    socTenantId.value = tenantId;
    hideTenantTab.value = true;
  } else {
    current.value = 0;
    socTenantId.value = '';
    hideTenantTab.value = false;
  }
  runCallback = callback;
  applyData.value = [];
  visible.value = true;
};


defineExpose({
  showModal
})

</script>


<style scoped lang="less">

/deep/.ax-step-back {
  display: none!important;
}

</style>
