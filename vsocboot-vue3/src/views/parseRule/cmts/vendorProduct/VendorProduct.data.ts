import {BasicColumn} from '/@/components/Table';
import {FormSchema} from '/@/components/Table';
import {useI18n} from "/@/hooks/web/useI18n";

const {t} = useI18n();


export const columns: BasicColumn[] = [
  {
    title: t('routes.VendorProduct.name'),
    dataIndex: 'name',
    slots: {customRender: 'name'},
  },
  {
    title: t('routes.VendorProduct.source'),
    dataIndex: 'source',
    customRender: ({value}) => {
      if (value === 1) {
        return t('routes.VendorProduct.manual');
      } else {
        return t('routes.VendorProduct.system');
      }
    },
    width: 150
  },
  {
    title: t('routes.VendorProduct.createBy'),
    dataIndex: 'createBy',
    customRender: (opt: any) => {
      if (opt.record.source === 2) {
        return t('common.none');
      } else {
        return opt.record.createBy;
      }
    },
    width: 120
  },
];


export const formSchema: FormSchema[] = [
  {label: '', field: 'id', component: 'Input', show: false},
  {
    label: t('routes.VendorProduct.name2'),
    field: 'name',
    component: 'Input',
    componentProps: {
      maxLength: 64
    },
    required: true,
  },
  {
    label: t('routes.VendorProduct.icon'),
    field: 'icon',
    component: 'Input',
    slot: "icon",
    required: true,
  }
];

export const updFormSchema: FormSchema[] = [
  {label: '', field: 'id', component: 'Input', show: false},
  {
    label: t('routes.VendorProduct.name2'),
    field: 'name',
    component: 'Text'
  },
  {
    label: t('routes.VendorProduct.icon'),
    field: 'icon',
    component: 'Input',
    slot: "icon",
    required: true,
  }
];


export const searchFormSchema: FormSchema[] = [
  {
    label: '',
    field: 'name',
    component: 'JInput',
    componentProps: {
      search:true,
      placeholder: t('routes.VendorProduct.name2')
    }
  },
];
