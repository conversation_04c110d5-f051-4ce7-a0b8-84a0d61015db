import {defHttp} from '/@/utils/http/axios';


enum Api {
  list = '/vendorProduct/vendorProduct/list',
  queryList = '/vendorProduct/vendorProduct/queryList',
  save = '/vendorProduct/vendorProduct/add',
  queryById = '/vendorProduct/vendorProduct/queryById',
  edit = '/vendorProduct/vendorProduct/edit',
  deleteOne = '/vendorProduct/vendorProduct/delete',
}


/**
 * 列表接口
 * @param params
 */
export const list = (params) =>
  defHttp.get({url: Api.list, params});

export const queryList = (params) =>
  defHttp.get({url: Api.queryList, params});

export const queryById = (params) =>
  defHttp.get({url: Api.queryById, params});

/**
 * 删除单个
 * @param params
 * @param handleSuccess
 */
export const deleteOne = (params, handleSuccess) => {
  return defHttp.delete({url: Api.deleteOne, params}, {joinParamsToUrl: true}).then(() => {
    handleSuccess();
  });
}

/**
 * 保存或者更新
 * @param params
 * @param isUpdate 是否是更新数据
 */
export const saveOrUpdate = (params, isUpdate) => {
  const url = isUpdate ? Api.edit : Api.save;
  return defHttp.post({url: url, params});
}
