<template>
  <div class="flex flex-row" :class="heightClass">
    <div 
      class="w-64px flex items-center justify-center ax-border-right" 
      @click="navigatePrev" 
      :class="{ 'cursor-pointer': canNavigatePrev, 'cursor-not-allowed': !canNavigatePrev }"
    >
      <div class="ax-icon-button" :class="{ 'is-disabled': !canNavigatePrev }">
        <span class="soc ax-com-Arrow-left ax-icon"></span>
      </div>
    </div>
    <div class="p-24px fcolor3 flex-1 h-[100%] overflow-y-auto">
      <div v-if="currentLog">{{ currentLog }}</div>
      <div v-else class="text-center text-gray-400">{{ noDataMessage }}</div>
    </div>
    <div
      class="w-64px flex items-center justify-center ax-border-left"
      @click="navigateNext"
      :class="{ 'cursor-pointer': canNavigateNext, 'cursor-not-allowed': !canNavigateNext }"
    >
      <div class="ax-icon-button" :class="{ 'is-disabled': !canNavigateNext }">
        <span class="soc ax-com-Arrow-right ax-icon"></span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watchEffect } from 'vue';

interface LogData {
  raw_log: string;
  [key: string]: any;
}

interface StepData {
  checkedLogs: LogData[];
  [key: string]: any;
}

const props = defineProps<{
  value: StepData;
  heightClass: string;
  noDataMessage?: string;
}>();

const carouselIndex = ref(0);
const stepData = ref<StepData>({ checkedLogs: [] });

// Computed properties for navigation
const canNavigatePrev = computed(() => carouselIndex.value > 0);
const canNavigateNext = computed(() => {
  const logsLength = stepData.value.checkedLogs?.length || 0;
  return carouselIndex.value < logsLength - 1 && logsLength > 0;
});

// Get current log with safety check
const currentLog = computed(() => {
  const logs = stepData.value.checkedLogs || [];
  return logs.length > 0 ? logs[carouselIndex.value]?.raw_log : null;
});

// Navigation functions
const navigatePrev = () => {
  if (canNavigatePrev.value) {
    carouselIndex.value--;
  }
};

const navigateNext = () => {
  if (canNavigateNext.value) {
    carouselIndex.value++;
  }
};

// Reset carousel index when data changes
watchEffect(() => {
  if (props.value) {
    stepData.value = props.value;
    carouselIndex.value = 0;
  }
});
</script>
