import JsonFormat from '/@/assets/images/step/JsonFormat.png';
// import ApacheLog from '/@/assets/images/step/ApacheLog.png';
// import NginxLog from '/@/assets/images/step/NginxLog.png';
// import UsingParsingFile from '/@/assets/images/step/UsingParsingFile.png';
import RegularExpressions from '/@/assets/images/step/RegularExpressions.png';
import Separator from '/@/assets/images/step/Separator.png';
import {useI18n} from "/@/hooks/web/useI18n";
import {buildUUID} from "/@/utils/uuid";

const {t} = useI18n();

function tp(name) {
  return t('routes.parserule.' + name);
}

export const ParseMethodData = [
  {
    label: tp('JsonFormat'),
    value: 1,
    icon: JsonFormat,
    tip: tp('jsonTip')
  }, {
    label: tp('RegularExpressions'),
    value: 2,
    icon: RegularExpressions,
    tip: tp('RegularExpressionsTip')
  },
  // {
  //   label: tp('Separator'),
  //   value: 3,
  //   icon: Separator,
  //   tip: tp('SeparatorTip')
  // }
  // , {
  //   label: tp('NginxLog'),
  //   value: 4,
  //   icon: NginxLog,
  //   tip: tp('NginxLogTip'),
  // }, {
  //   label: tp('ApacheLog'),
  //   value: 5,
  //   icon: ApacheLog,
  //   tip: tp('ApacheLogTip')
  // }, {
  //   label: tp('UsingParsingFile'),
  //   value: 6,
  //   icon: UsingParsingFile,
  //   tip: tp('UsingParsingFileTip')
  // }
  ];


export function parseStepData(data) {
  data.parsedFields = JSON.parse(data.parsedFieldsJson) ?? [];
  data.checkedLogs = JSON.parse(data.checkedLogs) ?? [];
  data.applyData = data.applyData ? JSON.parse(data.applyData) : [];
  data.jsonParseResult = JSON.parse(data.jsonParseResult) ?? [];
  return data;
}

let dataSource: any = [];

export function parseJsonObject(arr, obj) {
  const isArray = Array.isArray(obj);
  const keys: any[] = Object.keys(obj);
  //给每一项都设置一个uuid，防止不同子集之间属性名一样导致问题,如user和user2中的属性
  // {
  //   "thread": "main",
  //   "user": {
  //     "ip": "***********",
  //    },
  //   "user2": {
  //     "ip": "***********",
  //   }
  // }
  keys.forEach(key => {
    const data: any = {
      fieldName: key,
      newValue: '',
      sFileName: '',
      uuid: buildUUID(),
      isArray: isArray
    };
    if (obj[key] && typeof (obj[key]) == "object") {
      data.children = parseJsonObject([], obj[key]);
    } else {
      data.value = obj[key];
    }
    arr.push(data);
  });
  return arr;
}

export function jsonParseLog(test) {
  try {
    dataSource = [];
    const json = JSON.parse(test);
    const isArray = Array.isArray(json);
    const keys: any[] = Object.keys(json);
    keys.forEach(key => {
      const data: any = {fieldName: key, newValue: '', sFileName: '', uuid: buildUUID(),isArray:isArray};
      if (json[key] && typeof (json[key]) == "object") {
        data.children = parseJsonObject([], json[key]);
      } else {
        data.value = json[key];
      }
      dataSource.push(data);
    });
    console.log('dataSource', dataSource)
    return dataSource;
  } catch (e) {
    console.log(e)
    return -1;
  }
}














