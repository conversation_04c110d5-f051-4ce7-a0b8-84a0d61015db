<template>
  <div class="h-[100%] w-[100%] rule_block">
    <step :stepName="stepList" @back="closeRetun">
      <template #backBtnName>{{ t('common.goBack') }}</template>
      <template #actions>
        <!-- 上一步 start -->
        <a-button v-if="current > 0" size="small" @click="doBack"  class="ax-btn-pre">
          <span class="soc ax-com-Arrow-left ax-icon"></span>
          {{ t('workflow.workflow.Back') }}
        </a-button>
        <!-- 上一步 end -->

        <!-- 下一步 start -->
<!--        <a-button v-if="current < stepList.length-1 && current != 3" type="primary" size="small" @click="doNext"  class="ax-btn-suf">-->
<!--          {{ t('common.nextText') }}-->
<!--          <span class="soc ax-com-Arrow-right ax-icon"></span>-->
<!--        </a-button>-->
        <a-button v-if="current < stepList.length-1 " type="primary" size="small" @click="doNext"  class="ax-btn-suf">
          {{ t('common.nextText') }}
          <span class="soc ax-com-Arrow-right ax-icon"></span>
        </a-button>
        <!-- 下一步 end -->

        <!-- 保存 start -->
        <a-button type="primary" :loading="saveLoading" v-if="(current == 2 || current == stepList.length-1) && !stepData.ids" size="small"
                  @click="doSubmitData">
          {{ t('common.Submit') }}
        </a-button>
        <!-- 保存 end -->
      </template>
      <template #content>
        <!-- step content =======-->
        <div class="pt-24px ax-w-1000 flex flex-col mx-auto">
          <!-- step 1-->
<!--          <div class="step_block py-16px" v-if="current == 0">-->
<!--            <a-form layout="vertical" :model="stepData" class="step-content_1">-->
<!--              <div class="ax-border-bottom px-16px mb-24px">-->
<!--                <a-form-item>-->
<!--                  <a-input v-model:value="stepData.ruleName" :placeholder="tp('RuleName')"/>-->
<!--                </a-form-item>-->
<!--              </div>-->
<!--              <div class="flex flex-row flex-wrap gap-8px px-16px">-->
<!--                <div class="step1-input">-->
<!--                  <a-form-item :label="tp('logtype')">-->
<!--                    <a-select v-model:value="stepData.logType" :options="ThreatHuntingLogOptions(t)"-->
<!--                              @change="changeLogType"></a-select>-->
<!--                  </a-form-item>-->
<!--                </div>-->
<!--                <div class="step1-input">-->
<!--                  <a-form-item :label="tp('vendorProduct')">-->
<!--                    <div v-if="vendorProductData?.id" class="flex flex-row items-center gap-16px">-->
<!--                      <div class="flex flex-row items-center gap-8px">-->
<!--                        <img :src="render.renderUploadImageSrc(vendorProductData.icon)" alt=""-->
<!--                             class="w-32px h-32px rounded-1/2"/>-->
<!--                        <div class="font13 font-600 fcolor1">{{ vendorProductData.name }}</div>-->
<!--                      </div>-->
<!--                      <div class="flex flex-row items-center gap-4px">-->
<!--                        <div class="ax-icon-button primary-button" @click="showVendorProduct">-->
<!--                          <span class="soc ax-com-Update ax-icon"></span>-->
<!--                        </div>-->
<!--                        <div class="ax-icon-button primary-button" @click="delVendorProduct">-->
<!--                          <Icon icon="ant-design:delete-outlined" class="ax-icon"/>-->
<!--                        </div>-->
<!--                      </div>-->
<!--                    </div>-->
<!--                    <div v-else class="ax-icon-button primary-button" @click="showVendorProduct">-->
<!--                      <span class="soc ax-com-Increase ax-icon"></span>-->
<!--                    </div>-->
<!--                  </a-form-item>-->
<!--                </div>-->
<!--                <div class="step1-input">-->
<!--                  <a-form-item :label="tp('vendorCategory')">-->
<!--                    <JSearchSelect v-model:value="stepData.vendorCategory" dict="vendorCategoryDict"-->
<!--                                   :allowClear="true"/>-->
<!--                  </a-form-item>-->
<!--                </div>-->
<!--                <div class="step1-input">-->
<!--                  <a-form-item :label="tp('vendorService')">-->
<!--                    <JSearchSelect v-model:value="stepData.vendorService" dict="vendorServiceDict"-->
<!--                                   :allowClear="true"/>-->
<!--                  </a-form-item>-->
<!--                </div>-->
<!--                <div class="step1-input" v-if="showHomeTenant || (isAdmin && tenantType === 2)">-->
<!--                  <a-form-item v-if="showHomeTenant" :label="t('common.tenant')">-->
<!--                    <JSearchSelect v-model:value="stepData.socTenantId" dict="tenantActiveDict"/>-->
<!--                  </a-form-item>-->
<!--                  <a-form-item v-else-if="isAdmin && tenantType === 2" :label="t('common.tenant')">-->
<!--                    <JSearchSelect v-model:value="stepData.socTenantId" dict="tenantActiveDict"-->
<!--                                   :ifDisable="true"/>-->
<!--                  </a-form-item>-->
<!--                </div>-->
<!--                <div class="step1-input">-->
<!--                  <a-form-item>-->
<!--                    <template #label>-->
<!--                      <div class="flex flex-row items-center">-->
<!--                        <span class="pr-4px">{{ t('common.sort') }}</span>-->
<!--                        <a-tooltip>-->
<!--                          <template #title>{{ t('common.sortTip') }}</template>-->
<!--                          <div class="ax-icon-button ax-icon-smallest" @click="showVendorProduct">-->
<!--                            <span class="soc ax-com-Tips ax-icon"></span>-->
<!--                          </div>-->
<!--                        </a-tooltip>-->
<!--                      </div>-->
<!--                    </template>-->
<!--                    <a-input-number v-model:value="stepData.ruleSort" max="9999" min="0"-->
<!--                                    default-value="0"></a-input-number>-->
<!--                  </a-form-item>-->
<!--                </div>-->
<!--              </div>-->
<!--            </a-form>-->
<!--          </div>-->

          <div class="step_block py-16px" v-if="current == 0">
            <a-form layout="vertical" :model="stepData" class="step-content_1">
              <div class="ax-border-bottom px-16px mb-24px">
                <a-form-item>
                  <a-input v-model:value="stepData.ruleName" :placeholder="tp('RuleName')"/>
                </a-form-item>
              </div>
              <div class="flex flex-row flex-wrap gap-30px px-16px">
                <div class="step1-input">
                  <a-form-item :label="tp('logtype')">
                    <a-select v-model:value="stepData.logType" :options="ThreatHuntingLogOptions(t)"
                              @change="changeLogType"></a-select>
                  </a-form-item>
                </div>
                <div class="step1-input">
                  <a-form-item  :label="tp('SuspectedHaltAfter')">
                    <div class="flex flex-row gap-8px">
                      <a-input-number v-model:value="suspectedHaltAfter" style="width: 180px"
                                      :max="suspectedHaltType == 2 ? 24 : 60"/>
                      <a-select v-model:value="suspectedHaltType" style="width: 120px" @change="haltTypeChange">
                        <a-select-option :value="1">{{ t('common.Min') }}</a-select-option>
                        <a-select-option :value="2">{{ t('common.Hour') }}</a-select-option>
                      </a-select>
                    </div>
                  </a-form-item>
                </div>
                <div class="step1-input">
                  <a-form-item>
                    <template #label>
                      <div class="flex flex-row items-center">
                        <span class="pr-4px">{{ t('common.sort') }}</span>
                      </div>
                    </template>
                    <a-input-number v-model:value="stepData.ruleSort" max="9999" min="0"
                                    default-value="0"></a-input-number>
                  </a-form-item>
                </div>
                <div class="step1-input">
                  <a-form-item :label="tp('vendorProduct')">
                    <div v-if="vendorProductData?.id" class="flex flex-row items-center gap-30px">
                      <div class="flex flex-row items-center gap-8px">
                        <img :src="render.renderUploadImageSrc(vendorProductData.icon)" alt=""
                             class="w-32px h-32px rounded-1/2"/>
                        <div class="font13 font-600 fcolor1">{{ vendorProductData.name }}</div>
                      </div>
                      <div class="flex flex-row items-center gap-4px">
                        <div class="ax-icon-button primary-button" @click="showVendorProduct">
                          <span class="soc ax-com-Update ax-icon"></span>
                        </div>
                        <div class="ax-icon-button primary-button" @click="delVendorProduct">
                          <Icon icon="ant-design:delete-outlined" class="ax-icon"/>
                        </div>
                      </div>
                    </div>
                    <div v-else>
                      <a-button size="small" type="primary" ghost @click="showVendorProduct()">
                        <span class="soc ax-com-Add ax-icon"></span>
                        {{ t('common.add') }}
                      </a-button>
                    </div>
<!--                    <div v-else class="ax-icon-button primary-button" @click="showVendorProduct">-->
<!--                      <span class="soc ax-com-Add ax-icon"></span>-->
<!--                      {{ t('common.add') }}-->
<!--                    </div>-->
                  </a-form-item>
                </div>
              </div>
            </a-form>
          </div>
            <!--   filter start-->
          <div class="step-content_1_filter " style="margin-top: 16px" v-if="current == 0">
              <div class="step_block" >
                <!--  create filter-->
                <div class="p-16px ax-border-bottom">
                  <SelectBlock>
                    <template #left>
                      <div class="fcolor font14 px-12px">{{ tp('CreateFilter') }}</div>
                    </template>
                    <template #right>
                      <div class="flex flex-row gap-8px fcolor3 items-center flex-1">
                        <span class="soc ax-com-Tips font13"></span>
                        <div class="font13"> {{ tp('tip1') }}</div>
                        <a-button type="primary" class="ml-auto" @click="doSearch">
                          {{ t('common.confirm') }}
                        </a-button>
                      </div>
                    </template>
                  </SelectBlock>
                </div>
                <!--   filter content-->
                <div class="flex flex-col py-24px pl-16px pr-24px gap-8px">
                  <template v-for="(item, logFilterIndex) in stepData.logFilter"
                            :key="`logFilter_${logFilterIndex}`">
                    <div class="flex flex-row gap-8px items-center">
                      <!--删除-->
                      <div class="ax-icon-button is_del" @click="delFilter(logFilterIndex)">
                        <span class="soc ax-com-Decrease ax-icon"/>
                      </div>
                      <div class="flex flex-row gap-8px">
                        <a-select v-model:value="item.isContain" style="width: 150px" @change="searchFilter">
                          <a-select-option :value="true">{{ t('common.Contain') }}</a-select-option>
                          <a-select-option :value="false">{{ t('common.NotContain') }}</a-select-option>
                        </a-select>
                      </div>
                      <div class="flex flex-row gap-8px">
                        <a-select v-model:value="item.flag" style="width: 150px" @change="searchFilter">
                          <a-select-option :value="true">{{ t('common.IgnoreCase') }}</a-select-option>
                          <a-select-option :value="false">{{ t('common.NotIgnoreCase') }}</a-select-option>
                        </a-select>
                      </div>
                      <div class="flex flex-row gap-16px items-center flex-1">
                        <!--rule name-->
                        <a-input v-model:value="item.value" @change="searchFilter"
                                 class="flex-1 input-default"/>
                        <!--Flag-->
<!--                        <a-tooltip>-->
<!--                          <template #title>{{ tp('tip13') }}</template>-->
<!--                          <a-switch v-model:checked="item.flag" @change="searchFilter" size="small"/>-->
<!--                        </a-tooltip>-->
                      </div>
                    </div>
                  </template>
                  <div>
                    <a-button size="small" type="primary" ghost @click="addFilter()">
                      <span class="soc ax-com-Add ax-icon"></span>
                      {{ t('common.add') }}
                    </a-button>
                  </div>
                </div>
              </div>
          </div>
               <!--  filter end-->
          <!--  select Sample log start-->
          <div class="step-content_1_select_log " style="margin-top: 16px;display:flex;flex-direction: column;gap: 16px;" v-if="current == 0">
            <div class="step_block" >
              <div class="p-16px ax-border-bottom">
                <SelectBlock>
                  <template #left>
                    <div class="fcolor font14 px-10px">{{ tp('SelectSampleLog') }}</div>
                  </template>
                  <template #right>
                    <div class="flex flex-row items-center gap-4px fcolor3 flex-1">
                        <div class="font13">Last 24 hours</div>
                        <!-- Split 和 Upload 按钮放在同一个容器内 -->
                        <div class="flex flex-row ml-auto gap-8px">
                          <a-button v-if="isSplit" type="primary" class="ml-auto" size="small" @click="doSplit">
                            {{ t('common.group') }}
                          </a-button>
                          <a-button v-else type="primary" class="ml-auto" size="small" @click="doSplit">
                            {{ t('common.split') }}
                          </a-button>
                          <a-button type="primary" class="ml-auto" size="small" @click="addLog">
                            {{ t('common.uploadLogSource') }}
                          </a-button>
                          <a-button type="primary" class="ml-auto" size="small" @click="openInput">
                            {{ t('common.upload') }}
                          </a-button>
                          <a-select v-model:value="stepData.parseMethod" size="small" style="width: auto; min-width: 90px;" @change="parseMethodChange" >
                            <a-select-option v-for="item in ParseMethodData" :key="item.value" :value="item.value" >
                              {{ item.label }}
                            </a-select-option>
                          </a-select>
                        </div>
                    </div>
                  </template>
                </SelectBlock>
              </div>
            </div>
            <!--local text log list  -->
            <div  v-if="fileLogData.length > 0">
                <div class="step_block" >
                  <div
                    class="flex flex-row gap-8px fcolor1 p-8px"
                    :class="{ 'log-item_checked': item.checked }"
                    v-for="(item, logI) in fileLogData"
                    :key="'logSource-' + logI"
                  >
                    <a-checkbox v-model:checked="item.checked"
                                @click="item.checked = !item.checked"></a-checkbox>
                    <div class="break-all">{{ item.raw_log }}</div>
                  </div>
                </div>
            </div>

            <!--log Source list  -->
            <div class="px-16px pb-16px" v-if="logSourceData.length > 0">
              <div class="log-list-content">
                <div
                  class="flex flex-row gap-8px fcolor1 p-12px"
                  :class="{ 'log-item_checked': item.checked }"
                  v-for="(item, logI) in logSourceData"
                  :key="'logSource-' + logI"
                >
                  <a-checkbox v-model:checked="item.checked"
                              @click="item.checked = !item.checked"></a-checkbox>
                  <div class="break-all">{{ item.raw_log }}</div>
                </div>
              </div>
            </div>
            <!--log Source list  -->
<!--            <div class="px-16px pb-16px" v-if="logSourceData.length > 0">-->
<!--              <div class="step_block">-->
<!--                <div-->
<!--                  class="flex flex-row gap-8px fcolor1 p-12px"-->
<!--                  :class="{ 'log-item_checked': item.checked }"-->
<!--                  v-for="(item, logI) in logSourceData"-->
<!--                  :key="'logSource-' + logI"-->
<!--                >-->
<!--                  <a-checkbox v-model:checked="item.checked"-->
<!--                              @click="item.checked = !item.checked"></a-checkbox>-->
<!--                  <div class="break-all">{{ item.raw_log }}</div>-->
<!--                </div>-->
<!--              </div>-->
<!--            </div>-->
          </div>
          <!--  select Sample log end-->



          <!-- step 2-->
          <div class="step-content_2 flex flex-col gap-16px" v-else-if="current == 1">
            <div class="step_block">
              <!--  create filter-->
              <div class="p-16px ax-border-bottom">
                <SelectBlock>
                  <template #left>
                    <div class="fcolor font14 px-12px">{{ tp('CreateFilter') }}</div>
                  </template>
                  <template #right>
                    <div class="flex flex-row gap-8px fcolor3 items-center flex-1">
                      <span class="soc ax-com-Tips font13"></span>
                      <div class="font13"> {{ tp('tip1') }}</div>
                      <a-button type="primary" class="ml-auto" @click="doSearch">
                        {{ t('common.confirm') }}
                      </a-button>
                    </div>
                  </template>
                </SelectBlock>
              </div>
              <!--   filter content-->
              <div class="flex flex-col py-24px pl-16px pr-24px gap-8px">
                <template v-for="(item, logFilterIndex) in stepData.logFilter"
                          :key="`logFilter_${logFilterIndex}`">
                  <div class="flex flex-row gap-8px items-center">
                    <!--删除-->
                    <div class="ax-icon-button is_del" @click="delFilter(logFilterIndex)">
                      <span class="soc ax-com-Decrease ax-icon"/>
                    </div>
                    <div class="flex flex-row gap-16px items-center flex-1">
                      <!--rule name-->
                      <a-input v-model:value="item.value" @change="searchFilter"
                               class="flex-1 input-default"/>
                      <!--Flag-->
                      <a-tooltip>
                        <template #title>{{ tp('tip13') }}</template>
                        <a-switch v-model:checked="item.flag" @change="searchFilter" size="small"/>
                      </a-tooltip>
                    </div>
                  </div>
                </template>
                <div>
                  <a-button size="small" type="primary" ghost @click="addFilter()">
                    <span class="soc ax-com-Add ax-icon"></span>
                    {{ t('common.add') }}
                  </a-button>
                </div>
              </div>
            </div>
            <!-- log source start -->
            <!-- add log source  -->
            <div class="step_block p-16px"
                 v-if="logSourceData.length == 0 && !checkLogSource.deviceName">
              <select-block class-name="h-48px">
                <template #left>
                  <div class="flex flex-col items-center gap-8px">
                    <div class="ax-icon-button ax-icon-small primary-button" @click="addLog">
                      <span class="soc ax-com-Add fs-18"></span>
                    </div>
                    <div class="font-600 primaryColor">{{ tp('AddLogSource') }}</div>
                  </div>
                </template>
                <template #right>
                  <div class="flex flex-row gap-8px fcolor3 items-center flex-1">
                    <span class="soc ax-com-Tips font13"></span>
                    <div class="font13"> {{ tp('tip2') }}</div>
                  </div>
                </template>
              </select-block>
            </div>
            <!-- change log source  -->
            <div class="step_block" v-else>
              <div class="p-16px ax-border-bottom">
                <select-block class-name="h-48px">
                  <template #left>
                    <div class="flex flex-col items-center gap-8px">
                      <div class="ax-icon-button ax-icon-small primary-button" @click="addLog">
                        <span class="soc ax-com-Update fs-18"></span>
                      </div>
                      <div class="font-600 primaryColor">{{ tp('ChangeLogSource') }}</div>
                    </div>
                  </template>
                  <template #right>
                    <div class="flex flex-row gap-40px items-center">
                      <div class="flex flex-col gap-4px w-160px">
                        <div class="fcolor3">{{ tp('DeviceName') }}</div>
                        <div class="font14 primaryColor">{{ checkLogSource.deviceName || '' }}</div>
                      </div>
                      <div class="flex flex-col gap-4px w-160px">
                        <div class="fcolor3">{{ tp('ParsingRules') }}</div>
                        <div class="font14 primaryColor">{{ checkLogSource.parseRule || 0 }}</div>
                      </div>
                      <div class="flex flex-col gap-4px w-160px">
                        <div class="fcolor3">{{ tp('AddLogSource') }}</div>
                        <div class="font14 primaryColor">{{ checkLogSource.parseField || 0 }}</div>
                      </div>
                    </div>
                  </template>
                </select-block>
              </div>
              <div class="p-16px flex flex-row gap-8px items-center">
                <span class="soc ax-com-Tips fcolor3"></span>
                <div class="fcolor3"> {{ tp('tip3') }}</div>
                <div class="flex flex-row ml-auto">
                  <a-select default-value="7d" @change="doSearch" v-model:value="queryParam.time"
                            class="w-140px">
                    <a-select-option value="24h"> {{ t('common.Last') }} 24 {{
                        t('common.hours')
                      }}
                    </a-select-option>
                    <a-select-option value="7d"> {{ t('common.Last') }} 7 {{
                        t('common.days')
                      }}
                    </a-select-option>
                    <a-select-option value="30d"> {{ t('common.Last') }} 30 {{
                        t('common.days')
                      }}
                    </a-select-option>
                    <a-select-option value="3m"> {{ t('common.Last') }} 3 {{
                        t('common.months')
                      }}
                    </a-select-option>
                    <a-select-option value="1y"> {{ t('common.Last') }} 1 {{
                        t('common.year')
                      }}
                    </a-select-option>
                  </a-select>
                </div>
              </div>
              <!--log list  -->
              <div class="px-16px pb-16px" v-if="logSourceData.length > 0">
                <div class="log-list-content">
                  <div
                    class="flex flex-row gap-8px fcolor1 p-12px"
                    :class="{ 'log-item_checked': item.checked }"
                    v-for="(item, logI) in logSourceData"
                    :key="'logSource-' + logI"
                  >
                    <a-checkbox v-model:checked="item.checked"
                                @click="item.checked = !item.checked"></a-checkbox>
                    <div class="break-all">{{ item.raw_log }}</div>
                  </div>
                </div>
              </div>
            </div>
            <!-- log source end -->

            <!-- local text start -->
            <div class="step_block">
              <!-- add local text  -->
              <div class="p-16px" :class="{ 'ax-border-bottom': fileLogData.length > 0 }">
                <select-block class-name="h-48px">
                  <template #left>
                    <div class="flex flex-col items-center gap-8px">
                      <div class="ax-icon-button ax-icon-small primary-button" @click="openInput">
                        <span class="soc ax-com-Add fs-18"></span>
                      </div>
                      <div class="font-600 primaryColor">{{ tp('AddLocalText') }}</div>
                    </div>
                  </template>
                  <template #right>
                    <div class="flex flex-row gap-8px fcolor3 items-center flex-1">
                      <span class="soc ax-com-Tips font13"></span>
                      <div class="font13"> {{ tp('tiptext') }}</div>
                    </div>
                  </template>
                </select-block>
              </div>
              <!--log list  -->
              <div class="p-16px" v-if="fileLogData.length > 0">
                <div class="log-list-content">
                  <div
                    class="flex flex-row gap-8px fcolor1 p-12px"
                    :class="{ 'log-item_checked': item.checked }"
                    v-for="(item, logI) in fileLogData"
                    :key="'logSource-' + logI"
                  >
                    <a-checkbox v-model:checked="item.checked"
                                @click="item.checked = !item.checked"></a-checkbox>
                    <div class="break-all">{{ item.raw_log }}</div>
                  </div>
                </div>
              </div>
            </div>
            <!-- local text end  -->

            <!-- show checked unparse log start  旧版本，现在美工没有画，先去掉2025-04-18-->
            <!--            <div v-if="unparseLogIds" class="">
                          <div class="step-content_3">
                            <div class="step-content mb-16 content-filter" style="height: 150px">
                              <div class="content-filter_width">
                                <div class="font14 fcolor1 mb-16">
                                  <Icon icon="ant-design:exclamation-circle-outlined" :size="14"
                                        class="primaryColor"></Icon>
                                  {{ tp('tip1') }}
                                </div>
                                <a-form layout="vertical" style="width: 100%; position: relative">
                                  <a-form-item :label="tp('CreateFilter')">
                                    <div style="display: flex; gap: 10px">
                                      <div
                                        style="display: flex; flex-direction: column; gap: 8px; width: calc(100% - 80px)">
                                        <div
                                          v-for="(item, logFilterIndex) in stepData.logFilter"
                                          :key="`logFilter_${logFilterIndex}`"
                                          style="display: flex; gap: 8px; align-items: center"
                                        >
                                          <a-input v-model:value="item.value" @change="searchFilter"
                                                   style="width: calc(100% - 70px)"/>

                                          <a-tooltip>
                                            <template #title>{{ tp('tip13') }}</template>
                                            <a-switch v-model:checked="item.flag" @change="searchFilter"
                                                      size="small"/>
                                          </a-tooltip>

                                          <Icon
                                            icon="ant-design:delete-outlined"
                                            :size="16"
                                            style="cursor: pointer; margin-left: 5px"
                                            v-if="logFilterIndex > 0"
                                            @click="delFilter(logFilterIndex)"
                                          />
                                        </div>
                                        <div>
                                          <Icon icon="ant-design:plus-outlined" style="cursor: pointer"
                                                :size="16"
                                                @click="addFilter"/>
                                        </div>
                                      </div>
                                      <a-button type="primary" class="font13 fcolor"
                                                @click="doSearchUnparseLog">
                                        {{ t('common.confirm') }}
                                      </a-button>
                                    </div>
                                  </a-form-item>
                                </a-form>
                              </div>
                            </div>
                          </div>
                          <div class="flex-v padding16 content table-log">
                            <div class="flex-v log-list">
                              <div
                                :class="['flex-h', 'log-item', 'font12', 'fcolor1', { 'log-item_checked': item.checked }]"
                                v-for="(item, index) in unparseLogArr"
                                :key="index"
                              >
                                <div class="check">
                                  <a-checkbox v-model:checked="item.checked"
                                              @click="item.checked = !item.checked"></a-checkbox>
                                </div>
                                <div class="log">{{ item.raw_log }}</div>
                              </div>
                            </div>
                          </div>
                        </div>-->
            <!-- show checked unparse log end  旧版本，现在美工没有画，先去掉2025-04-18-->
          </div>

          <!-- step 3-->
          <div class="step-content_3 flex flex-col" v-else-if="current == 2">
            <div class="step_block p-16px">
              <div class="flex flex-row gap-8px">
                <span class="soc ax-com-Tips font13"></span>
                <div class="flex flex-col gap-16px flex-1">
                  <div class="font13 fcolor1">{{ tp('tip4') }}</div>
                  <a-form layout="vertical" style="width: 100%">
                    <a-row :gutter="8" style="position: relative">
                      <a-col :span="12">
                        <a-form-item :label="tp('FrontReferenceMark')">
                          <a-input v-model:value="stepData.logFrontMark"/>
                        </a-form-item>
                      </a-col>
                      <a-col :span="12">
                        <a-form-item :label="tp('PostReferenceMark')">
                          <a-input v-model:value="stepData.logPostMark"/>
                        </a-form-item>
                      </a-col>
                    </a-row>
                  </a-form>
                  <div class="mt-auto ml-auto flex flex-row gap-8px">
                    <a-button @click="RollbackLog">
                      {{ tp('Rollback') }}
                    </a-button>
                    <a-button type="primary" @click="markLog">{{ t('common.confirm') }}</a-button>
                  </div>
                </div>
              </div>
            </div>
            <div class="fcolor font16 mb-8px pt-24px"> {{ t('common.preview') }}</div>
            <div class="step_block">
              <select-preview :value="stepData" :height-class="'h-240px'"/>
            </div>
          </div>


          <!-- step 4 -->
          <div class="step-content_3" v-if="current == 3">
            <div class="fcolor font16 mb-8px"> {{ t('common.preview') }}</div>
            <div class="step_block">
              <select-preview :value="stepData" :height-class="'h-240px'"/>
            </div>

            <div class="font13 fcolor3 tip pt-24px pb-16px flex flex-row gap-8px">
              <span class="soc ax-com-Tips font13"></span>
              {{ tp('tip2') }}
            </div>
            <div class="flex flex-row flex-wrap gap-16px">
              <div
                @click="checkMethod(item)"
                v-for="item in ParseMethodData"
                :key="item.value"
                class="flex flex-row w-322px h-160px p-16px gap-16px cursor-pointer ax-border-radius-8px ax-border"
                :class="{ 'ax-bg-default': stepData.parseMethod == item.value }"
              >
                <div>
                  <img :src="item.icon" width="40px" height="40px"/>
                </div>
                <div class="flex flex-col gap-8px flex-1">
                  <div class="primaryColor font16">{{ item.label }}</div>
                  <div class="fcolor3">{{ item.tip }}</div>
                </div>
              </div>
            </div>
          </div>
          <!-- step 5   -->
          <!-- using parse file-->
          <div class="step-content_4" v-if="current == 4">
            <!--   正则-->
            <div class="step_block flex flex-col gap-16px p-16px mb-2px bottom_radius_0" v-if="stepData.parseMethod == 2">
              <a-textarea   v-model:value="regularValue" style="height: 80px;"></a-textarea>
              <div class="ml-auto flex flex-row gap-8px items-center">
                <span class="soc ax-com-Tips font13 cursor-pointer" @click="showTip"></span>
                <a-button   @click="regularValue = ''">
                  {{ tp('Empty') }}
                </a-button>
                <a-button type="primary" @click="getExecRegular">
                  {{ tp('confirm') }}
                </a-button>
              </div>
            </div>

            <div class="step_block" :class="{'top_radius_0':stepData.parseMethod == 2 ,'bottom_radius_0' : stepData.parseMethod == 3}">
              <select-preview :value="stepData" :height-class="'h-240px'"/>
            </div>
            <!-- 分隔符 -->
            <div class="step_block top_radius_0 p-16px flex flex-col gap-16px mt-4px " v-if="stepData.parseMethod == 3">
              <div class="flex flex-row gap-8px">
                <div class="flex-1 flex flex-col gap-8px">
                  <div class="fcolor1 font-600">{{ tp('Separator') }}</div>
                  <a-input v-model:value="separator" @change="separatorChange" class="input-default"/>
                </div>
                <div class="flex-1 flex flex-col gap-8px">
                  <div class="fcolor1 font-600"> ASCII (HEX)</div>
                  <a-input v-model:value="separator_hex" @change="separator_hexChange" class="input-default"/>
                 </div>
              </div>
              <div class="flex flex-row gap-8px ml-auto">
                <a-button @click="separator = ''">
                  {{ tp('Empty') }}
                </a-button>
                <a-button  type="primary" @click="changeSeparatorTable">
                  {{ t('common.confirm') }}
                </a-button>
              </div>
            </div>
            <!-- table -->
            <div class="mt-16px step_block">

              <BasicTable
                @register="registerRegularTable"
                :dataSource="regularTableDataSource"
                :columns="stepData.parseMethod == 1 ? treeColumns : regularColumns"
              >
                <!--操作栏-->
                <template #form-formFooter>
                  <div class="flex flex-row justify-between w-968px">
                    <div class="font16 fcolor">
                      {{ METHOD_TABLE[stepData.parseMethod] }}
                    </div>
                    <a-button class="ml-auto" @click="showFieldModal2(regularTableDataSource)" size="small">
                      <span class="soc ax-com-Record ax-icon"></span>
                      {{ tp('FieldsList') }}
                    </a-button>
                  </div>
                </template>
                <template #action="{ record }">
                  <TableAction :actions="getTableAction(record)"
                               :dropDownActions="getDropDownAction(record)"/>
                </template>
              </BasicTable>
            </div>

          </div>

          <!-- step 6  start-->
          <div class="step-content_5 flex flex-col gap-16px"  v-if="current == 5 && (stepData.logType == SecurityLog)">

                <LogAggregation :stepData="stepData" v-if="stepData.ruleName"/>

            </div>
          <!-- step 7  start （倒数第二步，选租户）-->
          <div class="step-content_5" v-if="current == stepList.length - 2 && showTopTenant">
            <div class="step_block" v-if="stepData.ruleName">
              <RuleTenantList v-model:value="stepData.socTenantId"/>
            </div>
          </div>

          <!--最后一步 -->
          <div class="step-content_5 flex flex-col gap-16px" v-if="current == stepList.length - 1">
            <div class="step_block p-16px" v-if="stepData.ruleName">
              <div class="font16 fcolor">  {{ stepData.ruleName || '&nbsp;' }}  </div>
              <div class="font13 fcolor3 pt-16px pb-8px">
                {{
                  tp('ParsedFields')
                }}
              </div>
              <div class="flex flex-row flex-wrap gap-4px">
                <div class="ax-label" v-for="item in stepData.parsedFields" :key="item.label">
                  {{ item.label }}
                </div>
              </div>
            </div>
            <div class="step_block relative" >
              <TenantLogSource @ok="setApply" :checkedKeyData="applyData"
                               :socTenantId="stepData.socTenantId"/>
            </div>
          </div>
          </div>
      </template>
    </step>
    <AddSourceLogModal @register="registerModal" @success="handleCheckLog"/>
    <FieldListModal ref="fieldListModalRef"/>
    <FixOriginalValueModal ref="fixValueModalRef"/>
    <AddLocalTextModal ref="addLocalTextModaRef" @register="registertxtModal"
                       @handleData="handleData"/>
    <RegularTip ref="RegularTipRef"/>
    <VendorProduct ref="VendorProductRef" @set="setVendorProduct"/>
  </div>
</template>

<script lang="ts" setup>
import {useRouter} from 'vue-router';
import {computed, defineProps, onMounted, provide, reactive, ref, toRaw, watch} from 'vue';
import {SelectBlock, SelectPreview} from './components/index';
import {
  jsonParseLog,
  ParseMethodData,
  parseStepData
} from '/@/views/parseRule/ParseRuleUtils';
import {BasicTable, TableAction, useTable} from '/@/components/Table';
import {
  METHOD_TABLE,
  regularColumns,
  treeColumns
} from '/@/views/parseRule/ParseRuleManage.data';
import {
  handleRegData,
  reg_execRegular,
  separator_split
} from '/@/views/parseRule/Regular';
import {ParseRule, splitSymbol, splitSymbol2} from './ParseRule';
import AddSourceLogModal from '/@/views/parseRule/modules/AddSourceLogModal.vue';
import {useModal} from '/@/components/Modal';
import {formatToDateTime} from '/@/utils/dateUtil';
import dayjs from 'dayjs';
import {
  loadUnparsedLog,
  queryById, regExp,
  saveOrUpdate
} from '/@/views/parseRule/ParseRuleManage.api';
import {message, Modal} from 'ant-design-vue';
import {getToken, isAdministrator} from '/@/utils/auth';
import FieldListModal from '/@/views/parseRule/modules/FieldListModal.vue';
import {useListPage} from '/@/hooks/system/useListPage';
import {useMessage} from '/@/hooks/web/useMessage';
import {deleteFile, downloadFileBlob, render} from '/@/utils/common/renderUtils';
import FixOriginalValueModal from '/@/views/parseRule/modules/FixOriginalValueModal.vue';
import {fromMenu, unparseLogArr, unparseLogIds} from '/@/views/parseRule/UnparseLogUtils';
import {useI18n} from '/@/hooks/web/useI18n';
import Icon from '/@/components/Icon';
import LogAggregation from '/@/views/parseRule/LogAggregation.vue';
import AddLocalTextModal from '/@/views/parseRule/modules/AddLocalTextModal.vue';
import RuleTenantList from '/@/views/parseRule/modules/RuleTenantList.vue';
import TenantLogSource from '/@/views/parseRule/TenantLogSource.vue';
import RegularTip from '/@/views/parseRule/modules/RegularTip.vue';
import {ThreatHuntingLogOptions} from '/@/utils/ckTable';

import VendorProduct from '/@/views/parseRule/cmts/vendorProduct/VendorProduct.vue';
import JSearchSelect from '/@/components/Form/src/jeecg/components/JSearchSelect.vue';

import {Step} from '/@/components/Step/index';
import {SecurityLog} from "/@/utils/ckTable";

const suspectedHaltAfter = ref(10);
const suspectedHaltType = ref(1);
const isSplit = ref(false); // 默认为 false，表示初始状态为 Group
function haltTypeChange(value) {
  console.log(value);
  if (value == 2) {
    if (suspectedHaltAfter.value > 24) {
      suspectedHaltAfter.value = 24;
    }
  }
}
function doSplit() {
  isSplit.value = !isSplit.value;
  if (isSplit.value) {
    // Split logic: display raw logs
    splitLogs();
  } else {
    // Group logic: aggregate logs
    groupLogs();
  }
}
//todo7.25 11:44
function splitLogs() {
  // Logic to display raw logs
  // For example, you can set a flag or modify the data source to show raw logs
  console.log('Split logs');
}

function groupLogs() {
  // Logic to aggregate logs
  // For example, you can set a flag or modify the data source to show aggregated logs
  console.log('Group logs');
}
const {t} = useI18n();

function tp(name) {
  return t('routes.parserule.' + name);
}

const isAdmin = isAdministrator();

const {createMessage} = useMessage();

const props = defineProps({
  close: Function,
  id: String,
  flag: String,
  sourceType: String,
  tenantType: Number,
});

const headers = reactive({
  'X-Access-Token': getToken(),
});
const {createConfirm} = useMessage();
const queryParam = ref<any>({});

/**
 * 首页选择租户，管理员添加租户规则，在第一个tab页选择租户，顶部步骤里的选择租户不显示
 */
const showHomeTenant = ref(false);
//tenantType === 2 管理员添加租户规则
showHomeTenant.value = isAdmin && props.tenantType === 2;
/**
 * 是否显示顶部的租户选择
 */
const showTopTenant = ref(false);
showTopTenant.value = isAdmin && props.tenantType != 2;

//弹出修正规则
const fixValueModalRef = ref();
//弹出选择字段
const fieldListModalRef = ref();

const separator = ref('');
const separator_hex = ref('');
const regularValue = ref('');
const VendorProductRef = ref();
const vendorProductData = ref<any>({});
/**
 * 显示厂商和产品页面
 */
const showVendorProduct = () => {
  if (showHomeTenant.value && !stepData.socTenantId) {
    message.warning(tp('tip24'));
    return;
  }
  VendorProductRef.value.open(stepData.socTenantId);
};
/**
 * 设置厂商名称
 * @param data
 */
const setVendorProduct = (data: any) => {
  vendorProductData.value = data;
  if (data) {
    stepData.vendorId = data.id;
    stepData.vendorName = data.name;
    stepData.vendorIcon = data.icon;
  } else {
    stepData.vendorId = '';
    stepData.vendorName = '';
    stepData.vendorIcon = '';
  }
};
/**
 * 删除厂商名称
 */
const delVendorProduct = () => {
  vendorProductData.value = {};
  stepData.vendorId = '';
  stepData.vendorName = '';
  stepData.vendorIcon = '';
};

//存储历史解析方法
const historyParseMethod = ref();

function separator_hexChange() {
  separator.value = hex_to_ascii(separator_hex.value);
}

function separatorChange() {
  separator_hex.value = ascii_to_hex(separator.value);
}

function hex_to_ascii(str1) {
  let hex = str1.toString();
  let str = '';
  for (let n = 0; n < hex.length; n += 2) {
    str += String.fromCharCode(parseInt(hex.substring(n, 2), 16));
  }
  return str;
}

function ascii_to_hex(str) {
  let arr1: string[] = [];
  for (let n = 0, l = str.length; n < l; n++) {
    let hex = Number(str.charCodeAt(n)).toString(16);
    arr1.push(hex);
  }
  return arr1.join('');
}

const router = useRouter();
const current = ref(0); //step current number
provide('current', current);
const carouselIndex = ref(0); //carousel current index
const logSourceData = ref<any>([]); //step2 choice clickhouse log
const fileLogData = ref<any>([]); // step2 upload file log

const tenantMap = ref<any>({});
const checkLogSource = ref<any>({});
let stepData = reactive<any>({
  id: '',
  ruleName: '',
  oldLogType: SecurityLog, //记录历史的logType值，判断是否有改变，为后面聚合字段显示做判断
  logType: SecurityLog,
  applyData: [],
  parseFileRule: '', //文件解析规则
  checkedLogs: [],
  applyAll: false,
  device: {},
  parseRuleNum: '',
  logSourceNum: '',
  parseMethod: '',
  parsedFields: [],
  jsonParseResult: [],
  fileParsePath: '',
  tenant: '',
  // logFilter: [{value: '', flag: false}],
  logFilter: [{value: '', flag: false, isContain: true}],
});
const stepList = computed(() => {
  let stepArray = [tp('Name'), tp('SelectSampleLog'), tp('LogClipping'), tp('SelectParseMethod'), tp('ExtractFields')];

  if (stepData.logType == SecurityLog) {
    stepArray.push(tp('logAggregation'));
  }
  if (showTopTenant.value) {
    stepArray.push(tp('BatchPublic'));
  }
  stepArray.push(tp('BulkApply'));

  return stepArray;
});
// const stepList = computed(() => {
//   let stepArray = [tp('BasicInformation'), tp('ExtractFields'), tp('BulkApply')];
//   return stepArray;
// });

const isUpdate = ref(false);
//是否更改的日志
const logIsUpdate = ref(false);
let applyData = ref<any[]>([]);

onMounted(() => {
  // 设置默认解析方法为 JSON (值为 1)先写死为 JSON，后续与选中的sampleLog推荐方法保持一致
  if (!stepData.parseMethod) {
    stepData.parseMethod = 1;
  }
  logIsUpdate.value = false;
  unparseLogIds.value = '';

  //集成管理2024-09-09
  if (props?.id) {
    isUpdate.value = true;
    getParseRule(props.id);
    carouselIndex.value = 0;
    current.value = 0;
    return;
  }
});
let oldCheckedLogs = '';

/**
 * update get rule data
 * @param id
 */
async function getParseRule(id) {
  const data = await queryById({id});

  let parseData = parseStepData(data);

  stepData.id = parseData.id;
  if (parseData.socTenantId) {
    stepData.socTenantId = parseData.socTenantId;
  }
  //编辑，租户规则不显示选择租户
  if (parseData.tenantType === 2) {
    showHomeTenant.value = false;
    showTopTenant.value = false;
  }

  stepData.ruleName = parseData.ruleName;
  stepData.logType = parseData.logType;
  stepData.oldLogType = parseData.logType;
  stepData.tenant = parseData.tenant;
  stepData.aggregationFields = parseData.aggregationFields;
  stepData.aggregationExpire = parseData.aggregationExpire;
  stepData.aggregationTimeType = parseData.aggregationTimeType;
  stepData.summaryField = parseData.summaryField;
  stepData.tenant = parseData.tenant;
  stepData.vendorId = parseData.vendorId;
  stepData.vendorName = parseData.vendorName;
  stepData.vendorIcon = parseData.vendorIcon;
  stepData.ruleSort = parseData.ruleSort;
  stepData.vendorCategory = parseData.vendorCategory;
  stepData.vendorService = parseData.vendorService;
  vendorProductData.value = {
    id: parseData.vendorId,
    name: parseData.vendorName,
    icon: parseData.vendorIcon,
  };
  stepData.tenant = parseData.tenant;
  historyParseMethod.value = parseData.parseMethod;

  if (parseData.applyData) {
    stepData.applyData = parseData.applyData;
  }
  applyData.value = parseData.applyData;

  stepData.applyAll = parseData.applyAll;
  stepData.parseMethod = parseData.parseMethod;
  stepData.parsedFields = parseData.parsedFields;
  stepData.checkedLogs = parseData.checkedLogs;
  oldCheckedLogs = JSON.stringify(parseData.checkedLogs);
  // if (parseData.logFilter) {
  //   stepData.logFilter = [];
  //   const array = parseData.logFilter.split(splitSymbol);
  //
  //   for (let i = 0; i < array.length; i++) {
  //     const array2 = array[i].split(splitSymbol2);
  //
  //     if (array2.length > 1) {
  //       array2[1] == 'true' ? (array2[1] = true) : (array2[1] = false);
  //     }
  //     stepData.logFilter.push({
  //       value: array2[0],
  //       flag: array2[1] ?? false,
  //     });
  //   }
  // }
  if (parseData.logFilter) {
    stepData.logFilter = [];
    const array = parseData.logFilter.split(splitSymbol);

    for (let i = 0; i < array.length; i++) {
      const array2 = array[i].split(splitSymbol2);

      if (array2.length > 1) {
        array2[1] == 'true' ? (array2[1] = true) : (array2[1] = false);
        array2[2] == 'true' ? (array2[1] = true) : (array2[1] = false);
      }
      stepData.logFilter.push({
        value: array2[0],
        flag: array2[1] ?? false,
        isContain: array2[2] ?? true,
      });
    }
  }
  stepData.jsonParseResult = parseData.jsonParseResult;
  stepData.fileParsePath = parseData.fileParsePath ?? '';
  stepData.logFrontMark = parseData.logFrontMark ?? '';
  stepData.logPostMark = parseData.logPostMark ?? '';

  if (stepData.parseMethod == '1') {
    regularTableDataSource.value = stepData.jsonParseResult;
  } else if (stepData.parseMethod == '2' || stepData.parseMethod == '3') {
    if (stepData.jsonParseResult.dataValue) {
      regularTableDataSource.value = stepData.jsonParseResult.dataValue;
    }
    regularValue.value = stepData.jsonParseResult.regularValue;
    separator.value = stepData.jsonParseResult.regularValue;
    separatorChange();
  } else if (stepData.parseMethod == '6') {
    let json = JSON.parse(stepData.fileParsePath);
    stepData.fileParsePath = json.path;
    stepData.fileName = json.fileName;
  }
}

//注册model
const [registerModal, {openModal}] = useModal();
const [registertxtModal, {openModal: opentxtModal}] = useModal();
//file parse table
const [registerTable] = useTable({
  title: 'Json Parsing Result',
  columns: [
    {
      title: tp('SampleLogs'),
      dataIndex: 'raw_log',
    },
  ],
  rowKey: 'id',
  canResize: false,
  pagination: {pageSize: 5},
  showIndexColumn: false,
  ellipsis: false,
});

//列表
const regularTableDataSource = ref<any>([]);
const {tableContext} = useListPage({
  tableProps: {
    // title: 'Json Parsing Result',
    rowKey: 'uuid',
    canResize: false,
    useSearchForm: true,
    showActionColumn: true,
    showTableSetting: false,
    actionColumn: {
      width: 300,
    },
  },
});
const [registerRegularTable] = tableContext;

/**
 * table action
 * @param record
 */
function getTableAction(record) {
  return [
    {
      label: t('common.resetText'),
      onClick: resetValue.bind(null, record),
      // ifShow: () =>  record.children ? false : true ,
    },
    {
      label: tp('CorrespondingField'),
      onClick: correspondingField.bind(null, record),
    },
  ];
}

/**
 * 下拉操作栏
 */
function getDropDownAction(record) {
  return [
    {
      label: tp('enumeratingBtn'),
      onClick: fixValue.bind(null, record, 1),
    },
    {
      label: tp('enumeratingBtn2'),
      onClick: fixValue.bind(null, record, 2),
    },
    {
      label: tp('enumeratingBtn3'),
      onClick: fixValue.bind(null, record, 3),
    },
    {
      label: tp('enumeratingBtn4'),
      onClick: fixValue.bind(null, record, 4),
    },
    {
      label: tp('enumeratingBtn5'),
      onClick: fixValue.bind(null, record, 5),
    },
    {
      label: tp('convertJsonBtn'),
      onClick: recursiveJson.bind(null, record),
      ifShow: () => !record.hide,
    },
  ];
}

/**
 * 解析json字符串变json对象
 * @param record
 */
function recursiveJson(record) {
  console.log(record)
  if (record && record.value) {
    try {
      record.children = jsonParseLog(record.value)
      if(record.children && record.children != -1){
        record.children.forEach(item => {
          item.hide = true;
        })
      }
      record.sFileName = "";
      record.newValue = "";
      record.recursiveField = record.fieldName
      delete record.value;
      delete record.bind;
      delete record.sFileType;
      delete record.sFileTypeId;
    } catch (e) {
      console.log(e)
    }
  }
  console.log(record)
  const list = regularTableDataSource.value;
  console.log(JSON.parse(JSON.stringify(list)));
  handleSource(list,record);
  console.log(list)
  console.log(regularTableDataSource.value)
}

/**
 * 处理json二次解析数据，把数据赋给regularTableDataSource
 * @param list
 * @param record
 */
function handleSource(list, record) {
  for (let i = 0; i < list.length; i++) {
    let item = list[i];
    if (item.uuid == record.uuid) {
      list[i] = record;
    } else if (item.children && item.children.length > 0) {
      handleSource(item.children, record);
    }
  }
}
/**
 * show field modal
 * @param allData
 */
const showFieldModal2 = (allData) => {
  fieldListModalRef.value.show2(allData);
};

/**
 * corresponding Field(bo lin need to update)
 */
function correspondingField(record: Recordable) {
  fieldListModalRef.value.show(record, regularTableDataSource, 1);
}



/**
 * fix value
 */
function fixValue(record: Recordable, type: number) {
  console.log(record)
  console.log(type)
  if (!record.sFileName) {
    createMessage.warning(tp('tip11'));
    return;
  }
  fixValueModalRef.value.show(record, regularTableDataSource, type);
}

function loopChageData(list, data) {
  for (let i in list) {
    let child = list[i].children;
    if (list[i]?.fieldName == data.fieldName && list[i]?.value == data.value) {
      list[i].newValue = '';
      list[i].sFileName = '';
      list[i].sFileType = '';
      list[i].sFileTypeId = '';
      list[i].sFieldTimeType = '';
      list[i].bind = '';
      list[i].regular = '';
      list[i].value_array = '';
      list[i].valueArray = '';
    }
    if (child && child.length > 0) {
      loopChageData(child, data);
    }
  }
}

/**
 * reset value
 */
function resetValue(record: Recordable) {
  createConfirm({
    title: t('common.confirm'),
    content: t('common.resetTip'),
    iconType: 'warning',
    onOk: () => {
      const list = regularTableDataSource.value;

      loopChageData(list, record);

      record.newValue = '';
      record.sFileName = '';
      record.sFileType = '';
      record.sFileTypeId = '';
      record.sFieldTimeType = '';
      record.bind = '';
      record.regular = '';
      record.value_array = '';
      record.valueArray = '';
    },
  });
}

/**
 * method 3 table
 */
function changeSeparatorTable() {
  if (separator.value) {
    let data = separator_split(separator.value, stepData.checkedLogs[carouselIndex.value].raw_log);
    regularTableDataSource.value = data;
  }
}

const saveLoading = ref(false);
/**
 * submit data
 */
function doSubmitData() {
  const data = new ParseRule(stepData);
  data.sourceType = props.sourceType;

  //复制并编辑插件规则，是新增数据，插件规则的子规则
  if (props.flag === 'copyEdit') {
    data.pid = data.id;
    data.id = '';
  }
  //管理员添加租户规则，不是mssp规则
  if (isAdmin && props.tenantType) {
    data.tenantType = props.tenantType;
  }
  saveLoading.value = true;
  saveOrUpdate(data, !!data.id, (result) => {
    saveLoading.value = false;
    if(result){
      closeRetun();
    }
  });
}

/**
 * add log source
 */
function addLog() {
  openModal(true, {
    showFooter: false,
  });
}

/**
 * json parsing result
 */
async function getParsingTreeResult() {
  let log = stepData.checkedLogs[carouselIndex.value].raw_log;
  // log = '{"assets":{"group_branch":"","group_name":"","ip":"************/24","mac":"","name":"公网IP","section":"服务器","sub_type":""},"attacker":"**************","behave_uuid":"shm204352807186090","data":"*************","dest_assets":{"ip":"","name":"","section":"","sub_type":""},"direction":"in","external_ip":"**************","external_port":47673,"geo_data":{"ChinaCode":"","City":"洛杉矶","Continent":"","Country":"美国","ISO2":"US","Isp":"","Latitude":"34.051941","Longitude":"-118.244514","Org":"","PhoneCode":"","Province":"加利福尼亚州","TimeZone":"","UTC":""},"incident_id":"","is_black_ip":true,"machine":"*************","machine_port":9034,"net":{"dest_ip":"*************","dest_port":9034,"dns":{"flags":"","id":0,"rcode":"","rdata":"","rrname":"","rrtype":"","type":""},"flow":{"pkts_toserver":0,"bytes_toclient":0,"bytes_toserver":0,"pkts_toclient":0,"state":"","app_proto":""},"http":{"method":"","protocol":"","reqs_body":"","reqs_content_length":0,"reqs_header":"","reqs_host":"","reqs_line":"","reqs_user_agent":"","resp_body":"","resp_content_length":0,"resp_header":"","resp_line":"","resp_set_cookie":"","status":0,"url":""},"http_xff":"","pop3":{"date":"","from":"","pass":"","status":"","subject":"","to":"","type":"","user":""},"proto":"UDP","real_src_ip":"**************","src_ip":"**************","src_port":47673,"type":"UDP"},"threat":{"id":"cji9gusntmhsi9dcnlj0","ioc":"","is_apt":0,"level":"attack","module":"autocc","msg":"检测到Realtek UDPServer命令注入漏洞(CVE-2021-35394,CNNVD-202108-1421)攻击。","name":"Realtek UDPServer命令注入攻击","phase":"recon","result":"unknown","severity":2,"suuid":"S3100024556","tag":["命令注入漏洞"],"type":"recon"},"time":1692702843,"timeStr":"2023-08-22T19:14:03+08:00","victim":"*************"}';

  let array: any;
  if (log) {
    array = jsonParseLog(log);
  }
  if (array == -1) {
    Modal.error({
      title: t('common.Error'),
      content: tp('tip10'),
    });
    return false;
  }

  if (historyParseMethod.value == 2 || historyParseMethod.value == 3) {
    stepData.jsonParseResult = [];
  }

  console.log(JSON.parse(JSON.stringify(array)),JSON.parse(JSON.stringify(stepData.jsonParseResult)))
  // 调用函数
  await mergeAttributes(array, stepData.jsonParseResult);

  regularTableDataSource.value = array;
  stepData.jsonParseResult = regularTableDataSource.value;

  return true;
}

/**
 * 合并数据
 * @param a
 * @param b
 */
async function mergeAttributes(a, b) {
  // 创建一个映射，用于快速查找 b 中的元素
  const bMap = {};
  b.forEach((item) => {
    if (!item.fieldName) return;
    bMap[item.fieldName] = item;
  });

  // 遍历 a 中的每个元素
  for (const itemA of a) {
    if (!itemA.fieldName) continue;

    // 找到 b 中对应的元素
    const itemB = bMap[itemA.fieldName];
    if (itemB) {
      //存在二次解析字段，需要处理json字符串转对象
      if (itemB.recursiveField){
        itemA.children = jsonParseLog(itemA.value)
        itemA.recursiveField = itemA.fieldName
      }

      // 复制指定的属性
      if (itemB.sFileName) itemA.sFileName = itemB.sFileName;
      if (itemB.sFileType) itemA.sFileType = itemB.sFileType;
      if (itemB.sFileTypeId) itemA.sFileTypeId = itemB.sFileTypeId;
      if (itemB.sFieldTimeType) itemA.sFieldTimeType = itemB.sFieldTimeType;
      if (itemB.bind) itemA.bind = itemB.bind;
      if (itemB.valueArray) {
        itemA.valueArray = itemB.valueArray;
        for (let i = 0; i < itemB.valueArray.length; i++) {

          const v = itemB.valueArray[i];
          if (v.type === 1 && v.old.toString() === itemA.value.toString()) {
            itemA.newValue = v.new;
            break;
          } else if (v.type === 2 && itemA.value >= v.start && itemA.value <= v.end) {
            itemA.newValue = v.new;
            break;
          } else if (v.type === 3 && itemA.value.toString().indexOf(v.old) !== -1) {
            itemA.newValue = v.new;
            break;
          } else if (v.type === 4) {
            const val = await regExp({log: itemA.value, regExp: v.old});
            if (val && val.length > 0) {
              let value = v.new;
              for (let i = 0; i < val.length; i++) {
                if (value) {
                  value = value.replace("$" + (i + 1), val[i].value);
                }
              }
              itemA.newValue = value;
            }
            break;
          } else if (v.type === 5) {
            itemA.newValue = atob(itemA.value);
            break;
          }
        }
      }
      // 如果有 children，递归处理
      if (itemA.children && itemB.children) {
        await mergeAttributes(itemA.children, itemB.children);
      }
    }
  }
}

function changeLogType() {
  // regularTableDataSource.value.forEach((element) => {
  //   element.sFileName = '';
  //   element.sFieldTimeType = '';
  //   element.sFileType = '';
  //   element.sFileTypeId = '';
  //   element.bind = '';
  // });
}

// watch(current, (newVal) => {

// });
watch(carouselIndex, () => {
  console.log('carouselIndex:', carouselIndex);
  console.log(regularTableDataSource.value);
  if (current.value == 4) {
    if (stepData.parseMethod == 1 && current.value == 4) {
      regularTableDataSource.value = [];
      console.log('watch index')
      getParsingTreeResult();
    } else if (stepData.parseMethod == 2 && current.value == 4) {
      handleSwitchLog();
    } else {
      regularTableDataSource.value = [];
    }
  }
});

// watch(logSourceData.value, (newVal) => {

// });

// watch(stepData, (newVal) => {

// });

function closeRetun() {
  //2024-09-09 集成添加规则
  if (props.close) {
    props.close();
    return;
  }
  //2024-09-09 集成添加规则

  if (unparseLogIds.value != '') {
    if (fromMenu.value == 'logManager') {
      router.push('/configure/logsourcemanager/LogSourceManagerList');
    } else if (fromMenu.value == 'unparseLog') {
      router.push('/configure/unparsedLog/UnparsedLogList');
    }
  } else {
    let state = {};
    if (history.state.data) {
      state = JSON.parse(history.state.data);
    }
    router.push({
      path: '/parseRule',
      state: {data: JSON.stringify(state)},
    });
  }
}

function RollbackLog() {
  if (stepData.checkedLogs[carouselIndex.value].raw_log_old) {
    stepData.checkedLogs[carouselIndex.value].raw_log = stepData.checkedLogs[carouselIndex.value].raw_log_old;
  }
}

const markLog = () => {
  //json格式如果添加的Front reference mark和Post reference mark需要处理选值的日志，掐头去尾
  let logFrontMark = stepData.logFrontMark;
  let logPostMark = stepData.logPostMark;
  let log = stepData.checkedLogs[carouselIndex.value].raw_log_old ?? stepData.checkedLogs[carouselIndex.value].raw_log;
  if (!stepData.checkedLogs[carouselIndex.value].raw_log_old) {
    stepData.checkedLogs[carouselIndex.value].raw_log_old = log;
  }

  if (logFrontMark) {
    if (log) {
      let index1 = log.indexOf(logFrontMark);
      if (index1 > -1) {
        index1 += logFrontMark.length;
        log = log.substring(index1, log.length);
      }
    }
  }

  if (logPostMark) {
    if (log) {
      let index1 = log.lastIndexOf(logPostMark);
      if (index1 > -1) {
        log = log.substring(0, index1);
      }
    }
  }
  stepData.checkedLogs[carouselIndex.value].raw_log = log;
};

const tenantList = ref<any[]>([]);
const tempJsonParseResult = ref<any[]>([]);

/**
 * next step
 */
function doNext() {
  if (current.value == 0) {
    if (!stepData.ruleName) {
      Modal.info({
        title: t('common.Warning'),
        content: tp('tip9'),
      });
      return;
    }
    if (showHomeTenant.value && !stepData.socTenantId) {
      Modal.info({
        title: t('common.Warning'),
        content: tp('tip24'),
      });
      return;
    }
  } else if (isUpdate.value && current.value == 0) {
    // current.value = 4;
    // return false;
  } else if (current.value == 1) {
    //添加的时候校验
    let flag = getAllCheckedLog();
    if (!flag) {
      return;
    }
  } else if (current.value == 2) {
    markLog();
  } else if (current.value == 4) {
    //file parsing
    // if(!isUpdate.value){
    //file parsing
    if (stepData.parseMethod == '6') {
      if (!stepData.fileParsePath) {
        Modal.info({
          title: t('common.Warning'),
          content: tp('tip8'),
        });
        return false;
      }
    } else if (stepData.parseMethod == '1') {
      //正则解析后下一步 把数据存到stepData里
      console.log(regularTableDataSource.value,stepData)
      handleRegData(regularTableDataSource.value, stepData, null);
    } else if (stepData.parseMethod == '2') {
      //正则解析后下一步 把数据存到stepData里

      tempJsonParseResult.value = stepData.jsonParseResult;
      handleRegData(regularTableDataSource.value, stepData, regularValue);
    } else if (stepData.parseMethod == '3') {
      //分隔符解析后下一步 把数据存到stepData里
      handleRegData(regularTableDataSource.value, stepData, separator);
    }
    // }
  } else if (current.value == 6) {
    tenantList.value = [];

    let map = toRaw(tenantMap.value);
    let tenant = stepData.tenant;
    if (tenant) {
      let array1 = tenant.split(',');
      for (let i = 0; i < array1.length; i++) {
        let array2 = array1[i].split(':');
        if (array2[1] == '1') {
          tenantList.value.push({
            name: map[array2[0]].name,
            id: map[array2[0]].id,
          });
        }
      }
    }
  }

  current.value = current.value + 1;
  if (current.value != 6) {
    //Prevent backtracking, table is empty
    carouselIndex.value = 0;
  }
}

function getAllCheckedLog() {
  // const logSourceData = ref([]);
  // const fileLogData = ref([]);

  if (unparseLogIds.value != '') {
    const logs = unparseLogArr.value.filter((item) => item.checked == true);
    stepData.checkedLogs = [...logs];

    return true;
  }
  const sourceLog = logSourceData.value.filter((item) => item.checked == true);
  const fileLog = fileLogData.value.filter((item) => item.checked == true);

  if (!isUpdate.value) {
    if (sourceLog.length == 0 && fileLog.length == 0) {
      Modal.info({
        title: t('common.Warning'),
        content: tp('tip'),
      });
      return false;
    }
  }
  let num = sourceLog.length + fileLog.length;
  if (num > 10) {
    Modal.info({
      title: t('common.Warning'),
      content: tp('tip7'),
    });
    return false;
  }
  // 检查选中数据是否有过滤条件，并且大小写开关开启时是否有，没有的话则报错
  // if (stepData.logFilter.length > 0 && stepData.logFilter[0].value !== '') {
  //   if (fileLog.length > 0) {
  //     let logFilter = fileLog.filter((a) => a.checked == true);
  //     let logMap = logFilter.map((ele) => ele.raw_log);
  //     const hasInvalidValue = stepData.logFilter.some((e) => {
  //       // 检查logMap的值是否包含e.value这个字符串
  //       const hasEventCount = Object.values(logMap).some((value: any) => value.includes(e.value));
  //       if (!hasEventCount) {
  //         // 如果没有匹配的值，返回true
  //         return true;
  //       }
  //
  //       if (e.flag) {
  //         // 如果e.flag为true，且logMap的值忽略大小写地包含e.value
  //         const hasEventCount1 = Object.values(logMap).some((value: any) => value.toLowerCase().includes(e.value.toLowerCase()));
  //         if (!hasEventCount1) {
  //           // 如果没有匹配的值，返回true
  //           return true;
  //         }
  //       }
  //       // 如果没有不匹配的，返回false
  //       return false;
  //     });
  //
  //     if (hasInvalidValue) {
  //       Modal.info({
  //         title: t('common.Warning'),
  //         content: tp('tipfilter'),
  //       });
  //       return false;
  //     }
  //   }
  //
  //   if (sourceLog.length > 0) {
  //     let logFilter = sourceLog.filter((a) => a.checked == true);
  //     let logMap = logFilter.map((ele) => ele.raw_log);
  //     const hasInvalidValue = stepData.logFilter.some((e) => {
  //       // 检查logMap的值是否包含e.value这个字符串
  //       const hasEventCount = Object.values(logMap).some((value) => value.includes(e.value));
  //       if (!hasEventCount) {
  //         // 如果没有匹配的值，返回true
  //         return true;
  //       }
  //
  //       if (e.flag) {
  //         // 如果e.flag为true，且logMap的值忽略大小写地包含e.value
  //         const hasEventCount1 = Object.values(logMap).some((value) => value.toLowerCase().includes(e.value.toLowerCase()));
  //         if (!hasEventCount1) {
  //           // 如果没有匹配的值，返回true
  //           return true;
  //         }
  //       }
  //       // 如果没有不匹配的，返回false
  //       return false;
  //     });
  //
  //     if (hasInvalidValue) {
  //       Modal.info({
  //         title: t('common.Warning'),
  //         content: tp('tipfilter'),
  //       });
  //       return false;
  //     }
  //   }
  // }
  if (stepData.logFilter.length > 0 && stepData.logFilter[0].value !== '') {
    if (fileLog.length > 0) {
      let logFilter = fileLog.filter((a) => a.checked == true);
      let logMap = logFilter.map((ele) => ele.raw_log);
      const hasInvalidValue = stepData.logFilter.some((e) => {
        // 检查logMap的值是否包含e.value这个字符串
        const hasEventCount = Object.values(logMap).some((value: any) => value.includes(e.value));
        if (!hasEventCount) {
          // 如果没有匹配的值，返回true
          return true;
        }

        if (e.flag) {
          // 如果e.flag为true，且logMap的值忽略大小写地包含e.value
          const hasEventCount1 = Object.values(logMap).some((value: any) => value.toLowerCase().includes(e.value.toLowerCase()));
          if (!hasEventCount1) {
            // 如果没有匹配的值，返回true
            return true;
          }
        }
        // 如果没有不匹配的，返回false
        return false;
      });

      if (hasInvalidValue) {
        Modal.info({
          title: t('common.Warning'),
          content: tp('tipfilter'),
        });
        return false;
      }
    }

    if (sourceLog.length > 0) {
      let logFilter = sourceLog.filter((a) => a.checked == true);
      let logMap = logFilter.map((ele) => ele.raw_log);
      const hasInvalidValue = stepData.logFilter.some((e) => {
        // 检查logMap的值是否包含e.value这个字符串
        const hasEventCount = Object.values(logMap).some((value) => value.includes(e.value));
        if (!hasEventCount) {
          // 如果没有匹配的值，返回true
          return true;
        }

        if (e.flag) {
          // 如果e.flag为true，且logMap的值忽略大小写地包含e.value
          const hasEventCount1 = Object.values(logMap).some((value) => value.toLowerCase().includes(e.value.toLowerCase()));
          if (!hasEventCount1) {
            // 如果没有匹配的值，返回true
            return true;
          }
        }else if(!e.isContain){
          //过滤·： 不包含才能进去
          const hasEventCount1 = Object.values(logMap).some((value) => !value.includes(e.value));
          if(!hasEventCount1){
            return true;
          }
        }
        // 如果没有不匹配的，返回false
        return false;
      });

      if (hasInvalidValue) {
        Modal.info({
          title: t('common.Warning'),
          content: tp('tipfilter'),
        });
        return false;
      }
    }
  }

  if (sourceLog.length > 0 || fileLog.length > 0) {
    stepData.checkedLogs = [...sourceLog, ...fileLog];
  }

  if (oldCheckedLogs !== JSON.stringify(stepData.checkedLogs)) {
    //解析日志变更
    logIsUpdate.value = true;
  }

  return true;
}

/**
 * last step
 */
function doBack() {
  // if (current.value == 7) {
    //租户或没有开启租户,跳过选择租户步骤
    // if (!showTopTenant.value) {
    //   current.value = current.value - 1;
    // }
  // }
  //2024-11-20 没有选安全或主机表，不填写聚合信息
  // if (current.value == 6) {
    // if (stepData.logType != 1 && stepData.logType != 2) {
    //   current.value = current.value - 1;
    // }
  // }
  //如果先选了正则解析之后想要再修改解析方式，讲JsonParseResult的值还原
  if (stepData.parseMethod == 2 && current.value == 5) {
    stepData.jsonParseResult = tempJsonParseResult.value;
  }
  current.value = current.value - 1;
}

//设立临时变量，记录上一步的解析方式
const historyParseMethodTemp = ref(historyParseMethod.value);

/**
 * check parse method
 * @param item
 */
async function checkMethod(item) {
  // if (isUpdate.value && item.value == stepData.parseMethod) {
  //   current.value = current.value + 1;
  //   return;
  // }
  historyParseMethodTemp.value = stepData.parseMethod;
  stepData.parseMethod = item.value;
  carouselIndex.value = 0;
  //如果之前设置的方法是json清空数据
  //如果之前设置的方法是分隔符并且现在点进去的是json或者正则，清空数据
  if (
    historyParseMethod.value == 1 ||
    (historyParseMethod.value == 3 && (stepData.parseMethod == 2 || stepData.parseMethod == 1)) ||
    (historyParseMethod.value == 2 && (stepData.parseMethod == 3 || stepData.parseMethod == 1)) ||
    historyParseMethodTemp.value != stepData.parseMethod
  ) {
    regularTableDataSource.value = [];
  }
  let flag = true;
  //json parse
  if (stepData.parseMethod == 1) {
    flag = await getParsingTreeResult();
  } else if (stepData.parseMethod == 2 && !isUpdate.value) {
    createRegularValueDefaultText();
  } else if (stepData.parseMethod == 2 && logIsUpdate.value) {
    //日志变更了，需要重新解析一遍日志，但是保留之前配置的字段，按照顺序对应
    await getExecRegular();
    await mergeAttributes(regularTableDataSource.value, stepData.jsonParseResult.dataValue);
    if (regularTableDataSource.value.length === 0) {
      message.warning(tp('tip25'));
    }
    logIsUpdate.value = false;
  } else if (stepData.parseMethod == 3 && logIsUpdate.value) {
    changeSeparatorTable();
    if (regularTableDataSource.value && stepData.jsonParseResult.dataValue) {
      await mergeAttributes(regularTableDataSource.value, stepData.jsonParseResult.dataValue);
    }
  }

  if (flag) {
    current.value = current.value + 1;
  }
}

/**
 * 切换日志处理
 */
async function handleSwitchLog() {
  const old = JSON.parse(JSON.stringify(regularTableDataSource.value));
  await getExecRegular();
  await mergeAttributes(regularTableDataSource.value, old);
}

/**
 * get log query param
 */
function getQueryParam() {
  let startDate = formatToDateTime(dayjs().subtract(7, 'day'));
  let time = queryParam.value.time;
  if (!time) {
    //default is 7 days
    startDate = formatToDateTime(dayjs().subtract(30, 'day'));
  } else if (time == '24h') {
    startDate = formatToDateTime(dayjs().subtract(24, 'hour'));
  } else if (time == '7d') {
    startDate = formatToDateTime(dayjs().subtract(7, 'day'));
  } else if (time == '30d') {
    startDate = formatToDateTime(dayjs().subtract(30, 'day'));
  } else if (time == '3m') {
    startDate = formatToDateTime(dayjs().subtract(3, 'month'));
  } else if (time == '1y') {
    startDate = formatToDateTime(dayjs().subtract(1, 'year'));
  }
  let rawLog = queryParam.value.rawLog;
  let param = {
    fromIp: checkLogSource.value.logSourceIp,
    socTenantId: checkLogSource.value.socTenantId,
    limitNum: 100,
    startTime: startDate,
    rawLog: rawLog,
  };

  return param;
}

function handleCheckLog(data) {
  checkLogSource.value = data.data;
  stepData.device = data.data;
  doSearch();
}

function searchFilter() {
  queryParam.value.rawLog = getFilterStr();
}

/**
 * 获取过滤条件字符串
 */
function getFilterStr() {
  const array: any[] = [];
  for (let i = 0; i < stepData.logFilter.length; i++) {
    if (stepData.logFilter[i].value) {
      let flag = !!stepData.logFilter[i].flag;
      let isContain = !!stepData.logFilter[i].isContain;
      array.push(stepData.logFilter[i].value + splitSymbol2 + flag + splitSymbol2 + isContain);
    }
  }
  return array.join(splitSymbol);
}
// function getFilterStr() {
//   const array: any[] = [];
//   for (let i = 0; i < stepData.logFilter.length; i++) {
//     if (stepData.logFilter[i].value) {
//       let flag = !!stepData.logFilter[i].flag;
//       array.push(stepData.logFilter[i].value + splitSymbol2 + flag );
//     }
//   }
//   return array.join(splitSymbol);
// }

/**
 * 添加过滤条件
 */
function addFilter() {
  stepData.logFilter.push({value: '', flag: false, isContain: true});
}
// function addFilter() {
//   stepData.logFilter.push({value: '', flag: false});
// }

/**
 * 删除过滤条件
 * @param index
 */
function delFilter(index) {
  stepData.logFilter.splice(index, 1);
  searchFilter();
}

/**
 * search log
 */
function doSearch() {
  const param = getQueryParam();
  loadUnparsedLog(param).then((data) => {
    for (let i in data) {
      data[i].raw_log = data[i].rawLog;
    }
    logSourceData.value = data;
  });
}

let unparseLogArr_lod = reactive<any[]>([]);

/**
 * filter UnparseLog
 * @param f
 */
function doSearchUnparseLog() {
  let text = queryParam.value.rawLog;
  if (unparseLogArr_lod.length == 0) {
    for (let i = 0; i < unparseLogArr.value.length; i++) {
      let base = {};
      for (let j in unparseLogArr.value[i]) {
        base[j] = unparseLogArr.value[i][j];
      }
      unparseLogArr_lod.push(base);
    }
  }
  let source = unparseLogArr_lod;

  source = source.filter((item) => {
    const array = text.split(splitSymbol);
    let flag = false;
    for (let i = 0; i < array.length; i++) {
      flag = item.rawLog.indexOf(array[i]) > -1;
      if (!flag) {
        break;
      }
    }
    return flag;
  });
  unparseLogIds.value = source.map((item) => item.logId);
  unparseLogArr.value = source;
}

function changeFile(f) {
  if (f.file.response) {
    fileLogData.value = [];
    let msg = f.file.response.result;
    if (msg) {
      let json = msg;

      let arr = [];
      json.forEach((item) => {
        arr.push({raw_log: item, checked: false});
      });
      fileLogData.value = arr;
    }
  }
}

function changeParseFile(f) {
  stepData.fileName = f.file.name;
  if (f.file.response) {
    fileLogData.value = [];
    let msg = f.file.response.result.list;
    let path = f.file.response.result.path;
    stepData.fileParsePath = path;
    if (msg) {
      let json = msg;

      stepData.parseFileRule = json;
    }
  }
}

const fileTableDataSource = ref<any>([]);

async function expandFileData(value, record) {
  if (value) {
    fileTableDataSource.value = await reg_execRegular(stepData.parseFileRule, record.raw_log);
  }
}

/**
 * checked apply log source
 */
function setApply(data) {
  stepData.applyData = data;
}

/**
 * delete upload file
 */

/**
 * download upload file
 */
function downLoadFile() {
  downloadFileBlob(stepData.fileParsePath, stepData.fileName);
}

/**
 * delete upload files
 */
function deleteUploadFiles() {
  deleteFile(stepData.fileParsePath, (data) => {
    if (-1 != data.indexOf('success')) {
      stepData.fileParsePath = '';
      stepData.fileName = '';
    }
  });
}

function createRegularValueDefaultText() {
  console.log('createRegularValueDefaultText');
  let text = stepData.checkedLogs[carouselIndex.value].raw_log;

  if (!text) return;
  let val = '';
  if (text.indexOf('=') == -1) return (regularValue.value = '(.+)');
  val = '';
  var i = 0;
  let suffix = ',';
  let suffix_1 = text.split(',');
  suffix_1 = suffix_1.filter((item) => /(.)=(.)/.test(item));
  let suffix_2 = text.split(' ');
  suffix_2 = suffix_2.filter((item) => /(.)=(.)/.test(item) && (item.lastIndexOf(',') == -1 || item.lastIndexOf(',') != item.length - 1));
  if (suffix_2.length > suffix_1.length) suffix = ' ';

  let index = 0; //text.indexOf("(")+1;
  while (text.indexOf('=', index) > -1) {
    let dd = text.indexOf('=', index);
    let key = text.substring(index, dd);
    let dh = text.indexOf(suffix, index);
    if (key.indexOf('(') > -1) {
      key = key.replaceAll('(', '\\(');
    }
    key = key.replaceAll(')', '\\)');

    let value = '';
    if (dh == -1) {
      //let hk = text.lastIndexOf(")");
      //if(hk==-1)hk = text.length;
      let hk = text.length;
      value = text.substring(dd + 1, hk);
      index = index + key.length + 1;
    } else {
      value = text.substring(dd + 1, dh);
      index = dh + 1;
    }

    if (value == '') {
      if (suffix == ' ' && !key.startsWith(suffix)) {
        key = suffix + key;
      }
      val += key + '=()';
    } else if (value.indexOf('=') > -1) {
      continue;
    } else {
      if (i > 0) val += suffix;
      i = 1;
      val += key + '=(.+)';
    }
  }
  val += '';

  return (regularValue.value = val);
}

const applyAll = ref(false);

const applyAllLoading = ref(false);

function doChange() {
  if (applyAll.value == true) {
    applyAllLoading.value = true;
    Modal.confirm({
      title: t('common.tips'),
      content: t('routes.parserule.tip1'),
      okText: t('common.okText'),
      cancelText: t('common.cancelText'),
      onOk: () => {
        applyAllLoading.value = false;
        stepData.applyAll = true;
      },
      onCancel: () => {
        applyAllLoading.value = false;
        applyAll.value = false;
        stepData.applyAll = false;
      },
    });
  } else {
    stepData.applyAll = false;
  }
}

async function getExecRegular() {
  regularTableDataSource.value = await reg_execRegular(regularValue.value, stepData.checkedLogs[carouselIndex.value].raw_log);
}

const addLocalTextModaRef = ref();

function openInput() {
  opentxtModal(true, {
    showFooter: false,
  });
}

function handleData(data) {
  fileLogData.value = [];
  let msg = data;
  if (msg) {
    let json = msg;

    let arr = [] as any;
    json.forEach((item) => {
      arr.push({raw_log: item, checked: false});
    });
    fileLogData.value = arr;
  }
}

const RegularTipRef = ref();

function showTip() {
  RegularTipRef.value.showTip();
}
</script>

<style lang="less" scoped>
@import './index.less';

/deep/ .ax-step-wrapper {
  background-color: @dark-bg1 !important;
}

.border-top-radius {
  border-radius: 0 0 12px 12px !important;
}

.border-bottom-radius {
  border-radius: 12px 12px 0 0 !important;
}

.step-content_4 {
  position: relative;
}

.field_list__div {
  position: absolute;
  right: 15px;
  z-index: 9;
  top: 8px;
}

.align-right {
  align-items: flex-end;
}

.step_block_wrapper {
}

.step_block {
  border-radius: 8px;
  background: @dark-bg2;

  .step1-input {
    width: 236px;

    /deep/ .ant-input-number {
      width: 100%;
    }
  }

}

.bottom_radius_0{
  border-radius: 8px 8px 0 0 !important;
}
.top_radius_0{
  border-radius: 0 0 8px 8px !important;
}
.log-list-content {
  border-radius: 8px;
  max-height: 400px;
  overflow-y: auto;
  width: 100%;
  background: #414356;

  padding: 8px;

  .log-item_checked {
    background: @primary-bg-01;
  }
}

.disabled-button {
  opacity: 0.5;
  cursor: not-allowed;
}
</style>
