<template>
  <div class="view_body">
    <div class="view_body_div">
      <div class="flex_column">

        <div class="flex_row border_bottom" v-if="type == 'parentRule' || type == 'pluginRule'">
          <div style="min-width: 120px;">
            {{ tp('RuleName') }}
          </div>
          <div style="display: flex; gap: 10px">
            <div class="text_bg">
              {{ dataSource?.ruleName }}
            </div>
          </div>
        </div>
        <div class="flex_row border_bottom" v-if="type == 'parentRule'">
          <div style="min-width: 120px;">
            {{ tp('RuleType') }}
          </div>
          <div style="display: flex; gap: 10px">
            <div class="text_bg">
              {{ dataSource?.tenantType == 3 ? tp('PluginRule') : tp('MSSPRule') }}
            </div>
          </div>
        </div>

        <div class="flex_row border_bottom" v-if="type == 'pluginRule'">
          <div style="min-width: 120px;">
            {{ tp('integrationPlugins') }}
          </div>
          <div style="display: flex; gap: 10px">
            <div v-for="(text, index) in dataSource?.pluginNameList" :key="index" class="text_bg">
              {{ text }}
            </div>
          </div>
        </div>

        <div class="flex_row border_bottom">
          <div style="min-width: 120px;">
            {{ tp('filter') }}
          </div>
          <div style="display: flex; gap: 10px">
            <div v-for="(text, index) in dataSource?.logFilters" :key="index" class="text_bg">
              {{
								text ?
										(text.split(splitSymbol2)[0] +
												(text.split(splitSymbol2)[1] === 'false' ? ' (' + tp('Case-Sensitive') + ')' : ' (' + tp('Case-Insensitive') + ')'))
										: ''
							}}
            </div>
          </div>
        </div>
        <div class="flex_row border_bottom">
          <div style="min-width: 120px;">
            {{ tp('clipper') }}
          </div>
          <div style="display: flex; gap: 10px">
            <div class="text_bg">
              {{ tp('FrontReferenceMark') }} : {{ dataSource?.logFrontMark }}
            </div>
            <div class="text_bg">
              {{ tp('PostReferenceMark') }} : {{ dataSource?.logPostMark }}
            </div>
          </div>
        </div>
        <div class="flex_row border_bottom">
          <div style="min-width: 120px;">
            {{ tp('logtype') }}
          </div>
          <div style="display: flex; gap: 10px">
            <div class="text_bg">
              {{ LOG_TYPE[dataSource?.logType] }}
            </div>
          </div>
        </div>
        <div class="flex_row border_bottom">
          <div style="min-width: 120px;">
            {{ tp('ParseMethod') }}
          </div>
          <div style="display: flex; gap: 10px">
            <div class="text_bg">
              {{ METHOD_TYPE[dataSource?.parseMethod] }}
            </div>
          </div>
        </div>

        <div class="flex_row border_bottom" v-if="dataSource?.parseMethod == 2">
          <div style="min-width: 120px;">
            {{ tp('regularExpression') }}
          </div>
          <div style="display: flex; gap: 10px">
            <div class="text_bg">
              {{ dataSource?.jsonParseResult?.regularValue }}
            </div>
          </div>
        </div>
        <div class="flex_row border_bottom" v-if="dataSource?.parseMethod == 3">
          <div style="min-width: 120px;">
            {{ tp('Separator') }}
          </div>
          <div style="display: flex; gap: 10px">
            <div class="text_bg">
              {{ dataSource?.jsonParseResult?.regularValue }}
            </div>
          </div>
        </div>

        <div class="flex_row border_bottom">
          <div style="min-width: 120px;">
            {{ tp('sampleLog') }}
          </div>
          <div style="display: flex; gap: 10px">
            <div class="text_bg">
              {{ dataSource?.checkedLogs ? dataSource?.checkedLogs[0].raw_log_old : '' }}
            </div>
          </div>
        </div>
        <div class="flex_row border_bottom">
          <div style="min-width: 120px;">
            {{ t('common.sort') }}
          </div>
          <div style="display: flex; gap: 10px">
            <div class="text_bg">
              {{ dataSource?.ruleSort }}
            </div>
          </div>
        </div>
        <div style="width: 100%;">
          <div class="flex_row" style="justify-content: right;">
            <a-checkbox v-model:checked="showValueFile" @change="showValueFileChange">
              {{ tp('displayField') }}
            </a-checkbox>
          </div>
          <BasicTable
            @register="registerRegularTable"
            :dataSource="showTableDataSource"
            :columns="dataSource.parseMethod == 1 ? treeColumns : regularColumns"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import {defineProps, ref} from 'vue';
import {queryById} from '/@/views/parseRule/ParseRuleManage.api';

import {
  LOG_TYPE,
  METHOD_TYPE,
  regularColumns,
  treeColumns,
} from '/@/views/parseRule/ParseRuleManage.data';
import {BasicTable} from '/@/components/Table';
import {useListPage} from '/@/hooks/system/useListPage';
import {parseStepData} from '/@/views/parseRule/ParseRuleUtils';
import {useI18n} from '/@/hooks/web/useI18n';
import {splitSymbol, splitSymbol2} from '/@/views/parseRule/ParseRule';
import item from "/@/views/ticket/edit/component/flow/permission/condition/component/item.vue";

const {t} = useI18n();

function tp(name) {
  return t('routes.parserule.' + name);
}

const emits = defineEmits(['setData']);
const props = defineProps({
  id: String,
  type: String
});

const dataSource = ref<any>({});
const socTenantId = ref('');
const pageWidth = ref('80%');
const showValueFile = ref(true);

const ruleId = ref('');
pageWidth.value = '100%';
loadInfo(props.id);

function loadInfo(id) {
  showValueFile.value = true;
  ruleId.value = id;
  socTenantId.value = '';
  queryById({id}).then((data) => {
    data = parseStepData(data);
    emits('setData', data)

    if (data.parseMethod == '1') {
      regularTableDataSource.value = data.jsonParseResult;
    } else if (data.parseMethod == '2' || data.parseMethod == '3') {
      if (data.jsonParseResult.dataValue) {
        regularTableDataSource.value = data.jsonParseResult.dataValue;
      }
    } else if (data.parseMethod == '6') {
      let json = JSON.parse(data.fileParsePath);
      data.fileParsePath = json.path;
      data.fileName = json.fileName;
    }
    const logFilter = data.logFilter;
    if (logFilter) {
      data.logFilters = logFilter.split(splitSymbol);
    }
    if (data.pluginName) {
      data.pluginNameList = data.pluginName.split(",");
    }
    showValueFileChange();
    console.log(data);
    dataSource.value = data;
  });
}

//列表
const regularTableDataSource = ref<any[]>([]);

const showTableDataSource = ref<any[]>([]);
const {tableContext} = useListPage({
  tableProps: {
    // title: 'Json Parsing Result',
    rowKey: 'fieldName',
    canResize: false,
    useSearchForm: false,
    showActionColumn: false,
  },
});
const [registerRegularTable] = tableContext;


function showValueFileChange() {
  console.log(regularTableDataSource.value)
  if (showValueFile.value) {
    const list: any = JSON.parse(JSON.stringify(regularTableDataSource.value));
    showTableDataSource.value = filterData(list);
  } else {
    showTableDataSource.value = regularTableDataSource.value;
  }
}

function filterData(data) {
  // 判断一个对象是否包含sFileName
  function hasSFileName(item) {
    return item.sFileName && item.sFileName.trim() !== '';
  }

  // 递归处理children
  function processChildren(children) {
    if (!children) return null;

    const filteredChildren = children.filter(child => {
      if (hasSFileName(child)) {
        // 如果当前项包含sFileName，直接保留
        return true;
      } else if (child.children) {
        // 如果当前项不包含sFileName，但有子项
        const filteredChildren = processChildren(child.children);
        if (filteredChildren) {
          // 如果子项中有包含sFileName的，保留当前项，并更新子项
          child.children = filteredChildren;
          return true;
        }
      }
    });
    if (filteredChildren.length === 0) return null;

    return filteredChildren;
  }

  // 过滤主数据
  return data.filter(item => {
    if (hasSFileName(item)) {
      // 如果当前项包含sFileName，直接保留
      return true;
    } else if (item.children) {
      // 如果当前项不包含sFileName，但有子项
      const filteredChildren = processChildren(item.children);
      if (filteredChildren) {
        // 如果子项中有包含sFileName的，保留当前项，并更新子项
        item.children = filteredChildren;
        return true;
      }
    }
    return false;
  });
}

</script>

<style scoped lang="less">
.view_body {

  margin-top: 12px;

  .view_body_div {
    width: v-bind(pageWidth);
    margin: auto;
    border-radius: 12px;

    .flex_column {
      display: flex;
      gap: 10px;
      flex-flow: column;

      .flex_row {
        display: flex;
        gap: 10px;
        min-height: 30px;
        align-items: center;
        padding: 0 16px 8px 16px;

        .text_bg {
          background-color: @bg-color;
          padding: 2px 6px;
          border-radius: 4px;
          word-break: break-all;
        }
      }
    }

    .border_bottom {
      border-bottom: 1px sloid @border-color;
    }

    .rule_name {
      padding-bottom: 12px;
    }

    :deep(.ant-form-item-label > label) {

      font-size: 13px !important;
      font-weight: 600;
      line-height: 20px;
      letter-spacing: 0px;
      color: @font-color-white;
    }

    :deep(.fcolor1 .ant-form-item-label > label) {
      color: @font-color-default;
    }

    :deep(.ant-form-item-control-input-content) {

      font-size: 13px;
      font-weight: normal;
      line-height: 20px;
      letter-spacing: 0em;

      /* Font/白0.6 */
      color: @font-color-1;
    }

    .tenant_span {
      display: flex;
      flex-direction: column;
      padding: 4px 8px;

      /* 品牌色/SOC蓝/0.2 */
      background: rgba(48, 140, 255, 0.2);
      margin: 5px 10px;
      border-radius: 4px;
      color: @m-text-color;
    }
  }
}

:deep(.ant-form-item) {
  margin-bottom: 12px;
}

.return-wraper {
  padding: 10px 16px;
  cursor: pointer;
  border-bottom: 1px solid @border-color;
}
</style>
