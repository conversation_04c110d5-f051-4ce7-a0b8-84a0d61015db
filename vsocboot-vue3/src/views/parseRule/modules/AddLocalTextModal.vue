<template>
  <BasicModal
    v-bind="$attrs"
    @register="registerModal"
    :title="tp('AddLocalText')"
    width="1200px"
    :helpMessage="tp('tiptext')"
    @ok="doOK"
    @cancel="handleCancel"
  >
    <div style="padding: 16px">
      <a-textarea v-model:value="jsonContent" style="min-height: 230px; height: 230px"></a-textarea>
    </div>
  </BasicModal>
</template>

<script lang="ts" setup>
  import { ref } from 'vue';
  import { BasicModal, useModalInner } from '/@/components/Modal';
  import { useI18n } from '/@/hooks/web/useI18n';
  const { t } = useI18n();
  function tp(name) {
    return t('routes.parserule.' + name);
  }
  // Emits声明
  const emit = defineEmits(['register', 'handleData']);

  //表单赋值
  const [registerModal, { closeModal }] = useModalInner(async () => {});
  const result = ref<any[]>([]);
  const jsonContent = ref();

  //表单提交事件
  function doOK(data) {
    // 将数据按行分割，处理换行符
    // 匹配 \n 或 \r\n
    const lines = jsonContent.value.split(/\r?\n\r?\n/);
    // 遍历每一行，将每一行直接推入结果数组
    lines.forEach((line) => {
      if (line.trim()) {
        // 忽略空行
        result.value.push(line.trim());
      }
    });
    emit('handleData', result.value);
    closeModal();
    jsonContent.value = null;
  }

  function handleCancel() {
    jsonContent.value = null;
  }
</script>

<style lang="less" scoped></style>
