<template>
  <a-drawer v-model:visible="visible" class="custom-class h-[100%] ax-route-bg1"
            :title="tp('fieldList')" placement="right">
    <div style="padding: 16px;height: calc(100% - 46px)" class="relative">
      <a-input v-model:value="searchInput" @input="searchChange" class="mb-16px">
        <template #suffix>
          <span class="soc ax-com-Search"></span>
        </template>
      </a-input>
      
      <div class="ax-tab-card h-[100%] overflow-hidden">
        <a-tabs type="card" v-model:activeKey="tabKey">
          <a-tab-pane v-for="tab in tabs" :key="tab.key" :tab="tab.tab">
            <div class="title_content">{{ tp('NotUsed') }}</div>
            <div class="p-16px">
              <a-checkbox-group v-model:value="selectValue" name="radioGroup" style="width: 100%"
                                class="flex flex-col gap-16px">
                <template v-for="item in filterList" :key="item.id">
                  <div v-if="!item.used && isFieldTypeMatch(item.fieldType, tab.type) && item.show"
                       class="notUsed">
                    <span style="flex: 1">
                      <a-checkbox :value="item.fieldValue">{{ item.fieldValue }}</a-checkbox>
                    </span>
                    <div class="flex flex-row flex-wrap gap-4px">
                      <div v-for="(tag, index) in item.tagList" :key="index"
                           class="ax-label ax-label-primary">
                        {{ tag }}
                      </div>
                    </div>
                  </div>
                </template>
              </a-checkbox-group>
            </div>
            <div class="title_content pt-8px">{{ tp('Used') }}</div>
            <div class="p-16px">
              <template v-for="item in filterList" :key="item.id">
                <div v-if="item.used && isFieldTypeMatch(item.fieldType, tab.type)" class="used">
                  {{ item.fieldValue }}
                </div>
              </template>
            </div>
          </a-tab-pane>
        </a-tabs>
      </div>
      <div v-if="!isShow" class="absolute top-111px right-32px">
        <a-button @click="setField" size="small">
          <span class="soc ax-com-Add ax-icon"></span>
          {{ t('common.add') }}
        </a-button>
      </div>
    </div>
  </a-drawer>
  
  <a-modal v-model:visible="timeTypeVisible" :title="tp('TimeFormat')" @ok="handleOk"
           :destroy-on-close="true">
    <div class="padding16">
      <a-checkbox-group v-model:value="timeType">
        <div style="padding: 4px 0;" v-for="(item,index) in timeTypeList" :key="index">
          <a-checkbox :value="item.format">
            <div>
              {{ item.format }}
            </div>
            <div>
              {{ item.example }}
            </div>
          </a-checkbox>
        </div>
      </a-checkbox-group>
    </div>
  </a-modal>
</template>

<script setup name="FieldListModal" lang="ts">
import {defineEmits, defineExpose, ref} from 'vue';
import {useI18n} from '/@/hooks/web/useI18n';
import {getTabLogFieldList} from "/@/utils/ckTable";
import {getTimeTypeList} from "/@/views/parseRule/ParseRuleManage.api";

const {t} = useI18n();

function tp(name) {
  return t('routes.parserule.' + name);
}

const emit = defineEmits(['ok']);

const visible = ref(false);
const filterList = ref<any>([]);
const filterMap = ref({});
const searchInput = ref('');

const timeTypeVisible = ref(false);


async function loadListData() {
  filterList.value = [];
  
  const data = getTabLogFieldList();
  console.log(data);
  for (let i = 0; i < data.length; i++) {
    //ckEnterDate不作为解析字段,ruleField=2的才能作为解析规则字段
    if (data[i].fieldValue === 'ckEnterDate') {
      continue;
    }
    data[i].show = true;
    const tagName = data[i].tagName;
    data[i].tagList = [];
    if (tagName) {
      data[i].tagList = tagName.split(',');
    }
    filterList.value.push(data[i]);
    filterMap.value[data[i].fieldValue] = data[i];
  }
}

// loadListData(1)
let selectRow: any = {};
const selectValue = ref<any>([]);
let sourceData: any = [];
let sourceFlag = 0;

async function getTimeTypeListData() {
  
  await getTimeTypeList().then(data => {
    console.log(data)
    timeTypeList.value = data
  })
  
}

async function show(data, allData, flag) {
  searchInput.value = '';
  await loadListData();
  await getTimeTypeListData();
  isShow.value = false;
  tabKey.value = '1';
  sourceData = allData;
  sourceFlag = flag;
  selectValue.value = [];
  selectRow = data;
  console.log('selectRow', selectRow);
  
  for (let j in filterList.value) {
    filterList.value[j].used = false;
  }
  loop(allData.value);
  
  searchChange();
  visible.value = true;
}

function setUsed(field) {
  if (!field) {
    return;
  }
  let fieldArr = field.split(',');
  for (let j in fieldArr) {
    let userd = filterList.value.filter((item) => {
      return item.fieldValue === fieldArr[j];
    });
    if (userd.length > 0) {
      userd[0].used = true;
    }
  }
}

function loop(list) {
  for (let i in list) {
    let field = list[i].sFileName;
    let child = list[i].children;
    if (field) {
      setUsed(field);
    }
    if (child && child.length > 0) {
      loop(child);
    }
  }
}

const isShow = ref(false);

async function show2(allData) {
  searchInput.value = '';
  await loadListData();
  tabKey.value = '1';
  isShow.value = true;
  selectValue.value = [];
  for (let j in filterList.value) {
    filterList.value[j].used = false;
  }
  for (let i in allData) {
    let field = allData[i].sFileName;
    setUsed(field);
  }
  visible.value = true;
}

const tabKey = ref('1');

function changeData(data) {
  let sFileType: any = [];
  let fieldTimeType: any = [];
  for (let i in selectValue.value) {
    let fieldTypeName = filterMap.value[selectValue.value[i]].fieldType;
    sFileType.push(fieldTypeName);
    fieldTimeType.push(timeType.value);
  }
  data.sFileName = selectValue.value.toString();
  data.sFileType = sFileType.toString();
  data.sFieldTimeType = fieldTimeType;
  console.log(data)
}

function loopChageData(list) {
  console.log('list', JSON.parse(JSON.stringify(list)));
  for (let i in list) {
    let child = list[i].children;
    if (list[i].fieldName === selectRow.fieldName && list[i].uuid === selectRow.uuid) {
      changeData(list[i]);
      return false;
    }
    if (child && child.length > 0) {
      let flag = loopChageData(child);
      if (flag === false) {
        return false;
      }
    }
  }
}

const setField = () => {
  console.log(selectValue.value);
  //时间类型，需要选择时间格式
  if (selectValue.value.length > 0 && tabKey.value === '4') {
    timeType.value = [];
    timeTypeVisible.value = true;
    return;
  } else {
    timeType.value = [];
  }
  saveField();
};

function saveField() {
  if (selectValue.value) {
    loopChageData(sourceData.value);
    changeData(selectRow);
    emit('ok', sourceData, sourceFlag);
  }
  visible.value = false;
}

const searchChange = () => {
  let val = searchInput.value;
  console.log(val);
  filterList.value.forEach((item) => {
    const field = item.fieldValue.toLocaleLowerCase();
    const tag = item.tagName?.toLocaleLowerCase() ?? '';
    if (val) {
      val = val.toLocaleLowerCase();
    }
    if (field.indexOf(val) > -1 || tag.indexOf(val) > -1) {
      item.show = true;
    } else {
      item.show = false;
    }
  });
};

const tabs = [
  {key: '1', tab: 'String', type: 'String'},
  {key: '2', tab: 'Int', type: ['UInt16', 'UInt64', 'number']},
  // { key: '3', tab: 'IPv4', type: 'IPv4' },
  {key: '4', tab: 'Time', type: 'Time'}
];

function isFieldTypeMatch(value, targetType) {
  if (!value || !targetType) return false;
  if (Array.isArray(targetType)) {
    return targetType.some(type =>
      value.toLocaleLowerCase() === type.toLocaleLowerCase()
    );
  }
  return value.toLocaleLowerCase() === targetType.toLocaleLowerCase();
}

defineExpose({
  show,
  show2,
});

const timeType = ref<string[]>([]);
const timeTypeList = ref<any[]>([]);

function handleOk() {
  console.log(timeType.value);
  if (timeType.value && timeType.value.length === 0) {
    return;
  }
  timeTypeVisible.value = false;
  saveField();
}
</script>

<style scoped lang="less">
.notUsed {
  //padding: 5px 10px;
  width: 100%;
  display: flex;
  
  &:not(:last-child) {
    padding-bottom: 12px;
  }
}

.used {
  &:not(:last-child) {
    padding-bottom: 12px;
  }
}

:deep(.ant-drawer-content-wrapper .ant-drawer-content .ant-drawer-wrapper-body .ant-drawer-header) {
  background-color: #1f1f1f !important;
}

:deep(.ant-drawer-header) {
  background: #1f1f1f !important;
}

:deep(.ant-tabs-content) {
  height: 100%;
}

:deep(.ant-tabs-tabpane) {
  overflow: auto;
  height: 100%;
}

.title_content {
  position: relative;
  font-size: 14px;
  font-weight: 600;
  line-height: 24px;
  padding: 16px 16px 0 16px;
}

.pt-8px {
  padding-top: 8px !important;
}
</style>
