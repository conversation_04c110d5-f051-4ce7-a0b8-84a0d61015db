<template>
  <div class="rule_integration_div">
    <BasicTable @register="registerTable" :row-selection="rowSelection">
      <template #userInfo="{ text }">
        <img v-if="!!text" :src="render.renderUploadImageSrc(text)" class="tenantImage" alt=""/>
      </template>
    </BasicTable>
  </div>

</template>

<script setup lang="ts">

import {useListPage} from "/@/hooks/system/useListPage";
import {formLayout} from "/@/settings/designSetting";
import BasicTable from "/@/components/Table/src/BasicTable.vue";
import type {TableRowSelection} from "/@/components/Table";
import {defineEmits, onMounted} from "vue";
import {getTenantList} from "/@/views/system/tenant/tenant.api";
import {columns2, searchFormSchema} from "/@/views/system/tenant/tenant.data";
import {render} from "/@/utils/common/renderUtils";

const props = defineProps({
  value: String
});
console.log(props.value)
const emit = defineEmits(['update:value']);
//注册table数据
const {tableContext} = useListPage({
  tableProps: {
    title: 'Integration Management',
    api: getTenantList,
    columns: columns2,
    canResize: false,
    showTableSetting: false,
    formConfig: {
      schemas: searchFormSchema,
      autoSubmitOnEnter: true,
      showAdvancedButton: true,
      layout: formLayout,
      baseColProps: {
        lg: 12, // ≥992px
        xl: 8, // ≥1200px
        xxl: 6, // ≥1600px
      },
    },
    showActionColumn: false,
  },
})

const rowSelection: TableRowSelection = {
  onChange(rowKeys) {
    const ids: any = [];
    for (let i = 0; i < rowKeys.length; i++) {
      if (rowKeys[i]) {
        ids.push(rowKeys[i]);
      }
    }
    emit('update:value', ids.join(","));
  },
}

const [registerTable, {setSelectedRowKeys}, {}] = tableContext

onMounted(() => {
  console.log("1123123", props.value)
  setSelectedRowKeys(props.value?.split(",") ?? [])
})


</script>


<style scoped lang="less">
.rule_integration_div {

  .tenantImage {
    width: 25px;
    height: 25px;
    border-radius: 6px;
    image-rendering: -moz-crisp-edges;
    image-rendering: -o-crisp-edges;
    image-rendering: crisp-edges;
    -ms-interpolation-mode: nearest-neighbor;
  }

  :deep(.searchForm) {
    background-color: @dark-bg2 !important;
  }
}
</style>
