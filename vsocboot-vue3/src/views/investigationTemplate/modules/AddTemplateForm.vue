<template>
  <div class="template-container">
    <div class="template-head">
      <a-form ref="formDataRef" layout="vertical" class="form-container" :model="formData">
        <div class="tabs-head" v-if="tenantMode">
          <a-row gutter="8">
            <a-col :span="18">
              <a-form-item>
                <a-input v-model:value="formData.templateName" :placeholder="t('routes.InvestigationTemplate.NewTemplatename')" />
              </a-form-item>
            </a-col>
            <a-col :span="6">
              <a-form-item>
                <a-select
                  v-model:value="formData.selectedType"
                  :options="tenantOptions"
                  :fieldNames="{ label: 'name', value: 'id' }"
                  allowClear
                  @change="changeTenant"
                />
              </a-form-item>
            </a-col>
          </a-row>
        </div>
        <div class="tabs-head" v-if="!tenantMode">
          <a-row gutter="16">
            <a-col :span="24">
              <a-form-item>
                <a-input v-model:value="formData.templateName" :placeholder="t('routes.InvestigationTemplate.NewTemplatename')" />
              </a-form-item>
            </a-col>
          </a-row>
        </div>

        <div class="tabs-container">
          <div class="node-title">{{ t('routes.InvestigationTemplate.Node') }}</div>
          <div class="node-wrapper">
            <!-- 添加按钮 -->
            <div class="ax-icon-button primary-button" @click.stop="addNode">
              <span class="soc ax-com-Add ax-icon-small" @click.stop="addNode"> </span>
            </div>
            <!-- 节点列表容器 -->
            <div class="node-container">
              <div
                v-for="(item, index) in formData.nodeList"
                :key="'s_' + item"
                :class="[{ 'ax-bg-primary': selectedNode === index }]"
                @click="selectNode(item, index)"
              >
                <div class="ax-label fcolor" style="cursor: pointer">
                  <span class="node-text">{{ item.nodeName }}</span>
                  <a-dropdown :trigger="['click']" v-if="index > 5">
                    <span @click.prevent> ... </span>
                    <template #overlay>
                      <a-menu>
                        <a-menu-item key="0">
                          <div @click="editNode(index)" class="node-remove-icon">{{ t('routes.InvestigationTemplate.Editnode') }}</div>
                        </a-menu-item>
                        <a-menu-item key="1">
                          <div @click="delNode(index)" class="node-remove-icon">{{ t('routes.InvestigationTemplate.DeleteNode') }}</div>
                        </a-menu-item>
                      </a-menu>
                    </template>
                  </a-dropdown>
                </div>
              </div>
            </div>
          </div>

          <a-form-item class="description-container">
            <a-textarea v-model:value="formData.description" rows="16" class="description-textarea fcolor1" disabled v-if="ifShowText" />
            <div class="custom-modal" v-if="!ifShowText">
              <div class="modal-content">
                <!-- Header Section -->
                <div class="header">
                  <a-input
                    v-model:value="nodeData.nodeName"
                    :placeholder="t('routes.InvestigationTemplate.EnterNodeName')"
                    class="node-name-input"
                    disabled
                  />
                  <a-select
                    v-model:value="nodeData.usageRange"
                    :options="templateOptions"
                    class="template-select"
                    :placeholder="t('routes.InvestigationTemplate.SelectUsage')"
                    disabled
                    :dropdown-style="{ color: '@font-color-white', backgroundColor: '#1a1a1a' }"
                  />
                </div>

                <!-- Log Type Section -->
                <div v-for="(log, index) in nodeData.logList" :key="'key' + index" class="filter-row">
                  <div class="data-setting-row pr" style="display: flex; align-items: center; width: 100%; gap: 10px; margin-top: 5px">
                    <!-- 第一个选择框 -->
                    <div style="flex: 1">
                      <a-select
                        v-model:value="log.templateType"
                        :options="TEMPLATE_TYPE_OPTION"
                        :showSearch="true"
                        :dropdown-style="{ color: '@font-color-white', backgroundColor: '#1a1a1a' }"
                        disabled
                      />
                    </div>

                    <!-- 第二个输入框 -->
                    <div style="flex: 1; margin-left: 5px">
                      <a-input v-model:value="log.field" class="log-field-input" disabled />
                    </div>

                    <!-- 第三个输入框 -->
                    <div class="flex1" id="searchSelectWrapper" style="flex: 3">
                      <a-input v-model:value="log.echo" disabled />
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </a-form-item>

          <a-form-item class="search-template">
            <div class="node-title">{{ t('routes.InvestigationTemplate.SearchTemplate') }}</div>
            <div v-for="(item2, index2) in formData.searchTemplateList" :key="'key' + index2">
              <div class="filter-row">
                <div class="ax-icon-button is_del" @click="delTemplateList(index2)">
                  <span class="soc ax-com-Decrease ax-icon" />
                </div>
                <!-- 第一个选择框 -->
                <div>
                  <a-input v-model:value="item2.label" style="width: 200px" :placeholder="'Template Name#' + (index2 + 1)" />
                </div>
                <!-- 第二个选择框 -->
                <div>
                  <a-select v-model:value="item2.templateType" :options="TEMPLATE_TYPE_OPTION" :showSearch="true" style="width: 120px" />
                </div>
                <!-- 第三个输入框 -->
                <div id="searchSelectWrapper">
                  <a-input v-model:value="item2.echo" @click="openSearch(item2)" style="width: 608px" />
                </div>
              </div>
            </div>
            <div style="margin-bottom: 8px; margin-top: 8px; border-radius: 4px">
              <a-button type="primary" ghost @click="addTemplateList()">
                <span class="soc ax-com-Add ax-icon"></span>{{
                t('routes.InvestigationTemplate.Addatemplate')
              }}</a-button>
            </div>
          </a-form-item>

          <a-form-item>
            <div class="node-title">{{ t('routes.InvestigationTemplate.RelatedProposals') }}</div>
            <div class="proposal-wrapper">
              <!-- 添加按钮 -->
              <div class="ax-icon-button primary-button" @click.stop="addRelatedProposal">
                <span class="soc ax-com-Add ax-icon-small" @click.stop="addRelatedProposal"> </span>
              </div>
              <!-- 节点列表容器 -->
              <div class="node-container">
                <div v-for="(item3, index3) in formData.relatedProposals" :key="'s_' + item3" @click="selectRelatedProposal(item3, index3)">
                  <div class="ax-label fcolor" style="cursor: pointer; display: flex; align-items: center">
                    <span class="proposal-text">{{ item3.proposalName }}</span>
                    <div class="ax-icon-button ax-icon-smallest" @click.stop="delRelatedProposal(index3)">
                      <span class="soc ax-com-Close ax-icon" @click.stop="delRelatedProposal(index3)"> </span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </a-form-item>
        </div>
      </a-form>
    </div>
  </div>
  <LogSearchModal ref="logSearchModelRef" :tableSource="logType" @search="saveSearch" />
  <AddNodeModal ref="addNodeModalRef" :isUpdate="ifUpdateNode" :nodeData="nodeData" @queryNodelist="queryNodelist" />
  <RelatedProposalModal ref="relatedProposalModalRef" v-model:relatedProposals="formData.relatedProposals" :socTenantId="formData.socTenantId" />
</template>

<script setup lang="ts">
  import { ref, watch } from 'vue';
  import { useI18n } from '/@/hooks/web/useI18n';
  import { TEMPLATE_TYPE_OPTION } from '/@/views/investigationTemplate/InvestigationTemplate.data';
  import LogSearchModal from '/@/views/posture/modules/LogSearchModal.vue';
  import { queryTenantList, queryInitList, queryById } from '/@/views/investigationTemplate/InvestigationTemplate.api';
  import AddNodeModal from '/@/views/investigationTemplate/modules/AddNodeModal.vue';
  import RelatedProposalModal from '/@/views/investigationTemplate/modules/RelatedProposalModal.vue';
  import { getTenantMode, isAdministrator, isTenant } from '/@/utils/auth';

  const tenantMode = getTenantMode();
  const isAdmin = isAdministrator();
  const formDataRef = ref();
  const { t } = useI18n();
  const props = defineProps({
    record: {
      type: Object,
      default: () => ({}),
    },
    isUpdate: {
      type: Boolean,
      default: false,
    },
  });

  const formData = ref<any>({
    searchTemplateList: [{}],
    relatedProposals: [],
  });
  // 记录当前选中的节点索引
  const selectedNode = ref<number | null>(null);
  const selectedPropsal = ref<number | null>(null);
  const logSearchModelRef = ref();
  const relatedProposalModalRef = ref();
  const addNodeModalRef = ref();
  const logType = ref();
  const searchItem = ref<any>({});
  const tenantOptions = ref<any>([]);
  const ifUpdateNode = ref<boolean>(false);
  const nodeData = ref<any>({});
  const ifShowText = ref<boolean>(true);

  const templateOptions = [
    { label: t('routes.InvestigationTemplate.Inthistemplate'), value: 1 },
    { label: t('routes.InvestigationTemplate.Inallinvestigations'), value: 2 },
  ];

  if (props.isUpdate) {
    queryById({ id: props.record }).then((res) => {
      formData.value = res;
      if (res.tenantMode === 1) {
        formData.value.selectedType = 'MSSP';
      } else {
        formData.value.selectedType = res.socTenantId;
      }
      selectedNode.value = 0;
      formData.value.description = formData.value.nodeList[0].description;
      formData.value.searchTemplateList = JSON.parse(res.searchTemplate);
    });
  }

  watch(
    () => formData.value.relatedProposals,
    () => {
      console.log('formData.value.relatedProposals', formData.value.relatedProposals);
    }
  );

  queryTenantList({}).then((res) => {
    tenantOptions.value = res;
    tenantOptions.value.splice(0, 0, {
      id: 'MSSP',
      name: 'MSSP',
    });
  });

  function changeTenant() {
    if (formData.value.selectedType === 'MSSP') {
      formData.value.tenantMode = 1;
    } else {
      formData.value.tenantMode = 2;
      formData.value.socTenantId = formData.value.selectedType;
    }
    formData.value.relatedProposals = [];
  }

  queryInitList({}).then((res) => {
    formData.value.nodeList = res;

    selectedNode.value = 0;
    formData.value.description = formData.value.nodeList[0].description;
  });

  function queryNodelist(res) {
    if (ifUpdateNode.value) {
      formData.value.nodeList.splice(selectedNode.value, 1);
    }
    formData.value.nodeList.push(res);
    ifUpdateNode.value = false;
    selectedNode.value = 0;
    ifShowText.value = true;
    console.log('formData.value.nodeList1212', formData.value.nodeList);
    formData.value.description = formData.value.nodeList[0].description ? formData.value.nodeList[0].description : '';
    console.log('formData.value.nodeList1213', formData.value.nodeList[0].description);
  }

  //节点
  function addNode() {
    let index = formData.value.nodeList.length - 6;
    nodeData.value = {
      nodeName: 'Node#' + (index + 1),
      logList: [],
    };
    addNodeModalRef.value.showModal();
  }
  function editNode(index) {
    ifUpdateNode.value = true;
    nodeData.value = formData.value.nodeList[index];
    nodeData.value.logList = formData.value.nodeList[index].description ? JSON.parse(formData.value.nodeList[index].description) : [];
    addNodeModalRef.value.showModal();
  }
  function delNode(index) {
    formData.value.nodeList.splice(index, 1);
    selectedNode.value = 0;
    ifShowText.value = true;
    formData.value.description = formData.value.nodeList[0].description ? formData.value.nodeList[0].description : '';
  }
  const selectNode = (item, index: number) => {
    if (index <= 5) {
      ifShowText.value = true;
      selectedNode.value = index;
      formData.value.description = item.description;
    } else {
      ifShowText.value = false;
      selectedNode.value = index;
      nodeData.value = item;
      nodeData.value.logList = item.description ? JSON.parse(item.description) : [];
    }
  };

  //查询条件
  function openSearch(item) {
    logType.value = item.templateType;
    searchItem.value = item;
    logSearchModelRef.value.open(item.echo ?? '');
  }

  function saveSearch(where, view, field) {
    searchItem.value.echo = view;
    searchItem.value.str = where;
    searchItem.value.fieldValue = field;
  }

  function addTemplateList() {
    formData.value.searchTemplateList.push({});
  }

  function delTemplateList(index) {
    formData.value.searchTemplateList.splice(index, 1);
  }

  //相关建议
  function addRelatedProposal() {
    relatedProposalModalRef.value.showModal();
  }

  function delRelatedProposal(index) {
    formData.value.relatedProposals.splice(index, 1);
  }

  function selectRelatedProposal(item, index: number) {
    selectedPropsal.value = index;
  }

  //保存数据
  async function getFormData() {
    const valid = await formDataRef.value.validate();
    if (!valid) {
      return null;
    }
    console.log('formData.value', formData.value);
    if (formData.value.selectedType === 'MSSP') {
      formData.value.tenantMode = 1;
    } else {
      formData.value.tenantMode = 2;
      formData.value.socTenantId = formData.value.selectedType;
    }
    formData.value.searchTemplate = JSON.stringify(formData.value.searchTemplateList);
    return formData.value;
  }

  defineExpose({
    getFormData,
  });
</script>

<style scoped lang="less">
  .template-container {
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: @dark-bg1;
    width: 100%;
    .template-head {
      width: 100%;
      max-width: 1048px;
      background-color: @dark-bg1;
      padding: 24px;
      border-radius: 8px;
      box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
      color: @font-color-white;
      .form-container {
        background: @dark-bg2;
        border-radius: 8px;
        display: flex;
        flex-direction: column;
        width: 100%;
      }
      .tabs-head {
        box-sizing: border-box;
        border-width: 0px 0px 1px 0px;
        border-style: solid;
        border-color: @border-color-01;
        width: 100%;
        padding: 16px 16px 0px 16px;
      }
    }
    .tabs-container {
      padding: 24px 16px 16px 16px;
      .node-title {
        font-size: 16px;
        font-weight: 600;
        line-height: 24px;
        letter-spacing: 0px;
        margin-bottom: 8px;
      }
    }
  }

  .node-wrapper {
    display: flex;
    align-items: center;
    gap: 8px;
  }

  .node-container {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    flex: 1;
  }
  .selected-proposal {
    background: @primary-color!important;
    border-radius: 4px;
  }
  .proposal-text {
    font-size: 12px;
    font-weight: normal;
    line-height: 16px;
    letter-spacing: 0px;
  }
  .node-text {
    font-size: 12px;
    font-weight: normal;
    line-height: 16px;
    letter-spacing: 0px;
  }
  .node-remove-icon {
    cursor: pointer;
    color: @font-color-white;
    font-size: 12px;
    font-weight: normal;
    line-height: 16px;
    letter-spacing: 0px;
  }

  .description-container {
    margin-top: 8px;
    background-color: @dark-bg1 !important;
    width: 968px;
    padding: 16px;
    border-radius: 8px;

    .description-textarea {
      width: 100%;
      border: none;
      resize: none;
      background-color: @dark-bg1 !important;
      font-size: 12px;
      font-weight: normal;
      line-height: 16px;
      letter-spacing: 0px;
    }
  }

  .filter-row {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 8px;
  }

  .proposal-wrapper {
    display: flex;
    align-items: center;
    gap: 8px;
  }

  .custom-modal {
    background-color: @dark-bg1;
    color: #fff;
    border: none;
    border-radius: 8px;
  }

  .modal-content {
    padding: 16px;
  }

  .header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
  }

  .node-name-input {
    width: 60%;
    margin-right: 10px;
    color: @font-color-white !important;

    font-size: 12px;
    font-weight: normal;
    line-height: 16px;
    letter-spacing: 0px;
  }

  .template-select {
    width: 35%;
    color: @font-color-white !important;

    font-size: 12px;
    font-weight: normal;
    line-height: 16px;
    letter-spacing: 0px;
  }

  .log-row {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 10px;
    color: @font-color-white !important;

    font-size: 12px;
    font-weight: normal;
    line-height: 16px;
    letter-spacing: 0px;
  }

  .log-field-input {
    flex: 1;
    font-size: 12px;
    font-weight: normal;
    line-height: 16px;
    letter-spacing: 0px;
  }

  .ant-select-selector {
    color: @font-color-white !important;
  }

  .ant-select-item-option-content {
    color: @font-color-white !important;
  }
</style>
