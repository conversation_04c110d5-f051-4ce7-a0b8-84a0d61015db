<template>
  <div class="alarmContent">
    <!-- 顶部 start -->
    <div class="flex gap-8px pb-12px pr-16px justify-between">
      <div class="ax-search-wrapper">
        <a-input :allowClear="true" v-model:value.trim="agms.templateName" :placeholder="placeholder" @change="onSearch" class="input-default-width">
          <template #suffix>
            <span class="soc ax-com-Search" />
          </template>
        </a-input>

        <div v-if="tenantMode && isAdmin">
          <a-select
            v-model:value="agms.selectedType"
            :options="tenantOptions"
            :fieldNames="{ label: 'name', value: 'id' }"
            allowClear
            :placeholder="t('routes.InvestigationTemplate.selectTenant')"
            @change="changeTenant"
          />
        </div>
      </div>
      <div class="flex items-center ml-auto h-32px">
        <a-button  type="primary" @click="addTemplate"  >  <span class="soc ax-com-Add ax-icon"></span>
          {{ t('routes.InvestigationTemplate.createTemplate') }}</a-button
        >
      </div>
    </div>

    <!-- 卡片网格布局 -->
    <a-spin :spinning="spinning">
      <a-row gutter="{[24, 24]}" class="card-grid">
        <a-col :span="6" v-for="(item, index) in ListData" :key="index">
          <a-card class="template-card" @click="queryDetail(item)" :bordered="false" >
            <div class="card-title">
              <span>{{ item.templateName }}</span>
              <a-dropdown style="cursor: pointer" @click.prevent.stop trigger="click">
                <div style="cursor: pointer" @click.prevent.stop v-if="canEditDelete(item)">...</div>
                <template #overlay>
                  <a-menu>
                    <a-menu-item v-if="canEditDelete(item)" @click="edit(item)" style="cursor: pointer">
                      {{ t('common.editText') }}
                    </a-menu-item>
                    <a-menu-item v-if="canEditDelete(item)" @click="handleDelete(item)" style="cursor: pointer">
                      {{ t('common.delText') }}
                    </a-menu-item>
                  </a-menu>
                </template>
              </a-dropdown>
            </div>
            <div class="template-header">
              <UserColumn :value="getUserRecord(item)" />
              <div>
                <div class="time">{{ t('routes.InvestigationTemplate.CreationTime') + ': ' + item.createTime }}</div>
                <div class="tenant" v-if="item.tenantMode == 2">{{
                  t('routes.InvestigationTemplate.Tenant') + ': ' + item.socTenantId_dictText
                }}</div>
                <div class="tenant" v-if="item.tenantMode == 1">{{ 'MSSP' }}</div>
              </div>
            </div>
            <div>
              <span class="fontStyle">{{ t('routes.InvestigationTemplate.Node') }}</span>
              <div class="tags">
                <TagDisplay :tags="item.nodeNames?.split(',')" />
              </div>

              <span class="fontStyle">{{ t('routes.InvestigationTemplate.SearchTemplate') }}</span>
              <div class="tags">
                <TagDisplay :tags="getSearchTemplateTags(item.searchTemplate)" />
              </div>
              <!-- <div class="tags">
                <div v-for="(tag, i) in JSON.parse(item.searchTemplate)" :key="'node-' + i" class="ax-label fcolor">
                  {{ tag.label }}
                </div>
              </div> -->
              <span class="fontStyle">{{ t('routes.InvestigationTemplate.RelatedProposals') }}</span>
              <div class="tags">
                <TagDisplay :tags="item.proposalNames?.split(',')" />
              </div>
              <!-- <div class="tags">
                <div v-for="(tag, i) in item.proposalNames?.split(',')" :key="'node-' + i" class="ax-label fcolor">
                  {{ tag }}
                </div>
              </div> -->
            </div>
          </a-card>
        </a-col>
      </a-row>
    </a-spin>
  </div>
  <IPagination @handlePageChange="handlePageChange" :pageSizeOptions="['8', '16', '32', '64', '128']" :defaultPageSize="8" :total="total" />
</template>

<script setup lang="ts">
  import { reactive, ref, unref, createVNode, onMounted } from 'vue';
  import { list, deleteOne, queryTenantList } from '/@/views/investigationTemplate/InvestigationTemplate.api';
  import { useI18n } from 'vue-i18n';
  import { ExclamationCircleOutlined, SearchOutlined } from '@ant-design/icons-vue';
  import UserColumn from '/@/components/vsoc/UserColumn.vue';
  import { getTenantMode, isAdministrator, getLoginBackInfo } from '/@/utils/auth';
  import { Modal } from 'ant-design-vue';
  import { useRouter } from 'vue-router';
  import { TagDisplay } from '/@/components/TagDisplay';
  import IPagination from '/@/components/IPagination/IPagination.vue';
  const { t } = useI18n();
  const placeholder = t('routes.InvestigationTemplate.searchinvestigation');
  const agms = reactive({
    templateName: '',
    selectedType: '',
  });
  const router = useRouter();
  const isUpdate = ref<boolean>(false) as any;
  const tenantOptions = ref<any>([]);
  const tenantMode = getTenantMode();
  const isAdmin = isAdministrator();
  const currentUser = getLoginBackInfo();
  const ListData = ref<any>([]);
  const total = ref(10);
  const spinning = ref(false);

  function getUserRecord(record) {
    return { username: record.createBy, avatar: null, email: null };
  }

  // 搜索框变化刷新表格
  const onSearch = (v, e) => {
    const { value } = v.target ?? { value: v };
    if (e && value) {
      ListData.value = ListData.value.filter((item) => item.templateName.includes(agms.templateName));
    }
    if (!value && !e) {
      getInitList({ page: 1, size: 8 });
    }
  };
  const searchIcon = () => {
    ListData.value = ListData.value.filter((item) => item.templateName.includes(agms.templateName));
  };

  function changeTenant(value) {
    if (value === 'MSSP') {
      agms.selectedType = 'MSSP';
      getInitList({ page: 1, size: 8, tenantMode: 1 } as any);
    } else {
      agms.selectedType = value;
      getInitList({ page: 1, size: 8, socTenantId: value } as any);
    }
  }
  function getSearchTemplateTags(content) {
    const arr = JSON.parse(content);
    return arr.map((item) => item.label);
  }

  // 分页查询
  function handlePageChange(page, size) {
    getInitList({ page: page, size: size });
  }

  //查看
  async function queryDetail(item) {
    router.push({
      path: '/investigationTemplate/modules/AddTemplate',
      query: { isView: true, record: item.id },
    });
  }
  //新增
  function addTemplate() {
    router.push({
      path: '/investigationTemplate/modules/AddTemplate',
      query: { isUpdate: 'false' },
    });
  }
  //编辑
  async function edit(item) {
    router.push({
      path: '/investigationTemplate/modules/AddTemplate',
      query: { isUpdate: 'true', record: item.id },
    });
  }
  //更新模型
  const getInitList = async (arg = { page: 1, size: 8 }) => {
    spinning.value = true;
    const res = await list({ pageNo: arg.page, pageSize: arg.size, order: 'desc', column: 'createTime', ...arg });
    const { records } = res;
    ListData.value = records;
    total.value = res.total;
    spinning.value = false;
    console.log('ssss ', records);
  };
  onMounted(() => {
    getInitList({ page: 1, size: 8 });
  });

  const handleDelete = (record) => {
    Modal.confirm({
      title: t('common.delConfirmText'),
      icon: createVNode(ExclamationCircleOutlined),
      content: t('common.delContent'),
      cancelText: t('common.cancelText'),
      okText: t('common.okText'),
      async onOk() {
        deleteOne({ id: record.id }, getInitList({ page: 1, size: 8 }));
      },
      onCancel() {
        console.log('Cancel');
      },
    });
  };

  queryTenantList({}).then((res) => {
    tenantOptions.value = res;
    tenantOptions.value.splice(0, 0, {
      id: 'MSSP',
      name: 'MSSP',
    });
  });

  // 判断当前用户是否可以编辑删除模板
  function canEditDelete(record) {
    // 如果当前用户是管理员或者当前用户是模板的创建者，则可以编辑删除
    const username = currentUser ? (currentUser as any).username : '';
    return isAdmin || (username && username === record.createBy);
  }
</script>

<style scoped lang="less">
  .alarmContent {
    display: flex;
    flex-direction: column;
    height: 100%;
    width: 100%;
    color: #fff;
  }

  .head {
    // padding: 8px 16px;
    align-items: center;
    display: flex;
    height: 50px;
    // background: #fff;
  }
  .import {
    display: flex;
    align-items: center;
  }

  .card-grid {
    border-radius: 8px;
    opacity: 1;
  }

  .template-card {
    background: rgba(255, 255, 255, 0.08);
    color: #ffffff;
    margin-right: 16px;
    margin-bottom: 16px;
    height: 400px;
    .card-title {
      font-size: 16px;
      font-weight: 600;
      line-height: 24px;
      letter-spacing: 0px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 8px;
    }
  }

  .template-header {
    margin-bottom: 12px;
  }

  .time,
  .tenant {
    margin: 0;
    margin-top: 8px;
    font-size: 12px;
    font-weight: normal;
    line-height: 16px;
    letter-spacing: 0px;
    color: @font-color-1;
  }

  .tags {
    display: flex;
    flex-direction: row;
    padding: 0px;
    gap: 4px;
    flex-wrap: wrap;
    align-content: flex-start;
    margin: 8px 0 16px 0;

    .tagsContent {
      border-radius: 4px;
      opacity: 1;
      /* 自动布局 */
      display: flex;
      flex-direction: column;
      padding: 4px 8px;
      /* Font/白0.1 */
      background: rgba(255, 255, 255, 0.1);
    }
  }

  .proposal {
    white-space: pre-wrap;
    color: #e0e0e0;
  }

  .fontStyle {
    font-size: 12px;
    font-weight: 600;
    line-height: 16px;
    letter-spacing: 0px;
  }

  :global(.setting-right) {
    overflow: scroll !important;
  }

  :deep(.ant-card-body) {
    padding: 16px !important;
  }
</style>
