<template>
  <a-row :gutter="[12,12]" style="padding: 16px">
    <a-col :span="8" class="center_wrapper" v-for="item in dataSource">


      <div class="content">
        <div class="header">
<!--          <div class="logo"><img :src="item.logo"/></div>-->
          <div class="font20">{{ item.name }}</div>
          <div :class="['font14','status','status-' + item.status]">{{ item.status }}</div>
        </div>
        <img :src="item.img" alt="" class="img" >
      </div>

    </a-col>
  </a-row>
</template>
<script lang="ts" name="tenant-center" setup>
import {useMessage} from '/@/hooks/web/useMessage';
import {useI18n} from "/@/hooks/web/useI18n";

const {t} = useI18n();
const {createMessage} = useMessage();
import china from '/@/assets/images/tenant/china.jpg';
import OIP from '/@/assets/images/tenant/OIP-C.jpg';
import th from '/@/assets/images/tenant/th.jpg';
import Singapore from '/@/assets/images/tenant/Singapore.jpg';
import Hongkong from '/@/assets/images/tenant/Hongkong.jpg';
import img1 from '/@/assets/images/tenant/1.png';
import img2 from '/@/assets/images/tenant/2.png';
import img3 from '/@/assets/images/tenant/3.png';
import img4 from '/@/assets/images/tenant/4.png';
import img5 from '/@/assets/images/tenant/5.png';
import img6 from '/@/assets/images/tenant/6.png';
import img7 from '/@/assets/images/tenant/7.png';
import img8 from '/@/assets/images/tenant/8.png';
import img9 from '/@/assets/images/tenant/9.png';
const dataSource = [{
  name: 'SoC-1',
  status: 'Normal',
  logo: OIP,
  img : img1
}, {
  name: 'SoC-2',
  status: 'Normal',
  logo: OIP,
  img : img2
}, {
  name: 'SoC-3',
  status: 'Normal',
  logo: th,
  img : img3
}, {
  name: 'SoC-4',
  status: 'Normal',
  logo: th,
  img : img4
}, {
  name: 'SoC-5',
  status: 'Normal',
  logo: Singapore,
  img : img5
}, {
  name: 'SoC-6',
  status: 'Normal',
  logo: Hongkong,
  img : img6
}, {
  name: 'SoC-7',
  status: 'Normal',
  logo: china,
  img : img7
}, {
  name: 'SoC-8',
  status: 'Freeze ',
  logo: china,
  img : img8
}, {
  name: 'SoC-9 ',
  status: 'Freeze ',
  logo: china,
  img : img9
}];

/*const dataSource = [{
  name: 'ABU Dhabi Region',
  status: 'Normal',
  logo: OIP,
  img : img1
}, {
  name: 'Dubai Region',
  status: 'Normal',
  logo: OIP,
  img : img2
}, {
  name: 'North Cairo',
  status: 'Normal',
  logo: th,
  img : img3
}, {
  name: 'West Cairo',
  status: 'Normal',
  logo: th,
  img : img4
}, {
  name: 'Singapore Region',
  status: 'Normal',
  logo: Singapore,
  img : img5
}, {
  name: 'Hongkong',
  status: 'Normal',
  logo: Hongkong,
  img : img6
}, {
  name: 'Beijing',
  status: 'Normal',
  logo: china,
  img : img7
}, {
  name: 'Chengdu',
  status: 'Freeze ',
  logo: china,
  img : img8
}, {
  name: 'Shanghai ',
  status: 'Freeze ',
  logo: china,
  img : img9
}];*/
</script>
<style scoped lang="less">
.center_wrapper {
  display: flex;
  justify-content: center;
  align-items: center;
  .content {
    width: 95%;
    display: flex;
    flex-direction: column;
    align-content: center;
    justify-content: center;
    align-items: center;
    gap: 12px;
    img{
      image-rendering:-moz-crisp-edges;
      image-rendering:-o-crisp-edges;
      image-rendering:-webkit-optimize-contrast;
      image-rendering: crisp-edges;
      -ms-interpolation-mode:nearest-neighbor;
      padding: 0.5px;
    }
    .img{
      width: 100%;
      height: 100%;
      border : 1px solid @primary-color;
      border-radius: 4px;

    }
    .header {
      width: 100%;
      display: flex;
      flex-direction: row;
      align-items: center;
      justify-content: flex-start;
      gap: 16px;
      .logo {
        width: 40px;
        height: 40px;
        img {
          width: 100%;
          height: 100%;
          border-radius: 50%;
          border: 1px solid @font-color-white;
        }
      }
      .status{
        padding: 1px 16px;
        border-radius: 4px;
        box-shadow: 4px 4px 8px #d5d5d5;
      }
      .status-Normal{
        background: #69DBFF;
      }
      .status-Freeze{
        background: #EC808D;
      }
    }
  }
}
</style>
