<template>
  <BasicDrawer v-bind="$attrs" @register="registerDrawer" :title="title" width="1000px" :destroyOnClose="true" @close="handleCancel">
    <div class="my-[-12px]">
      <BasicTable @register="registerTable">
        <template #action="{ record }">
          <TableAction :actions="getActions(record)" />
        </template>
      </BasicTable>
    </div>
  </BasicDrawer>
  <EspConfirm ref="espConfirmRef" />
  <SaveTenant ref="saveTenantRef" @handleSuccess="handleSuccess" />
</template>

<script lang="ts" setup>
  import { ref, computed } from 'vue';
  import { BasicTable, TableAction } from '/@/components/Table';
  import { useListPage } from '/@/hooks/system/useListPage';
  import { BasicDrawer, useDrawerInner } from '/@/components/Drawer';
  import { useI18n } from '/@/hooks/web/useI18n';
  import { useDrawer } from '/@/components/Drawer';
  import { draftList, deleteCalculator } from '/@/views/system/tenant/tenant.api';
  import EspConfirm from '/@/views/system/tenant/EspConfirm.vue';
  import SaveTenant from '/@/views/system/tenant/SaveToTenantModal.vue';

  const { t } = useI18n();
  const emits = defineEmits(['getData', 'reloadTable']);
  const title = computed(() => t('routes.eps.Draft'));
  const [register, { openDrawer }] = useDrawer();
  const [registerDrawer, { closeDrawer }] = useDrawerInner();
  const espConfirmRef = ref();
  const saveTenantRef = ref();

  const columns1: any = [
    {
      title: t('routes.eps.Calculatorname'),
      dataIndex: 'calculatorName',
      width: 120,
      align: 'center',
    },
    {
      title: t('routes.eps.lasttime'),
      dataIndex: 'updateTime',
      width: 120,
      align: 'center',
    },
    {
      title: t('routes.eps.EPS'),
      dataIndex: 'eps',
      width: 100,
      align: 'center',
    },
    {
      title: t('routes.eps.Dailyvolume'),
      dataIndex: 'dailyVolume',
      width: 100,
      align: 'center',
    },
    {
      title: t('routes.eps.Archivesize'),
      dataIndex: 'archiveSize',
      width: 100,
      align: 'center',
    },
  ];

  const { prefixCls, tableContext } = useListPage({
    tableProps: {
      api: draftList,
      columns: columns1,
      title: '',
      rowKey: 'id',
      canResize: false,
      showActionColumn: true,
    },
  });
  const [registerTable, { reload }] = tableContext;

  /**
   * 删除事件
   */
  async function handleDelete(record) {
    await deleteCalculator({ id: record.id }, handleSuccess);
  }

  /**
   * 删除成功之后回调事件
   */
  function handleSuccess() {
    reload();
    emits('reloadTable');
  }

  function saveToTenant(record) {
    saveTenantRef.value.showModal(record);
  }

  function editEps(record) {
    closeDrawer();
    emits('getData', record);
  }

  function handleCancel() {
    closeDrawer();
  }

  /**
   * 操作列定义
   * @param record
   */
  function getActions(record) {
    return [
      {
        label: t('common.editText'),
        onClick: editEps.bind(null, record),
      },
      {
        label: t('routes.eps.tenant'),
        onClick: saveToTenant.bind(null, record),
      },
      {
        label: t('common.delText'),
        popConfirm: {
          title: t('common.delConfirmText'),
          placement: 'left',
          confirm: handleDelete.bind(null, record),
        },
      },
    ];
  }
</script>
<style scoped lang="less">
  .eps-calculator {
    font-family: Arial, sans-serif;
  }

  .header {
    color: #ffff;
    border-radius: 8px;
  }

  .header h1 {
    font-size: 24px;
    margin-bottom: 8px;
  }

  .header p {
    margin: 0 0 8px 0;
  }

  .summary {
    display: flex;
    gap: 16px;
    margin-top: 16px;
  }

  .summary span {
    font-size: 16px;
  }

  .actions {
    margin-top: 16px;
  }

  .action-btn {
    margin-right: 8px;
  }

  .table {
    background-color: #060606;
    border-radius: 8px;
  }

  .info-icon {
    color: #1890ff;
    cursor: pointer;
    margin-left: 8px;
  }
</style>
