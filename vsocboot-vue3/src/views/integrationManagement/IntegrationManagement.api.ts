import {defHttp} from '/@/utils/http/axios';
import {Modal} from 'ant-design-vue';
import {useI18n} from "/@/hooks/web/useI18n";

const {t} = useI18n();

enum Api {
  list = '/integrationManagement/integrationManagement/list',
  save = '/integrationManagement/integrationManagement/add',
  saveInstall = '/integrationManagement/integrationManagement/saveInstall',
  queryById = '/integrationManagement/integrationManagement/queryById',
  edit = '/integrationManagement/integrationManagement/edit',
  deleteOne = '/integrationManagement/integrationManagement/delete',
  deleteBatch = '/integrationManagement/integrationManagement/deleteBatch',
  importExcel = '/integrationManagement/integrationManagement/importExcel',
  exportXls = '/integrationManagement/integrationManagement/exportXls',
  delPlugin = '/integrationManagement/integrationManagement/delPlugin',
  changePlugin = '/integrationManagement/integrationManagement/changePlugin',
  copyIntegration = '/integrationManagement/integrationManagement/copyIntegration',
  saveIntegrationRule = '/integrationManagement/integrationManagement/saveIntegrationRule'
}

/**
 * 导出api
 * @param params
 */
export const getExportUrl = Api.exportXls;
/**
 * 导入api
 */
export const getImportUrl = Api.importExcel;
/**
 * 列表接口
 * @param params
 */
export const list = (params) =>
  defHttp.get({url: Api.list, params});

export const queryById = (params) =>
  defHttp.get({url: Api.queryById, params});
/**
 * 删除集成插件
 * @param params
 */
export const delPlugin = (params) =>
  defHttp.get({url: Api.delPlugin, params});
/**
 * 变更集成插件
 * @param params
 */
export const changePlugin = (params) =>
  defHttp.get({url: Api.changePlugin, params});

/**
 * 复制集成
 * @param params
 */
export const copyIntegration = (params) =>
  defHttp.get({url: Api.copyIntegration, params});

/**
 * 删除单个
 * @param params
 * @param handleSuccess
 */
export const deleteOne = (params, handleSuccess) => {
  return defHttp.delete({url: Api.deleteOne, params}, {joinParamsToUrl: true}).then(() => {
    handleSuccess();
  });
}
/**
 * 批量删除
 * @param params
 * @param handleSuccess
 */
export const batchDelete = (params, handleSuccess) => {
  Modal.confirm({
    title: t('common.delConfirmText'),
    content: t('common.delContent'),
    okText: t('common.okText'),
    cancelText: t('common.cancelText'),
    wrapClassName: 'delete_confirm',
    onOk: () => {
      return defHttp.delete({
        url: Api.deleteBatch,
        data: params
      }, {joinParamsToUrl: true}).then(() => {
        handleSuccess();
      });
    }
  });
}
/**
 * 保存或者更新
 * @param params
 * @param isUpdate 是否是更新数据
 */
export const saveOrUpdate = (params, isUpdate) => {
  const url = isUpdate ? Api.edit : Api.save;
  return defHttp.post({url: url, params});
}

/**
 * 保存安装插件集成
 * @param params
 */
export const saveInstall = (params) => {
  return defHttp.post({url: Api.saveInstall, params});
}
/**
 * 批量新增集成和规则关系
 * @param params
 */
export const saveIntegrationRule = (params) => {
  return defHttp.post({url: Api.saveIntegrationRule, params});
}
