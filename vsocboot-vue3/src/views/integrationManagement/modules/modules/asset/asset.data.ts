import {BasicColumn} from '/@/components/Table';
import {useI18n} from "/@/hooks/web/useI18n";

const {t} = useI18n();

function tp(name) {
  return t("integration.asset." + name);
}

export const columns: BasicColumn[] = [{
  title: tp('name'),
  dataIndex: 'assetName',
  sorter: true
}, {
  title: tp('ip'),
  dataIndex: 'ipv4',
  sorter: true
}, {
  title: tp('level'),
  dataIndex: 'assetLevel',
  sorter: true,
  slots: {customRender: 'level'},
}, {
  title: tp('source'),
  dataIndex: 'ifLogSource',
  sorter: true,
  customRender(opt) {
    if (opt.value == 1) {
      return t('common.yes')
    } else {
      return t('common.no')
    }
  },
}];

