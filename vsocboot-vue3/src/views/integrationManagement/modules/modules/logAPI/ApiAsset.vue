<template>
  <BasicModal v-bind="$attrs" @register="registerModal" :title="tp('asset')"
              width="800px">
    <BasicTable @register="registerTable">
      <!--操作栏-->
      <template #action="{ record }">
        <TableAction :actions="getTableAction(record)"/>
      </template>
    </BasicTable>
  </BasicModal>
</template>

<script setup lang="ts">

import {BasicModal, useModalInner} from "/@/components/Modal";
import {BasicTable, TableAction} from "/@/components/Table";
import {
  assetColumns
} from "/@/views/integrationManagement/modules/modules/logAPI/InitApiAsset.data";
import {
  list,
  saveOrUpdate
} from "/@/views/integrationManagement/modules/modules/logAPI/InitApiAsset.api";
import {useListPage} from "/@/hooks/system/useListPage";
import {useI18n} from "/@/hooks/web/useI18n";
import {TABLE_CACHE_KEY} from "/@/utils/valueEnum";

const {t} = useI18n();

function tp(name) {
  return t("integration.logApi." + name);
}

const [registerModal, {setModalProps, closeModal}] = useModalInner(async (data) => {
  //重置表单
  setModalProps({
    confirmLoading: false,
    showCancelBtn: true,
    showOkBtn: false
  });
  reload({searchInfo: {apiId: data.apiId}});
});


//注册table数据
const {tableContext} = useListPage({
  tableProps: {
    api: list,
    columns: assetColumns,
    rowKey: "id",
    canResize: false,
    tableSetting:{
      cacheKey:TABLE_CACHE_KEY.integrationApiAsset
    },
    useSearchForm: false,
    immediate: false,
    actionColumn: {
      width: 140,
    },
  },
})

const [registerTable, {reload}, {}] = tableContext


/**
 * 测试接口
 * @param data
 */
function handleTest(data) {
  //todo
  console.log(data)
}

/**
 * 关闭资产接口
 * @param data
 */
function handleClose(data) {
  console.log(data)
  saveOrUpdate({id: data.id, closeStatus: 2}, true).then(data => {
    console.log(data)
    reload({searchInfo: {apiId: data.apiId}});
  })
}

/**
 * 操作栏
 */
function getTableAction(record) {
  return [
    {
      label: tp('test'),
      onClick: handleTest.bind(null, record),
    },
    {
      label: tp('close'),
      popConfirm: {
        title: t('closeConfirmText'),
        confirm: handleClose.bind(null, record),
      }
    }
  ]
}

</script>

<style scoped lang="less">

</style>
