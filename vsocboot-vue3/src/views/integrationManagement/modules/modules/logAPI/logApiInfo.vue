<template>
  <div>
    <div class="ft16-bold f-color-1 pd16">
      {{ tp('ConfigurationVariables') }}
      <a-tooltip>
        <template #title>
          {{ tp('tip1') }}
        </template>
        <img src="../../../image/help.png" class="helpImg" />
      </a-tooltip>
    </div>
    <div>
      <a-table :dataSource="selectInfo?.paramsList ?? []" :columns="apiColumns" :pagination="pagination" row-key="apiId" />
    </div>
  </div>
  <div style="padding: 16px">
    <div class="ft16-bold f-color-1">
      {{ tp('OutputLogExample') }}
    </div>
    <div class="logExample_div ft12 f-color-06">
      {{ selectInfo?.logExample }}
    </div>
  </div>
  <div>
    <div class="ft16-bold f-color-1 pd16">
      {{ tp('RelatedAssets') }}
      <a-tooltip>
        <template #title>
          <span v-if="isAdministrator()">{{ tp('tip2_mssp') }}</span>
          <span v-else>{{ tp('tip2') }}</span>
        </template>
        <img src="../../../image/help.png" class="helpImg" />
      </a-tooltip>
    </div>
    <BasicTable @register="registerTable">
      <!--操作栏-->
      <template #action="{ record }">
        <TableAction :actions="getTableAction(record)" />
      </template>
    </BasicTable>
    <div style="padding: 8px" v-if="!isView">
      <a-button @click="openAssetModal">{{ tp('otherTenants') }}</a-button>
    </div>
  </div>
  <ApiAsset @register="registerAssetModal" />
</template>

<script setup lang="ts">
  import { defineProps, watch, onMounted } from 'vue';
  import { useI18n } from '/@/hooks/web/useI18n';
  import { apiColumns } from '/@/views/integrationManagement/modules/modules/logAPI/InitApi.data';
  import { assetColumns } from '/@/views/integrationManagement/modules/modules/logAPI/InitApiAsset.data';
  import { deleteOne, list } from '/@/views/integrationManagement/modules/modules/logAPI/InitApiAsset.api';
  import { BasicTable, TableAction } from '/@/components/Table';
  import { useListPage } from '/@/hooks/system/useListPage';
  import { isAdministrator } from '/@/utils/auth';
  import ApiAsset from '/@/views/integrationManagement/modules/modules/logAPI/ApiAsset.vue';
  import { useModal } from '/@/components/Modal';

  const { t } = useI18n();

  function tp(name) {
    return t('integration.logApi.' + name);
  }

  const props = defineProps({
    selectInfo: {
      required: true,
      type: Object,
    },
    socTenantId: String,
    isView: {
      type: Boolean,
      default: false,
    },
    ifAsset: {
      type: Boolean,
      default: false,
    },
  });

  const pagination = {
    defaultPageSize: 5,
    hideOnSinglePage: true,
  };

  watch(
    () => props.selectInfo,
    () => {
      console.log('2222222222');
      console.log(props.selectInfo);
      loadApiAsset(props.selectInfo.apiId);
    },
    { deep: true }
  );

  onMounted(() => {
    console.log('111111111');
    loadApiAsset(props.selectInfo.apiId);
  });

  //注册table数据
  const { tableContext } = useListPage({
    tableProps: {
      api: list,
      columns: assetColumns,
      rowKey: 'id',
      canResize: false,
      pagination: pagination,
      showTableSetting: false,
      showActionColumn: props.ifAsset ? false : true,
      useSearchForm: false,
      immediate: false,
      beforeFetch: (params) => {
        params.socTenantId = props.socTenantId;
      },
      actionColumn: {
        width: 140,
      },
    },
  });

  const [registerTable, { reload }, {}] = tableContext;

  /**
   * 查询插件资产列表
   * @param apiId
   */
  function loadApiAsset(apiId) {
    reload({ searchInfo: { apiId: apiId } });
  }

  /**
   * 关闭资产接口
   * @param data
   */
  function handleClose(data) {
    console.log(data);
    deleteOne({ id: data.id }, () => {
      loadApiAsset(props.selectInfo.apiId);
    });
  }

  /**
   * 操作栏
   */
  function getTableAction(record) {
    return [
      // {
      //   label: tp('test'),
      //   onClick: handleTest.bind(null, record),
      // },
      {
        label: tp('close'),
        popConfirm: {
          title: tp('closeConfirmText'),
          confirm: handleClose.bind(null, record),
          placement: 'left',
        },
      },
    ];
  }

  //注册model
  const [registerAssetModal, { openModal }] = useModal();

  function openAssetModal() {
    openModal(true, props.selectInfo);
  }
</script>

<style scoped lang="less">
  .pd16 {
    padding: 16px;
  }

  .logExample_div {
    margin-top: 8px;
    padding: 8px;
    border-radius: 8px;
    background: @dark-bg2;
    white-space: pre-wrap;
    word-wrap: break-word;
    overflow-x: auto;
    max-width: 1000px;
  }

  :deep(.ant-table) {
    border-radius: 0 !important;
  }

  //:deep(.soc-basic-table .ant-table-wrapper) {
  //  background-color: @dark-bg1 !important;
  //}
  //
  //:deep(.ant-table) {
  //  background-color: @dark-bg1 !important;
  //}
  //
  //:deep(.ant-table-thead > tr > th) {
  //  background-color: @dark-bg1 !important;
  //  //border-bottom: 1px solid @border-color !important;
  //}

  :deep(.ant-table-container table > thead > tr:first-child th:first-child) {
    border-radius: 0;
  }
</style>
