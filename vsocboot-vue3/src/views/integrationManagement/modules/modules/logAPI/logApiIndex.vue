<template>
  <div class="logApi_div">
    <div class="left_div">
      <a-input :allowClear="true" :placeholder="tp('apiSearch')" @change="onSearch" class="input-default-width">
        <template #suffix>
          <span class="soc ax-com-Search" />
        </template>
      </a-input>

      <div class="data_div">
        <div
          v-for="(item, index) in dataList"
          :key="index"
          class="data_block"
          :class="{ active: selectInfo.apiId == item.apiId }"
          @click="onSelect(item)"
        >
          <div class="ft14-bold f-color-1">{{ item.apiName }}</div>
          <div class="ft12 f-color-06"> {{ tp('updateTime') }}: {{ handleDate(item.updateTime) }} </div>
          <!--          <div class="ft12 f-color-06">{{ tp('number') }}: {{ item.apiNumber }}</div>-->
        </div>
      </div>
    </div>
    <div class="right_div">
      <LogApiInfo v-if="selectInfo?.apiId" :select-info="selectInfo" :isView="isView" :soc-tenant-id="dataInfo?.socTenantId" :ifAsset="ifAsset" />
    </div>
  </div>
</template>

<script setup lang="ts">
  import { ref, defineProps, watch } from 'vue';
  import { useI18n } from '/@/hooks/web/useI18n';
  import { queryList } from '/@/views/integrationManagement/modules/modules/logAPI/InitApi.api';
  import LogApiInfo from '/@/views/integrationManagement/modules/modules/logAPI/logApiInfo.vue';

  const { t } = useI18n();

  function tp(name) {
    return t('integration.logApi.' + name);
  }

  const props = defineProps({
    dataInfo: {
      required: true,
      type: Object,
    },
    isView: {
      type: Boolean,
      default: false,
    },
    ifAsset: {
      type: Boolean,
      default: false,
    },
    integrationId: {
      type: String,
      default: '',
    },
  });
  console.log('api props', props);
  console.log(props.dataInfo.id);

  /**全部数据*/
  const dataAllList = ref<any[]>([]);
  /**过滤显示的数据*/
  const dataList = ref<any[]>([]);
  const selectInfo = ref<any>({});
  /**查询集成插件的api*/
  loadApiList();

  watch(
    () => props.dataInfo,
    () => {
      console.log('logapi watch');
      selectInfo.value = {};
      loadApiList();
    }
  );

  function onSearch(searchValue: string) {
    let list: any = [];
    if (searchValue) {
      for (let i in dataAllList.value) {
        const name = dataAllList.value[i]?.name?.toLowerCase();
        if (name && name.indexOf(searchValue.toLowerCase()) > -1) {
          list.push(dataAllList.value[i]);
        }
      }
    } else {
      list = dataAllList.value;
    }
    dataList.value = list;
    selectInfo.value = dataList.value[0];
  }

  function loadApiList() {
    queryList({ pluginId: props.ifAsset ? props.integrationId : props.dataInfo.pluginId }).then((data) => {
      for (let i in data) {
        const apiParams = data[i].apiParams;
        if (apiParams) {
          data[i].paramsList = JSON.parse(apiParams);
        }
      }
      dataAllList.value = data;
      dataList.value = data;
      selectInfo.value = dataList.value[0];
    });
  }

  function onSelect(data) {
    selectInfo.value = data;
  }

  function handleDate(dateTime) {
    if (dateTime) {
      return dateTime.substring(0, 11);
    }
  }
</script>

<style scoped lang="less">
  .logApi_div {
    display: flex;

    .pd16 {
      padding: 16px;
    }

    .left_div {
      flex: 0 0 240px;
      padding: 16px 8px;
      border-right: 1px solid @border-color;

      .data_div {
        margin-top: 16px;

        .data_block {
          border: 1px solid @border-color-01;
          border-radius: 8px;
          padding: 12px;
          margin-bottom: 8px;
          display: flex;
          flex-direction: column;
          gap: 4px;
        }

        .data_block.active {
          border: 2px solid @m-color;
        }
      }
    }

    .right_div {
      flex: 1;
      padding-bottom: 16px;
    }

    :deep(.ant-table) {
      border-radius: 0 !important;
    }

    :deep(.soc-basic-table .ant-table-wrapper) {
      background-color: @dark-bg1 !important;
    }

    :deep(.ant-table) {
      background-color: @dark-bg1 !important;
    }

    :deep(.ant-table-thead > tr > th) {
      background-color: @dark-bg1 !important;
      //border-bottom: 1px solid @border-color !important;
    }

    :deep(.ant-table-container table > thead > tr:first-child th:first-child) {
      border-radius: 0;
    }
  }
</style>
