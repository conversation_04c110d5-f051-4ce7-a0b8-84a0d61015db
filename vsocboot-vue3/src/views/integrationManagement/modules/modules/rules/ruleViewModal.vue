<template>
  <BasicDrawer v-bind="$attrs" @register="registerModal"
               width="1200px"
               :destroyOnClose="true">
    <template v-if="isShow">
      <ParseRuleView
        from-page="integration"
        :id="id"
      />
    </template>
  </BasicDrawer>
</template>

<script lang="ts" setup>
import {ref} from 'vue';
import {BasicDrawer, useDrawerInner} from '/@/components/Drawer';
import ParseRuleView from "/@/views/parseRule/modules/ParseRuleView.vue";


const isShow = ref(false);
const id = ref("");
const [registerModal, {setDrawerProps}] = useDrawerInner(async (data) => {
  id.value = data.id;
  isShow.value = true;
  setDrawerProps({
    confirmLoading: false,
    showFooter: false,
  });
});


</script>

<style lang="less" scoped>


.img_div {
  flex: 0 0 72px;
  display: flex;
  margin-left: 40px;
  margin-right: 16px;
  justify-content: center;
  align-items: center;
}

.name_div {
  margin-right: 20px;
  color: #fff;
  font-weight: bold;
  flex: 0 0 20%;
  font-size: 18px;
}

.text_div {
  max-width: 18%;
}

.number_div {
  padding: 4px 8px;
  background: rgba(48, 140, 255, 0.2);
  border-radius: 4px;
}

.border_bottom {
  border-bottom: 1px solid @border-color;
}
</style>
