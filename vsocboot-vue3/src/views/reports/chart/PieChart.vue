<template>
  <div ref="chartRef" :style="{ height, width }"></div>
</template>
<script lang="ts" name="reports-PieChart" setup>
import {
  defineComponent,
  PropType,
  ref,
  Ref,
  reactive,
  watchEffect,
  defineExpose,
  defineProps, inject
} from 'vue';
  import { useECharts } from '/@/hooks/web/useECharts';
import {
  axisLabelColor,
  axisLineColor, axisMainLineColor,
  axisPointerBg, labelNumColor, labelNumColr
} from "/@/components/chart/ChartColor";
import {
  CHART_AXIS_VALUE,
  CHART_CATEGORY, CHART_DEFAULT_OPTION,
  CHART_LEGEND, CHART_PIE_OPTION, CHART_TOOLTIP_AXIS, setChartColor, setContinous, setPieces
} from "/@/views/reports/chart/ts/ChartOption";
import {CHART_DEFAULT_COLORS} from "/@/views/reports/chart/ts/Setting";
import {GLOBAL_CHART_TYPE} from "/@/views/reports/chart/ts/ChartType";
const emit = defineEmits(['click','rendered','legend']);
const chartRef = ref<HTMLDivElement | null>(null);
const { setOptions, getInstance } = useECharts(chartRef as Ref<HTMLDivElement>);
const props = defineProps(
  {
    width: {
      type: String as PropType<string>,
      default: '100%',
    },
    height: {
      type: String as PropType<string>,
      default: '100%',
    }
  }
)
const option = reactive(CHART_PIE_OPTION);
const chartType = inject('chartType');


function initCharts(data,styleData) {

  //设置图形颜色
  setChartColor(option,styleData);

  //数据处理
  let legend = [];
  let seriesData = data.seriesDatas ? data.seriesDatas[0].data : [];
  for(let i in seriesData) {
    legend.push(seriesData[i].name);
  }
  emit("legend",legend);
  option.legend.data = legend;
  option.series =  data.seriesDatas ?? [];

  if(chartType.value == '12'){//Doughnut
    option.series[0].radius = ['40%', '70%'];
    option.series[0].label = {
      show: false,
      position: 'center'
    }
    option.series[0].labelLine = {
      show: false
    }
    option.series[0].emphasis ={
      label: {
          show: true,
          fontSize: 40,
          fontWeight: 'bold'
      }
    }
  }
  else if(chartType.value == '13'){//Semi circle donut
    option.series[0].radius = ['40%', '70%'];
    option.series[0].center = ['50%', '70%'];
    option.series[0].startAngle = 180;
    option.series[0].endAngle = 360;
  }else if(chartType.value == '14'){//Nightingale chart
    option.series[0].radius = ['40%', '70%'];
    option.series[0].center = ['50%', '50%'];
    option.series[0].roseType = 'area';
    option.series[0].itemStyle = {
      borderRadius: 8
    };
  }

  console.log(' option.series===>', option)
  setOptions(option);
  getInstance()?.off('click', onClick);
  getInstance()?.off('rendered', onRendered);
  getInstance()?.on('click', onClick);
  getInstance()?.on('rendered', onRendered);

  return getInstance();
}

function onClick(params) {
  emit('click', params);
}
function onRendered(params) {
  emit("rendered", getInstance());
}
defineExpose({
  initCharts
});
</script>
