{
  "socTenantId": "",
  "searchInfo": "{\"name\":\"11\",\"type\":1}",
  //查询条件(包含租户socTenantId)
  "chartType": "bar",
  //图形名称
  "datasource": "Log",
  //['Log','Risk event','Suspicious process','Bad actor','ML Event', 'Investigation','Ticket','Host asset','Asset discovery']
  "logType": "1",
  //1Security Log,2Host Log,3Network Log,4Operation Log
  "yAxis": {
    "dataType": "1",
    //1 field 2 field(designated value) 3 Time,
    "fieldGroup": [

      {
        "label" : "src ip",
        "fieldValue" : 'src_ip'
      }
    ],
    //dataType == 1
    "conditionGroup": [
      {
        //dataType == 2
        "label": "Condition1",
        "value": '*******',
        "name": "src_ip",
        "rel": "1"//1=,2>,3<,4!=
      }
    ],
    "timeInterval": {
      "value": "1",
      "type": '1',
      "field" : ""
      //1 min,2hour,3day,4week,5month,6year
    }
  },
  "xAxis": {
    "dataType": '1',
    //1 Field statistics（Deduplication,2Field value statistics,3Log volume ,4Field value
    "fieldGroup": [
      {
        "label" : "dst port",
        "fieldValue" :  "dst_port"
      }
    ],
    //dataType == 1
    "conditionGroup": [
      {
        //dataType == 2
        "name": "port",
        "rel": "1",
        //1=,2>,3<,4!=
        "value": "443",
        "label" : ""//legend名
      }
    ],
    "countGroup": [
      {
        "label" : "src ip",
        "fieldValue": "src_ip",
        "type": "1"
        //1 max 2 min,3mean,4 median
      }
    ]
  },
  "displaySetting" : {
    "maximum" : 10,
    "sortType" : "1",//1 majority first,2 mainority first,3Random
    "sortBy" : "1"//数组下标

  }
}

