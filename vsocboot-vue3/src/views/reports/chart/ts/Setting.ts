import {outRangeDefaultColor} from "/@/components/chart/ChartColor";
import {useI18n} from "/@/hooks/web/useI18n";
import {IAxis, IDisplaySetting} from "/@/views/posture/ts/dashboardModule";
import {
  EvaluationEnum,
  GroupTypeEnum,
  SortEnum, StyleTypeEnum,
  ValueTypeEnum
} from "/@/views/posture/enums/editPageEnum";
import {tp} from "/@/views/posture/ts/i18Utils";
import {CHART_DISPLAY_LIMIT} from "/@/views/posture/ts/InitializedData";
const {t} = useI18n();


export const REPORT_CONFIG_ERROR = "Statistics configuration is incomplete, please fill in at least Data setting and Display setting before saving as a report.";
export const CONFIG_ERROR = "Please check the configuration items!";
/**
 * 统计项 类型
 */
export const GROUP_DATA_TYPE_OPTION = [{
  label: tp("Field"),
  value: GroupTypeEnum.FIELD
}, {
  label: tp("FieldDesignated"),
  value: GroupTypeEnum.DESIGNATED
}, {
  label: tp("SystemData"),
  value: GroupTypeEnum.SYSTEM
},{
  label: tp("Time"),
  value: GroupTypeEnum.TIME
}];

/**
 * 统计项 类型
 */
export const REPORT_GROUP_DATA_TYPE_OPTION = [{
  label: tp("Field"),
  value: GroupTypeEnum.FIELD
}, {
  label: tp("FieldDesignated"),
  value: GroupTypeEnum.DESIGNATED
},{
  label: tp("Time"),
  value: GroupTypeEnum.TIME
}];

/**
 * 统计项 类型
 */
export const GROUP_DATA_TYPE_OPTION_MAP = {
  [GroupTypeEnum.FIELD]: tp("Field"),
  [GroupTypeEnum.DESIGNATED]: tp("FieldDesignated"),
  [GroupTypeEnum.TIME]: tp("Time"),
  [GroupTypeEnum.SYSTEM]: tp("SystemData"),
}

/**
 * 统计值 选项
 */
export const COUNT_DATA_TYPE_OPTION_MAP = {
  [ValueTypeEnum.FIELD]: "Field statistics(Deduplication)",
  [ValueTypeEnum.DESIGNATED]: "Field value statistics",
  [ValueTypeEnum.SYSTEM]: "System data",
  [ValueTypeEnum.VOLUME]: "Log volume",
  [ValueTypeEnum.FIELDVALUE]: "Field value",
}

/**
 * 统计类型
 */
export const EVALUATION_OPTION_MAP = {
  [EvaluationEnum.MAX]: tp("MaximumValue"),
  [EvaluationEnum.MIN]: tp("MinimumValue"),
  [EvaluationEnum.MEAN]: tp("MeanValue"),
}

/**
 * 统计类型
 */
export const EVALUATION_OPTION = [{
  label: tp("MaximumValue"),
  value: EvaluationEnum.MAX
}, {
  label: tp("MinimumValue"),
  value: EvaluationEnum.MIN
}, {
  label: tp("MeanValue"),
  value: EvaluationEnum.MEAN
}
// ,{
//   label : "Median",
//   value : "4"
// }
];

/**
 * 排序
 */
export const SORT_METHOD_OPTION_MAP = {
  [SortEnum.MAJORITY]: tp('MajorityFirst'),
  [SortEnum.MINORITY]: tp('MinorityFirst'),
  [SortEnum.RANDOM]: tp('Random'),
};
/**
 * 排序
 */
export const SORT_METHOD_OPTION = [{
  label: tp('MajorityFirst'),
  value: SortEnum.MAJORITY
}, {
  label: tp('MinorityFirst'),
  value: SortEnum.MINORITY
}, {
  label: tp('Random'),
  value: SortEnum.RANDOM
}];

export const TIME_INTERVAL_OPTION_MAP = {
  0: t("common.min"),
  1: t("common.hour"),
  2: t("common.day"),
  3: t("common.week"),
  4: t("common.month"),
  5: t("common.year"),
  6: t("common.year")
};
export const TIME_INTERVAL_OPTION = [{
  label: t("common.min"),
  value: "0"
}, {
  label: t("common.hour"),
  value: "1"
}, {
  label: t("common.day"),
  value: "2"
}, {
  label: t("common.week"),
  value: "3"
}, {
  label: t("common.month"),
  value: "4"
}, {
  label: t("common.year"),
  value: "5"
}];
//2 day 3 week 4 month 5 year
export const TIME_INTERVAL_OPTION_USABLE = (t) => {
  return {
    1: [{
      label: t("common.min"),
      value: "0"
    }],
    2: [{
      label: t("common.min"),
      value: "0"
    }, {
      label: t("common.hour"),
      value: "1"
    }],
    3: [{
      label: t("common.min"),
      value: "0"
    }, {
      label: t("common.hour"),
      value: "1"
    }, {
      label: t("common.day"),
      value: "2"
    }],
    4: [{
      label: t("common.min"),
      value: "0"
    }, {
      label: t("common.hour"),
      value: "1"
    }, {
      label: t("common.day"),
      value: "2"
    }, {
      label: t("common.week"),
      value: "3"
    }],
    5: [{
      label: t("common.min"),
      value: "0"
    }, {
      label: t("common.hour"),
      value: "1"
    }, {
      label: t("common.day"),
      value: "2"
    }, {
      label: t("common.week"),
      value: "3"
    }, {
      label: t("common.month"),
      value: "4"
    }]
  }
};


export const groupAxisDefaultData:IAxis = {
  dataType: '1',
  fieldGroup: [],
  conditionGroup: [],
  timeInterval: {}
}

export const valueAxisDefaultData:IAxis = {
  dataType: '1',
  valueType: 1,//只有饼图有这个属性1Specifying value,2Not specifying value
  fieldGroup: [],
  conditionGroup: [],
  countGroup: [],
  floor: [],//旭日图用
}


/**
 * 默认颜色
 */
export const CHART_DEFAULT_COLORS = [
  '#308CFF', '#14BBDD', '#945DF8', '#F75555',
  '#2ECF99', '#3F5BA7', '#F8A556', '#587BF5',
  '#F75D9C', '#9DADFC', '#9B701A', '#95D545',
  '#3293CE', '#F3A99E', '#3FD45A', '#54D3E7',
  '#D066EA', '#FB7E52', '#E2E89F',

]
/**
 * 样式初默认数据
 */
export const styleSettingDefaultData = {
  type: StyleTypeEnum.DEFAULT,//1 default color,2threshold color 3 visualMap
  colors: [],// type =1,2
  thresholdColors: [],// type =2
  rangeColors: [],//type=3
  arrayColors: [],//type=1 pie
  // min : 0,
  // max : 100,
  headerColor: CHART_DEFAULT_COLORS[0],
  outRangeColor: outRangeDefaultColor,//超出范围的颜色

}

/**
 * 获取图表颜色
 * @param index
 */
export function getChartColor(index) {
  if (index >= CHART_DEFAULT_COLORS.length) {
    index = index % CHART_DEFAULT_COLORS.length;
  }
  return CHART_DEFAULT_COLORS[index];
}

export const displaySettingDefaultData: IDisplaySetting = {
  maximum: CHART_DISPLAY_LIMIT,
  sortType: SortEnum.MINORITY,//1 majority first,2 mainority first,3Random
}
