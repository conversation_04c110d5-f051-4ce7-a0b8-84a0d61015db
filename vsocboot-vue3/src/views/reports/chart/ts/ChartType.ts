import {ref} from "vue";
import baseBar from '/@/assets/images/report/bar/base-bar.png';
import baseColumn from '/@/assets/images/report/bar/base-column.png';
import stackedBar from '/@/assets/images/report/bar/stacked-bar.png';
import radialBar from '/@/assets/images/report/bar/radial-bar.png';
import stackedColumn from '/@/assets/images/report/bar/stacked-column.png';
import lineStack from '/@/assets/images/report//line/line-stack.png';
import lineBiaxial from '/@/assets/images/report/line/line-biaxial.png';
import areaBasic from '/@/assets/images/report/line/area-basic.png';
import areaStack from '/@/assets/images/report/line/area-stack.png';
import themeRiverBasic from '/@/assets/images/report/line/themeRiver-basic.png';
import pieBase from '/@/assets/images/report/pie/pie-base.png';
import pieDoughnut from '/@/assets/images/report/pie/pie-doughnut.png';
import pieHalfDoughnut from '/@/assets/images/report/pie/pie-half-donut.png';
import pieNightingale from '/@/assets/images/report/pie/pie-nightingale.png';
import pieSunburst from '/@/assets/images/report/pie/pie-sunburst.png';
import basicList from '/@/assets/images/report/list/basic-list.png';
import statisticalList from '/@/assets/images/report/list/statistical-list.png';
import heatmapCartesian from '/@/assets/images/report/other/heatmap-cartesian.png';
import otherFunnel from '/@/assets/images/report/other/funnel.png';
import otherWordCloud from '/@/assets/images/report/other/word-cloud.png';
import otherGauge from '/@/assets/images/report/other/gauge-grade.png';
import otherRadar from '/@/assets/images/report/other/radar.png';
import {useI18n} from "/@/hooks/web/useI18n";
import {ChartNOEnum, ChartTypeEnum} from "/@/views/posture/enums/chartEnum";

const {t} = useI18n();

/**
 * i18n
 * @param name
 */
export function tp(name) {
  return t('routes.report.report.' + name);
}

/**
 * threat hunting 图表类型
 */
export const STATISTIC_CHART_TYPE = [
  {
    chartNo: ChartNOEnum.STATISTICAL_LIST,
    isTop: 1
  },
  {
    chartNo: ChartNOEnum.BASIC_BAR,
    isTop: 0
  },
  {
    chartNo: ChartNOEnum.BASIC_COLUMN,
    isTop: 0
  },
  {
    chartNo: ChartNOEnum.STACKED_BAR,
    isTop: 0
  },
  {
    chartNo: ChartNOEnum.STACKED_COLUMN,
    isTop: 0
  },
  {
    chartNo: ChartNOEnum.RADIAL_BAR,
    isTop: 0
  },
  {
    chartNo: ChartNOEnum.BASIC_LINE,
    isTop: 0
  },
  {
    chartNo: ChartNOEnum.BIAXIAL_LINE,
    isTop: 0
  },
  {
    chartNo: ChartNOEnum.BASIC_AREA,
    isTop: 0
  },
  {
    chartNo: ChartNOEnum.STACKED_AREA,
    isTop: 0
  },
  {
    chartNo: ChartNOEnum.STREAMGRAPH,
    isTop: 0
  },
  {
    chartNo: ChartNOEnum.BASIC_PIE,
    isTop: 0
  },
  {
    chartNo: ChartNOEnum.SEMI_CIRCLE,
    isTop: 0
  },
  {
    chartNo: ChartNOEnum.NIGHTINGALE,
    isTop: 0
  },
  {
    chartNo: ChartNOEnum.DOUGHNUT,
    isTop: 0
  },
  {
    chartNo: ChartNOEnum.DONUT,
    isTop: 0
  },
  {
    chartNo: ChartNOEnum.BASIC_LIST,
    isTop: 0
  },

  {
    chartNo: ChartNOEnum.BASIC_HEAT,
    isTop: 0
  },
  {
    chartNo: ChartNOEnum.FUNNEL,
    isTop: 0
  },
  {
    chartNo: ChartNOEnum.WORD_CLOUD,
    isTop: 0
  },
  {
    chartNo: ChartNOEnum.SPEEDOMETER,
    isTop: 0
  },
  {
    chartNo: ChartNOEnum.RADAR,
    isTop: 0
  }
]

/**
 * 图形对应图片
 */
export const STATISTIC_CHART_IMAGE = {
    [ChartNOEnum.STATISTICAL_LIST]: statisticalList,
    [ChartNOEnum.BASIC_BAR]: baseBar,
    [ChartNOEnum.BASIC_COLUMN]: baseColumn,
    [ChartNOEnum.STACKED_BAR]: stackedBar,
    [ChartNOEnum.STACKED_COLUMN]: stackedColumn,
    [ChartNOEnum.RADIAL_BAR]: radialBar,
    [ChartNOEnum.BASIC_LINE]: lineStack,
    [ChartNOEnum.BIAXIAL_LINE]: lineBiaxial,
    [ChartNOEnum.BASIC_AREA]: areaBasic,
    [ChartNOEnum.STACKED_AREA]: areaStack,
    [ChartNOEnum.STREAMGRAPH]: themeRiverBasic,
    [ChartNOEnum.BASIC_PIE]: pieBase,
    [ChartNOEnum.SEMI_CIRCLE]: pieHalfDoughnut,
    [ChartNOEnum.NIGHTINGALE]: pieNightingale,
    [ChartNOEnum.DOUGHNUT]: pieDoughnut,
    [ChartNOEnum.DONUT]: pieSunburst,
    [ChartNOEnum.BASIC_LIST]: basicList,
    [ChartNOEnum.BASIC_HEAT]: heatmapCartesian,
    [ChartNOEnum.FUNNEL]: otherFunnel,
    [ChartNOEnum.WORD_CLOUD]: otherWordCloud,
    [ChartNOEnum.SPEEDOMETER]: otherGauge,
    [ChartNOEnum.RADAR]: otherRadar,
  };

/**
 * 图表类型
 */
export const CHART_CLASS_ARRAY = ref([
  {
    name: tp('BarColumn'),
    collapse: false,
    children: [
      {
        id: ChartNOEnum.BASIC_BAR,
        Description: '',
      }, {
        id: ChartNOEnum.BASIC_COLUMN,
        Description: '',
      }, {
        id: ChartNOEnum.STACKED_BAR,
        Description: '',
        imagePath: stackedBar
      }, {
        id: ChartNOEnum.STACKED_COLUMN,
        Description: '',
      }, {
        id: ChartNOEnum.RADIAL_BAR,
        Description: '',
      }]
  },
  {
    name: tp('LineArea'),
    collapse: false,
    children: [
      {
        id: ChartNOEnum.BASIC_LINE,
        Description: '',
      }, {
        id: ChartNOEnum.BIAXIAL_LINE,
        Description: '',
      }, {
        id: ChartNOEnum.BASIC_AREA,
        Description: '',
      }, {
        id: ChartNOEnum.STACKED_AREA,
        Description: '',
      }, {
        id: ChartNOEnum.STREAMGRAPH,
        Description: '',
      }]
  },
  {
    name: tp('Pie'),
    collapse: false,
    children: [
      {
        id: ChartNOEnum.BASIC_PIE,
        Description: '',
      }, {
        id: ChartNOEnum.DOUGHNUT,
        Description: '',
      }, {
        id: ChartNOEnum.SEMI_CIRCLE,
        Description: '',
      }, {
        id: ChartNOEnum.NIGHTINGALE,
        Description: '',
      }, {
        id: ChartNOEnum.DONUT,
        Description: '',
      }]
  },
  {
    name: tp('List'),
    collapse: false,
    children: [
      {
        id: ChartNOEnum.BASIC_LIST,
        Description: '',
      },
      {
        id: ChartNOEnum.STATISTICAL_LIST,
        Description: '',
      }
    ]
  },
  {
    name: tp('Other'),
    collapse: false,
    children: [
      {
        id: ChartNOEnum.BASIC_HEAT,
        Description: '',
      }, {
        id: ChartNOEnum.FUNNEL,
        Description: '',
      }, {
        id: ChartNOEnum.WORD_CLOUD,
        Description: '',
      }, {
        id: ChartNOEnum.SPEEDOMETER,
        Description: '',
      }, {
        id: ChartNOEnum.RADAR,
        Description: '',
      }]
  }
]);

/**
 * 图表类型（针对echarts）
 */
export const CHART_SUBCLASS_TYPE = {
  [ChartNOEnum.BASIC_BAR]: ChartTypeEnum.BAR,
  [ChartNOEnum.BASIC_COLUMN]: ChartTypeEnum.COLUMN,
  [ChartNOEnum.STACKED_BAR]: ChartTypeEnum.BAR,
  [ChartNOEnum.STACKED_COLUMN]: ChartTypeEnum.COLUMN,
  [ChartNOEnum.RADIAL_BAR]: ChartTypeEnum.RADIUBAR,
  [ChartNOEnum.BASIC_LINE]: ChartTypeEnum.LINE,
  [ChartNOEnum.BIAXIAL_LINE]: ChartTypeEnum.XAXIS,
  [ChartNOEnum.BASIC_AREA]: ChartTypeEnum.AREA,
  [ChartNOEnum.STACKED_AREA]: ChartTypeEnum.AREA,
  [ChartNOEnum.STREAMGRAPH]: ChartTypeEnum.THEMERIVER,
  [ChartNOEnum.BASIC_PIE]: ChartTypeEnum.PIE,
  [ChartNOEnum.DOUGHNUT]: ChartTypeEnum.PIE,
  [ChartNOEnum.SEMI_CIRCLE]: ChartTypeEnum.PIE,
  [ChartNOEnum.NIGHTINGALE]: ChartTypeEnum.PIE,
  [ChartNOEnum.DONUT]: ChartTypeEnum.SUNBURST,
  [ChartNOEnum.BASIC_HEAT]: ChartTypeEnum.HEATMAP,
  [ChartNOEnum.FUNNEL]: ChartTypeEnum.FUNNEL,
  [ChartNOEnum.WORD_CLOUD]: ChartTypeEnum.WORLD,
  [ChartNOEnum.SPEEDOMETER]: ChartTypeEnum.GAUGE,
  [ChartNOEnum.RADAR]: ChartTypeEnum.RADAR,
  [ChartNOEnum.BASIC_LIST]: ChartTypeEnum.TABLE,
  [ChartNOEnum.STATISTICAL_LIST]: ChartTypeEnum.TABLE,

}

/**
 * 图表类型——对应英文名称
 */
export const GLOBAL_CHART_TYPE_NAME = {
  [ChartNOEnum.BASIC_BAR]: 'Basic bar',
  [ChartNOEnum.BASIC_COLUMN]: 'Basic column',
  [ChartNOEnum.STACKED_BAR]: 'Stacked bar',
  [ChartNOEnum.STACKED_COLUMN]: 'Stacked column',
  [ChartNOEnum.RADIAL_BAR]: "Radial bar chart",
  [ChartNOEnum.BASIC_LINE]: 'Basic line',
  [ChartNOEnum.BIAXIAL_LINE]: 'Biaxial line chart',
  [ChartNOEnum.BASIC_AREA]: 'Basic area',
  [ChartNOEnum.STACKED_AREA]: 'Stacked area',
  [ChartNOEnum.STREAMGRAPH]: 'Streamgraph',
  [ChartNOEnum.BASIC_PIE]: 'Basic pie',
  [ChartNOEnum.DOUGHNUT]: 'Doughnut',
  [ChartNOEnum.SEMI_CIRCLE]: 'Semi circle donut',
  [ChartNOEnum.NIGHTINGALE]: 'Nightingale chart',
  [ChartNOEnum.DONUT]: 'Donut chart',
  [ChartNOEnum.BASIC_LIST]: 'Basic list',
  [ChartNOEnum.STATISTICAL_LIST]: 'Statistical list',
  [ChartNOEnum.BASIC_HEAT]: 'Basic heat map',
  [ChartNOEnum.FUNNEL]: 'Funnel chart',
  [ChartNOEnum.WORD_CLOUD]: 'Word cloud',
  [ChartNOEnum.SPEEDOMETER]: 'Speedometer',
  [ChartNOEnum.RADAR]: 'Radar chart',
}

/**
 * 图表配置项：【统计项group】显示名称
 */
export const CHART_SUBCLASS_GROUP_NAME = {
  [ChartNOEnum.BASIC_BAR]: 'Y-axis',
  [ChartNOEnum.BASIC_COLUMN]: 'X-axis',
  [ChartNOEnum.STACKED_BAR]: "Y-axis",
  [ChartNOEnum.STACKED_COLUMN]: "X-axis",
  [ChartNOEnum.RADIAL_BAR]: "Radius Axis",
  [ChartNOEnum.BASIC_LINE]: "X-axis",
  [ChartNOEnum.BIAXIAL_LINE]: "X-axis",
  [ChartNOEnum.BASIC_AREA]: "X-axis",
  [ChartNOEnum.STACKED_AREA]: "X-axis",
  [ChartNOEnum.STREAMGRAPH]: "X-axis",
  [ChartNOEnum.BASIC_PIE]: "",
  [ChartNOEnum.DOUGHNUT]: "",
  [ChartNOEnum.SEMI_CIRCLE]: "",
  [ChartNOEnum.NIGHTINGALE]: "",
  [ChartNOEnum.DONUT]: "",
  [ChartNOEnum.BASIC_HEAT]: "",
  [ChartNOEnum.FUNNEL]: "",
  [ChartNOEnum.WORD_CLOUD]: "",//Word cloud
  [ChartNOEnum.SPEEDOMETER]: "",
  [ChartNOEnum.RADAR]: "Area",
  [ChartNOEnum.BASIC_LIST]: "",
  [ChartNOEnum.STATISTICAL_LIST]: "Statistical condition",
}

/**
 * 图表配置项：【统计值value】显示名称
 */
export const CHART_SUBCLASS_VALUE_NAME = {
  [ChartNOEnum.BASIC_BAR]: 'X-axis',
  [ChartNOEnum.BASIC_COLUMN]: 'Y-axis',
  [ChartNOEnum.STACKED_BAR]: "X-axis",
  [ChartNOEnum.STACKED_COLUMN]: "Y-axis",
  [ChartNOEnum.RADIAL_BAR]: "Angle Axis",
  [ChartNOEnum.BASIC_LINE]: "Y-axis",
  [ChartNOEnum.BIAXIAL_LINE]: "Y-axis",
  [ChartNOEnum.BASIC_AREA]: "Y-axis",
  [ChartNOEnum.STACKED_AREA]: "Y-axis",
  [ChartNOEnum.STREAMGRAPH]: "Y-axis",
  [ChartNOEnum.BASIC_PIE]: "Area",
  [ChartNOEnum.DOUGHNUT]: "Area",
  [ChartNOEnum.SEMI_CIRCLE]: "Area",
  [ChartNOEnum.NIGHTINGALE]: "Area",
  [ChartNOEnum.DONUT]: "Area",
  [ChartNOEnum.BASIC_HEAT]: "Data type",
  [ChartNOEnum.FUNNEL]: "Area",
  [ChartNOEnum.WORD_CLOUD]: "Counted field",
  [ChartNOEnum.SPEEDOMETER]: "Counted field",
  [ChartNOEnum.RADAR]: "Axis",
  [ChartNOEnum.BASIC_LIST]: "",
  [ChartNOEnum.STATISTICAL_LIST]: "Data to be counted",
}

/**
 * 图表配置项：display setting是否显示
 */
export const CHART_DISPLAY_SETTING = {
  [ChartNOEnum.BASIC_BAR]: true,
  [ChartNOEnum.BASIC_COLUMN]: true,
  [ChartNOEnum.STACKED_BAR]: true,
  [ChartNOEnum.STACKED_COLUMN]: true,
  [ChartNOEnum.RADIAL_BAR]: true,
  [ChartNOEnum.BASIC_LINE]: false,
  [ChartNOEnum.BIAXIAL_LINE]: false,
  [ChartNOEnum.BASIC_AREA]: false,
  [ChartNOEnum.STACKED_AREA]: false,
  [ChartNOEnum.STREAMGRAPH]: false,
  [ChartNOEnum.BASIC_PIE]: false,
  [ChartNOEnum.DOUGHNUT]: false,
  [ChartNOEnum.SEMI_CIRCLE]: false,
  [ChartNOEnum.NIGHTINGALE]: false,
  [ChartNOEnum.DONUT]: false,
  [ChartNOEnum.BASIC_HEAT]: false,
  [ChartNOEnum.FUNNEL]: false,
  [ChartNOEnum.WORD_CLOUD]: false,
  [ChartNOEnum.SPEEDOMETER] : true,
  [ChartNOEnum.RADAR]: true,
  [ChartNOEnum.BASIC_LIST] : true,
  [ChartNOEnum.STATISTICAL_LIST]: true,
}

/**
 * 图表配置项：按钮名称
 */
export const CHART_BTN_NAME = {
  [ChartNOEnum.BASIC_BAR]: 'Bar',
  [ChartNOEnum.BASIC_COLUMN]: 'Column',
  [ChartNOEnum.STACKED_BAR]: "Bar",
  [ChartNOEnum.STACKED_COLUMN]: "Column",
  [ChartNOEnum.RADIAL_BAR]: "Bar",
  [ChartNOEnum.BASIC_LINE]: "Line",
  [ChartNOEnum.BIAXIAL_LINE]: "Line",//Biaxial line chart
  [ChartNOEnum.BASIC_AREA]: "Area",//Basic area
  [ChartNOEnum.STACKED_AREA]: "Area",
  [ChartNOEnum.STREAMGRAPH]: "Field",
  [ChartNOEnum.BASIC_PIE]: "Area",
  [ChartNOEnum.DOUGHNUT]: "Area",
  [ChartNOEnum.SEMI_CIRCLE]: "Area",
  [ChartNOEnum.NIGHTINGALE]: "Area",
  [ChartNOEnum.DONUT]: "Floor",
  [ChartNOEnum.BASIC_HEAT]: "Field",
  [ChartNOEnum.FUNNEL]: "Area",
  [ChartNOEnum.WORD_CLOUD]: "Field",
  [ChartNOEnum.SPEEDOMETER]: "Field",
  [ChartNOEnum.RADAR]: "Axis",
  [ChartNOEnum.BASIC_LIST]: "Column",
  [ChartNOEnum.STATISTICAL_LIST]: "Column",
}

//-----------------------------不同图表的特殊配置 start-----------------------------------------

//统计项：时间
export const DATA_TYPE_TIME_CHART = [ChartTypeEnum.AREA, ChartTypeEnum.LINE, ChartTypeEnum.THEMERIVER, ChartTypeEnum.XAXIS];
//统计项不能变更
export const DATA_TYPE_DISABLE_CHART = [ChartTypeEnum.SUNBURST];
// 统计项没有volume
export const DATA_NOT_LOGVOLUME_CHART = [ChartTypeEnum.PIE, ChartTypeEnum.FUNNEL];
//not SPECIFYING
export const DATA_SPECIFYING_CHART = [ChartTypeEnum.PIE, ChartTypeEnum.FUNNEL];
//只能配置一项
export const DATA_FIELD_ONLY_ONE = [ChartTypeEnum.XAXIS, ChartTypeEnum.GAUGE, ChartTypeEnum.WORLD, ChartTypeEnum.HEATMAP];
//Has Sum total sort
export const DATA_SUMTOTAL_SORT_CHART = [ChartNOEnum.STACKED_BAR, ChartNOEnum.STACKED_COLUMN, ChartNOEnum.RADAR];
// only visualmap
export const DATA_VISUALMAP_CHART = [ChartTypeEnum.SUNBURST, ChartTypeEnum.RADAR, ChartTypeEnum.HEATMAP, ChartTypeEnum.GAUGE];
//column配置项
export const DATA_COLUMN_CHART = [ChartNOEnum.BASIC_LIST];

export const VIEW_NOT_COLOR_CHART = [ChartTypeEnum.PIE];

//-----------------------------不同图表的特殊配置 end-----------------------------------------
