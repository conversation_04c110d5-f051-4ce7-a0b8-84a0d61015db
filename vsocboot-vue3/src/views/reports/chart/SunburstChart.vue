<template>
  <div ref="chartRef" :style="{ height, width }"></div>
</template>
<script lang="ts" name="reports-PieChart" setup>
import {defineExpose, defineProps, inject, PropType, reactive, ref, Ref} from 'vue';
import {useECharts} from '/@/hooks/web/useECharts';
import {
  CHART_TOOLTIP_ITEM,
  CHART_VISUALMAP_PIECEWISE, setChartColor, setContinous,
  setPieces
} from "/@/views/reports/chart/ts/ChartOption";
import {axisLineColor, axisMainLineColor} from "/@/components/chart/ChartColor";

const emit = defineEmits(['click','rendered','legend']);
const chartRef = ref<HTMLDivElement | null>(null);
const { setOptions, getInstance } = useECharts(chartRef as Ref<HTMLDivElement>);
const props = defineProps(
  {
    width: {
      type: String as PropType<string>,
      default: '100%',
    },
    height: {
      type: String as PropType<string>,
      default: '100%',
    }
  }
)
const option = reactive({
  tooltip : CHART_TOOLTIP_ITEM,
  animationDuration: 500,//动画执行时间
  series: {
    type: 'sunburst',
    data: [],
    itemStyle: {
      borderColor: axisMainLineColor,
      borderWidth:2
    },
    radius: [0, '90%'],
    label: {
      rotate: 'radial',
      color:'#fff'
    }
  }
});

function initCharts(data,styleData) {
  //设置图形颜色
  setChartColor(option,styleData);

  //数据处理
  let seriesDatas = [];
  if(data.seriesDatas && data.seriesDatas.length > 0){
    seriesDatas = data.seriesDatas[0].data;
  }
  option.series.data =  seriesDatas;

  // console.log(' option.series===>', JSON.stringify(option ))
  setOptions(option);
  getInstance()?.off('click', onClick);
  getInstance()?.off('rendered', onRendered);
  getInstance()?.on('click', onClick);
  getInstance()?.on('rendered', onRendered);

  return getInstance();
}

function onClick(params) {
  emit('click', params);
}
function onRendered(params) {
  emit("rendered", getInstance());
}
defineExpose({
  initCharts
});
</script>
