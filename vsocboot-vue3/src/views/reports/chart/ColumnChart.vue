<template>
  <div ref="chartRef" :style="{ height, width }"></div>
</template>
<script lang="ts" name="reports-ColumnChart" setup>
import {defineExpose, defineProps, PropType, reactive, Ref, ref} from 'vue';
import {useECharts} from '/@/hooks/web/useECharts';
import {axisMainLineColor, labelNumColor} from "/@/components/chart/ChartColor";
import {
  CHART_AXIS_LABEL,
  CHART_DEFAULT_XLABEL_OPTION,
  setChartColor
} from "/@/views/reports/chart/ts/ChartOption";

const emit = defineEmits(['click','rendered']);
const chartRef = ref<HTMLDivElement | null>(null);
const { setOptions, getInstance } = useECharts(chartRef as Ref<HTMLDivElement>);
const props = defineProps(
  {
    width: {
      type: String as PropType<string>,
      default: '100%',
    },
    height: {
      type: String as PropType<string>,
      default: '100%',
    }
  }
)
const option = reactive(CHART_DEFAULT_XLABEL_OPTION);



function initCharts(data,styleData) {

  //设置图形颜色
  setChartColor(option,styleData);

  //数据处理
  let legend = [];
  let seriesData = data.seriesDatas;
  let xData = data.xaxisData ? data.xaxisData[0].data : [];
  console.log('xData',xData)
  for(let i in seriesData){
    legend.push(seriesData[i].name);
    seriesData[i].itemStyle = {};
    seriesData[i].label = {
      show: true,
      color : labelNumColor
    };
    if(!seriesData[i].stack ){
      seriesData[i].label.position = 'top';
    }

    if(styleData.type == 1 && styleData.colors[seriesData[i].name]){
      seriesData[i].itemStyle.color =  styleData.colors[seriesData[i].name]
      //图形上标数量颜色和柱图颜色一致
      if(!seriesData[i].stack){
        seriesData[i].label.color =  styleData.colors[seriesData[i].name];
      }
    }
    seriesData[i].barMaxWidth = 30;
    if(seriesData[i].stack && styleData.sortBy == -1){
      let sdata = [];
      seriesData[i].data.forEach((item,index)=>{
        let arr = [];
        arr.push(xData[index]);
        arr.push(item);
        sdata.push(arr)
      })
      seriesData[i].data = sdata;
    }
  }

  option.series = seriesData;
  option.xAxis.axisLabel = CHART_AXIS_LABEL;
  //y轴不显示数值
  option.yAxis.axisLabel.show = false;
  option.yAxis.axisLine.show = false;
  //x轴轴线加深
  option.xAxis.axisLine.lineStyle.color = axisMainLineColor;
  option.xAxis.data = xData;
  if(styleData.sortBy == -1) {
    option['xAxis']['data'] = option['xAxis']['data'].sort((prev, post) => {
      // get sum of fail for the same x_axis: loop series, get data of each series item, if it contains this xaxis, get its yaxis
      // prev and post are both x_axis
      console.log(prev, post)
      const prev_y_num = option['series'].reduce((total_fail, item) => {
        return total_fail + item['data'].reduce((fail, point) => {
          if (point[0] === prev) {
            return fail + point[1]
          } else {
            return fail;
          }
        }, 0);
      }, 0)
      const post_y_num = option['series'].reduce((total_fail, item) => {
        return total_fail + item['data'].reduce((fail, point) => {
          if (point[0] === post) {
            return fail + point[1]
          } else {
            return fail;
          }
        }, 0);
      }, 0)
      return post_y_num - prev_y_num;
    });
  }
  console.log(' option.series===>', option)
  setOptions(option);
  getInstance()?.off('click', onClick);
  getInstance()?.off('rendered', onRendered);
  getInstance()?.on('click', onClick);
  getInstance()?.on('rendered', onRendered);
  return getInstance();
}

function onClick(params) {
  emit('click', params);
}
function onRendered(params) {
  emit("rendered", getInstance());
}
defineExpose({
  initCharts
});
</script>
