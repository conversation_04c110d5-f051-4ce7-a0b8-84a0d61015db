<template>
  <div ref="chartRef" :style="{ height, width }"></div>
</template>
<script lang="ts" name="reports-XAxisChart" setup>
import {defineExpose, defineProps, PropType, reactive, Ref, ref} from 'vue';
import {useECharts} from '/@/hooks/web/useECharts';
import {
  CHART_DEFAULT_XAXIS_OPTION, setChartColor,
  setContinous, setDefaultColor,
  setPieces
} from "/@/views/reports/chart/ts/ChartOption";
import {axisLineColor} from "/@/components/chart/ChartColor";

const emit = defineEmits(['click','rendered']);
const chartRef = ref<HTMLDivElement | null>(null);
const { setOptions, getInstance } = useECharts(chartRef as Ref<HTMLDivElement>);
const props = defineProps(
  {
    width: {
      type: String as PropType<string>,
      default: '100%',
    },
    height: {
      type: String as PropType<string>,
      default: '100%',
    }
  }
)
const option = reactive(CHART_DEFAULT_XAXIS_OPTION);



function initCharts(data,styleData) {
  //设置图形颜色
  setChartColor(option,styleData);

  //数据处理
  let seriesData = data.seriesDatas;
  for(let i in seriesData){

    // seriesData[i].name = option.legend.data[i];
    seriesData[i].smooth = true;
  }

  option.series = seriesData;
  //y轴没有轴线
  option.yAxis.axisLine.show = false;
  option.yAxis.splitLine.show = true;
  //x轴轴线加深
  let xaxisData = data.xaxisData ?? [];
  for(let i in xaxisData){
    xaxisData[i].axisLine = {
        show: true,
        onZero: false,
        lineStyle : {
            color : axisLineColor
        }
    }
    if(option.color){
      xaxisData[i].lineStyle = {
        color:  option.color[i]
      }
    }
    xaxisData[i].axisTick = {
      alignWithLabel: true
    }
    // xaxisData[i].axisPointer = {
    //   label: {
    //     formatter: function (params) {
    //       console.log(params)
    //       return (
    //         params.value +
    //         (params.seriesData.length ? '：' + params.seriesData[0].data : '')
    //       );
    //     }
    //   }
    // }
  }
  option.xAxis = xaxisData;
  console.log(' option.series===>', option)
  setOptions(option);
  getInstance()?.off('click', onClick);
  getInstance()?.off('rendered', onRendered);
  getInstance()?.on('click', onClick);
  getInstance()?.on('rendered', onRendered);
  return getInstance();
}

function onClick(params) {
  emit('click', params);
}
function onRendered(params) {
  emit("rendered", getInstance());
}
defineExpose({
  initCharts
});
</script>
