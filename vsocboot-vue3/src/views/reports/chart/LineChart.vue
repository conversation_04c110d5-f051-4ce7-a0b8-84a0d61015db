<template>
  <div ref="chartRef" :style="{ height, width }"></div>
</template>
<script lang="ts" name="reports-LineChart" setup>
import {
  defineComponent,
  PropType,
  ref,
  Ref,
  reactive,
  watchEffect,
  defineExpose,
  defineProps
} from 'vue';
  import { useECharts } from '/@/hooks/web/useECharts';
import {
  axisLabelColor,
  axisLineColor, axisMainLineColor,
  axisPointerBg
} from "/@/components/chart/ChartColor";
import {
  CHART_DEFAULT_XLABEL_OPTION, setChartColor,
  setContinous,
  setPieces
} from "/@/views/reports/chart/ts/ChartOption";
const emit = defineEmits(['click','rendered']);
const chartRef = ref<HTMLDivElement | null>(null);
const { setOptions, getInstance } = useECharts(chartRef as Ref<HTMLDivElement>);
const props = defineProps(
  {
    width: {
      type: String as PropType<string>,
      default: '100%',
    },
    height: {
      type: String as PropType<string>,
      default: '100%',
    }
  }
)
const option = reactive(CHART_DEFAULT_XLABEL_OPTION);



function initCharts(data,styleData) {
  //设置图形颜色
  setChartColor(option,styleData);

  //数据处理
  let legend = [];
  let seriesData = data.seriesDatas;
  for(let i in seriesData){
    legend.push(seriesData[i].name);
    seriesData[i].itemStyle = {};
    seriesData[i].smooth = true;
    if(styleData.type == 1 && styleData.colors[seriesData[i].name]){
      seriesData[i].itemStyle.color =  styleData.colors[seriesData[i].name]
    }

  }

  //y轴没有轴线
  option.yAxis.axisLine.show = false;
  option.yAxis.splitLine.show = true;
  //x轴轴线加深
  option.xAxis.axisLine.lineStyle.color = axisMainLineColor;
  option.xAxis.splitLine.show = false;
  option.series = seriesData;
  //x轴坐标值
  let labelData = [];
  if(data.xaxisData && data.xaxisData.length  > 0 && data.xaxisData[0]?.data.length > 0 ){
    labelData = data.xaxisData[0].data;
  }
  option.xAxis.data = labelData;
  // console.log(' option.series===>', option)
  setOptions(option);
  getInstance()?.off('click', onClick);
  getInstance()?.off('rendered', onRendered);
  getInstance()?.on('click', onClick);
  getInstance()?.on('rendered', onRendered);
  return getInstance();
}

function onClick(params) {
  emit('click', params);
}
function onRendered(params) {
  emit("rendered", getInstance());
}
defineExpose({
  initCharts
});
</script>
