<template>
  <div class="report-setting_wrapper">
    <div class="fix-left gap16">
      <div class="flex flex-row flex-between flex-align-center">
        <div class="font16 fcolor "> {{tp('Setting')}}</div>
        <a-button type="primary" @click="getChart">{{t('common.confirm')}}</a-button>
      </div>

      <!-- column value
        ======================================================================-->
      <div class="setting-menu" v-if="chartType == 16">
        <div class="setting-head">
          <div class="font14 title-level2">{{tp('DataSetting')}}</div>
        </div>
        <div class="setting-content">
          <div class="flex flex-column gap12 padding0-16" v-for="(item,index) in columns">
            <div class="flex flex-row flex-align-center gap16">
              <div> {{tp('Column')}}{{index + 1 }}#</div>
              <Icon  class="cursor fcolor3 icon-delete" icon="ant-design:minus-circle-filled" @click="delColumns(index)"  size="14"/>
              <a-select v-model:value="columns[index]"  :options="fieldDataOption" class="flex1" show-search></a-select>
            </div>
          </div>
          <div class="padding0-16">
            <a-button type="primary"  ghost  @click="addColumnField"
            >  <span class="soc ax-com-Add ax-icon"></span>{{tp('AddColumn')}}</a-button>
          </div>
        </div>
      </div>


      <!-- 热力图 （资产、badactor ） 时间设置
      ======================================================================-->
      <div class="setting-menu" v-if="DO_TIME_DATASOURCE.includes(source) && CHART_SUBCLASS_TYPE[chartType] == 'HeatMap'">
        <div class="setting-head border-bottom0">
          <div class="font14 title-level2"> {{tp('StatisticalRange')}}</div>
          <div class="item_group" style="width: 200px">
            <a-input-number  min="1"    v-model:value="groupAxisData.timeInterval.rangeValue"    max="99999"></a-input-number>
            <div class="splitLine">|</div>
            <a-select
              style="width: 100px"
              :options="TIME_OPTION_NO_HOUR"
              v-model:value="groupAxisData.timeInterval.rangeType" />
          </div>
        </div>

      </div>


      <!-- field value
      ======================================================================-->
      <div class="setting-menu" v-if="CHART_SUBCLASS_GROUP_NAME[chartType]">
        <div class="setting-head">
            <div class="font14 title-level2">{{tp2(CHART_SUBCLASS_GROUP_NAME[chartType])}} </div>
            <div class="flex flex-row flex-align-center gap8">
              <a-select v-model:value="groupAxisData.dataType" :disabled="DATA_TYPE_TIME_CHART.includes(CHART_SUBCLASS_TYPE[chartType])" @change="changeGroupType" :options="REPORT_GROUP_DATA_TYPE_OPTION" style="width: 220px"></a-select>
              <a-tooltip placement="bottom">
                <template #title>
                  <div>
                    <p>{{tp('DataType')}}:</p>
                    <p>{{tp('tip1')}}</p>
                    <p>{{tp('tip2')}}</p>
<!--                    <p>{{tp('tip3')}}</p>-->
                    <p>{{tp('tip4')}}</p>
                  </div>

                </template>
                <Icon class="fcolor3 cursor"  icon="ant-design:question-circle-outlined" :size="20"  />
              </a-tooltip>
            </div>
        </div>
        <div class="setting-content">
          <!--  field -->
          <template v-if="groupAxisData.dataType == 1">
            <div class="setting-content_row" v-for="(item,index) in groupAxisData.fieldGroup">
              <div class="setting-content_label">
                <span>{{index + 1 }}#</span>
                <Icon  class="cursor fcolor3" icon="ant-design:minus-circle-filled" @click="delGroupAxisData(index)"  size="14"/>
              </div>
              <div class="setting-content_row_select">
                <a-select v-model:value="item.fieldValue" @change="setItemLabel(item)" show-search>
                  <a-select-option :value="f.value" v-for="f in fieldDataOption" :disabled="groupAxisData.fieldGroup.map(v=> v.fieldValue).includes(f.value)"> {{f.label}}</a-select-option>
                </a-select>
              </div>
            </div>
          </template>
          <!--  field  designated value-->
          <template v-else-if="groupAxisData.dataType == 2">
            <div class="setting-content_row p-relative" v-for="(item,index) in groupAxisData.conditionGroup">
              <div class="setting-content_label" >
                <span>{{index + 1 }}#</span>
                <Icon  class="cursor fcolor3" icon="ant-design:minus-circle-filled" @click="delGroupAxisData(index)"  size="14"/>
              </div>
              <a-input v-model:value="item.label"  class='input-default' :placeholder="'Condition#' + (index+1)"  :style="{width: source=='Log' ? '110px' : '370px','flex-shrink': 0}"></a-input>
              <div class="flex1" v-if="source == 'Log'">
                <a-input v-model:value="item.echo"  @click="openSearch(item)"  class='input-default' ></a-input>
                <FieldSearch ref="fieldSearchRef" v-if="item.isFieldSearch && source == 'Log'"  v-model:value="groupAxisData.conditionGroup[index]"   ></FieldSearch>
              </div>
              <div v-else>
                <Icon :class="['cursor',{'primaryColor' : item.advancedQueryFlag == true}]" icon="ant-design:file-search-outlined"
                      :size="22" @click="openSearch(item)"></Icon>
              </div>

             </div>
          </template>
          <!--  time -->
          <template v-else-if="groupAxisData.dataType == 3">
            <div class="flex flex-column gap12 padding0-16">
              <template  v-if="DO_TIME_DATASOURCE.includes(source)">
                <div class="flex flex-column gap12">
                  <div class="font13 fcolor">{{tp('TimeField')}}</div>
                  <a-select v-model:value="groupAxisData.timeInterval.timeField" style="width: 250px">
                    <a-select-option :value="item.value" v-for="item in timeFieldOption">{{ item.label }}</a-select-option>
                  </a-select>
                </div>
                <div class="flex flex-column gap12">
                  <div class="font13 fcolor">{{tp('StatisticalRange')}}</div>
                  <div class="item_group"  style="width: 250px">
                    <a-input-number  min="1" class='input-default'   style="width: 150px" v-model:value="groupAxisData.timeInterval.rangeValue"    max="99999"></a-input-number>
                    <div class="splitLine">|</div>
                    <a-select v-if="CHART_SUBCLASS_TYPE[chartType] == 'HeatMap'"
                              style="width: 100px"
                      :options="TIME_OPTION_NO_HOUR"
                      v-model:value="groupAxisData.timeInterval.rangeType" />

                    <a-select v-else
                      style="width: 100px"
                      :options="TIME_OPTION"
                      v-model:value="groupAxisData.timeInterval.rangeType" />
                  </div>
                </div>
              </template>

              <div class="font13 fcolor">{{tp('TimeInterval')}}</div>
              <div class="font12 fcolor4" v-if="DO_TIME_DATASOURCE.includes(source)">
                {{tp('tip5')}}
                 {{groupAxisData.timeInterval.rangeValue }} {{TIME_INTERVAL_OPTION_MAP[groupAxisData.timeInterval.rangeType]}}
                {{tp('tip6')}}  {{groupAxisData.timeInterval.rangeValue}}
                {{TIME_INTERVAL_OPTION_MAP[groupAxisData.timeInterval.rangeType]}} </div>
              <div class="font12 fcolor4" v-else-if="baseInfo.statisticalType == 1">
                {{tp('tip5')}}
                {{baseInfo.rangeValue}} {{TIME_INTERVAL_OPTION_MAP[baseInfo.rangeType]}}
                {{tp('tip6')}}  {{baseInfo.rangeValue}}  {{TIME_INTERVAL_OPTION_MAP[baseInfo.rangeType]}} </div>

              <div class="font12 fcolor4" v-else-if="baseInfo.statisticalType == 2">
                {{tp('tip5')}}
                {{timeIntervalValue}} {{timeIntervalUnitName}}
                {{tp('tip6')}} {{timeIntervalValue}} {{timeIntervalUnitName}} </div>
              <div class="flex flex-row gap8">

                <div class="item_group" >
                  <a-input-number placeholder="interval value" min="1" class='input-default'  style="width: 150px" v-model:value="groupAxisData.timeInterval.value" />
                  <div class="splitLine">|</div>
                  <a-select v-if="DO_TIME_DATASOURCE.includes(source) && groupAxisData.timeInterval.rangeType" v-model:value="groupAxisData.timeInterval.type"
                            :options="TIME_INTERVAL_OPTION_USABLE(t)[groupAxisData.timeInterval.rangeType]"  style="width: 100px"></a-select>
                  <a-select v-else-if="baseInfo.rangeType" v-model:value="groupAxisData.timeInterval.type"
                            :options="TIME_INTERVAL_OPTION_USABLE(t)[baseInfo.rangeType]"  style="width: 100px"></a-select>
                  <a-select v-else v-model:value="groupAxisData.timeInterval.type"
                            :options="timeIntervalOption"  style="width: 100px"></a-select>
                </div>


              </div>
            </div>
          </template>
          <!--  add field -->
          <div class="addBtn">
            <a-button type="primary"  ghost
                      @click="addGroupField" v-if="groupAxisData.dataType != 3">  <span class="soc ax-com-Add ax-icon"></span>{{tp('AddField')}}</a-button>
          </div>
        </div>
      </div>

      <!-- statistic value
      =========================================================================-->
      <div  class="setting-menu" v-if="CHART_SUBCLASS_VALUE_NAME[chartType]">
        <div :class="['setting-head',{'border_bottom_0' : valueAxisData.dataType == 3}]">
          <div class="font14 title-level2">{{tp2(CHART_SUBCLASS_VALUE_NAME[chartType])}} </div>
          <div class="flex flex-row flex-align-center gap8">
            <a-select v-model:value="valueAxisData.dataType" v-if="CHART_SUBCLASS_TYPE[chartType] == 'WorldChart'"    @change="changeValueType"  style="width: 260px">
              <a-select-option value="1">{{tp('FieldValue')}}</a-select-option>
            </a-select>
            <a-select v-else v-model:value="valueAxisData.dataType"  :disabled="DATA_TYPE_DISABLE_CHART.includes(CHART_SUBCLASS_TYPE[chartType])"   @change="changeValueType"  style="width: 260px">
              <a-select-option value="1">{{tp('FieldStatistics')}}</a-select-option>
              <a-select-option value="2">{{tp('FieldValueStatistics')}}</a-select-option>
              <a-select-option value="3"
                               v-if="!DATA_NOT_LOGVOLUME_CHART.includes(CHART_SUBCLASS_TYPE[chartType])"> {{source}} {{tp('volume')}}</a-select-option>
              <a-select-option value="4" v-if="numberDataOption.length > 0">{{tp('FieldValue')}}</a-select-option>
            </a-select>
            <a-tooltip placement="bottom">
              <template #title>
                <div  v-if="CHART_SUBCLASS_TYPE[chartType] == 'WorldChart'">
                  {{tp('tip7')}}
                </div>
                <div v-else>
                  <p>{{tp('DataType')}}:</p>
                  <p>{{tp('tip1')}}</p>
                  <p>{{tp('tip2')}}</p>
<!--                  <p>{{tp('tip3')}}</p>-->
                  <p>{{tp('tip4')}}</p>
                </div>
              </template>
              <Icon class="fcolor3 cursor"  icon="ant-design:question-circle-outlined" :size="20"  />
            </a-tooltip>
          </div>
        </div>
        <!--Sunburst-->
        <div class="setting-content" v-if="CHART_SUBCLASS_TYPE[chartType] == 'Sunburst'">
          <div class="flex flex-column gap12 padding0-16" v-for="(item,index) in valueAxisData.floor">
            <div class="">
               {{tp('Floor')}}{{index + 1 }}#
            </div>

            <div class="style-bg flex flex-column gap12 padding0-16">
              <div class="flex flex-row flex-align-center gap12">
                <span style="width: 62px">{{tp('Field')}}</span>
                <Icon  class="cursor fcolor3 icon-delete" icon="ant-design:minus-circle-filled" @click="delFloorField(index)"  size="14"/>
                <a-select v-model:value="item.name"  :options="fieldDataOption" style="width: 100%" show-search></a-select>
              </div>
              <div class="flex flex-row flex-align-center gap12" v-for="(valueItem,j) in item.value">
                <span>{{tp('Value')}}{{j + 1 }}#</span>
                <Icon  class="cursor fcolor3 icon-delete" icon="ant-design:minus-circle-filled" @click="delFloorValueAxisData(index,j)"  size="14"/>
                <a-select v-if="fieldChange(item)"  v-model:value="item.value[j]" class="flex1" :options="item.options" show-search></a-select>
                <a-input v-model:value="item.value[j]"  class="flex1 h-32px" v-else></a-input>
              </div>
              <div class="">
                <a-button type="primary"  ghost  @click="addFloorFieldValue(index)"
                >  <span class="soc ax-com-Add ax-icon"></span>{{tp('AddValue')}}</a-button>
              </div>
            </div>
          </div>
          <div class="padding0-16">
            <a-button type="primary" preIcon="ant-design:plus-outlined" ghost  @click="addFloorField"
                     >{{t('common.add')}} {{CHART_BTN_NAME[chartType]}}</a-button>
          </div>
        </div>
        <!--data type-->
        <div class="setting-content" v-else-if="valueAxisData.dataType != 3">
          <!--  field statistic (deduplication)  -->
            <template v-if="valueAxisData.dataType == 1">
              <div class="setting-content_row" v-for="(item,index) in valueAxisData.fieldGroup">
                <div class="setting-content_label">
                  <span>{{index + 1 }}#</span>
                  <Icon  class="cursor fcolor3" icon="ant-design:minus-circle-filled" @click="delValueAxisData(index)"  size="14"/>
                </div>
                <div class="setting-content_row_select">
                  <a-select v-model:value="item.fieldValue"  @change="setItemLabel(item)" show-search>
                    <a-select-option :value="f.value" v-for="f in fieldDataOption" :disabled="valueAxisData.fieldGroup.map(v=> v.fieldValue).includes(f.value)"> {{f.label}}</a-select-option>
                  </a-select>
                </div>
              </div>
            </template>
          <!--  field value statistic -->
            <template v-else-if="valueAxisData.dataType == 2">
              <div class="setting-content_row" v-if="DATA_SPECIFYING_CHART.includes(CHART_SUBCLASS_TYPE[chartType])">
                <a-radio-group v-model:value="valueAxisData.valueType"  >
                  <a-radio :value="1">{{tp('SpecifyingValue')}}</a-radio>
                  <a-radio :value="2">{{tp('NotSpecifyingValue')}}</a-radio>
                </a-radio-group>
              </div>
              <div class="setting-content_row " v-if="DATA_SPECIFYING_CHART.includes(CHART_SUBCLASS_TYPE[chartType]) && valueAxisData.valueType == 2">
                <span>{{tp('Maximum')}} </span>
                <a-input-number   v-model:value="displaySettingData.maximum"  min="1"  class="flex1 input-default"/>
              </div>
              <!-- Specifying value -->
              <div class="setting-content_row p-relative" v-if="valueAxisData.valueType != 2" v-for="(item,index) in valueAxisData.conditionGroup">
                <div class="setting-content_label">
                  <span>{{index + 1 }}#</span>
                  <Icon  class="cursor fcolor3" icon="ant-design:minus-circle-filled" @click="delValueAxisData(index)"  size="14"/>
                </div>
                <a-input v-model:value="item.label" :placeholder="'Condition#' + (index+1)" class="input-default" :style="{width: source=='Log' ? '110px' : '370px','flex-shrink': 0}"></a-input>
                <div class="flex1" v-if="source == 'Log'">
                  <a-input v-model:value="item.echo"  @click="openSearch(item)" class="input-default"  ></a-input>
                  <FieldSearch ref="fieldSearchRef" v-if="item.isFieldSearch && source == 'Log'"  v-model:value="valueAxisData.conditionGroup[index]"   ></FieldSearch>
                </div>
                <div v-else>
                  <Icon :class="['cursor',{'primaryColor' : item.advancedQueryFlag == true}]" icon="ant-design:file-search-outlined"
                        :size="22" @click="openSearch(item)"></Icon>
                </div>
               </div>
              <!-- Not specifying value-->
              <div class="setting-content_row" v-if="valueAxisData.valueType == 2" v-for="(item,index) in valueAxisData.conditionGroup">
                <div class="setting-content_label">
                  <span>{{index + 1 }}#</span>
                  <Icon  class="cursor fcolor3" icon="ant-design:minus-circle-filled" @click="delValueAxisData(index)"  size="14"/>
                </div>
                <div class="setting-content_row_select">
                  <a-select v-model:value="item.name"  @change="setItemLabel(item)" :options="fieldDataOption" show-search></a-select>
                </div>
              </div>
            </template>
          <!--  field value -->
            <template v-else-if="valueAxisData.dataType == 4">
              <div class="setting-content_row" v-for="(item,index) in valueAxisData.countGroup">
                <div class="setting-content_label">
                  <span>{{index + 1 }}#</span>
                  <Icon  class="cursor fcolor3" icon="ant-design:minus-circle-filled" @click="delValueAxisData(index)"  size="14"/>
                </div>
                <a-input v-model:value="item.label" :placeholder="'Condition#' + (index+1)" style="width:100px" ></a-input>
                <a-select v-model:value="item.fieldValue"  @change="setFieldValueItemLabel(item)" :options="numberDataOption" class="flex1"></a-select>
                <a-select v-model:value="item.type" :options="EVALUATION_OPTION" style="width: 150px"></a-select>
               </div>
            </template>
            <div class="addBtn">
              <template v-if="valueAxisData.dataType != 3 && DATA_FIELD_ONLY_ONE.includes(CHART_SUBCLASS_TYPE[chartType])">
                <a-button type="primary"  ghost  @click="addValueField"
                          v-if="!valueAxisData[dataTypeFieldNameMap[valueAxisData.dataType]] || (valueAxisData[dataTypeFieldNameMap[valueAxisData.dataType]].length == 0)">  <span class="soc ax-com-Add ax-icon"></span>{{tp('Add')}}{{tp2(CHART_BTN_NAME[chartType])}}</a-button>
              </template>


              <a-button type="primary"  ghost  @click="addValueField"  v-else-if="valueAxisData.dataType != 3 && btnShow">
                <span class="soc ax-com-Add ax-icon"></span>
                {{tp('Add')}}{{tp2(CHART_BTN_NAME[chartType])}}</a-button>
            </div>

        </div>
      </div>

      <!--  Display setting
      ===================================================================-->
      <div class="setting-menu" v-if="groupAxisData.dataType != 3 && CHART_DISPLAY_SETTING[chartType]">
        <div class="setting-head">
          <div class="font14 title-level2">{{tp('DisplaySetting')}} </div>
        </div>
        <div class="setting-content" v-if="CHART_SUBCLASS_TYPE[chartType] == 'Gauge'">
          <div class="setting-content_row setting-content_row_display">
            <span>{{tp('Min')}} </span>
            <a-input-number   v-model:value="styleSettingData.min"  min="0"  class="flex1"/>
          </div>
          <div class="setting-content_row setting-content_row_display">
            <span>{{tp('Max')}} </span>
            <a-input-number   v-model:value="styleSettingData.max"  min="1"  class="flex1"/>
          </div>
        </div>
        <div class="setting-content" v-else>
          <div class="setting-content_row setting-content_row_display">
            <span>{{tp('Maximum')}} </span>
            <a-input-number   v-model:value="displaySettingData.maximum"  min="1"  class="flex1"/>
          </div>
          <div class="setting-content_row setting-content_row_display">
            <span>{{tp('SortingMethod')}} </span>
            <a-select v-model:value="displaySettingData.sortType"   :options="SORT_METHOD_OPTION" class="flex1"></a-select>
          </div>
          <div class="setting-content_row setting-content_row_display">
            <span>{{tp('Sorting')}} </span>
            <a-select v-model:value="displaySettingData.sortBy"   class="flex1">
              <template  v-if="DATA_SUMTOTAL_SORT_CHART.includes(CHART_SUBCLASS_TYPE[chartType])">
                <a-select-option value="-1">{{tp('SumTotal')}}</a-select-option>
              </template>
              <template v-if="CHART_SUBCLASS_TYPE[chartType] == 'table' && columns.length > 0" >
                <a-select-option  v-for="(item,index) in columns" :value="index"
                                 :key="'te_'+index">
                  {{ item}}
                </a-select-option>
              </template>
              <a-select-option v-else  v-for="(item,index) in legendOption" :value="item.value"
                               :key="'te_'+index">
                {{ item.label }}
              </a-select-option>
            </a-select>
          </div>
        </div>
      </div>

      <!--  style setting
      =====================================================================-->
      <div class="setting-menu">
        <div class="setting-head">
          <div class="font14 title-level2">{{tp('StyleSetting')}}</div>
        </div>
        <div v-if="CHART_SUBCLASS_TYPE[chartType]=='table'" class="setting-content padding12-16">
          <div class="flex flex-row flex-align-center gap16">
            <span>{{tp('HeaderColor')}} </span>
            <div class="flex flex-row flex-align-center color-group flex1">
              <ColorPicker lang="en" theme="black" v-model:pureColor="styleSettingData.headerColor"
                           disableAlpha="true"        shape="square" format="hex6" useType="pure"></ColorPicker>
              <a-input v-model:value="styleSettingData.headerColor"  class="flex1"></a-input>
            </div>
          </div>
        </div>
        <div v-else class="setting-content padding12-16">
          <a-radio-group v-model:value="styleSettingData.type">
            <a-radio value="1"
                     :disabled="DATA_VISUALMAP_CHART.includes(CHART_SUBCLASS_TYPE[chartType])">
              {{tp('DefaultColor')}}</a-radio>
            <a-radio value="2">{{tp('ThresholdColor')}}</a-radio>
            <a-radio value="3" :disabled="CHART_SUBCLASS_TYPE[chartType]=='Gauge'">{{tp('ContinuousColor')}}</a-radio>
          </a-radio-group>
          <div class="style-bg" v-if="legendOption.length > 0 && styleSettingData.type == 1
          && !DATA_VISUALMAP_CHART.includes(CHART_SUBCLASS_TYPE[chartType]) && !VIEW_NOT_COLOR_CHART.includes(CHART_SUBCLASS_TYPE[chartType])">
              <div class="flex flex-row gap16 flex-align-center" v-for="(item,index) in legendOption" >
                <div class="flex1 ellipsis font12">{{item.label}}</div>
                <div class="color-group" >
                  <ColorPicker lang="en" theme="black" v-model:pureColor="item.pureColor"
                               shape="square" format="hex6"  ></ColorPicker>
                  <a-input v-model:value="item.pureColor"  ></a-input>
                </div>
              </div>
          </div>
          <div class="style-bg color-content"  v-if="styleSettingData.type == 2">
            <div class="flex flex-row gap16 flex-align-center">
              <div>{{tp('rangeColor')}}</div>
              <div class="flex flex-row flex-align-center color-group">
                <ColorPicker lang="en" theme="black" v-model:pureColor="styleSettingData.outRangeColor"
                             disableAlpha="true"        shape="square" format="hex6" useType="pure"></ColorPicker>
                <a-input v-model:value="styleSettingData.outRangeColor" ></a-input>
              </div>
            </div>
            <div class="flex flex-row gap16" v-for="(item,index) in styleSettingData.thresholdColors" >
              <div class="flex flex-row flex-align-center gap8 flex1">
                <Icon  class="cursor fcolor3 icon-delete" icon="ant-design:minus-circle-filled" @click="delColors(index)"  size="14"/>
                <a-input-number v-model:value="item.start" class="flex1"></a-input-number>
                <Icon v-if="CHART_SUBCLASS_TYPE[chartType]!='Gauge'" class="cursor fcolor3" icon="ant-design:minus-outlined" style="width:14px" size="14"/>
                <a-input-number v-if="CHART_SUBCLASS_TYPE[chartType]!='Gauge'"   v-model:value="item.end" class="flex1" ></a-input-number>
              </div>
              <div class="flex flex-row flex-align-center color-group">
                <ColorPicker lang="en" theme="black" v-model:pureColor="item.pureColor"
                             disableAlpha="true"        shape="square" format="hex6" useType="pure"></ColorPicker>
                <a-input v-model:value="item.pureColor" ></a-input>
              </div>
            </div>
            <div><a-button type="primary" ghost="" @click="addThreshold" >{{tp('AddThreshold')}}</a-button></div>
          </div>
          <div class="setting-content padding12-16" v-if="styleSettingData.type == 3">
            <div class="flex flex-row gap16">
              <span>{{tp('Min')}} </span>
              <a-input-number v-model:value="styleSettingData.min" class="flex1"></a-input-number>
            </div>
            <div class="flex flex-row gap16">
              <span>{{tp('Max')}} </span>
              <a-input-number v-model:value="styleSettingData.max" class="flex1"></a-input-number>
            </div>
            <div class="flex flex-row gap16 flex-align-center " v-for="(colors,index) in styleSettingData.rangeColors">
                <Icon  class="cursor fcolor3 icon-delete" icon="ant-design:minus-circle-filled" @click="delColors(index)"  size="14"/>
                <div class="flex flex-row flex-align-center color-group flex1">
                  <ColorPicker lang="en" theme="black" v-model:pureColor="styleSettingData.rangeColors[index]"
                               disableAlpha="true"        shape="square" format="hex6" useType="pure"></ColorPicker>
                  <a-input v-model:value="styleSettingData.rangeColors[index]"  class="flex1"></a-input>
                </div>
            </div>
            <div><a-button type="primary" ghost="" @click="addColor" >{{tp('AddColor')}}</a-button></div>
          </div>
        </div>
      </div>

    </div>
    <!--chart show
    =======================================================================-->
    <div class="chart_content_wrapper">
      <div class="chart_content">
<!--        <div class="preview font12 fcolor3">Preview</div>-->
        <div class="preview-border-bottom"></div>
        <div class="noData" v-if="isNoData">{{tp('NoDataDisplay')}}</div>
        <div class="flex flex1 flex-column" v-if="!isNoData" style="height:100%;max-width:
        calc(100vw - 555px);">
        <BarChart ref="chartRef" @rendered="saveChartImg"  v-if="CHART_SUBCLASS_TYPE[chartType] == 'Bar'"></BarChart>
        <ColumnChart ref="chartRef" @rendered="saveChartImg"  v-else-if="CHART_SUBCLASS_TYPE[chartType] == 'Column'"></ColumnChart>
        <RadiuBarChart ref="chartRef" @rendered="saveChartImg"  v-else-if="CHART_SUBCLASS_TYPE[chartType] == 'RadiuBar'"></RadiuBarChart>
        <LineChart ref="chartRef"  @rendered="saveChartImg"  v-else-if="CHART_SUBCLASS_TYPE[chartType] == 'Line'"></LineChart>
        <AreaChart ref="chartRef" @rendered="saveChartImg"  v-else-if="CHART_SUBCLASS_TYPE[chartType] == 'Area'"></AreaChart>
        <ThemeRiverChart ref="chartRef" @rendered="saveChartImg"  v-else-if="CHART_SUBCLASS_TYPE[chartType] == 'ThemeRiver'"></ThemeRiverChart>
        <XAxisChart ref="chartRef" @rendered="saveChartImg"  v-else-if="CHART_SUBCLASS_TYPE[chartType] == 'XAxisChart'"></XAxisChart>
        <GaugeChart ref="chartRef" @rendered="saveChartImg"  v-else-if="CHART_SUBCLASS_TYPE[chartType] == 'Gauge'"></GaugeChart>
        <PieChart ref="chartRef" @rendered="saveChartImg" @legend="setLegend" v-else-if="CHART_SUBCLASS_TYPE[chartType] == 'Pie'"></PieChart>
        <FunnelChart ref="chartRef" @rendered="saveChartImg" @legend="setLegend" v-else-if="CHART_SUBCLASS_TYPE[chartType] == 'Funnel'"></FunnelChart>
        <SunburstChart ref="chartRef" @rendered="saveChartImg"  v-else-if="CHART_SUBCLASS_TYPE[chartType] == 'Sunburst'"></SunburstChart>
        <RadarChart ref="chartRef" @rendered="saveChartImg"  v-else-if="CHART_SUBCLASS_TYPE[chartType] == 'Radar'"></RadarChart>
        <WorldChart ref="chartRef" @rendered="saveChartImg"  v-else-if="CHART_SUBCLASS_TYPE[chartType] == 'WorldChart'"></WorldChart>
        <HeatmapChart ref="chartRef" @rendered="saveChartImg"  v-else-if="CHART_SUBCLASS_TYPE[chartType] == 'HeatMap'"></HeatmapChart>
        <TableChart  ref="chartRef" @rendered="saveTableImg"  v-else-if="CHART_SUBCLASS_TYPE[chartType] == 'table'"></TableChart>
        </div>
      </div>
    </div>

</div>
  <!--高级查询
    =======================================================================-->
  <TableSearchModel ref="tableSearchModelRef" :isReport="true" @search="saveSearch" :source="QUERY_TABLE_NAME[tableName]" />


</template>

<script lang="ts" name="reports-ReportViewSetting" setup>
import {ColorPicker} from "vue3-colorpicker";
import "vue3-colorpicker/style.css";
import {defineExpose, inject, nextTick, onMounted, ref, watch} from "vue";
import html2canvas from 'html2canvas';
import dayjs from "dayjs";
import FieldSearch from "/@/views/reports/components/FieldSearch.vue";
import BarChart from "/@/views/reports/chart/BarChart.vue";
import ColumnChart from "/@/views/reports/chart/ColumnChart.vue";
import RadiuBarChart from "/@/views/reports/chart/RadiuBarChart.vue";
import LineChart from "/@/views/reports/chart/LineChart.vue";
import AreaChart from "/@/views/reports/chart/AreaChart.vue";
import ThemeRiverChart from "/@/views/reports/chart/ThemeRiverChart.vue";
import XAxisChart from "/@/views/reports/chart/XAxisChart.vue";
import PieChart from "/@/views/reports/chart/PieChart.vue";
import SunburstChart from "/@/views/reports/chart/SunburstChart.vue";
import FunnelChart from "/@/views/reports/chart/FunnelChart.vue";
import GaugeChart from "/@/views/reports/chart/GaugeChart.vue";
import RadarChart from "/@/views/reports/chart/RadarChart.vue";
import WorldChart from "/@/views/reports/chart/WorldChart.vue";
import HeatmapChart from "/@/views/reports/chart/HeatmapChart.vue";
import TableChart from "/@/views/reports/chart/TableChart.vue";
import {useMessage} from "/@/hooks/web/useMessage";
import {
  CONFIG_ERROR,
  displaySettingDefaultData,
  EVALUATION_OPTION,
  getChartColor,
  GROUP_DATA_TYPE_OPTION,
  groupAxisDefaultData, REPORT_GROUP_DATA_TYPE_OPTION,
  SORT_METHOD_OPTION,
  styleSettingDefaultData,
  TIME_INTERVAL_OPTION_MAP,
  TIME_INTERVAL_OPTION_USABLE,
  valueAxisDefaultData
} from "/@/views/reports/chart/ts/Setting";
import {
  CHART_BTN_NAME,
  CHART_DISPLAY_SETTING,
  CHART_SUBCLASS_GROUP_NAME,
  CHART_SUBCLASS_TYPE,
  CHART_SUBCLASS_VALUE_NAME,
  DATA_FIELD_ONLY_ONE,
  DATA_NOT_LOGVOLUME_CHART,
  DATA_SPECIFYING_CHART,
  DATA_SUMTOTAL_SORT_CHART,
  DATA_TYPE_DISABLE_CHART,
  DATA_TYPE_TIME_CHART,
  DATA_VISUALMAP_CHART,
  VIEW_NOT_COLOR_CHART
} from "/@/views/reports/chart/ts/ChartType";
import {
  DO_TIME_DATASOURCE,
  QUERY_TABLE_NAME,
  TIME_DEFAULT_USED_FIELD
} from "/@/views/reports/chart/ts/Source";
import TableSearchModel from "/@/views/tableSearch/TableSearchModel.vue";
import {getTabFieldList} from "/@/utils/ckTable";
import {TIME_OPTION, TIME_OPTION_NO_HOUR} from "/@/views/reports/ReportList.data";
import {doValidateChartConfig} from "/@/views/reports/chart/ts/ChartValidate";

import {useI18n} from "/@/hooks/web/useI18n";
import {TABLE_SOURCE_SELECT} from "/@/views/posture/ts/Dict";
import {
  DATASOURCE_NUMBER_OPTION,
  FIELD_TYPE_NO,
  TIME_DATASOURCE
} from "/@/views/reportChart/ts/dataSource";

const dataTypeFieldNameMap = {
  1 : 'fieldGroup',
  2 : 'conditionGroup',
  4 : 'countGroup',
}
const {t} = useI18n();
function tp(name){
  return t('routes.report.report.'+name);
}

/**
 * 特殊处理，名称不能直接使用in8的,名称有其他地方使用做为判断条件等
 * 名称如包含空格则吧空格转化_
 * @param name
 */
function tp2(name) {
  name = name.replaceAll(' ', '_');
  return t('routes.report.report.' + name);
}
const {createMessage} = useMessage();
const emit = defineEmits(['chart','']);
const chartType = inject('chartType') as number;
const fieldDataOption = inject('fieldDataOption');
const source = inject('source');
const tableSource = inject('tableSource');
const groupAxisData:any = ref({});
const valueAxisData:any = ref({});
const displaySettingData:any = ref({})
const styleSettingData:any = ref({})
const legendOption = ref([]);
const chartRef = ref();
const fieldSearchRef = ref();
const imgData = ref('');
const btnShow = ref(true);
const isNoData = ref(false);
//查询
const tableSearchModelRef = ref();
const searchItem = ref({});
//table 列
const columns = ref([]);

//基础配置信息
const baseInfo = inject('baseInfo');
//表类型名称
const tableName = ref(source.value);
if(source.value == 'Bad actor' || source.value == 'Log'){
  tableName.value = source.value + tableSource.value;
}
//选择时间字段
const timeFieldOption = ref(TIME_DATASOURCE[tableName.value] )
//选择数字字段
const numberDataOption = ref(DATASOURCE_NUMBER_OPTION[tableName.value]);
//时间间隔下拉选
const timeIntervalOption = ref(TIME_INTERVAL_OPTION_USABLE(t)[5]);
const timeIntervalUnitName = ref('day');
const timeIntervalValue = ref('1');
const currentChart = ref();
//初始化
onMounted(()=>{
  initSettingData();
})

/**
 * 初始化数据
 */
function initSettingData() {
  legendOption.value = [];
  columns.value = [];
  displaySettingData.value = JSON.parse(JSON.stringify(displaySettingDefaultData));
  styleSettingData.value = JSON.parse(JSON.stringify(styleSettingDefaultData));
  groupAxisData.value = JSON.parse(JSON.stringify(groupAxisDefaultData));
  valueAxisData.value = JSON.parse(JSON.stringify(valueAxisDefaultData));
  if(DATA_TYPE_TIME_CHART.includes(CHART_SUBCLASS_TYPE[chartType.value])){
    groupAxisData.value.dataType = "3";
  }
  if(CHART_SUBCLASS_TYPE[chartType.value] == "Sunburst"){
    groupAxisData.value.dataType = "2";
  }else if(CHART_SUBCLASS_TYPE[chartType.value] == "HeatMap"){
    // valueAxisData.value.dataType = "4";
  }
  if(DATA_VISUALMAP_CHART.includes(CHART_SUBCLASS_TYPE[chartType.value] )){
    styleSettingData.value.type = "2";
  }
}

//------------------------------------------right 展示部分 start------------------------------------------------------------

/**
 * show chart
 */
async function showChart(data) {
  isNoData.value = false;
  if(CHART_SUBCLASS_TYPE[chartType.value] == 'table' && (data.dataList == null || data.dataList.length == 0)){
    data.dataList = [];
  }else if(CHART_SUBCLASS_TYPE[chartType.value] != 'table' && (data.seriesDatas == null || data.seriesDatas.length == 0) ){
    isNoData.value = true;
    return false;
  }
  if(styleSettingData.value.type == 1){
    let map = {};
    legendOption.value.map(item=>{
      map[item.label] = item.pureColor;
    })
    styleSettingData.value.colors = map;
  }
  nextTick(()=>{
    if(CHART_SUBCLASS_TYPE[chartType.value] == 'table'){
      chartRef.value.initCharts(data,styleSettingData.value ,columns.value)
    }else{
      chartRef.value.initCharts(data,styleSettingData.value)
    }
  })



}
/**
 * 保存table图片base64
 * @param chart
 */
async function saveTableImg(chart) {
  let el = document.getElementById(chart);
  const width = parseInt(window.getComputedStyle(el).width);
  const height = parseInt(window.getComputedStyle(el).height);

  const canvas = await html2canvas(el, {
    scale : 1, //缩放比例,默认为1
    allowTaint : true, //是否允许跨域图像污染画布
    useCORS : true, //是否尝试使用CORS从服务器加载图像
    width : width, //画布的宽度
    height :height, //画布的高度
    imageTimeout: 5000 // 设置图片的超时，设置0为禁用
    //backgroundColor: null //画布的背景色，默认为透明
  })
  imgData.value = canvas.toDataURL('image/png')
  // console.log('saveTableImg imgData.value==============',imgData.value)


}
/**
 * 保存图片base64
 * @param chart
 */
function saveChartImg(chart) {
  currentChart.value = chart;
 imgData.value =  chart.getDataURL({
    type: 'png',
    pixelRatio: 1,  //放大两倍下载，之后压缩到同等大小展示。解决生成图片在移动端模糊问题
    backgroundColor: 'transparent'
  });//获取到的是一串base64信息

}

/**
 * 获取图片
 */
function getChartImg(){
  // console.log('get image chart-------------------------------')
  return imgData.value;
}
/**
 * 图表配置数据(传给后台的)
 */
function getChartParam() {
  let param = {
    chartType : chartType.value
  }
  if(CHART_DISPLAY_SETTING[chartType.value] || (valueAxisData.value.dataType == 2 && valueAxisData.value.valueType == 2)){
    param.displaySetting = displaySettingData.value;
  }
  if(CHART_SUBCLASS_GROUP_NAME[chartType.value] || (DO_TIME_DATASOURCE.includes(source.value) && CHART_SUBCLASS_TYPE[chartType.value] == 'HeatMap')){
    param.yAxis = groupAxisData.value;
  }
  if(columns.value.length > 0){
    param.columns = columns.value;
  }

  param.xAxis = valueAxisData.value;

  return param;
}

/**
 * setting data
 */
function getSettingData(){
  return {
    chartType: chartType.value,
    groupAxisData: groupAxisData.value,
    valueAxisData: valueAxisData.value,
    displaySettingData: displaySettingData.value,
    styleSettingData: styleSettingData.value,
    legendOption: legendOption.value,
    columns: columns.value
  };
}
/**
 * get chart
 */
function getChart(){
  let validate = validateSetting();
  if(validate){
    let param = getChartParam();
    emit("chart",param);
  }else{
    createMessage.warning(CONFIG_ERROR);
  }

}

/**
 * validate setting config
 */
function validateSetting(){
  return doValidateChartConfig(getSettingData());
}
//------------------------------------------right 展示部分 end------------------------------------------------------------

//============================== setting xAxis start=====================
watch(()=>valueAxisData.value,(n)=>{
  getLegendOption()
},{deep : true})

/**
 * 校验是否可以添加 value field
 */
function validateAddValueField(fieldName) {
  if(DATA_FIELD_ONLY_ONE.includes(CHART_SUBCLASS_TYPE[chartType.value])){
    if(valueAxisData.value[fieldName].length == 1){
      btnShow.value = false;
      return false;
    }else{
      btnShow.value = true;
    }

  }else{
    btnShow.value = true;
  }

  return true;
}

/**
 * 添加分组
 */
function addValueField(){
  let dataType = valueAxisData.value.dataType ;
  if((dataType == 1)&& validateAddValueField('fieldGroup')){

    valueAxisData.value.fieldGroup.push({
      label : '',
      fieldValue : ''
    })
  }else if(dataType == 2  && validateAddValueField('conditionGroup')){

    if(valueAxisData.value.valueType && valueAxisData.value.valueType == 2){
      valueAxisData.value.conditionGroup.push({
        name : '',
      })
    }else{
      valueAxisData.value.conditionGroup.push({
        label : '',
        fieldValue : ''
      })
    }

  }else if(dataType == 4  && validateAddValueField('countGroup')){
    valueAxisData.value.countGroup.push({
      label : '',
      fieldValue : '',
      type : 1
    })
  }

}

/**
 * change value type
 */
function changeValueType(){

  let dataType:any = valueAxisData.value.dataType ;
  if(dataType == 1){
    valueAxisData.value = {
      dataType : dataType,
      fieldGroup : []
    }
  }else if(dataType == 2){
    valueAxisData.value = {
      dataType : dataType,
      valueType: "1",
      conditionGroup : []
    }
  }else if(dataType == 3){
    valueAxisData.value = {
      dataType : dataType,
    }
  }else if(dataType == 4){
    valueAxisData.value = {
      dataType : dataType,
      countGroup : []
    }
  }
}

/**
 * delete value
 * @param index
 */
function delValueAxisData(index){
  let dataType:any = valueAxisData.value.dataType ;
  //display setting
  if(displaySettingData.value.sortBy == index ){
    displaySettingData.value.sortBy = "";
  }else if(displaySettingData.value.sortBy){
    displaySettingData.value.sortBy = displaySettingData.value.sortBy - 1;
  }
  if(dataType == 1){
    valueAxisData.value.fieldGroup.splice(index,1);
  }else if(dataType == 2){
    valueAxisData.value.conditionGroup.splice(index,1);
  }else if(dataType == 4){
    valueAxisData.value.countGroup.splice(index,1);
  }
}

/**
 * sort by option
 */
function getLegendOption() {
  console.log('getLegend valueAxisData.value',valueAxisData.value)
  let dataType = valueAxisData.value.dataType ;
  let option = [];

  if(dataType == 1 &&  valueAxisData.value.fieldGroup){
     valueAxisData.value.fieldGroup.forEach((item,index)=>{
      if(item.fieldValue){
        option.push({ label : item.label || item.fieldValue,value : index , pureColor :getLegendColor(index)});
      }
    })
  }else if(dataType == 2 &&  valueAxisData.value.conditionGroup){
    valueAxisData.value.conditionGroup.forEach((item,index)=>{
      if(item.str && item.label){
        option.push({ label : item.label,value : index , pureColor : getLegendColor(index)});
      }
    })
  }else if(dataType == 4 &&  valueAxisData.value.countGroup){
    valueAxisData.value.countGroup.forEach((item,index)=>{
      if(item.fieldValue){
        option.push({ label : item.label || item.fieldValue,value : index , pureColor : getLegendColor(index)});
      }
    })
  }

  if(CHART_SUBCLASS_TYPE[chartType.value] == 'XAxisChart'){
    if(option.length > 0 && option.length < 2){
      let optionData = JSON.parse(JSON.stringify(option[0]));
      optionData.pureColor = getChartColor(1);
      optionData.label = optionData.label + "_2";
      option.push(optionData);
      btnShow.value = false;
    }else if(option.length == 2){
      btnShow.value = false;
    }else if(option.length == 0){
      btnShow.value = true;
    }
  }else if(CHART_SUBCLASS_TYPE[chartType.value] != 'WorldChart'){
    console.log('legendOption=================',option)
    legendOption.value = [...option];
  }

}

/**
 * legend color
 * @param index
 */
function getLegendColor(index){
  if(legendOption.value.length >= index + 1 && legendOption.value[index] && legendOption.value[index].pureColor){
    return legendOption.value[index].pureColor;
  }
  return getChartColor(index)
}

/**
 * floor field
 */
function addFloorField() {
  valueAxisData.value.floor.push({name : '',value : []})
}

/**
 * delete floor
 */
function delFloorField(j) {
  valueAxisData.value.floor.splice(j,1);
}
/**
 * delete floor
 * @param i floor index
 * @param j value index
 */
function delFloorValueAxisData(i,j) {
  valueAxisData.value.floor[i].value.splice(j,1);
}
/**
 * field change
 */
function fieldChange(v){
  const item = fieldDataOption.value.filter(item=>item.value == v.name);
  if(item){
    const options = TABLE_SOURCE_SELECT[tableName.value]?.[item[0]?.classFieldName];
    if(options && options.length > 0){
      v.options = options;
      return true;
    }
    return false
  }
  return false;

}
/**
 * add flow Value
 * @param i
 */
function addFloorFieldValue(i) {
  valueAxisData.value.floor[i].value.push('');
}
/**
 * 设置label
 * @param item
 */
function setFieldValueItemLabel(item){
  item.label = numberDataOption.value.filter(data=>data.value == item.fieldValue)[0].label;

}
/**
 * 设置label
 * @param item
 */
function setItemLabel(item) {
  // console.log('-----------------change item label---------------')
  if(valueAxisData.value.valueType == 2 ){
    item.label = item.name;
  }
  else if(valueAxisData.value.dataType == 1){
    item.label = fieldDataOption.value.filter(data=>data.value == item.fieldValue)[0].label;
  }
  // else if(!item.label){
  //   item.label = item.fieldValue;
  // }
}
//============================== setting xAxis end=====================

//============================== setting YAxis start=====================
/**
 * 添加分组
 */
function addGroupField(){
  let dataType = groupAxisData.value.dataType ;
  if(dataType == 1){
    groupAxisData.value.fieldGroup.push({
      label : '' ,
      fieldValue : ''
    })
   }else if(dataType == 2){
    groupAxisData.value.conditionGroup.push({
      label : '',
      fieldValue : ''
    })
  }
}

/**
 * group 类型改变
 */
function changeGroupType(){
  let dataType:any = groupAxisData.value.dataType ;
  if(dataType == 1){
    groupAxisData.value = {
      dataType : dataType,
      fieldGroup : []
    }
  }else if(dataType == 2){
    groupAxisData.value = {
      dataType : dataType,
      conditionGroup : []
    }
  }else if(dataType == 3){
    groupAxisData.value = {
      dataType : dataType,
      timeInterval : { type : "1"}
    }
    console.log('baseInfo.value.statisticalMode',baseInfo.value.statisticalMode)
    if(DO_TIME_DATASOURCE.includes(source.value)){
      if(baseInfo.value.statisticalType == 2){
        groupAxisData.value.timeInterval.rangeType = 2;
        groupAxisData.value.timeInterval.rangeValue = 1;
      }
      groupAxisData.value.timeInterval.timeField =  TIME_DEFAULT_USED_FIELD[tableName.value];
    }

  }
}

/**
 * delete group by
 * @param index
 */
function delGroupAxisData(index){
  let dataType:any = groupAxisData.value.dataType ;
  if(dataType == 1){
    groupAxisData.value.fieldGroup.splice(index,1);
  }else if(dataType == 2){
    groupAxisData.value.conditionGroup.splice(index,1);
  }
}

/**
 * Time interval
 */
function getTimeIntervalOption() {
    let rangeValue = baseInfo.value.rangeValue;
    console.log('getTimeIntervalOption', baseInfo.value)
    if(CHART_SUBCLASS_TYPE[chartType.value] == 'HeatMap'){
      timeIntervalUnitName.value = TIME_INTERVAL_OPTION_MAP[baseInfo.value.rangeType];
      return TIME_INTERVAL_OPTION_USABLE(t)[baseInfo.value.rangeType]
    }
    if(!rangeValue){
      return null;
    }
    const startDate = dayjs(rangeValue.split(',')[0], "YYYY-MM-DD HH:mm:ss");
    const endDate = dayjs(rangeValue.split(',')[1], "YYYY-MM-DD HH:mm:ss")
    const diff = endDate.diff(startDate);

    // 提取差异的时间单位
    const duration = dayjs.duration(diff);
    console.log('duration============',duration.$d)
    if(duration.$d.years > 0){
      timeIntervalUnitName.value = TIME_INTERVAL_OPTION_MAP[5];
      return TIME_INTERVAL_OPTION_USABLE(t)[5];
    }else if(duration.$d.months > 0 ||
      (duration.$d.days == 29 && duration.$d.hours == 23
        && duration.$d.minutes == 59 && duration.$d.seconds == 59)){
      timeIntervalUnitName.value = TIME_INTERVAL_OPTION_MAP[4];
      return TIME_INTERVAL_OPTION_USABLE(t)[4];
    }else if(duration.$d.days > 0){
      return TIME_INTERVAL_OPTION_USABLE(t)[3];
    }else if(duration.$d.hours > 0){
      return TIME_INTERVAL_OPTION_USABLE(t)[2];
    }else if(duration.$d.minutes > 0){
      return TIME_INTERVAL_OPTION_USABLE(t)[1];
    }

}

//============================== setting YAxis end=====================
//============================== setting StyleSetting start=====================


/**
 * add threshold
 */
function addThreshold() {
  if(!styleSettingData.value.thresholdColors)
    styleSettingData.value.thresholdColors = [];
  let i = styleSettingData.value.thresholdColors.length;
  styleSettingData.value.thresholdColors.push({start : null,end : null ,pureColor :  getChartColor(i)});
}

/**
 * add color
 */
function addColor() {
  if(!styleSettingData.value.rangeColors)
    styleSettingData.value.rangeColors = [];
  let i = styleSettingData.value.rangeColors.length;
  styleSettingData.value.rangeColors.push(getChartColor(i));
}
/**
 * delete threshold
 * @param index
 */
function delColors(index){
  if(styleSettingData.value.type == 2){
    styleSettingData.value.thresholdColors.splice(index,1)
  }else if(styleSettingData.value.type == 3){
    styleSettingData.value.rangeColors.splice(index,1)
  }

}
//============================== setting StyleSetting end=====================
//============================== setting  Column  start=====================
/**
 * add column
 */
function addColumnField() {
  columns.value.push('');
}

/**
 * delete column
 * @param index
 */
function delColumns(index) {
  columns.value.splice(index,1);
}
//============================== setting  Column  end=====================
/**
 * get chart config data
 */
function getChartConfig(){
  let data =  {
    configData : getSettingData(),
    imgData : imgData.value,
    chartData : getChartParam()
  }
  return data;
}

/**
 * 修改时回显数据
 * @param data
 */
function setChartConfig(data){
  console.log('setChartConfig baseInfo.value.',baseInfo.value)
  if(baseInfo.value && baseInfo.value.statisticalType == 2){//just once
    timeIntervalOption.value = getTimeIntervalOption();
  }
  if(JSON.stringify(data) == '{}'){
    // console.log('setChartConfig',data)
    // console.log('valueAxisDefaultData',valueAxisDefaultData)
    initSettingData();


  }else{
    console.log('setChartConfig',data )
    if(data.displaySettingData){
      displaySettingData.value = JSON.parse(JSON.stringify(data.displaySettingData));
    }
    if(data.displaySettingData){
      styleSettingData.value = JSON.parse(JSON.stringify(data.styleSettingData));
    }
    if(data.groupAxisData){
      groupAxisData.value = JSON.parse(JSON.stringify(data.groupAxisData));
    }
    if(data.valueAxisData){
      valueAxisData.value = JSON.parse(JSON.stringify(data.valueAxisData)) ;
    }
    if(data.legendOption){
      legendOption.value = JSON.parse(JSON.stringify(data.legendOption)) ;
    }

    if(data.columns){
      columns.value = JSON.parse(JSON.stringify(data.columns));
    }


    getChart();
  }

}

/**
 * 设置legend
 * @param data
 */
function setLegend(data) {
  legendOption.value = [];
  for(let i in data){
    legendOption.value.push({label : data[i],pureColor : getChartColor(i)})
  }
}

/**
 * field search
 * @param item
 */
function openSearch(item) {

  if(source.value == 'Log'){
    item.isFieldSearch = true;
  }else{
    searchItem.value = item;
    let list = getTabFieldList(FIELD_TYPE_NO[tableName.value]);
    if(item.str){
      tableSearchModelRef.value.init(list, item.str);
    }else {
      tableSearchModelRef.value.init(list,"");
    }

  }
}

/**
 * 保存查询
 * @param value
 */
function saveSearch(value) {
  searchItem.value.advancedQueryFlag = true;
  searchItem.value.str = JSON.stringify(value);
}

/**
 * echart clear
 */
function clearChart(){
  if(currentChart.value){
    // currentChart.value.dispose();
    currentChart.value.clear();
  }
}

/**
 * 清除统计内容
 */
function clearStatistic(){
  setChartConfig({});
  clearChart();
  btnShow.value = true;
}
defineExpose({
  showChart,
  getChartConfig,
  getChartImg,
  setChartConfig,
  clearChart,
  clearStatistic,
  validateSetting
});
</script>

<style lang="less" scoped>
@import '../less/common.less';
@import '../less/chart.less';
.report-setting_wrapper{
  display: flex;
  flex-direction: row;
  height:100%;
  overflow: hidden;
  .fix-left{
    display: flex;
    flex : 0 0 513px;
    flex-direction: column;
    height:100%;
    /*overflow-y: auto;*/
    border-right: 1px solid @border-color;
    padding: 16px 16px;
    .title-level1{
      margin-bottom: 16px;
    }
    .title-level2{
      margin-bottom: 0px;
    }
  }


}
.divider{
  border-top: 1px solid @border-color;
  margin: 16px 0px;
}
.setting-menu{
  border-radius: 4px;
  background: #28282C;
  border: 1px solid @border-color;
  .setting-head{
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    border-bottom: 1px solid @border-color;
    padding: 12px 16px;
    align-items: center;
  }
  .setting-content{
    display: flex;
    flex-direction: column;
    gap: 12px;
    padding: 12px 0px;
    .addBtn{
      padding-left: 62px;
      border: 0px!important;
      //display: flex;
      //flex-direction: row;
      //justify-content: flex-end;
      //padding-right: 16px;
    }
    .setting-content_label{
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      flex:0 0 40px;
      align-items: center;
      gap: 0px;
    }

    .setting-content_row{
      display: flex;
      flex-direction: row;
      gap: 8px;
      align-items: center;
      padding: 4px 16px;
      &:hover{
        background: rgba(255, 255, 255, 0.08);
        .setting-content_label span:last-child{
          color: #F75555;
        }
      }
      &.setting-content_row_display{
        span {
          width:120px;
        }
      }
      .setting-content_row_select{
        flex: 1;
        /deep/ .ant-select{
          width:100%!important;
        }
      }
    }
  }

}

.text-input{
  border: 1px solid @border-color;
}
.color-content{
  display: flex;
  flex-direction: column;
  gap: 12px;
}
/deep/.vc-color-wrap{
  width:24px!important;
}
.border_bottom_0{
  border-bottom: 0px!important;
}
.style-bg{
  border-radius: 4px;
  background: #1A1B1F;
  border: 1px solid rgba(255, 255, 255, 0.08);
  padding: 8px;
  max-height: 330px;
  overflow-y: auto;
  .color-group{
    border: 1px solid @border-color;
    width:120px;
    border-radius: 4px;
    display: flex;
    flex-direction: row;
    align-items: center;
    /deep/.ant-input{
      border: 0px;
      flex:1;
      border-radius: 0;
      border-left: 1px solid @border-color!important;
    }
    /deep/.ant-input:focus{
      box-shadow: none!important;
    }

    /deep/.vc-color-wrap{
      border-radius: 4px;
      left: 4px;
      margin-right: 8px;
      box-shadow: none;
      &.transparent{
        background-image: none;
      }
      .current-color{

        border-radius: 4px;

      }
    }

  }

}
/deep/.vc-alpha-slider{
  display: none;
}
/deep/.ant-tabs-nav{
  margin-bottom: 0px;
}

.icon-delete:hover{
  background: rgba(255, 255, 255, 0.08);
  color: #F75555;

}
.noData{
  margin-top: 30px;
  display: flex;
  justify-content: center;
}
.p-relative{
  position: relative;
}

.item_group {
  border: 1px solid @border-color;
  border-radius: 6px;
  display:flex;
  align-items: center;
  .splitLine{
    color: rgba(255, 255, 255, 0.08);
  }

  /deep/.ant-input-number-handler-wrap{
    display: none;
  }
  /deep/ .ant-input-number,/deep/.ant-select-selector{
    border: 0px!important;
    height:30px;
  }
  /deep/ .ant-input-number-focused,/deep/.ant-select-focused .ant-select-selector{
    box-shadow: none!important;
    border: 0px!important;
  }
}
</style>
