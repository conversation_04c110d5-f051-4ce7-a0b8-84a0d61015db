<template>

  <div v-if="legendOption.length > 0 && styleSettingData.type == 1">
    <div class="flex flex-row gap16 flex-align-center" v-for="(item) in legendOption">
      <div class="color-group">
        <ColorPicker lang="en" theme="black" v-model:pureColor="item.pureColor"
                     shape="square" format="hex6"></ColorPicker>
        {{ item.pureColor }}
      </div>
    </div>
  </div>
  <div v-if="styleSettingData.type == 2">
    <div class="flex flex-row flex-align-center gap16"
         v-for="(item,index) in styleSettingData.thresholdColors">
      <div class="color-group">
        <ColorPicker lang="en" theme="black" v-model:pureColor="item.pureColor"
                     disableAlpha="true" shape="square" format="hex6" useType="pure"></ColorPicker>
        {{ item.pureColor }}
      </div>
      <div>{{ item.start || 0 }}
        <template v-if="item.end">-{{ item.end }}</template>
      </div>
    </div>
  </div>
  <div class="setting-content padding12-16" v-if="styleSettingData.type == 3">
    <div class="flex flex-row gap16">
      Minimum value {{ styleSettingData.min }} - maximum value ({{ styleSettingData.max }})
    </div>
    <div class="flex flex-row gap16 flex-align-center "
         v-for="(colors,index) in styleSettingData.rangeColors">
      <div class="flex flex-row flex-align-center color-group flex1">
        <ColorPicker lang="en" theme="black" v-model:pureColor="styleSettingData.rangeColors[index]"
                     disableAlpha="true" shape="square" format="hex6" useType="pure"></ColorPicker>
        {{ styleSettingData.rangeColors[index] }}
      </div>
    </div>
  </div>

</template>

<script lang="ts" name="reports-ReportSettingColor" setup>

import {
  CHART_SUBCLASS_TYPE,
  GLOBAL_CHART_TYPE_NAME
} from "/@/views/reports/chart/ts/ChartType";
import {ColorPicker} from "vue3-colorpicker";
import "vue3-colorpicker/style.css";
import {defineProps, PropType, ref, watch} from "vue";

const props = defineProps(
  {
    value: {
      type: Object,
      default: {},
    },

  }
)
const styleSettingData = ref({type: 1});
const legendOption = ref([]);
const chartType = ref();
watch(() => props.value, (n) => {
  console.log('props.value', props.value)
  styleSettingData.value = n.styleSettingData;
  legendOption.value = n.legendOption;
  chartType.value = GLOBAL_CHART_TYPE_NAME[n.chartType];
}, {immediate: true, deep: true})
</script>

<style lang="less" scoped>
@import '../less/common.less';
</style>
