<template>
  <div class="search-container">
    <ReportSearchLog ref="logRef"
                     v-model:value="tableSource"
                     v-if="source == DatasourceEnum.LOG"
                     @search="saveSearch"
                     :width="477"
                     />
    <ReportSearchForm ref="searchRef" v-else></ReportSearchForm>
  </div>
</template>

<script lang="ts" name="reports-FieldSearch" setup>
import ReportSearchForm from "/@/views/reports/components/ReportSearchForm.vue";
import ReportSearchLog from "/@/views/reports/components/ReportSearchLog.vue";
import {defineExpose, defineProps, inject, nextTick, ref} from "vue";
import {DatasourceEnum} from "/@/views/reports/enums/dataSourceEnum";
import SearchSelect from "/@/views/threatHunting/components/SearchSelect.vue";

const emit = defineEmits(['update:value', 'ok']);
const source = inject('source');
const tableSource = inject('tableSource');
const logRef = ref();
const searchRef = ref();
const props = defineProps({
  value: {
    type: Object,
    default: {},
  }
})
console.log('field search props.value======', props.value)
if (props.value.echo) {
  setSearchVal(props.value.echo);
}

/**
 * 保存查询条件
 * @param val
 */
function saveSearch(val) {
  console.log('props.value======', props.value)
  console.log('saveSearch======', val)
  let item = JSON.parse(JSON.stringify(props.value));

  item.str = val.queryStr;
  item.echo = logRef.value.getSearchStr();
  item.isFieldSearch = false;
  console.log('saveSearch item======', item)
  emit("update:value", item);
}

/**
 * 回显查询条件
 * @param echo
 */
function setSearchVal(echo) {
  nextTick(() => {
    logRef.value.addSearchStr(echo);
  })
  // window.setTimeout(()=>{
  //   console.log('setSearchVal=======',echo)
  //   logRef.value.addSearchStr(echo);
  // },100)

}


</script>

<style lang="less" scoped>
.search-container {
  width: 477px;

  background: @dark-bg2;
  height: auto;
  position: absolute;
  z-index: 11;
  top: 39px;
  left: 0;
  border: 1px solid @border-color;

  /deep/ .view-line {
    border-right: 0px !important;
  }

  /deep/ .tree-search {
    top: 32px !important;
  }
}
</style>
