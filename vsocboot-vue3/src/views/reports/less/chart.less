.chart_content_wrapper{
  flex : 1;
  display: flex;
  align-items:start;
  justify-content: center;
  padding: 16px;

  .chart_content{
    background: @dark-bg1;
    height: 100%;
    width: 100%;
    border-radius: 8px;
    border: 1px solid @border-color;
    position: relative;

    .preview{
      position: absolute;
      left: 16px;
      top: 12px;
      border-radius: 4px;
      padding: 4px 8px;
      background: rgba(255, 255, 255, 0.08);
    }
    .preview-border-bottom{
      position: absolute;
      left: 0px;
      top: 47px;
      height: 1px;
      border-top: 1px solid rgba(255, 255, 255, 0.2);
    }
  }

}
