<template>
  <!-- header
   ===================================================================-->
  <div class="flex-row page_title flex-between">
    <div class="font20">
      <PageTitle/>
    </div>
    <div class="flex-row flex-align-center gap8">
      <div :title="currentShow == 'card' ? 'Grid mode' : 'Card mode'" class="ax-icon-button">
        <div class="ax-icon-button">
          <a-tooltip :title="tp('gridMode')" v-if="currentShow == 'card'">
            <div class="soc ax-com-Formview ax-icon" @click="currentShow = 'grid'"></div>
          </a-tooltip>
          <a-tooltip :title="tp('cardMode')" v-else-if="currentShow == 'grid'">
            <div class="soc ax-com-Barview ax-icon" @click="reloadCard"></div>
          </a-tooltip>
        </div>

      </div>
      <!--  草稿箱 start-->
      <a-button class="ax-btn-pre" @click="openDraftDialog">
        <span class="soc ax-com-Record ax-icon"></span>
        {{ tp('Draft') }}
      </a-button>
      <!--  草稿箱 end-->
      <!--  新建 start-->
      <a-button type="primary" @click="handleAdd" class="ax-btn-pre">
        <span class="soc ax-com-Add ax-icon"></span>{{ tp('CreateReport') }}
      </a-button>
      <!--  新建 end-->
    </div>
  </div>

  <!-- 查询项
 ===================================================================-->
  <div class="ax-search-wrapper pt-8px pl-16px pr-16px ">
    <j-input v-model:value="queryParam.viewName" :placeholder="tp('ReportName')" :search="true"/>
    <a-select v-model:value="queryParam.chartType" allow-clear :placeholder="tp('ViewType')">
      <a-select-option :value="key" v-for="(value, key) in GLOBAL_CHART_TYPE_NAME">{{
          tp2(value)
        }}
      </a-select-option>
    </a-select>
    <template
      v-if="!getTenantMode() || isTenant() || (isAdministrator() && (currentTenantId == '' || currentTenantId == 'MSSP'))">
      <a-select v-model:value="queryParam.permissionType" allow-clear
                :placeholder="tp('ReportStatus')">
        <a-select-option value="1">{{ tp('Private') }}</a-select-option>
        <a-select-option value="2">{{ tp('Public') }}</a-select-option>
      </a-select>
    </template>
    <a-switch :checked="queryParam.isTop == 1" @change="handleChangeTop" :placeholder="tp('isTop')"
              :title="tp('isTop')"/>
  </div>

  <!-- content
  ===================================================================-->
  <div class="report-wrapper">


    <!-- 租户Tab（只有开启租户模式的MSSP才可见）
     ===================================================================-->
    <div class="pl-16px pr-16px">
      <CustomTab v-if="isAdministrator()" :tabs="tenantList" attrId="id" @change="tenantChange"
                 v-model:active="currentTenantId"/>
    </div>

    <a-spin :spinning="spinning">
      <!-- 卡片模式
      ===================================================================-->
      <div class="flex flex-row flex-wrap gap-16px pl-16px pr-16px pt-16px"
           v-if="currentShow == 'card'">
        <template v-for="(item, index) in reportList" :key="'report-card-'+index">
          <ReportCard
            v-model:value="reportList[index]"
            @del="doDelete"
            @edit="doEdit"
            @public="doPublic"
            @private="doPrivate"
            @cancelTop="cancelTop"
            @top="doTop"
            @show="showReportData"
            v-model:tenantMap="tenantMap"
            v-model:currentTenantId="currentTenantId"
          ></ReportCard>
        </template>
      </div>

      <!-- 列表模式
      ===================================================================-->
      <ReportList
        v-else
        v-model:queryParam="queryParam"
        ref="gridRef"
        @del="doDelete"
        @edit="doEdit"
        @public="doPublic"
        @private="doPrivate"
        v-model:value="currentTenantId"
      ></ReportList>
      <div style="margin-top: 16px" v-if="currentShow == 'card'">
        <IPagination @handlePageChange="handlePageChange"
                     :pageSizeOptions="['5','10', '15', '20', '25', '50']" :defaultPageSize="10"
                     :total="total"/>
      </div>
    </a-spin>
  </div>

  <!-- draft modal -->
  <draft-modal @register="registerModal" @reload="load"></draft-modal>
</template>

<script lang="ts" name="reports-report" setup>
import {useI18n} from '/@/hooks/web/useI18n';
import {onMounted, ref, watch} from 'vue';
import {queryList} from '/@/views/system/tenant/tenant.api';
import {getTenantId, getTenantMode, isAdministrator, isTenant} from '/@/utils/auth';
import ReportList from '/@/views/reports/ReportList.vue';
import ReportCard from '/@/views/reports/components/ReportCard.vue';
import {
  cardPageList,
  deleteOne,
  handelCancelTop,
  handelPrivate,
  handelPublic,
  handelTop,
} from '/@/views/reports/ReportBase.api';
import {GLOBAL_CHART_TYPE_NAME} from '/@/views/reports/chart/ts/ChartType';
import {JInput} from '/@/components/Form';
import {useRouter} from 'vue-router';
import {useModal} from '/@/components/Modal';
import DraftModal from '/@/views/reports/modules/DraftModal.vue';
import PageTitle from '/@/components/Menu/src/components/PageTitle.vue';
import {CustomTab} from "/@/components/CustomTab/index";

import IPagination from '/@/components/IPagination/IPagination.vue';

const [registerModal, {openModal}] = useModal();

const {t} = useI18n();

function tp(name) {
  return t('routes.report.report.' + name);
}

/**
 * 特殊处理，名称不能直接使用in8的,名称有其他地方使用做为判断条件等
 * 名称如包含空格则吧空格转化_
 * @param name
 */
function tp2(name) {
  name = name.replaceAll(' ', '_');
  return t('routes.report.report.' + name);
}

const router = useRouter();
//租户合集
const tenantList = ref([]);
const tenantMap = ref({});
//选中租户
const currentTenantId = ref('');


//当前显示内容
const currentShow = ref('card');
//report 集合
const reportList = ref([]);
const gridRef = ref();

const total = ref(10);
const defaultPageSize = 10;
const spinning = ref(false);

//查询参数
const queryParam = ref({reportStatus: 1, isTop: 1});
//初始化
onMounted(() => {
  queryTenant();
  load(1, defaultPageSize);

});

watch(
  () => queryParam.value,
  () => {
    load(1, defaultPageSize);
  },
  {deep: true}
);

/**
 * 刷新card
 */
function reloadCard() {
  currentShow.value = 'card';
  load(1, defaultPageSize);
}

/**
 * 查询租户数据
 */
function queryTenant() {
  tenantList.value = [{id: '', name: 'All'}];
  tenantMap.value['MSSP'] = 'MSSP';
  queryList({status: 1}).then((data) => {
    if (isAdministrator()) {
      tenantList.value.push({id: 'MSSP', name: 'MSSP'});
      tenantList.value.push(...data);
    }
    data.forEach((item) => {
      tenantMap.value[item.id] = item.name;
    });
  });

  if (isTenant()) {
    currentTenantId.value = getTenantId();
  }
}

function handlePageChange(page, size) {
  load(page, size);
}

/**
 * 租户选中改变
 * @param activeKey
 */
function tenantChange(activeKey) {
  currentTenantId.value = activeKey;
  load(1, defaultPageSize);
}

/**
 * 删除
 * @param id
 */
async function doDelete(id) {
  await deleteOne({id}, load);
}

/**
 * 编辑
 */
function doEdit(id) {
  router.push({
    path: '/reports/ReportEdit',
    query: {id: id},
  });
}

/**
 * 置顶
 * @param id
 */
async function doTop(id) {
  await handelTop({baseId: id, isTop: 1}, load);
}

/**
 * 取消置顶
 * @param id
 */
async function cancelTop(id) {
  await handelCancelTop({baseId: id, isTop: 0}, load);
}

/**
 * public
 * @param id
 */
async function doPublic(id) {
  await handelPublic({id: id, permissionType: 2}, load);
}

/**
 * Private
 * @param id
 */
async function doPrivate(id) {
  await handelPrivate({id: id, permissionType: 1}, load);
}

/**
 * 查询置顶
 */
function handleChangeTop(value) {
  console.log('handleChangeTop', value);
  queryParam.value.isTop = value ? 1 : null;
  load(1, defaultPageSize);
}

/**
 * 添加
 */
function handleAdd() {
  router.push({
    path: '/reports/ReportEdit',
    query: {},
  });
}

/**
 * 报表查看
 * @param id
 */
function showReportData(id, title) {
  router.push({
    path: '/reports/ReportView',
    query: {id: id, title: title, tenant: currentTenantId.value},
  });
}

/**
 * 页面展示数据加载
 */
function load(page: 1, size: 10) {
  let now = currentShow.value;
  if (now == 'card') {
    spinning.value = true;
    reportList.value = [];
    loadCardReport(page, size);
    spinning.value = false;
  } else {
    gridRef.value.reloadGrid();
  }
}

/**
 * 加载report List
 */
function loadCardReport(page, size) {
  let param: any = {...queryParam.value};
  if (currentTenantId.value) {
    param.socTenantIds = currentTenantId.value;
  }
  param.pageNo = page;
  param.pageSize = size;
  cardPageList(param).then((result) => {
    total.value = result.total;
    reportList.value = result.records;
    console.log('reportList.value:', reportList.value);
  });
}

/**
 * 查看草稿箱
 */
function openDraftDialog() {
  openModal(true, {
    isUpdate: false,
    showFooter: false,
    tenantList: tenantList.value,
    tenantMap: tenantMap.value,
  });
}
</script>

<style lang="less" scoped>
@import './less/common.less';

.report-wrapper {
  padding: 16px 0;
}

/deep/ .ant-tabs-nav-wrap {
  padding-left: 16px;
}

.iconBtn {
  width: 16px;
  cursor: pointer;
}
</style>
