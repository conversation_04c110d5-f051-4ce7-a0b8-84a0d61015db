import {BasicColumn} from '/@/components/Table';
import {CHART_SUBCLASS_TYPE} from "/@/views/reports/chart/ts/ChartType";
import {useI18n} from "/@/hooks/web/useI18n";

const {t} = useI18n();
function tp(name){
  return t('routes.report.report.'+name);
}
export const TIME_OPTION  = [{
  label : t('common.Hour'),
  value : 1
},{
  label : t('common.Day'),
  value : 2
},{
  label :  t('common.Week'),
  value : 3
},{
  label : t('common.Month'),
  value : 4
},{
  label : t('common.Year'),
  value : 5
}];
export const TIME_OPTION_NO_HOUR  = [ {
  label : t('common.Day'),
  value : 2
},{
  label : t('common.Week'),
  value : 3
},{
  label : t('common.Month'),
  value : 4
},{
  label : t('common.Year'),
  value : 5
}];
export const columns: BasicColumn[] = [
    {
    title: tp('ReportName'),
    dataIndex: 'viewName',
    width: '20%'
   },

   {
    title: tp('ViewType'),
    dataIndex: 'chartType',
     customRender: ({text}) => {
       return CHART_SUBCLASS_TYPE[text]
     },

   },
  {
    title: tp('ReportStatus'),
    dataIndex: 'permissionType',
    customRender: ({text}) => {
      const map = {1 : 'Private',2 : 'Public'};
      return map[text]
    },
    width: '10%'
  },
  {
    title: tp('Description'),
    dataIndex: 'viewDesc'
  },
   {
    title: tp('Creator'),
    dataIndex: 'createBy',
     slots: {customRender: 'userInfo'},
     width: '12%'
   },

   {
    title: tp('RefreshTime'),
    dataIndex: 'updateTime',
     customRender: ({text,record}) => {
       if(text){
         return text;
       }
       return record.createTime;
     },
     width: '12%'
   }
];




