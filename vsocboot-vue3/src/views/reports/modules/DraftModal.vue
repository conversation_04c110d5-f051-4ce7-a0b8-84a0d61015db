<template>
  <BasicModal   @register="registerModal"  :title="tp('Draft')"  :footer="null" width="1200px"   @cancel="reload">
    <div class="padding16" style="height: auto;min-height: 600px">
      <div class="ax-search-wrapper pb-16px">
        <j-input v-model:value="queryParam.viewName" :placeholder="tp('ReportName')" :search="true" />
      </div>


      <!-- 租户Tab（只有开启租户模式的MSSP才可见）
     ===================================================================-->
      <CustomTab v-if="isAdministrator()" :tabs="tenantList" attrId="id" @change="tenantChange" v-model:active="currentTenantId"/>

      <div class="flex flex-row flex-wrap gap-16px pt-16px" >
        <template v-for="(item, index) in reportList" :key="'report-card-'+index">
          <ReportCard
            v-model:value="reportList[index]"
            @del="doDelete"
            @edit="doEdit"
            @public="doPublic"
            @private="doPrivate"
            @publish="doPublish"
            @cancelTop="cancelTop"
            from="draft"
            v-model:tenantMap="tenantMap"
            v-model:currentTenantId="currentTenantId"

          ></ReportCard>
        </template>
      </div>

    </div>
    <div style="margin-top: 16px">
      <IPagination @handlePageChange="handlePageChange" :pageSizeOptions="['3', '6', '9', '12','15','30']" :defaultPageSize="6" :total="total" />
    </div>
  </BasicModal>
</template>

<script lang="ts" setup>
  import { BasicModal, useModalInner } from '/@/components/Modal';
  import ReportCard from '/@/views/reports/components/ReportCard.vue';
  import { isAdministrator } from '/@/utils/auth';
  import { nextTick, ref, watch } from 'vue';
  import IPagination from '/@/components/IPagination/IPagination.vue';

  const { t } = useI18n();
  function tp(name) {
    return t('routes.report.report.' + name);
  }

  const router = useRouter();
  const emit = defineEmits(['reload']);
  //租户合集
  const tenantList = ref([]);
  const tenantMap = ref({});
  //选中租户
  const currentTenantId = ref('');
  //report 集合
  const reportList = ref([]);
  //查询参数
  const queryParam = ref({ viewName: '', reportStatus: 2 });
  //是否刷新首页
  const isEmit = ref(false);
  //数量
  const defaultPagesize = 6;
  watch(
    () => queryParam.value,
    () => {
      loadCardReport(1, defaultPagesize);
    },
    { deep: true }
  );

  const total = ref(0);

  import { cardPageList, deleteOne, handelCancelTop, handelPublic, handelPrivate, handelTop, handelPublish } from '/@/views/reports/ReportBase.api';
  import { JInput } from '/@/components/Form';
  import { useRouter } from 'vue-router';
  import { useI18n } from '/@/hooks/web/useI18n';
  import {CustomTab} from "/@/components/CustomTab/index";
  //表单赋值
  const [registerModal, { setModalProps, closeModal }] = useModalInner(async (data) => {
    //重置表单

    setModalProps({
      confirmLoading: false,
      showCancelBtn: data?.showFooter,
      showOkBtn: data?.showFooter,
      // defaultFullscreen: true,
    });
    nextTick(() => {
      tenantList.value = data.tenantList;
      tenantMap.value = data.tenantMap;
      loadCardReport(1, defaultPagesize);
    });
  });
  /**
   * 租户选中改变
   * @param activeKey
   */
  function tenantChange(activeKey) {
    currentTenantId.value = activeKey;
    loadCardReport(1, defaultPagesize);
  }

  function reload() {
    if (isEmit.value) {
      emit('reload');
    }
  }
  /**
   * 加载report List
   */
  function loadCardReport(page: 1, size: 6) {
    reportList.value = [];
    let param: any = { ...queryParam.value };
    if (currentTenantId.value) {
      param.socTenantIds = currentTenantId.value;
    }
    param.pageNo = page;
    param.pageSize = size;
    cardPageList(param).then((result) => {
      total.value = result.total;
      reportList.value = result.records;
      console.log('reportList.value:', reportList.value);
    });
  }

  /**
   * 删除
   * @param id
   */
  async function doDelete(id) {
    await deleteOne({ id }, loadCardReport);
  }

  /**
   * 编辑
   */
  function doEdit(id) {
    router.push({
      path: '/reports/ReportEdit',
      query: { isUpdate: false, id: id },
    });
  }

  //分页回调
  function handlePageChange(page, size) {
    loadCardReport(page, size);
  }

  /**
   * 置顶
   * @param id
   */
  async function doTop(id) {
    await handelTop({ baseId: id, isTop: 1 }, loadCardReport);
  }
  /**
   * public
   * @param id
   */
  async function doPublic(id) {
    await handelPublic({ id: id, permissionType: 2 }, loadCardReport);
  }
  /**
   * publish
   * @param id
   */
  async function doPublish(id) {
    isEmit.value = true;
    await handelPublish({ id: id, reportStatus: 1 }, loadCardReport);
  }
  /**
   * Private
   * @param id
   */
  async function doPrivate(id) {
    await handelPrivate({ id: id, permissionType: 1 }, loadCardReport);
  }
  /**
   * 取消置顶
   * @param id
   */
  async function cancelTop(id) {
    await handelCancelTop({ baseId: id, isTop: 0 }, loadCardReport);
  }
</script>

<style lang="less" scoped>
  @import '../less/common.less';
  /deep/.report-card{
    width: 376px;
  }
  .tenant_item {
    padding: 6px 16px;
    color: @font-color-1;
    cursor: pointer;
  }
  .tenant_selected {
    border-radius: 8px;
    background: rgba(255, 255, 255, 0.08);
    color: @font-color-white;
  }

  .report-list {
    margin-top: 16px;
  }
 /deep/.ant-modal-body{
   min-height: 500px!important;
 }

</style>
