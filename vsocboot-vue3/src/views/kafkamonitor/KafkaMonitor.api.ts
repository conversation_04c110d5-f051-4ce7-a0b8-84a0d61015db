import { defHttp } from '/@/utils/http/axios';

enum Api {
  getTopics = '/operation/kafka/topics',
  getPartitions = '/operation/kafka/topic/info',
  getConsumerGroupsByTopic = '/operation/kafka/topic/groups',
  getGroupOffsets = '/operation/kafka/group/offsets',
}

/**
 * 获取Kafka主题列表
 */
export const getKafkaTopics = (params: any) => {
  return defHttp.get({
    url: Api.getTopics,
    params,
  });
};

/**
 * 获取Kafka分区列表
 */
export const getKafkaPartitions = (params: any) => {
  return defHttp.get({
    url: Api.getPartitions,
    params,
  });
};

/**
 * 获取Kafka主题消费者组列表
 */
export const getConsumerGroupsByTopic = (params: any) => {
  return defHttp.get({
    url: Api.getConsumerGroupsByTopic,
    params,
  });
};

/**
 * 获取Kafka消费者组偏移量
 */
export const getGroupOffsets = (params: any) => {
  return defHttp.get({
    url: Api.getGroupOffsets,
    params,
  });
};
