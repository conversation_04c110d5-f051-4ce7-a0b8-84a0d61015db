import { defHttp } from '/@/utils/http/axios';
import { Modal } from 'ant-design-vue';
import { useI18n } from '/@/hooks/web/useI18n';

const { t } = useI18n();

enum Api {
  list = '/anomalyrecords/anomalyRecords/list',
  queryList = '/anomalyrecords/anomalyRecords/queryList',
  save = '/anomalyrecords/anomalyRecords/add',
  queryById = '/anomalyrecords/anomalyRecords/queryById',
  edit = '/anomalyrecords/anomalyRecords/edit',
  deleteOne = '/anomalyrecords/anomalyRecords/delete',
  deleteBatch = '/anomalyrecords/anomalyRecords/deleteBatch',
  importExcel = '/anomalyrecords/anomalyRecords/importExcel',
  exportXls = '/anomalyrecords/anomalyRecords/exportXls',
  closed = '/anomalyrecords/anomalyRecords/closed',
  assign = '/anomalyrecords/anomalyRecords/assign',
  triage = '/anomalyrecords/anomalyRecords/triage',
  batchClose = '/anomalyrecords/anomalyRecords/batchClose',
  batchAssign = '/anomalyrecords/anomalyRecords/batchAssign',
  batchTriage = '/anomalyrecords/anomalyRecords/batchTriage',
}

/**
 * 关闭
 * @param params
 */
export const closed = (params) => {
  return defHttp.post({ url: Api.closed, params });
};
/**
 * 批量关闭
 * @param params
 */
export const batchClose = (params) => {
  return defHttp.post({ url: Api.batchClose, params });
};

/**
 * 分配用户
 * @param params
 */
export const assign = (params) => {
  return defHttp.post({ url: Api.assign, params });
};
/**
 * 批量分配用户
 * @param params
 */
export const batchAssign = (params) => {
  return defHttp.post({ url: Api.batchAssign, params });
};
/**
 * 改变验证状态
 * @param params
 */
export const triage = (params) => {
  return defHttp.post({ url: Api.triage, params });
};
/**
 * 批量改变验证状态
 * @param params
 */
export const batchTriage = (params) => {
  return defHttp.post({ url: Api.batchTriage, params });
};

/**
 * 导出api
 * @param params
 */
export const getExportUrl = Api.exportXls;
/**
 * 导入api
 */
export const getImportUrl = Api.importExcel;
/**
 * 列表接口
 * @param params
 */
export const list = (params) => defHttp.get({ url: Api.list, params });

export const queryList = (params) => defHttp.get({ url: Api.queryList, params });

export const queryById = (params) => defHttp.get({ url: Api.queryById, params });
export const queryAnomalyById = (params) => defHttp.get({ url: Api.queryById, params });

/**
 * 删除单个
 * @param params
 * @param handleSuccess
 */
export const deleteOne = (params, handleSuccess) => {
  return defHttp.delete({ url: Api.deleteOne, params }, { joinParamsToUrl: true }).then(() => {
    handleSuccess();
  });
};
/**
 * 批量删除
 * @param params
 * @param handleSuccess
 */
export const batchDelete = (params, handleSuccess) => {
  Modal.confirm({
    title: t('common.delConfirmText'),
    content: t('common.delContent'),
    okText: t('common.okText'),
    cancelText: t('common.cancelText'),
    wrapClassName: 'delete_confirm',
    onOk: () => {
      return defHttp
        .delete(
          {
            url: Api.deleteBatch,
            data: params,
          },
          { joinParamsToUrl: true }
        )
        .then(() => {
          handleSuccess();
        });
    },
  });
};
/**
 * 保存或者更新
 * @param params
 * @param isUpdate 是否是更新数据
 */
export const saveOrUpdate = (params, isUpdate) => {
  const url = isUpdate ? Api.edit : Api.save;
  return defHttp.post({ url: url, params });
};
