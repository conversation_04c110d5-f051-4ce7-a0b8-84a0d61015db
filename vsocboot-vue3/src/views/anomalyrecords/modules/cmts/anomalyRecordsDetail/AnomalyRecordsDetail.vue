<template>
  <div>
    <div class="r_div f-color-08" v-for="item in anomalyRecordsInfo" :key="item.value">
      <div class="r_h_div">
        {{ item.label }}
      </div>
      <div class="r_c_div">
        <div v-if="item.value == 'alertLevel'">
          <Severity2 :value="ruleInfo[item.value]" type="number" />
        </div>
        <div class="r_c_text" v-else-if="ruleInfo[item.value]">
          {{ ruleInfo[item.value] }}
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { ref } from 'vue';
  import { anomalyRecordsInfo } from '/@/views/anomalyrecords/modules/cmts/anomalyRecordsDetail/RuleInfo';
  import Severity2 from '/@/components/Severity/Severity2.vue';
  import { useI18n } from '/@/hooks/web/useI18n';
  import { LOG_TABLE } from '/@/utils/valueEnum';
  import TableUser from '/@/components/Table/src/components/TableUser.vue';

  const { t } = useI18n();
  const props = defineProps({
    record: Object as any,
  });
  console.log(props);
  const ruleInfo = ref<any>({});
  if (props.record) {
    ruleInfo.value = props.record;
    console.log(ruleInfo.value);
  }
</script>

<style scoped lang="less">
  .r_div:nth-child(odd) {
    background: rgba(255, 255, 255, 0.04);
  }

  .r_div {
    display: flex;
    gap: 8px;
    padding: 8px 16px;
    align-items: center;
    min-height: 46px;

    .r_h_div {
      width: 200px;
    }

    .r_c_div {
      display: flex;
      align-items: center;
      gap: 4px;
      flex-wrap: wrap;
      flex: 1;
      min-width: 0;

      .r_c_text {
        padding: 4px 8px;
        background: rgba(255, 255, 255, 0.1);
        border-radius: 4px;
        word-break: break-all;
      }
    }
  }
</style>
