<template>
  <a-spin :spinning="!dataInfo.id" style="height: 100%">
    <div class="risk_info_div" v-if="dataInfo.id">
      <div class="flex_top_div">
        <div>
          <img style="max-width: 80px; max-height: 60px" alt="" v-if="dataInfo.vendorIcon" :src="render.renderUploadImageSrc(dataInfo.vendorIcon)" />
          <img style="max-width: 80px; max-height: 60px" alt="" v-else src="../../aggregationRiskEventView/image/default.png" />
        </div>
        <div class="btn_div">
          <!-- <a-button>
            <Icon icon="ant-design:plus-outlined"/>
            {{ tp('investigation') }}
          </a-button> -->
          <div style="position: relative" v-if="hasTicketPermission()">
            <a-dropdown :trigger="['click']">
              <a-button class="ant-btn-sm">
                <span class="soc ax-com-Add"></span>
                {{ tp('ticket') }}
              </a-button>
              <template #overlay>
                <a-menu>
                  <EventApply :record="dataInfo" eventType="5" @applyOk="refReload" />
                </a-menu>
              </template>
            </a-dropdown>

            <div style="position: absolute; inset: 0; opacity: 0" v-if="dataInfo.ticketInstId">
              <Ticket :ticketInfo="dataInfo" />
            </div>
          </div>

          <a-dropdown :trigger="['click']">
            <Icon icon="ant-design:ellipsis-outlined" :size="24" @click.prevent />
            <template #overlay>
              <a-menu>
                <a-menu-item v-if="hasPermission('risk:assign_other') && dataInfo?.riskStatus !== 2">
                  <AnomalyRecordsAssign :record="dataInfo" type="assign_other" :reload="refReload" />
                </a-menu-item>
                <a-menu-item v-else-if="hasPermission('risk:assign_self') && dataInfo?.riskStatus !== 2">
                  <AnomalyRecordsAssign :record="dataInfo" type="assign_self" />
                </a-menu-item>

                <!-- 有权限且没有关闭且分配给登录人 -->
                <a-menu-item v-if="hasPermission('risk:triage') && dataInfo?.riskStatus !== 2 && dataInfo?.owner == userStore.getUserInfo.id">
                  <AnomalyRecordsTriage :reload="refReload" :record="dataInfo" />
                </a-menu-item>

                <!-- 有权限且已验证且分配给登录人且没有关闭 -->
                <a-menu-item
                  v-if="
                    hasPermission('risk:close') &&
                    dataInfo?.triageStatus !== 0 &&
                    dataInfo?.owner == userStore.getUserInfo.id &&
                    dataInfo?.riskStatus !== 2
                  "
                >
                  <AnomalyRecordsClose :reload="refReload" :record="dataInfo" type="close" />
                </a-menu-item>
                <a-menu-item v-if="hasPermission('risk:close') && dataInfo?.owner == userStore.getUserInfo.id && dataInfo?.riskStatus === 2">
                  <AnomalyRecordsClose :reload="refReload" :record="dataInfo" type="open" />
                </a-menu-item>
              </a-menu>
            </template>
          </a-dropdown>
        </div>
      </div>
      <div class="risk_content_div">
        <div class="ax-tab-card h-[100%]">
          <a-tabs type="card">
            <a-tab-pane key="1" :tab="tp('detail')">
              <AnomalyRecordsDetail :record="dataInfo" />
            </a-tab-pane>
          </a-tabs>
        </div>
      </div>
    </div>
  </a-spin>
</template>

<script lang="ts" setup>
  import { render } from '/@/utils/common/renderUtils';
  import { useI18n } from '/@/hooks/web/useI18n';
  import { hasTicketPermission, toThreatHunting } from '/@/views/aggregationRiskEventView/RiskEventView.data';
  import { useRouter } from 'vue-router';
  import AnomalyRecordsAssign from '/@/views/aggregationRiskEventView/component/uebaRecords/AnomalyRecordsAssign.vue';
  import AnomalyRecordsClose from '/@/views/aggregationRiskEventView/component/uebaRecords/AnomalyRecordsClose.vue';
  import AnomalyRecordsTriage from '/@/views/aggregationRiskEventView/component/uebaRecords/AnomalyRecordsTriage.vue';
  import { usePermission } from '/@/hooks/web/usePermission';
  import { useUserStore } from '/@/store/modules/user';
  import { Ticket } from '/@/views/aggregationRiskEventView/component/ticket/index';
  import EventApply from '/@/views/ticket/view/apply/eventApply.vue';
  import { ref } from 'vue';
  import { queryById } from '/@/views/anomalyrecords/AnomalyRecords.api';
  import AnomalyRecordsDetail from '/@/views/anomalyrecords/modules/cmts/anomalyRecordsDetail/AnomalyRecordsDetail.vue';
  import { list } from '/@/views/proposal/ProposalManagement.api';
  const router = useRouter();
  const { hasPermission } = usePermission();
  const userStore = useUserStore();
  const { t } = useI18n();
  const ifShowProposal = ref(false);
  const riskProposalRef = ref();

  function tp(name) {
    return t('routes.riskEvent.' + name);
  }

  const props = defineProps({
    id: String,
    reload: Function,
  });
  console.log(props);
  const dataInfo = ref<any>({});

  loadInfo();

  function loadInfo() {
    queryById({ id: props.id }).then((data) => {
      dataInfo.value = data;
    });
  }

  const proposalInfo = ref<any>({});
  function loadProposalInfo(info) {
    proposalInfo.value = {};
    let params: any = {
      riskEventName: info.eventName,
      socTenantId: info.socTenantId,
    };
    console.log('查询建议方案的参数:', params);

    list(params).then((info) => {
      console.log('建议方案API返回数据类型:', typeof info);
      console.log('建议方案API返回数据:', info);
      if (info && info.records && info.records.length > 0) {
        console.log('records数组长度:', info.records.length);
        let obj = {};

        // 打印查找条件和每条记录的值进行对比
        console.log('当前事件名称:', dataInfo.value.eventName, '类型:', typeof dataInfo.value.eventName);

        // 打印每条记录的riskEventName值进行对比
        info.records.forEach((item, index) => {
          console.log(
            `记录[${index}].riskEventName:`,
            item.riskEventName,
            '类型:',
            typeof item.riskEventName,
            '比较结果:',
            item.riskEventName == dataInfo.value.eventName
          );
        });

        // 尝试直接比较，忽略大小写
        obj = info.records.find((item: any) => {
          if (!item.riskEventName || !dataInfo.value.eventName) return false;

          // 尝试不同的比较方式
          const exactMatch = item.riskEventName == dataInfo.value.eventName;
          const lowerCaseMatch = item.riskEventName.toLowerCase?.() === dataInfo.value.eventName.toLowerCase?.();
          const trimMatch = item.riskEventName.trim?.() === dataInfo.value.eventName.trim?.();

          return exactMatch;
        });

        // 如果还是找不到，尝试使用第一条记录
        if (!obj && info.records.length > 0) {
          console.log('未找到匹配项，使用第一条记录');
          obj = info.records[0];
        }

        proposalInfo.value = obj ? obj : {};
        console.log('最终proposalInfo:', proposalInfo.value);
        ifShowProposal.value = Object.keys(proposalInfo.value).length > 0;
      } else {
        console.log('没有建议方案数据');
        ifShowProposal.value = false;
      }
    });
  }

  const riskSummaryRef = ref();

  function refReload() {
    props.reload && props.reload();
    riskSummaryRef.value.loadInfo();
    loadInfo();
  }
</script>

<style lang="less" scoped>
  .risk_info_div {
    display: flex;
    height: 100%;
    flex-direction: column;
    .flex_top_div {
      display: flex;
      gap: 16px;
      padding: 8px 40px;
      margin-bottom: 8px;

      .btn_div {
        display: flex;
        gap: 8px;
        align-items: center;
      }
    }

    .risk_content_div {
      padding-left: 24px;
      flex: 1;
    }
  }
  .detail-tab-pane {
    height: calc(100vh - 180px);
    overflow-y: auto;
    padding: 0;
  }

  :deep(.ant-tabs-card > .ant-tabs-content-holder) {
    overflow: auto;
  }
</style>
