<!--
 * @Author: fanglei =
 * @Date: 2023-07-20 10:03:20
 * @LastEditors: fanglei =
 * @LastEditTime: 2023-07-28 19:28:36
 * @FilePath: \vsoc-vue3\vsoc-vue3\vsocboot-vue3\src\views\risk\RiskEvent.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <div class="order-log">
    <BasicTable @register="registerTable" :indexColumnProps="indexColumnProps">
      <template #expandedRowRender="{ record }">
        <a-tabs>
          <a-tab-pane key="1" tab="Table">
            <a-row style="border: 1px solid #35373a;">
              <template v-for="(key,index) in record?.keyField" :key="index">
                <template v-if="TabFieldMap[index]?.name">
                  <a-col :span="2" class="borderBottom t_text" v-if="key">
                    <div class="ant-table-cell-ellipsis">{{ TabFieldMap[index]?.name }}:</div>
                  </a-col>
                  <a-col :span="4" class="borderBottom borderRight t_text" v-if="key">
                    <div class="ant-table-cell-ellipsis">
                      <a-tooltip placement="topLeft">
                        <template #title>
                          <div style="max-height: 400px;overflow: auto;">{{ key }}</div>
                        </template>
                        {{ key }}
                      </a-tooltip>
                    </div>
                  </a-col>
                </template>
              </template>
            </a-row>
          </a-tab-pane>
          <a-tab-pane key="2" tab="Json">
            <JsonPreview :data="record?.keyField"/>
          </a-tab-pane>
        </a-tabs>
      </template>
    </BasicTable>
  </div>
</template>

<script lang="ts" setup>
//ts语法
import {watch} from 'vue';
import {BasicColumn, BasicTable} from '/@/components/Table';
import {JsonPreview} from "/@/components/CodeEditor";
import {queryCHEventLogRelationHostPageListRequest,} from "/@/views/risk/RiskEvent.api";
import {getTabField, getTabFieldList, hostLogColumns} from "/@/utils/ckTable";
import {useI18n} from "/@/hooks/web/useI18n";
import {useListPage} from "/@/hooks/system/useListPage";
import {TABLE_CACHE_KEY} from "/@/utils/valueEnum";
import {isAdministrator} from "/@/utils/auth";

const {t} = useI18n();
const props = defineProps({
  id: {
    type: String, default: "",
  },
  param: {
    type: Object, default: () => {
    },
  }
})
const TabFieldMap = getTabField('2')
let columns: any = hostLogColumns(t);

loadColumns()

function loadColumns(): BasicColumn[] {

  let list: any = getTabFieldList(2);
  let have: any = [];
  for (let i in columns) {
    have.push(columns[i].dataIndex)
  }

  for (let i in list) {
    if (have.indexOf(list[i].fieldValue) == -1) {
      columns.push({
        title: list[i].fieldName,
        dataIndex: list[i].fieldValue,
        ellipsis: true,
        defaultHidden: true,
      })
    }
  }

  //租户管理员，显示租户列
  if (isAdministrator()) {
    columns = columns.filter(i => i.dataIndex.indexOf('soc_tenant_name') == -1)
    columns.unshift({
      title: t('common.tenantName'),
      dataIndex: 'soc_tenant_name',
      ellipsis: true,
      width: 150,
    })
  }

  console.log('columns', columns)
  return columns
}

watch(() => props.param.id, () => {
  reload()
})
/**
 * 序号列配置
 */
const indexColumnProps: BasicColumn = {
  title: '#',
  width: '50px',
};
const {tableContext} = useListPage({
  tableProps: {
    api: queryCHEventLogRelationHostPageListRequest,
    title: '',
    titleHelpMessage: [],
    columns: columns,
    rowKey: 'log_id',
    canResize: false,
    pagination: {pageSize: 10},
    searchInfo: props.param,
    tableSetting: {
      cacheKey: TABLE_CACHE_KEY.riskEventLog2,
    },
    beforeFetch: (param) => {
      param.id = props.param.id
      return param
    },
    afterFetch: dataSource
  }
})
const [registerTable, {reload}] = tableContext

query();

function query() {
  console.log("query");
  console.log(props);
}

function dataSource(a) {
  for (let i = 0; i < a.length; i++) {
    if (a[i].keyField) {
      for (let j in a[i].keyField) {
        a[i][j] = a[i].keyField[j];
      }
    }
  }
}


</script>

<style scoped lang="less">
.t_text {
  padding: 10px;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  word-break: keep-all;
}

.borderBottom {
  border-bottom: 1px solid @border-color;
}

.borderRight {
  border-right: 1px solid @border-color;
}

.order-log {
  :deep(.ant-table-thead tr .ant-table-cell-fix-right) {
    background: @dark-bg2 !important;
  }

  :deep(.ant-table-cell-fix-right) {
    background: @dark-bg2 !important;
  }

  :deep(.ant-table-tbody > tr.ant-table-row:hover > td.ant-table-cell-fix-right) {
    background: @dark-bg2 !important;
  }
}

</style>
<style>
.ant-table.ant-table-middle .ant-table-tbody .ant-table-wrapper:only-child .ant-table {
  margin: 0px;
}
</style>
