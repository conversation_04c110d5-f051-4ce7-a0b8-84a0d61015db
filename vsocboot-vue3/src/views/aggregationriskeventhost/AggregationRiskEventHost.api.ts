import {defHttp} from '/@/utils/http/axios';
import {Modal} from 'ant-design-vue';

enum Api {
  list = '/aggregationriskeventhost/aggregationRiskEventHost/list',
  save='/aggregationriskeventhost/aggregationRiskEventHost/add',
  queryById='/aggregationriskeventhost/aggregationRiskEventHost/queryById',
  edit='/aggregationriskeventhost/aggregationRiskEventHost/edit',
  deleteOne = '/aggregationriskeventhost/aggregationRiskEventHost/delete',
  deleteBatch = '/aggregationriskeventhost/aggregationRiskEventHost/deleteBatch',
  importExcel = '/aggregationriskeventhost/aggregationRiskEventHost/importExcel',
  exportXls = '/aggregationriskeventhost/aggregationRiskEventHost/exportXls',
  query30DayChartData = '/aggregationriskeventhost/aggregationRiskEventHost/query30DayChartData',
  editStatus = '/aggregationriskeventhost/aggregationRiskEventHost/editStatus',
  editOwner = '/aggregationriskeventhost/aggregationRiskEventHost/editOwner',
}
/**
 * 导出api
 * @param params
 */
export const getExportUrl = Api.exportXls;
/**
 * 导入api
 */
export const getImportUrl = Api.importExcel;
/**
 * 列表接口
 * @param params
 */
export const listHost = (params) =>
  defHttp.get({url: Api.list, params});

export const queryById = (params) =>
  defHttp.get({url: Api.queryById, params});


/**
 * 删除单个
 * @param params
 * @param handleSuccess
 */
export const deleteOne = (params,handleSuccess) => {
  return defHttp.delete({url: Api.deleteOne, params}, {joinParamsToUrl: true}).then(() => {
    handleSuccess();
  });
}
/**
 * 批量删除
 * @param params
 * @param handleSuccess
 */
export const batchDelete = (params, handleSuccess) => {
  Modal.confirm({
    title: '确认删除',
    content: '是否删除选中数据',
    okText: '确认',
    cancelText: '取消',
    onOk: () => {
      return defHttp.delete({url: Api.deleteBatch, data: params}, {joinParamsToUrl: true}).then(() => {
        handleSuccess();
      });
    }
  });
}
/**
 * 保存或者更新
 * @param params
 * @param isUpdate 是否是更新数据
 */
export const saveOrUpdate = (params, isUpdate) => {
  const url = isUpdate ? Api.edit : Api.save;
  return defHttp.post({url: url, params});
}

export const query30DayChartDataRequest = (params) =>
  defHttp.get({url: Api.query30DayChartData, params});

export const editStatusRequest = (params) =>
  defHttp.post({url: Api.editStatus, params});

export const editOwnerRequest = (params) => {
  return defHttp.post({url: Api.editOwner, params});
}

