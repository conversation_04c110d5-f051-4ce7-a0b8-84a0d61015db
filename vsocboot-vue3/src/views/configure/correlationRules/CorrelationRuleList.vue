<template>
  <div class='pl-16px overflow-y-auto h-[100%]'>
    <!--引用表格-->
    <BasicTable @register="registerTable" :rowSelection="rowSelection as any" :isSearch="isSearch"
                v-model:expandedRowKeys="expandedRowKeys"
                @expand="expandTable">
      <template #form-tags="{model,field}">
        <a-select style="width:100%;" v-model:value="model[field]"
                  mode="multiple"
                  optionFilterProp="label"
                  :options="tagsData" dropdownClassName="autoWidth"
                  :placeholder="t('common.chooseText')"/>
      </template>

      <!--插槽:table标题-->
      <template #form-formFooter>
        <a-dropdown v-if="selectedRowKeys.length > 0 && hasPermission('correlation:edit')">
          <template #overlay>
            <a-menu>
              <a-menu-item key="1" @click="batchHandleDelete">
                <Icon icon="ant-design:delete-outlined"/>
                {{ t('common.delText') }}
              </a-menu-item>
            </a-menu>
          </template>
          <a-button>{{ t('common.batch') }}
            <Icon icon="mdi:chevron-down"/>
          </a-button>
        </a-dropdown>
        <a-button type="primary" @click="handleAdd"   v-if="hasPermission('correlation:edit')">
          <span class="soc ax-com-Add ax-icon"></span>{{ t('common.add') }}
        </a-button>
      </template>
      <!--操作栏-->
      <template #action="{ record }">
        <a-space size="5" class="action-border">
          <template v-if="getIsEdit(record) && hasPermission('correlation:edit')">
            <span @click="handleEdit(record)">{{ t("common.editText") }}</span>
            <a-divider type="vertical"/>
            <Popconfirm :title="t('common.delConfirmText')" @confirm="handleDelete(record)">
              <span>{{ t('common.delText') }}</span>
            </Popconfirm>
          </template>
        </a-space>

      </template>

      <template #switchStatus="{  record }">
        <a-switch :checked="checkStatus(record)"
                  :loading="record?.loading" size="small"
                  :disabled="!getUpdStatus(record) || !hasPermission('correlation:start_stop')"
                  @change="ruleStatusChange(record,$event)"/>
      </template>

      <template #expandedRowRender="{ record }">
        <!--        {{ record }}-->
        <div style="padding: 16px">
          <a-row>
            <a-col :span="2">{{t('routes.CorrelationEvent.ruleName')}}</a-col>
            <a-col :span="4">
              <span>{{ record?.ruleName }}</span>
            </a-col>
            <a-col :span="2">{{t('routes.CorrelationEvent.severity')}}</a-col>
            <a-col :span="4">
              <span v-if="record?.ruleSeverity == 1">{{t('common.Low')}}</span>
              <span v-else-if="record?.ruleSeverity == 2">{{t('common.Middle')}}</span>
              <span v-else-if="record?.ruleSeverity == 3">{{t('common.High')}}</span>
              <span v-else-if="record?.ruleSeverity == 4">{{t('common.Critical')}}</span>
            </a-col>
            <a-col :span="24">{{t('common.description')}}</a-col>
            <a-col :span="24">
              <span>{{ record?.ruleDesc }}</span>
            </a-col>
            <a-col :span="24">
              <a-row v-for="(event,index) in record?.expandData?.events"
                     :key="'event'+index">
                <a-col :span="2" class="event_padding right_text">{{t('routes.CorrelationEvent.event')}} {{ index + 1 }} :</a-col>
                <a-col :span="22">
                  <a-row>
                    <div class="event_padding">
                      <span v-if="event.eventConditionType == 1">{{t('routes.CorrelationEvent.severity')}}</span>
                      <span v-else-if="event.eventConditionType == 2">{{t('routes.CorrelationEvent.host')}}</span>
                    </div>
                    <template v-if="event.eventConditionType == 2">
                      <template v-for="(item,index2) in event.eventConditionData.conditions"
                                :key="'event2'+index2">
                        <div class="event_padding">
                          <span>{{ hostFieldMap[item.fieldId] }}</span>
                        </div>
                      </template>
                    </template>
                    <template v-else>
                      <div v-for="(item,index2) in event.eventConditionData.conditions"
                           :key="'event2'+index2">
                        <a-row>
                          <div class="event_padding">
                            <span>{{ securityFieldMap[item.fieldId] }}</span>
                          </div>
                          <div class="event_padding">
                            <span>{{ relationOptions[item.judgmentCode] }}</span>
                          </div>
                          <div class="event_padding">
                            <span>{{ item.fieldVal }}</span>
                          </div>
                        </a-row>
                      </div>
                    </template>
                    <div class="event_padding">
                      {{t('routes.CorrelationEvent.repeats')}}:
                      <span>{{ event.eventRepeatCount }}</span>
                    </div>
                  </a-row>
                </a-col>
              </a-row>
            </a-col>
            <a-col :span="24">
              <a-row>
                <a-col :span="2" class="event_padding right_text">{{t('routes.CorrelationEvent.joinBy')}}</a-col>
                <a-col :span="22">
                  <div v-for="(item,index) in record?.expandData?.joinBy"
                       :key="'joinBy'+index">
                    <a-row>
                      <div class="event_padding">
                        <span>{{t('routes.ContentMlRule.Event')}} {{ item.eventIndexLeft + 1 }}</span>
                      </div>
                      <div class="event_padding">
                        <span>{{ securityFieldMap[item.riskEventFieldIdLeft] }}</span>
                      </div>
                      <div class="event_padding">==</div>
                      <div class="event_padding">
                        <span>{{t('routes.ContentMlRule.Event')}} {{ item.eventIndexRight + 1 }}</span>
                      </div>
                      <div class="event_padding">
                        <span>{{ securityFieldMap[item.riskEventFieldIdRight] }}</span>
                      </div>
                    </a-row>
                  </div>
                </a-col>
              </a-row>
            </a-col>
            <a-col :span="24">
              <a-row>
                <a-col :span="2" class="event_padding right_text">{{t('routes.CorrelationEvent.timespan')}}</a-col>
                <div class="event_padding">
                  <span>{{ record?.ruleTimeSpan }}</span>
                </div>
                <div class="event_padding">
                  <span v-if="record?.ruleTimeSpanType == 1">{{t('common.second')}}</span>
                  <span v-else-if="record?.ruleTimeSpanType == 2">{{t('common.minute')}}</span>
                  <span v-else-if="record?.ruleTimeSpanType == 3">{{t('common.Hour')}}</span>
                  <span v-else-if="record?.ruleTimeSpanType == 4">{{t('common.Day')}}</span>
                </div>
              </a-row>
            </a-col>
            <a-col :span="24">
              <a-row>
                <a-col :span="2" class="event_padding right_text">{{t('routes.CorrelationEvent.tactic')}}</a-col>
                <div class="event_padding">
                  <span>{{ tacticsMap[record?.ruleTacticId] }}</span>
                </div>
              </a-row>
            </a-col>
            <a-col :span="24">
              <a-row>
                <a-col :span="2" class="event_padding right_text">{{t('routes.CorrelationEvent.techniques')}}</a-col>
                <div class="event_padding">
                  <span>{{ record?.techniqueStr }}</span>
                </div>
              </a-row>
            </a-col>
            <a-col :span="24">
              <a-row>
                <a-col :span="2" class="event_padding right_text">{{t('routes.CorrelationEvent.subTechniques')}}</a-col>
                <div class="event_padding">
                  <span>{{ record?.subTechniqueStr }}</span>
                </div>
              </a-row>
            </a-col>
          </a-row>
        </div>
      </template>
    </BasicTable>

    <!-- 表单区域 -->
    <CorrelationRuleModal ref="ruleModalRef" @register="registerModal" @success="handleSuccess"
                          @callData="callData"/>
  </div>
</template>

<script lang="ts" name="correlationRules-correlationRule" setup>
import {ref} from 'vue';
import {BasicTable} from '/@/components/Table';
import {useModal} from '/@/components/Modal';
import {useListPage} from '/@/hooks/system/useListPage'
import CorrelationRuleModal from './modules/CorrelationRuleModal.vue'
import {getColumns, getIsEdit, getUpdStatus, searchFormSchema} from './CorrelationRule.data';
import {
  batchDelete,
  correlationCallTask,
  deleteOne,
  list,
  loadTagsData,
  queryById,
  updateRuleStatus
} from './CorrelationRule.api';
import {useI18n} from "/@/hooks/web/useI18n";
import {formLayout} from '/@/settings/designSetting';
import Icon from "/@/components/Icon";
import {Modal, Popconfirm} from "ant-design-vue";
import {getTenantMode, isAdministrator} from "/@/utils/auth";
import {usePermission} from "/@/hooks/web/usePermission";
import {TABLE_CACHE_KEY} from "/@/utils/valueEnum";

const {hasPermission} = usePermission();
const {t} = useI18n();
const isSearch = ref<boolean>(true);
//注册model
const [registerModal, {openModal}] = useModal();
loadTagsList()
const tagsData = ref([])
const ruleModalRef = ref()
const expandedRowKeys = ref([])

function loadTagsList() {
  loadTagsData().then((data) => {
    tagsData.value = data
  })
}

const hostFieldMap = ref({})
const securityFieldMap = ref({})
const tacticsMap = ref({})

function callData(data, type) {
  console.log(type, data)
  if (type == 'hostFieldMap') {
    hostFieldMap.value = data
  } else if (type == 'securityFieldMap') {
    securityFieldMap.value = data
  } else if (type == 'tacticsMap') {
    tacticsMap.value = data
  }

}


//注册table数据
const {tableContext} = useListPage({
  tableProps: {
    rowKey: 'ruleId',
    title: 1,
    api: list,
    columns: getColumns(),
    canResize: false,
    formConfig: {
      labelWidth: 120,
      schemas: searchFormSchema,
      autoSubmitOnEnter: true,
      showAdvancedButton: true,
      layout: formLayout,
      baseColProps: {
        lg: 5, // ≥992px
        xl: 4, // ≥1200px
        xxl: 4, // ≥1600px
      },
    },
    handleSearchInfoFn: collapseAll,
    actionColumn: {
      width: 160,
    },
    tableSetting: {
      cacheKey: TABLE_CACHE_KEY.correlationRule
    },
  },
})
const [registerTable, {reload}, {rowSelection, selectedRowKeys}] = tableContext

//1: enabled, 2: disabled
function ruleStatusChange(data, e) {
  console.log(data, e)
  data.loading = true
  let text = t('common.activateRuleTip')
  let ruleStatus = 1;
  if (!e) {
    text = t('common.closeRuleTip')
    ruleStatus = 2;
  }
  Modal.confirm({
    icon: '',
    content: text,
    okText: t('common.okText'),
    cancelText: t('common.cancelText'),
    onOk: () => {
      // updateStatusRequest({id: data.id, status: status}).then(() => {
      //   reload();
      // }).catch(() => {
      //   reload();
      // })
      updateRuleStatus({ruleId: data.ruleId, ruleStatus: ruleStatus}).then(() => {
        let type = "add"
        if (ruleStatus == 2) {
          type = "del"
        }
        callTask(data.ruleId, type);
        refTable()
      })
    },
    onCancel: () => {
      data.loading = false
    }
  });
}

function refTable() {
  reload();
  expandedRowKeys.value = []
}

function collapseAll() {
  console.log("4444444")
  expandedRowKeys.value = []
}


/**
 * 新增事件
 */
function handleAdd() {
  openModal(true, {
    isUpdate: false,
    showFooter: true,
  });
}

/**
 * 编辑事件
 */
function handleEdit(record: Recordable) {
  openModal(true, {
    record,
    isUpdate: true,
    showFooter: true,
  });
}

/**
 * 删除事件
 */
async function handleDelete(record) {
  await deleteOne({id: record.ruleId}, (data) => {
    if (data != "Fail deleted!") {
      callTask(record.ruleId, 'del');
    }
    refTable()
  });

}

/**
 * 批量删除事件
 */
async function batchHandleDelete() {
  batchDelete({ids: selectedRowKeys.value}, (data) => {
    console.log(data)
    if (data && data.length > 0) {
      callTask(data.join(','), 'del');
    }
    refTable()
  })

}

/**
 * 成功回调
 */
function handleSuccess() {
  refTable();
}


function callTask(id, type) {
  correlationCallTask({id: id, type: type}).then(data => {
    console.log(data)
  })
}

function expandTable(expanded, record) {
  console.log(expandedRowKeys)
  console.log(expanded, record)
  if (expanded && !record.expandData) {
    loadInfo(record)
  }
}

function loadInfo(record) {
  queryById({id: record.ruleId}).then((data) => {
    console.log(data)
    let events = data.events
    for (let i in events) {
      events[i].eventConditionData = JSON.parse(events[i].eventConditionJson)
    }
    record.expandData = data
  })
}

const relationOptions = {
  1: '==',
  2: '≠',
  3: '>',
  4: '<',
  5: '>=',
  6: '<=',
  7: 'in',
};


/**
 * 计算规则的状态，
 * @param data
 */
function checkStatus(data) {
  //租户列表
  if (getTenantMode() && !isAdministrator()) {
    //私有的规则显示规则主表的状态
    if (data.ruleScope == 1) {
      return data.ruleStatus == 1
    } else {
      //共享的规则显示分表的状态
      return data.tenantStatus == 1
    }
  } else {
    //管理员显示主表状态
    return data.ruleStatus == 1
  }

}

</script>
<style lang="less" scoped>
.event_padding {
  padding: 5px 5px;
  line-height: 32px;
}

.right_text {
  text-align: right;
}
</style>
