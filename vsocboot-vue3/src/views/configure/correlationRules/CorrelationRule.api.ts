import {defHttp} from '/@/utils/http/axios';
import {Modal} from 'ant-design-vue';
import {useI18n} from "/@/hooks/web/useI18n";

const {t} = useI18n();

enum Api {
  list = '/correlationRules/correlationRule/list',
  save = '/correlationRules/correlationRule/add',
  edit = '/correlationRules/correlationRule/edit',
  queryById = '/correlationRules/correlationRule/queryById',
  deleteOne = '/correlationRules/correlationRule/delete',
  deleteBatch = '/correlationRules/correlationRule/deleteBatch',
  loadEventField = '/correlationRules/riskEventField/queryList',
  loadTactics = '/correlationRules/tactics/queryList',
  loadTechnique = '/correlationRules/technique/queryList',
  updateRuleStatus = '/correlationRules/correlationRule/updateRuleStatus',
  loadDetectionList = '/detection/detectionRule/loadCodeList',
  loadTagsData = '/correlationRules/correlationRule/loadTagsData',
  correlationCallTask = '/correlationRules/correlationRule/correlationCallTask',
}

/**
 * 列表接口
 * @param params
 */
export const list = (params) =>
  defHttp.get({url: Api.list, params});

export const queryById = (params) =>
  defHttp.get({url: Api.queryById, params});

export const loadDetectionList = (params?) =>
  defHttp.get({url: Api.loadDetectionList, params});

export const loadTagsData = (params?) =>
  defHttp.get({url: Api.loadTagsData, params});


/**
 * 删除单个
 * @param params
 * @param handleSuccess
 */
export const deleteOne = (params, handleSuccess) => {
  return defHttp.delete({url: Api.deleteOne, params}, {joinParamsToUrl: true}).then(() => {
    handleSuccess();
  });
}
/**
 * 批量删除
 * @param params
 * @param handleSuccess
 */
export const batchDelete = (params, handleSuccess) => {
  Modal.confirm({
    title: t('common.delConfirmText'),
    content: t('common.delContent'),
    okText: t('common.okText'),
    cancelText: t('common.cancelText'),
    wrapClassName: 'delete_confirm',
    onOk: () => {
      return defHttp.delete({
        url: Api.deleteBatch,
        data: params
      }, {joinParamsToUrl: true}).then((data) => {
        handleSuccess(data);
      });
    }
  });
}
/**
 * 保存或者更新
 * @param params
 * @param isUpdate 是否是更新数据
 */
export const saveOrUpdate = (params, isUpdate) => {
  let url = isUpdate ? Api.edit : Api.save;
  return defHttp.post({url: url, params});
}
export const updateRuleStatus = (params) => {
  return defHttp.post({url: Api.updateRuleStatus, params});
}


export const loadEventField = (params?) =>
  defHttp.get({url: Api.loadEventField, params});

export const loadTactics = (params?) =>
  defHttp.get({url: Api.loadTactics, params});

export const loadTechnique = (params?) =>
  defHttp.get({url: Api.loadTechnique, params});

export const correlationCallTask = (params) =>
  defHttp.get({url: Api.correlationCallTask, params});
