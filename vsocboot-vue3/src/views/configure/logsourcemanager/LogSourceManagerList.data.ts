import { BasicColumn } from '/@/components/Table';
import { FormSchema } from '/@/components/Table';
import { rules } from '/@/utils/helper/validator';
import { useI18n } from '/@/hooks/web/useI18n';
import { h } from 'vue';
import Icon from '/@/components/Icon';
// import {isAdministrator} from "/@/utils/auth";

const { t } = useI18n();

//列表数据
export const getColumns = (): BasicColumn[] => [
  // {
  //   title: t('common.tenantName'),
  //   dataIndex: 'socTenantId_dictText',
  //   align: 'left',
  //   ifShow: isAdministrator(),
  // },
  {
    title: t('routes.sysmenu.configure.log_source_manager.device_name'),
    dataIndex: 'deviceName',
    align: 'left',
  },
  {
    title: t('routes.sysmenu.configure.log_source_manager.log_source_ip'),
    dataIndex: 'logSourceIp',
    align: 'left',
  },
  {
    title: t('routes.sysmenu.configure.log_source_manager.unparsed_logs'),
    align: 'center',
    dataIndex: 'unparsedLogs',
    slots: {customRender: 'unparsedLogs'},
  },
  {
    title: t('routes.sysmenu.configure.log_source_manager.parsing_rules'),
    align: 'center',
    dataIndex: 'parseRule',
  },
  {
    title: t('routes.sysmenu.configure.log_source_manager.parsed_fields'),
    align: 'center',
    dataIndex: 'parseField',
  },
  // {
  //   title: t('routes.sysmenu.configure.log_source_manager.file_for_parsing'),
  //   align: 'center',
  //   dataIndex: 'fileDictId_dictText',
  // },
  // {
  //   title: t('routes.sysmenu.configure.log_source_manager.log_collector'),
  //   align: 'left',
  //   dataIndex: 'proxyServerId_dictText',
  // },
  {
    title: t('routes.sysmenu.configure.log_source_manager.log_charset'),
    align: 'left ',
    dataIndex: 'logCharset_dictText',
  },
];

//查询数据
export const searchFormSchema: FormSchema[] = [
  {
    label: '',
    field: 'deviceName',
    component: 'JInput',
    componentProps: {
      search: true,
      placeholder: t('routes.sysmenu.configure.log_source_manager.device_name_placeholder'),
    },
  },
  {
    label: '',
    field: 'logSourceIp',
    component: 'JInput',
    componentProps: {
      search: true,
      placeholder: t('routes.sysmenu.configure.log_source_manager.log_source_ip_placeholder'),
    },
  },
  // {
  //   label: '',
  //   field: 'proxyServerId',
  //   component: 'JDictSelectTag',
  //   componentProps: {
  //     dictCode: 'proxyDict',
  //     placeholder: t('routes.sysmenu.configure.log_source_manager.log_collector_placeholder'),
  //     showChooseOption: false,
  //   },
  // },
  {
    label: '',
    field: 'logCharset',
    component: 'JDictSelectTag',
    componentProps: {
      dictCode: 'log_charset',
      placeholder: t('routes.sysmenu.configure.log_source_manager.log_charset_placeholder'),
      showChooseOption: false,
    },
  },
];

//表单数据
export const getFormSchema = (): FormSchema[] => [
  // {
  //   label: t('common.tenantName'),
  //   field: 'socTenantId',
  //   required: true,
  //   component: 'JDictSelectTag',
  //   slot: "tenant",
  //   ifShow: isAdministrator()
  // },
  {
    label: t('routes.sysmenu.configure.log_source_manager.device_name'),
    field: 'deviceName',
    component: 'Input',
    dynamicRules: ({}) => {
      return [{ required: true }, { ...rules.limitationCheckRule(1000)[0] }];
    },
  },
  {
    label: t('routes.sysmenu.configure.log_source_manager.log_source_ip'),
    field: 'logSourceIp',
    component: 'Input',
    dynamicRules: ({}) => {
      return [
        { required: true, message: t('routes.sysmenu.configure.log_source_manager.ip_placeholder') },
        {
          pattern: /^((25[0-5]|2[0-4]\d|((1\d{2})|([1-9]?\d)))\.){3}(25[0-5]|2[0-4]\d|((1\d{2})|([1-9]?\d)))$/,
          message: t('routes.sysmenu.common.rule.ip'),
          trigger: 'blur',
        },
      ];
    },
  },
  // {
  //   label: t('routes.sysmenu.configure.log_source_manager.file_for_parsing'),
  //   field: 'fileDictId',
  //   component: 'JDictSelectTag',
  //   required: true,
  //   componentProps: {
  //     dictCode: 'tbl_log_collections_file_dict,item_name,id',
  //     showChooseOption: false,
  //   },
  // },
  {
    label: t('routes.sysmenu.configure.log_source_manager.parsing_rule_manage'),
    field: 'ruleId',
    component: 'JSelectMultiple',
    required: false,
    slot: 'parsingRule',
    // componentProps: {
    //   dictCode: 'tbl_parsing_rule_manage,rule_name,id',
    //   showChooseOption: false,
    // },
  },
  {
    label: t('routes.sysmenu.configure.log_source_manager.log_collector'),
    field: 'proxyServerId',
    required: true,
    component: 'JDictSelectTag',
    componentProps: {
      dictCode: 'proxyDict',
      showChooseOption: false,
    },
  },
  {
    label: t('routes.sysmenu.configure.log_source_manager.log_charset'),
    field: 'logCharset',
    required: true,
    component: 'JDictSelectTag',
    componentProps: {
      dictCode: 'log_charset',
      showChooseOption: false,
    },
  },
  {
    // label: t('routes.sysmenu.configure.log_source_manager.dropPolicy'),
    label: h('div', { class: 'keywords_div' }, [
      h('span', {}, t('routes.sysmenu.configure.log_source_manager.dropPolicy')),
      h(Icon, {
        icon: 'ant-design:plus-outlined',
        class: 'keywords_icon',
        id: 'keywords_add',
      }),
    ]),
    field: 'keywords',
    component: 'Input',
    required: false,
    slot: 'keywords',
  },
  // TODO 主键隐藏字段，目前写死为ID
  {
    label: '',
    field: 'id',
    component: 'Input',
    show: false,
  },
];
