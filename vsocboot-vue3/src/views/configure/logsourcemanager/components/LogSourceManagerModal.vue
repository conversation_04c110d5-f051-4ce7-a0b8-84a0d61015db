<template>
  <BasicModal v-bind="$attrs" @register="registerModal" :title="title" @ok="handleSubmit" width="400px" :destroyOnClose="true">
    <BasicForm @register="registerForm">
      <!--      <template #tenant="{ model, field}">-->
      <!--        <JDictSelectTag v-model:value="model[field]" dict-code="tenantActiveDict"-->
      <!--                        :disabled="isUpdate"-->
      <!--                        @change="tenantChange"/>-->
      <!--      </template>-->
      <template #parsingRule="{ model, field }">
        <JSelectMultiple v-model:value="model[field]" :options="parsingRuleOptions" />
      </template>
      <template #keywords="{ model, field }">
        <a-input type="hidden" v-model:value="model[field]" />
        <a-form-item>
          <div v-for="(item, index) in keywordsList" :key="index" style="display: flex; margin-bottom: 6px">
            <a-input v-model:value="item.name" />
            <Icon
              icon="ant-design:delete-outlined"
              style="cursor: pointer; flex: 0 0 25px; display: flex; align-items: center; justify-content: center"
              @click="delKeywords(index)"
            />
          </div>
        </a-form-item>
      </template>
    </BasicForm>
  </BasicModal>
</template>

<script lang="ts" setup>
  import { computed, nextTick, ref, unref } from 'vue';
  import { BasicModal, useModalInner } from '/@/components/Modal';
  import { BasicForm, useForm } from '/@/components/Form/index';
  import { getFormSchema } from '../LogSourceManagerList.data';
  import { logSourceNotice, saveOrUpdate } from '../LogSourceManagerList.api';
  import { useI18n } from '/@/hooks/web/useI18n';
  import { loadParseRuleByTenant } from '/@/views/parseRule/ParseRuleManage.api';
  import JSelectMultiple from '/@/components/Form/src/jeecg/components/JSelectMultiple.vue';
  import { isAdministrator } from '/@/utils/auth';
  import Icon from '/@/components/Icon';

  const props = defineProps({
    socTenantId: String,
  });
  const parsingRuleOptions = ref<any[]>([]);
  const { t } = useI18n();
  // Emits声明
  const emit = defineEmits(['register', 'success']);
  const isUpdate = ref(true);
  const keywordsList = ref<any[]>([]);
  //表单配置
  const [registerForm, { resetFields, setFieldsValue, validate }] = useForm({
    // labelWidth: 150,
    schemas: getFormSchema(),
    showActionButtonGroup: false,
    rowProps: { gutter: 8 },
  });
  //表单赋值
  const [registerModal, { setModalProps, closeModal }] = useModalInner(async (data) => {
    //重置表单
    await resetFields();
    keywordsList.value = [];
    setModalProps({
      confirmLoading: false,
      showCancelBtn: data?.showFooter,
      showOkBtn: data?.showFooter,
    });
    await loadParsingRuleOptions(props.socTenantId);
    isUpdate.value = !!data?.isUpdate;
    console.log('data.record', data.record);

    nextTick(() => {
      console.log(document.getElementById('keywords_add'));
      document.getElementById('keywords_add')?.addEventListener('click', (event) => {
        keywordsList.value.push({
          name: '',
        });
      });
    });

    if (unref(isUpdate)) {
      //表单赋值
      await setFieldsValue({
        ...data.record,
      });
      if (data.record.keywords) {
        const array = data.record.keywords.split(',');
        for (const i in array) {
          keywordsList.value.push({
            name: array[i],
          });
        }
      }
    }
  });
  //设置标题
  const title = computed(() => (!unref(isUpdate) ? t('common.add') : t('common.editText')));

  //表单提交事件
  async function handleSubmit() {
    let values = await validate();
    values.keywords = '';
    if (keywordsList.value.length > 0) {
      let filter: any = [];
      for (let i in keywordsList.value) {
        if (keywordsList.value[i].name && keywordsList.value[i].name.trim()) {
          filter.push(keywordsList.value[i].name);
        }
      }
      values.keywords = filter.join(',');
    }
    console.log(values);
    setModalProps({ confirmLoading: true });
    if (isAdministrator()) {
      values.socTenantId = props.socTenantId;
    }
    //提交表单
    saveOrUpdate(values, isUpdate.value)
      .then((res) => {
        console.log('res', res);

        const paramData: any = {};
        if (isUpdate.value) {
          paramData.logSourceIds = res.id;
          paramData.removeRuleId = res.removeRuleId;
          paramData.removeProxyId = res.removeProxyServerId;
          paramData.proxyId = res.proxyServerId;
          paramData.ruleIds = res.ruleId;
          // createMessage.success(res.message);
        } else {
          paramData.logSourceIds = res.id;
          paramData.proxyId = res.proxyServerId;
          paramData.ruleIds = res.ruleId;
        }
        logSourceNotice(paramData).then((result) => {
          console.log('modifyApp result:', result);
        });
        //刷新列表
        emit('success', { isUpdate: isUpdate.value, values });
        closeModal();
      })
      .catch(() => {
        setModalProps({ confirmLoading: false });
        // createMessage.warning(res.message);
      });
    // .finally(() => {
    //   setModalProps({confirmLoading: false});
    //   //关闭弹窗
    //   closeModal();
    // });
  }

  async function loadParsingRuleOptions(id) {
    await loadParseRuleByTenant({ id: id }).then((data) => {
      console.log(data);
      parsingRuleOptions.value = [];
      let list: any = [];
      for (let i in data) {
        list.push({
          label: data[i].ruleName,
          value: data[i].id,
        });
      }
      parsingRuleOptions.value = list;
    });
  }

  function delKeywords(index) {
    keywordsList.value.splice(index, 1);
  }
</script>

<style lang="less" scoped>
  :deep(.keywords_icon) {
    margin-left: 4px;
    cursor: pointer;
  }
  :deep(.keywords_div) {
    display: flex;
  }
</style>
