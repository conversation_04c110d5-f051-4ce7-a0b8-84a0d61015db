<template>
  <div class="overflow-y-auto h-[100%] page_div">
    <a-tabs v-if="isAdministrator()" v-model:activeKey="activeKey">
      <a-tab-pane v-for="(tenant, tenantIndex) in tenantList" :key="tenantIndex" :tab="tenant.name">
        <LogSourceManagerTable :soc-tenant-id="tenant.id" :activeKey="activeKey" />
      </a-tab-pane>
    </a-tabs>
    <div v-else-if="isTenant()">
      <LogSourceManagerTable :soc-tenant-id="getTenantId()" />
    </div>
    <div v-else>
      <LogSourceManagerTable />
    </div>
  </div>
</template>

<script lang="ts" setup>
  import { queryList } from '/@/views/system/tenant/tenant.api';
  import { ref } from 'vue';
  import { getTenantId, isAdministrator, isTenant } from '/@/utils/auth';
  import LogSourceManagerTable from '/@/views/configure/logsourcemanager/LogSourceManagerTable.vue';
  import { getHistoryParamByRoute } from '/@/utils/ckTable';

  const tenantList = ref<any[]>([]);
  const activeKey = ref(0);
  const historyRoutePath = '/configure/unparsedLog/UnparsedLogList';
  const historyParam = getHistoryParamByRoute(historyRoutePath, false);
  if (historyParam) {
    activeKey.value = historyParam.activeKey;
  }
  /**
   * 查询租户数据
   */
  queryList({ status: 1 }).then((data) => {
    console.log(data);
    tenantList.value.push(...data);
  });
</script>

<style lang="less" scoped>
</style>
