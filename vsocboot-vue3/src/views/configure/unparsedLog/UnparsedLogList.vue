<template>
  <div class="page_title flex flex-row items-center gap-8px">
    <div @click="router.back()" class="ax-icon-button ">
      <span class="soc ax-com-Arrow-left ax-icon"></span>
    </div>
    <span> {{ fromIp }}</span>
  </div>

  <div class="search_transparent pt-12px">
    <BasicSearch @register="registerForm" @submit="handleSubmit" v-show="isSearch">
      <!--插槽:table标题-->
      <template #form-formFooter>
        <!--      <a-button :class="{'btn-checked': isSearch == true}" type="text" @click="isSearch=!isSearch"-->
        <!--                preIcon="ant-design:filter-outlined"> {{ t('common.filter') }}-->
        <!--      </a-button>-->
        <a-button type="primary" @click="createRule" v-if="hasPermission('parsingRule:edit')">
          {{ t('routes.unknownLog.creat') }}
        </a-button>
        <a-button type="primary" @click="useRule" v-if="hasPermission('parsingRule:edit')">
          {{ t('routes.unknownLog.using') }}
        </a-button>
      </template>
    </BasicSearch>
  </div>

  <!--引用表格-->
  <BasicTable @register="registerTable" :rowSelection="rowSelection as any">

  </BasicTable>

</template>

<script lang="ts" name="unparsedLog-unparsedLog" setup>
import {ref,} from 'vue';
import {BasicTable} from '/@/components/Table';
import {useListPage} from '/@/hooks/system/useListPage'
import {columns, searchFormSchema,} from './UnparsedLog.data';
import {queryLogList} from './UnparsedLog.api';
import {useI18n} from "/@/hooks/web/useI18n";
import {useRouter} from "vue-router";
import {useMessage} from "/@/hooks/web/useMessage";
import {usePermission} from "/@/hooks/web/usePermission";
import {formLayout} from "/@/settings/designSetting";
import {useForm} from "/@/components/Form";
import BasicSearch from "/@/components/Form/src/BasicSearch.vue";
import dayjs from "dayjs";
import {TABLE_CACHE_KEY} from "/@/utils/valueEnum";

const {hasPermission} = usePermission();
const router = useRouter();
const {createMessage} = useMessage();
const {t} = useI18n();
const isSearch = ref(true)

let routeParam: any = {}


let jsonStr = sessionStorage.getItem("logSourceToUnlog")
if (jsonStr) {
  routeParam = JSON.parse(jsonStr)
}
const fromIp = routeParam.ip
const queryParam = ref({
  logSourceIp: fromIp,
  rawLog: '',
  createTime: '',
  socTenantId: routeParam.socTenantId
});

const dateTime = dayjs().subtract(12, 'hour').format("YYYY-MM-DD HH:mm:ss") + "," +
  dayjs().format("YYYY-MM-DD HH:mm:ss")
console.log(dateTime)
/**
 * BasicForm绑定注册;
 * useForm 是整个框架的核心用于表单渲染，里边封装了很多公共方法;
 * 支持（schemas: 渲染表单列，autoSubmitOnEnter：回车提交,submitButtonOptions：自定义按钮文本和图标等方法）；
 * 平台通过此封装，简化了代码，支持自定义扩展;
 */
const [registerForm, {getFieldsValue, setFieldsValue}] = useForm({
  baseColProps: {
    lg: 6, // ≥992px
    xl: 4, // ≥1200px
    xxl: 3, // ≥1600px
  },
  schemas: searchFormSchema,
  rowProps: {gutter: 8},
  autoSubmitOnEnter: true,
  showAdvancedButton: true,

  layout: formLayout,
  model: {
    queryTime: dateTime
  }
});

let queryParams: any = {};

/**
 * 点击提交按钮的value值
 */
function handleSubmit() {
  const values = getFieldsValue();
  queryParams = values;
  console.log('提交按钮数据::::', values);
  reload();
}

//注册table数据
const {tableContext} = useListPage({
  tableProps: {
    title: '',
    api: queryLogList,
    rowKey: "logId",
    columns,
    canResize: false,
    useSearchForm: false,
    tableSetting:{
      cacheKey:TABLE_CACHE_KEY.unparselog
    },
    defSort: {
      column: 'ckEnterDate',
      order: 'desc'
    },
    beforeFetch: (params) => {
      let queryTime = queryParams.queryTime;
      if (queryTime) {
        let arr = queryTime.split(",");
        params.startTime = arr[0];
        params.endTime = arr[1];
      } else {
        params.startTime = dayjs().subtract(12, 'hour').format("YYYY-MM-DD HH:mm:ss");
        params.endTime = dayjs().format("YYYY-MM-DD HH:mm:ss");
        setFieldsValue({queryTime: params.startTime + ',' + params.endTime})
      }
      if (queryParams.rawLog != "**") {
        params.rawLog = queryParams.rawLog
      }
    },
    searchInfo: {
      socTenantId: queryParam.value.socTenantId,
      logSourceIp: queryParam.value.logSourceIp
    },
    showActionColumn: true,
  },
})

const [registerTable, {reload}, {rowSelection}] = tableContext

function createRule() {
  if (rowSelection.selectedRows.length == 0) {
    createMessage.warning('Please select unparsed log!');
    return;
  }
  const logId = rowSelection.selectedRows.map(item => {
    return item.logId;
  })
  console.log('logId:', logId)
  console.log('rowSelection:', rowSelection)
  router.push({
    path: "/parseRule/ParseRuleEdit",
    query: {from: fromIp ? 'logManager' : 'unparseLog', logIds: logId.toString()}
  });
}

function useRule() {
  if (rowSelection.selectedRows.length == 0) {
    createMessage.warning('Please select unparsed log!');
    return;
  }
}
</script>
<style lang="less" scoped>
.searchDivBg {
  margin: 16px;
  background: @dark-bg2;
  padding: 12px 10px 6px;
  border-radius: 2px;
}

.margin-true {
  margin-top: -32px;
}

:deep(.soc-basic-form) {
  padding-bottom: 0 !important;
}

//table表格中有多个空格全部显示
:deep(.ant-table-cell) {
  white-space: pre !important;
}
</style>
