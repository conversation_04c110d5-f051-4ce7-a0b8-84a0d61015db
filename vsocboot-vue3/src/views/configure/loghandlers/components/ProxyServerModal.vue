<template>
  <a-modal
    :title="title"
    :width="width"
    :visible="visible"
    @ok="handleOk"
    :okButtonProps="{ class: { 'jee-hidden': disableSubmit } }"
    @cancel="handleCancel"
  >
    <template #closeIcon>
      <div class="ax-icon-button ax-icon-large">
        <span class="soc ax-com-Close ax-icon"></span>
      </div>
    </template>
    <a-spin :spinning="confirmLoading">
      <div class="p-16px form-last-item-single-mb-0">
        <a-form ref="formRef" class="antd-modal-form" :labelCol="labelCol" :wrapperCol="wrapperCol"
                :layout="formLayout">
          <a-row>
            <a-col>
              <a-form-item
                :label="t('routes.sysmenu.configure.log_handlers.proxy_server.proxy_name')"
                v-bind="validateInfos.proxyName">
                <a-input
                  v-model:value="formData.proxyName"
                  :placeholder="t('routes.sysmenu.configure.log_handlers.proxy_server.proxy_name_placeholder')"
                />
              </a-form-item>
            </a-col>
            <a-col>
              <a-form-item :label="t('routes.sysmenu.configure.log_handlers.proxy_server.proxy_ip')"
                           v-bind="validateInfos.proxyIp">
                <a-input v-model:value="formData.proxyIp"
                         :placeholder="t('routes.sysmenu.configure.log_handlers.proxy_server.proxy_ip_placeholder')"/>
              </a-form-item>
            </a-col>
            <a-col>
              <a-form-item
                :label="t('routes.sysmenu.configure.log_handlers.proxy_server.proxy_port')"
                v-bind="validateInfos.proxyPort">
                <a-input-number
                  v-model:value="formData.proxyPort"
                  :placeholder="t('routes.sysmenu.configure.log_handlers.proxy_server.proxy_port_placeholder')"
                  :min="1"
                  :max="65535"
                  style="width: 100%"
                />
              </a-form-item>
            </a-col>

            <template v-if="getTenantMode()">
              <a-col>
                <a-form-item
                  :label="t('routes.sysmenu.configure.log_handlers.proxy_server.internet_proxy_ip')"
                  v-bind="validateInfos.internetIp">
                  <a-input v-model:value="formData.internetIp" v-bind="validateInfos.internetIp"
                           :placeholder="t('routes.sysmenu.configure.log_handlers.proxy_server.internet_proxy_ip_placeholder')">
                    <template #addonBefore>
                      <a-select v-model:value="formData.httpType" style="width: 90px">
                        <a-select-option value="http://">Http://</a-select-option>
                        <a-select-option value="https://">Https://</a-select-option>
                      </a-select>
                    </template>
                  </a-input>
                </a-form-item>
              </a-col>


              <a-col>
                <a-form-item
                  :label="t('routes.sysmenu.configure.log_handlers.proxy_server.internet_proxy_syslog_port')"
                  v-bind="validateInfos.internetPort">
                  <a-input-number
                    v-model:value="formData.internetSyslogPort"
                    :placeholder="t('routes.sysmenu.configure.log_handlers.proxy_server.internet_proxy_port_placeholder')"
                    :min="1"
                    :max="65535"
                    style="width: 100%"
                  />
                </a-form-item>
              </a-col>

              <a-col>
                <a-form-item
                  :label="t('routes.sysmenu.configure.log_handlers.proxy_server.internet_proxy_server_port')"
                  v-bind="validateInfos.internetPort">
                  <a-input-number
                    v-model:value="formData.internetServerPort"
                    :placeholder="t('routes.sysmenu.configure.log_handlers.proxy_server.internet_proxy_port_placeholder')"
                    :min="1"
                    :max="65535"
                    style="width: 100%"
                  />
                </a-form-item>
              </a-col>
            </template>

            <Col :span="24">
              <a-form-item
                :label="t('routes.sysmenu.configure.log_handlers.proxy_server.proxy_server_authorization_code')"
                v-bind="validateInfos.authorizationCode">
                <a-textarea
                  :placeholder="t('routes.sysmenu.configure.log_handlers.proxy_server.proxy_server_authorization_code_placeholder')"
                  v-model:value="formData.authorizationCode"
                  :rows="2"
                  :maxlength="128"
                  style="height:88px"
                />
              </a-form-item>
            </Col>
          </a-row>

        </a-form>
      </div>
    </a-spin>
  </a-modal>
</template>

<script lang="ts" setup>
import {defineExpose, nextTick, reactive, ref} from 'vue';
import {Col, Form} from 'ant-design-vue';
import {useMessage} from '/@/hooks/web/useMessage';
import {formLayout} from '/@/settings/designSetting';
import {useI18n} from '/@/hooks/web/useI18n';
import {saveOrUpdateProxyServer} from '../LogHandler.api';
import {getTenantMode} from "/@/utils/auth";

const {t} = useI18n();
const {createMessage} = useMessage();

const title = ref<string>('');
const width = ref<number>(400);
const visible = ref<boolean>(false);
const disableSubmit = ref<boolean>(false);
const confirmLoading = ref<boolean>(false);
const labelCol = ref<any>({xs: {span: 24}, sm: {span: 24}});
const wrapperCol = ref<any>({xs: {span: 24}, sm: {span: 24}});
const emit = defineEmits(['success']);
const formRef = ref();
const disabled = ref(false);
const useForm = Form.useForm;
const formData = reactive<Record<string, any>>({
  id: '',
  appServerId: '',
  proxyName: '',
  proxyIp: '',
  proxyPort: '',
  internetIp: '',
  internetSyslogPort: '',
  internetServerPort: '',
  httpType: 'https://',
  authorizationCode: ''
});
const validatorRules = {
  proxyName: [{
    required: true,
    message: t('routes.sysmenu.configure.log_handlers.proxy_server.proxy_name_placeholder')
  }],
  proxyIp: [
    {
      required: true,
      message: t('routes.sysmenu.configure.log_handlers.proxy_server.proxy_ip_placeholder')
    },
    {
      pattern: /^((25[0-5]|2[0-4]\d|((1\d{2})|([1-9]?\d)))\.){3}(25[0-5]|2[0-4]\d|((1\d{2})|([1-9]?\d)))$/,
      message: t('routes.sysmenu.common.rule.ip')
    },
  ],
  proxyPort: [{
    required: true,
    message: t('routes.sysmenu.configure.log_handlers.proxy_server.proxy_port_placeholder')
  }],
  authorizationCode: [{
    required: true,
    message: t('routes.sysmenu.configure.log_handlers.app_server.app_server_authorization_code_placeholder')
  }],
};
if (getTenantMode()) {
  validatorRules['internetIp'] = [
    {
      required: true,
      message: t('routes.sysmenu.configure.log_handlers.proxy_server.internet_proxy_ip_placeholder')
    }
  ];
  validatorRules['internetPort'] = [{
    required: true, message:
      t('routes.sysmenu.configure.log_handlers.proxy_server.internet_proxy_port_placeholder')
  }]
}
const {
  resetFields,
  validate,
  validateInfos
} = useForm(formData, validatorRules, {immediate: false});

/**
 * add proxy server
 */
function add(appServerId: string) {
  resetFields();
  formData.appServerId = appServerId;
  title.value = t('routes.global.model.title.add');
  visible.value = true;
}

/**
 * edit proxy server
 * @param record
 */
function edit(record) {
  title.value = disableSubmit.value ? t('routes.global.model.title.detail') : t('routes.global.model.title.edit');
  visible.value = true;
  nextTick(() => {
    resetFields();
    Object.assign(formData, record);
    console.log('record', record, 'formData', formData);
  });
}

/**
 * 确定按钮点击事件
 */
function handleOk() {
  submitForm();
}

/**
 * 取消按钮回调事件
 */
function handleCancel() {
  visible.value = false;
}

async function submitForm() {
  // 触发表单验证
  await validate();
  confirmLoading.value = true;
  const isUpdate = ref<boolean>(false);
  //时间格式化
  let model = formData;
  if (model.id) {
    isUpdate.value = true;
  }
  console.log(model);
  await saveOrUpdateProxyServer(model, isUpdate.value)
    .then((res) => {
      if (res.success) {
        createMessage.success(res.message);
        emit('success');
      } else {
        createMessage.warning(res.message);
        emit('success');
      }
    })
    .finally(() => {
      confirmLoading.value = false;
      visible.value = false;
    });
}

defineExpose({
  add,
  edit,
  disableSubmit,
});
</script>

<style lang="less" scoped>
/**隐藏样式-modal确定按钮 */
.jee-hidden {
  display: none !important;
}

</style>
