import {FormSchema} from '/@/components/Table';
import {useI18n} from "/@/hooks/web/useI18n";

const {t} = useI18n();

export const formSchema: FormSchema[] = [
  {label: '', field: 'appId', component: 'Input', show: false},
  {
    label: t('routes.AppRunningConfig.enrichedSecurity'),
    field: 'enrichedSecurity',
    component: 'InputNumber',
    required: true,
  },
  {
    label: t('routes.AppRunningConfig.enrichedHost'),
    field: 'enrichedHost',
    component: 'InputNumber',
    required: true,
  },
  {
    label: t('routes.AppRunningConfig.enrichedNetwork'),
    field: 'enrichedNetwork',
    component: 'InputNumber',
    required: true,
  },
  {
    label: t('routes.AppRunningConfig.enrichedOperation'),
    field: 'enrichedOperation',
    component: 'InputNumber',
    required: true,
  },
  {
    label: t('routes.AppRunningConfig.unparsed'),
    field: 'unparsed',
    component: 'InputNumber',
    required: true,
  },
  {
    label: t('routes.AppRunningConfig.aggregationSecurity'),
    field: 'aggregationSecurity',
    component: 'InputNumber',
    required: true,
  },
  {
    label: t('routes.AppRunningConfig.aggregationHost'),
    field: 'aggregationHost',
    component: 'InputNumber',
    required: true,
    colProps: {
      class: 'last-col-margin-0',
    }
  },
];
