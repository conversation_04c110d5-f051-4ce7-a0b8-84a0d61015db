import {defHttp} from '/@/utils/http/axios';
import {Modal} from 'ant-design-vue';
import {useI18n} from "/@/hooks/web/useI18n";
const { t } = useI18n();
enum Api {
  list = '/rule/aggregationRule/list',
  save='/rule/aggregationRule/add',
  edit='/rule/aggregationRule/edit',
  deleteOne = '/rule/aggregationRule/delete',
  deleteBatch = '/rule/aggregationRule/deleteBatch',
  batchStatus = '/rule/aggregationRule/batchStatus',
  importExcel = '/rule/aggregationRule/importExcel',
  exportXls = '/rule/aggregationRule/exportXls',
}
/**
 * 导出api
 * @param params
 */
export const getExportUrl = Api.exportXls;
/**
 * 导入api
 */
export const getImportUrl = Api.importExcel;
/**
 * 列表接口
 * @param params
 */
export const list = (params) =>
  defHttp.get({url: Api.list, params});

/**
 * 删除单个
 * @param params
 * @param handleSuccess
 */
export const deleteOne = (params,handleSuccess) => {
  return defHttp.delete({url: Api.deleteOne, params}, {joinParamsToUrl: true}).then(() => {
    handleSuccess();
  });
}
/**
 * 批量删除
 * @param params
 * @param handleSuccess
 */
export const batchDelete = (params, handleSuccess) => {
  Modal.confirm({
    title: t('common.delText'),
    content: t('common.delConfirmText'),
    okText: t('common.okText'),
    cancelText: t('common.cancelText'),
    wrapClassName: 'delete_confirm',
    onOk: () => {
      return defHttp.delete({url: Api.deleteBatch, data: params}, {joinParamsToUrl: true}).then(() => {
        handleSuccess();
      });
    }
  });
}
/**
 * 保存或者更新
 * @param params
 * @param isUpdate 是否是更新数据
 */
export const saveOrUpdate = (params, isUpdate) => {
  let url = isUpdate ? Api.edit : Api.save;
  return defHttp.post({url: url, params});
}

/**
 * 批量OPEN/CLOSE
 * @param params
 * @param handleSuccess
 */
export const batchStatus = (params, handleSuccess) => {
  let text = params.ruleStatus == 1 ? t('common.openText') : t('common.closeText');
  console.log('==params==',params)
  Modal.confirm({
    title: text,
    content: t('common.confirmText') + text,
    okText: t('common.okText'),
    cancelText: t('common.cancelText'),
    wrapClassName: 'delete_confirm',
    onOk: () => {
      return defHttp.post({url: Api.batchStatus, data: params}, {joinParamsToUrl: true}).then(() => {
        handleSuccess();
      });
    }
  });
}
