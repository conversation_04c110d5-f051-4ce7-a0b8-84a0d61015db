<template>
  <BasicModal v-bind="$attrs" @register="registerModal" :title="title" @ok="handleSubmit" width="80%" :destroyOnClose="true">
         <BasicForm @register="registerForm" style="overflow-x: hidden;min-height:580px">


         </BasicForm>
     </BasicModal>
</template>

<script lang="ts" setup>
    import {ref, computed, unref} from 'vue';
    import {BasicModal, useModalInner} from '/@/components/Modal';
    import {ApiSelect, BasicForm, useForm} from '/@/components/Form/index';
    import {formSchema} from '../AggregationRule.data';
    import {saveOrUpdate} from '../AggregationRule.api';
    import {RowProps} from "ant-design-vue/lib/grid/Row";
    import {useI18n} from "/@/hooks/web/useI18n";
    const { t } = useI18n();
    // Emits声明
    const emit = defineEmits(['register','success']);
    const isUpdate = ref(true);



    //表单配置
    const [registerForm, {resetFields, setFieldsValue, validate}] = useForm({
        // labelWidth: 150,
        schemas: formSchema,
        showActionButtonGroup: false,
        rowProps: { gutter: 8 },
    });
    //表单赋值
    const [registerModal, {setModalProps, closeModal}] = useModalInner(async (data) => {
        //重置表单
        await resetFields();
        setModalProps({confirmLoading: false,showCancelBtn:data?.showFooter,showOkBtn:data?.showFooter});
        isUpdate.value = !!data?.isUpdate;
        if (unref(isUpdate)) {
            //表单赋值
            await setFieldsValue({
                ...data.record,
            });
            if(data.record.riskGroupby){
              setFieldsValue({ riskGroupby: data.record.riskGroupby.split(",") });
            }

        }
    });
    //设置标题
    const title = computed(() => (!unref(isUpdate) ? t('common.add') : t('common.editText')));
    //表单提交事件
    async function handleSubmit(v) {
        try {
            let values = await validate();
            setModalProps({confirmLoading: true});
            //提交表单
            await saveOrUpdate(values, isUpdate.value);
            //关闭弹窗
            closeModal();
            //刷新列表
            emit('success', {isUpdate: isUpdate.value, values});
        } finally {
            setModalProps({confirmLoading: false});
        }
    }
</script>

<style lang="less" scoped>

</style>
