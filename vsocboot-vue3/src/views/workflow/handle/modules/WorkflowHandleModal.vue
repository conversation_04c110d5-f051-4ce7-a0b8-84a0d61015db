<template>
  <a-modal v-model:visible="visible"
           width="100%"
           :destroyOnClose="true"
           @ok="handleOk"
           :maskClosable="false"
           :title=" title"
            :wrapClassName="['full-modal', 'view-template']" >
    <template #footer  >
      <a-button key="back" @click="handleCancel">Cancel</a-button>
      <a-button key="submit" type="primary" :disabled="taskData.className=='no-foot'"  @click="handleOk">Submit</a-button>
    </template>

    <div class="padding16  flex flex-h"  style="height:100%">
      <div class="flex-1 flow_handle_wrapper">
        <a-tabs v-model:activeKey="activeKey" style="height: 100%" @change="changeTab">
          <a-tab-pane key="1" tab="Form Content"  force-render  >
            <HandleForm ref="handleRef"   v-model:value="templateData"
                        v-model:variables="variables"
                        v-model:rules="rules"
            @ok="showOption"></HandleForm>
          </a-tab-pane>
          <a-tab-pane key="2" tab="Disposal tracking" force-render >
            <FlowView ref="designRef" v-model:value="instData" @ok="drawHighLight"></FlowView>
          </a-tab-pane>
        </a-tabs>

        <OptionModal ref="optionRef" @ok="submitTask" ></OptionModal>
      </div>
      <div class="taskList fcolor">
        <div class="taskTitle font14">Process History Tracking</div>
        <Timeline>
          <template v-for="(item,index) in lineData">

            <TimelineItem   :color="item.endTime ? '#308cff' : '#ccc'"
                            v-if="item.activityType == 'startEvent' || item.activityType == 'nodeEndEvent' ||
                            (item.endTime && item.completeScope == true) || (!item.endTime && item.taskAssignee == username)">
              <div  style="color: rgb(249 249 249 / 60%);">
                {{ item.startTime }}</div>
              <template v-if="item.activityType == 'startEvent'">
                <div class="taskInfo" >Disposal process ：{{ item.activityName ?? 'start'}}</div>
                <div class="taskInfo">proposer：{{item.taskAssignee ?? variables.assignee}}</div>
              </template>
              <template v-else-if="item.activityType == 'nodeEndEvent'">
                <div class="taskInfo" >Disposal process ：{{ item.activityName ?? 'end'}}</div>
              </template>
              <template v-else>
                <div class="taskInfo">{{item.endTime ? 'Disposal process' : 'Current process'}}：{{item.activityName ||'Task'}}</div>
                <div class="taskInfo">Disposer：{{item.taskAssignee }}</div>
              </template>
              <div class="taskInfo" v-if="item.taskId && comments[item.taskId]">Disposal opinions：{{comments[item.taskId]}}</div>
            </TimelineItem>
          </template>

        </Timeline>
      </div>

    </div>
    <div>

    </div>

  </a-modal>
</template>

<script lang="ts" setup>
import HandleForm from "/@/views/workflow/handle/component/HandleForm.vue";
import FlowView from "/@/views/workflow/handle/component/FlowView.vue";
import OptionModal from "/@/views/workflow/handle/modules/OptionModal.vue";

import {defineEmits, defineExpose, nextTick, ref, toRaw, unref, watch} from "vue";
import {queryTemplateDesignInfo} from "/@/views/workflow/apply/WorkflowApply.api";
import {
  executionTask, getAllAcitivity,
  getTaskComments, queryProcessInfo,
  queryInst
} from "/@/views/workflow/handle/WorkflowOrder.api";
import {Timeline, TimelineItem} from "ant-design-vue";
import {store} from "/@/store";
const emit = defineEmits(['ok']);

const username = ref('');
const activeKey = ref('1');
const visible = ref(false);
const instData = ref({});
const taskData = ref({});
const templateData = ref({});
const title = ref('');
const variables = ref({});
const optionRef = ref();
const designRef = ref();
const rules = ref({});
const lineData = ref([]);
const comments = ref({});
const handleRef = ref();
let isInit = false;
function handleOk(){
 handleRef.value.validate();
}
function showOption(){
  optionRef.value.show();
}
function handleCancel() {
  visible.value = false;

}
function changeTab(val){
  if(val == '2'){
    nextTick(()=>{
      console.log(1111111)
      designRef.value.showBpmn(instData.value);
    })

  }
}
function submitTask(option){
  var nextConfig =  getNextConfig();
  console.log('nextConfig',nextConfig)
  let queryParam = {vals:JSON.stringify(variables.value),
    nextConfig : JSON.stringify(nextConfig),
    processInstanceId:taskData.value.processInstanceId,
    options:option};
  executionTask(queryParam,(result)=>{
    handleCancel();
    emit('ok');
  })
}
function show(data){
  console.log('datavariables:',data)
  username.value = data.assignee;
  visible.value = true;
  taskData.value = data;
  title.value = data.flowName + " Disposal";
  variables.value = JSON.parse(data.variables);
  activeKey.value = "2";
  isInit = false;
  console.log('variables:',variables)
  queryProcessInfo({processInstanceId:data.processInstanceId}).then((result)=>{
    console.log('querProcessInfo result:',result)
    instData.value = result;
    instData.value.activeId = taskData.value.taskDefinitionKey;

    let config = JSON.parse(instData.value.ruleConfig) ?? {};
    rules.value = config[instData.value.activeId] ?? {};
    console.log('rules.value',rules.value)

    // getTemplateData(result.templateId);
    nextTick(()=>{
      if(result.designContent){
        templateData.value.list = JSON.parse(result.designContent);
      }
      if(result.designConfig){
        templateData.value.formConfig = JSON.parse(result.designConfig);
      }
      console.log('templateData.value',templateData.value)
      designRef.value.showBpmn(instData.value);
    })

  });
  loadTimeLine(data);

}
async function loadTimeLine(data){
    // await getHistoryTaskInfo({processInstanceId : data.processInstanceId},(result)=>{
    //   console.log('result===========',result)
    // })
  await getAllAcitivity({processInstanceId : data.processInstanceId},(result)=>{
    console.log('result==getAllAcitivity=========',result)
    lineData.value = result;
  })
  await getTaskComments({processInstanceId : data.processInstanceId},(result)=>{
    console.log('getTemplateData:',result);
    result.forEach(item=>{
      comments.value[item.taskId] = item.message;
    })
    console.log('comments.value',comments.value)
  })
}
// async function getTemplateData(templateId){
//   await queryTemplateDesignInfo({templateKey:templateId},(result)=>{
//     console.log('getTemplateData:',result)
//     if(result.designContent){
//       templateData.value.list = JSON.parse(result.designContent);
//     }
//     if(result.designConfig){
//       templateData.value.formConfig = JSON.parse(result.designConfig);
//     }
//
//   })
// }
function drawHighLight(){
  designRef.value.showHighlight(lineData.value);
  if(!isInit){
    isInit = true;
    activeKey.value = "1";
  }

}

function getNextConfig(){
  var activityConfig = [];
  var currentId = getCurrentActivityId();
  if(currentId){
    var nextIdArr = designRef.value.getNextId(currentId);
    console.log('nextIdArr========>',nextIdArr)
    let config = JSON.parse(instData.value.ruleConfig) ?? {};
    console.log('config========>',config)
    nextIdArr.forEach(id=>{
      console.log('nextIdArr id========>',id)
      var nextData = config[id];
      if(nextData && nextData.isMultiInstance){
        var data = setNextConfigData(nextData);
        data.id = id;
        activityConfig.push(data);
      }

    })
  }

  return activityConfig;

}

function setNextConfigData(data){
  if(data.type == 1){
    if(Array.isArray(data.users)){
      data.users = data.users.toString();
    }
    data.roles = "";
  }else if(data.type == 3){
    if(data.roles){
      data.roles = data.roles.toString();
    }
    data.user = "";
  }
  return data;
}

function getCurrentActivityId(){
  var current = lineData.value.find(item=>  item.assignee == username.value && null == item.endTime );
  if(current){
    return current.activityId;
  }
  return "";
}

defineExpose({
  show,

});
</script>

<style lang="less" scoped>
@import "../../template/less/template.less";
 .flow_handle_wrapper{
   height: 100%;
   width:100%;
   /deep/ .ant-tabs-content-holder{
     height: 100%;
   }
   /deep/ .ant-tabs-content{
     height: 100%;
   }
 }
.taskList{
  width: 300px;
  padding-top: 10px;
  padding-left:20px;
  .taskTitle{
    margin-bottom: 20px;
  }
}
.taskTitle{
}
.no-foot{

      display: none!important;


}
</style>

