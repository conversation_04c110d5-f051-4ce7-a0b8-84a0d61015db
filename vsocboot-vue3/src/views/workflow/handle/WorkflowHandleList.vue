<template>
  <div>
    <!--引用表格-->
   <BasicTable @register="registerTable"   :isSearch="isSearch" >
     <!--插槽:table标题-->
          <template #form-formFooter>
<!--            <a-button  :class="{'btn-checked': isSearch == true}" type="text" @click="isSearch=!isSearch" preIcon="ant-design:filter-outlined"> {{ t('common.filter') }}</a-button>-->
          </template>
       <!--操作栏-->
      <template #action="{ record }">
        <TableAction :actions="getTableAction(record)"  />
      </template>
    </BasicTable>

  <WorkflowHandleModal ref="taskRef" @ok="reload"></WorkflowHandleModal>
  </div>
</template>

<script lang="ts" name="WorkflowHandleList" setup>
  import {ref, computed, unref} from 'vue';
  import {BasicTable, useTable, TableAction} from '/@/components/Table';
  import { useListPage } from '/@/hooks/system/useListPage'
  import {columns, searchFormSchema} from './WorkOrder.data';
  import {list} from './WorkflowOrder.api';
  import {useI18n} from "/@/hooks/web/useI18n";
  import {formLayout} from '/@/settings/designSetting';
  import WorkflowHandleModal from "/@/views/workflow/handle/modules/WorkflowHandleModal.vue";
  const { t } = useI18n();
  const isSearch = ref<boolean>(true);
  const taskRef = ref();
  const { prefixCls,tableContext} = useListPage({
       tableProps:{
            searchInfo:{isNow : 1,isDel:0},
            title: 'Agency Work Order',
            api: list,
            columns,
            canResize:false,
            formConfig: {
               labelWidth: 120,
               schemas: searchFormSchema,
               autoSubmitOnEnter:true,
               showAdvancedButton:true,
               layout: formLayout,
             },
            actionColumn: {
                width: 180,
             },
        },


   })
  const [registerTable, {reload},{ rowSelection, selectedRowKeys }] = tableContext
   function handleTask(record: Recordable){

     taskRef.value.show(record);
   }



   /**
      * 操作栏
      */
   function getTableAction(record){
       return [
         {
           label: 'handle',
           onClick: handleTask.bind(null, record),
           ifShow: () => {
             return record.isSuspended == false ;
           }
         },
         // {
         //   label: 'Suspended',
         //   ifShow: () => {
         //     return record.isSuspended == true ;
         //   }
         // },
         // {
         //   label: t('routes.workflow.design'),
         //   onClick: handleDesign.bind(null, record),
         // }, {
         //   label: t('common.delText'),
         //   popConfirm: {
         //     title: t('common.delConfirmText'),
         //     confirm: handleDelete.bind(null, record),
         //   }
         // }
       ]
     }

</script>
<style scoped>

</style>
