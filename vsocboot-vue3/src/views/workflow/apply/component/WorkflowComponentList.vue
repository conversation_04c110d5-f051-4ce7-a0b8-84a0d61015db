<template>

  <div class="row" v-for="(item,index) in childData.list" >
    <div :class="['item-widget']">
      <a-row class="grid-row " :gutter="item.gutter" :align="item.align" :justify="item.justify" v-if="item.type == 'row'">
        <a-col :span="item.col[j]"   v-for="(parent,j) in item.list" >
          <div class="row-col"><WorkflowComponentList v-model:value="item.list[j]" :type="type"
                                                      :ruleConfig="ruleConfig" v-model:form="form"> </WorkflowComponentList></div>
        </a-col>
      </a-row>
      <WorkflowComponents v-else :ref="setRefs(index)" :ruleConfig="ruleConfig" :type="type"  v-model:value="childData.list[index]" v-model:form="form"></WorkflowComponents>
    </div>
  </div>

</template>

<script  name="WorkflowComponentList" lang="ts" setup>
import {defineExpose, nextTick, inject, ref, watch, defineEmits} from 'vue'
  import {Component} from "/@/views/workflow/template/ts/Component";

  import {useRefs} from "/@/hooks/core/useRefs";
  import WorkflowComponents from "/@/views/workflow/apply/component/WorkflowComponents.vue";
import {string} from "vue-types";
  const emit = defineEmits(['update:form','ok']);

  const [refs, setRefs] = useRefs();

  const props = defineProps({
    value: {
      type: Object,
      default: {},
    },
    ruleConfig: {
      type: Object,
      default: {},
    },
    form: {
      type: Object,
      default: {},
    },
    type: {
      type: String,
      default: '',
    }

  });
   let childData = ref<Component>({});
  childData.value = props.value;
  let form = ref({});
  form.value = props.form;
  const type = props.type;
  watch(
    () =>  form.value,
    () => {
      emit('update:form',form.value);
    },
    { deep: true }
  )

</script>

<style lang="less" scoped>
  @import "../../template/less/template.less";

</style>
