<template>
  <div class="design-template-modal" style="height:100%;width: 100%;">
    <div :class="['flex','flex-h','template-design_wraper']" style="background-color: #1A1B1F;">
      <!-- 组件-->
      <div class="flex flex-v padding16 components_wraper">
        <div class="font14 fcolor list-item-title" style="margin-bottom: 22px;">Components
          <Icon icon="ant-design:table-outlined" @click="showModel = !showModel" style="cursor: pointer;float: right;margin-top: 4px;"></Icon>
        </div>
        <div class="components_wraper_list">
          <div :key="index" v-for="(data,index) in ComponentItems" class="list-items ">
            <div class="font12 fcolor list-item-title">{{ data.title }}</div>
            <template v-if="showModel">
              <div v-for="(liItem,index2) in data.column" :key="index2" :id="index + '-' + index2"
                   @dragstart="dragstart" draggable="true" @dragend="dragEnd" class="list-item">
                <div class="font13 ">
                  <span :class="'soc '+liItem.icon"></span>
                  <span>{{ liItem.name }}</span>
                </div>
              </div>
            </template>
            <template v-else>
              <a-row :gutter="[4,4]" style="text-align: center;">
                <a-col :span="8" class="icon-small-show-model font13 list-item"
                       v-for="(liItem,index2) in data.column" :key="index2"
                       @dragstart="dragstart" draggable="true" @dragend="dragEnd"
                       :id="index + '-' + index2">
                  <span :class="'soc '+liItem.icon" :title="liItem.name"></span>
                </a-col>
              </a-row>
            </template>
          </div>
        </div>

      </div>
      <!-- 页面内容-->
      <div class="flex flex-v flex-1 template_wraper" style="padding:0px 120px;">
        <!-- 操作栏-->
        <div class="flex flex-h template-tool font16" style="justify-content: flex-start;padding: 15.5px 0px;">
          Form editing area
        </div>
        <!-- 模板-->
        <div class="flex flex-v flex-1" @drop.stop.prevent="drop" @dragover.prevent style="background-color: #06080C;">

          <a-form layout="vertical">
            <TemplateComponentList v-model:value="templateDesignData"
                                   ref="componentRef" @show="rowComponentField" @dropSub="dropSub"/>
          </a-form>
        </div>
      </div>
      <!-- 属性-->
      <div class="flex flex-v padding16 property_wraper">
        <div class="font14 fcolor list-item-title" style="margin-bottom: 22px;">Component setting
        </div>
        <FieldProperty v-model:value="fieldData"   ref="fieldPropertyRef" />

      </div>
    </div>

  </div>
</template>

<script lang="ts" name="TemplatDesign" setup>

import FieldProperty from "/@/views/workflow/setting/component/FieldProperty.vue";
import TemplateComponentList from "/@/views/workflow/setting/component/TemplateComponentList.vue";
import {ComponentItems, SPLIT_STR} from '/@/views/workflow/setting/ts/template.api';
import {defineExpose, nextTick, onBeforeUnmount, onMounted, provide, ref, watch} from "vue";
import {getTimeStr} from '/@/views/workflow/setting/ts/TemplateUtils'
import {useMessage} from "/@/hooks/web/useMessage";

const { createMessage } = useMessage();

const currentClickId = ref('');
const currentItem = ref({});
const componentRef = ref();

const disabled = ref(false);
const emit = defineEmits(['success']);

let showModel = ref(true);
/**
 * form property in the templateDesignData.formPoperty
 * field property in the templateDesignData.list
 */
const templateDesignData = ref({
  // formPoperty: {},
  list: []
});
const fieldData = ref();
const fieldPropertyRef = ref();


//子孙通信
provide('clickId',currentClickId);
provide('clickItem',currentItem);
//自己调试用，上线前注释掉
// watch(templateDesignData.value, () => {
//   console.log('templateDesignData change', templateDesignData.value)
// }, {deep: true, immediate: true})


watch(currentItem, () => {
  // console.log('currentItem change', currentItem.value)
  rowComponentField();
}, {deep: true })

onMounted(()=>{
  console.log('onMounted~~~~')
  window.addEventListener('scroll', onScroll,true);
})
onBeforeUnmount(()=>{
  console.log('onBeforeUnmount~~~~')
  window.removeEventListener('scroll', onScroll);
})

function onScroll(){
  console.log("窗口滚动了");
}
/**
 * drag component
 * @param p
 * @param j
 * @param data
 */
function dropSub(p,j,data){
  let list = JSON.parse(JSON.stringify(templateDesignData.value.list));
  list.forEach(item=>{
    if(item.field == p.field){
      console.log(item.list[j])
      item.list[j].list.push(data);
    }
  })
  templateDesignData.value.list = list;
}



/**
 * show field config change
 * @param data
 */
function rowComponentField(){
  fieldData.value = currentItem.value;
  fieldPropertyRef.value.show(currentItem.value);
}





/**
 * drag start
 * @param ev
 */
function dragstart(ev) {
  let group = ev.target.id.split("-")[0];
  let columnIndex = ev.target.id.split("-")[1];
  let info = {isDrop: true, item: ComponentItems[group].column[columnIndex]};
  //判断当前浏览器是否为火狐浏览器
  let userAgent = navigator.userAgent;
  let ifFirefox = userAgent.indexOf("Firefox");
  if (ifFirefox) {
    ev.dataTransfer.setData("imgInfo", info);
  }
  ev.dataTransfer.setData('Text', JSON.stringify(info));

};

/**
 * mouse drop
 * @param e
 * @param flag
 */
function drop(e) {
  let info = JSON.parse(e.dataTransfer.getData('Text'));
  if (info.isDrop) {
    let item = {...info.item};
    item.field = item.component + SPLIT_STR + getTimeStr();
    templateDesignData.value.list.push(item);
    nextTick(()=>{
      currentClickId.value = item.field;
      currentItem.value = item;
    })
  }

}

/**
 * drag end
 * @param event
 */
function dragEnd(event) {
  event.preventDefault();
  event.stopPropagation();
};

/**
 * 回显Form配置
 * @param content
 */
function setFormDesign(content){
  if(content && content.length > 0){
    templateDesignData.value.list = JSON.parse(JSON.stringify(content));
    console.log('setFormDesign',templateDesignData.value.list )
  }
}

function isAddInvestigation(){
  let str = JSON.stringify(templateDesignData.value.list);
  if(-1 == str.indexOf("investigation_") && formData.templateType == 'Investigation'){
    let item = ComponentItems[2].column.find(sub=>sub.name == 'Investigation');
    item.field = item.component + SPLIT_STR + getTimeStr();
    templateDesignData.value.list.push(item);
  }
}

/**
 * Get Form configuration
 */
function getTemplateDesign(){
  let str = JSON.stringify(templateDesignData.value.list);
  if(str.split("bisTitle").length > 2){
    createMessage.warning('Duplicate Work Flow Title!');
    return false;
  }

  return getDesignContent();
}

function getDesignContent(){
  // 待添加Express entry
  // isAddInvestigation()

  return JSON.parse(JSON.stringify(templateDesignData.value.list));
}
defineExpose({
  setFormDesign,
  getTemplateDesign,
  getDesignContent
});

</script>


<style lang="less" scoped>
@import "../less/template.less";
@import "../../../../assets/font/iconfont.css";

</style>




