<template>
  <div :class="['row']"  >

    <a-form-item :label="childData.label" v-if="childData.type == 'input'">
      <a-input :id="childData.field"
               v-model:value="childData.defaultValue"
               :style="{width:childData.componentProps.width} "
               :placeholder="childData.componentProps.placeholder"/>
    </a-form-item>


    <a-form-item :label="childData.label" v-else-if="childData.type=='inputNumber'">
      <a-input-number :id="childData.field" v-model:value="childData.defaultValue"
                      :style="{width:childData.componentProps.width }"
                      :min="childData.componentProps.min" :max="childData.componentProps.max"
                      :step="childData.componentProps.step "/>
    </a-form-item>

    <a-form-item :label="childData.label" v-else-if="childData.type=='textArea'">
      <a-textarea :id="childData.field" v-model:value="childData.defaultValue"
                  :placeholder="childData.componentProps.placeholder"
                  :style="{width:childData.componentProps.width }"/>
    </a-form-item>
    <a-form-item :label="childData.label" v-else-if="childData.type=='radioGroup'">
      <a-radio-group v-model:value="childData.defaultValue" :name="childData.field"
                     :id="childData.field" :style="{width:childData.componentProps.width}">
        <template v-for="(group,index) in childData.items" :key="'radio_'+index">
          <a-radio :value="group.value" :class="childData.layout">{{ group.label }}</a-radio>
        </template>
      </a-radio-group>
    </a-form-item>
    <a-form-item :label="childData.label" v-else-if="childData.type=='checkboxGroup'">
      <a-checkbox-group v-model:value="childData.defaultValue" :name="childData.field"
                        :id="childData.field" :style="{width:childData.componentProps.width}">
        <template v-for="(group,index) in childData.items" :key="'checkbox_'+index">
          <a-checkbox :value="group.value" :class="childData.layout">{{ group.label }}</a-checkbox>
        </template>
      </a-checkbox-group>
    </a-form-item>
    <a-form-item :label="childData.label" v-else-if="childData.type=='select'">
      <a-select v-model:value="childData.defaultValue" :id="childData.field"
                :style="{width:childData.componentProps.width}"
                :mode="childData.componentProps.multiple ? 'multiple' : ''"
                :showSearch="childData.componentProps.showSearch"
                optionFilterProp="label"
                :placeholder="childData.componentProps.placeholder">
        <template v-for="(group,index) in childData.items" :key="'select'+index">
          <a-select-option :label="group.label" :value="group.value">{{ group.label }}</a-select-option>
        </template>
      </a-select>
    </a-form-item>
    <!--  <div v-else-if="childData.type=='radio'" :class="childData.class">
        <a-form-item :required="childData.state == 'required'" :label="childData.label" :style="{width:childData.width}" v-if="childData.dataType=='static'">

          <a-radio-group v-if="childData.radioType == 'default'" v-model="childData.value" :id="childData.id" :disabled="childData.state == 'readonly'">
            <template v-for="(group) in childData.items">
              <a-radio :value="group.value" :class="childData.layout">{{group.label}}</a-radio>
            </template>
          </a-radio-group>

          <a-radio-group v-else-if="childData.radioType == 'button'" v-model="childData.value" :id="childData.id"
                         :buttonStyle="childData.buttonStyle" :disabled="childData.state == 'readonly'">
            <template v-for="(group) in childData.items">
              <a-radio-button :value="group.value">{{group.label}}</a-radio-button>
            </template>
          </a-radio-group>


        </a-form-item>
        <a-form-item :label="childData.label" :style="{width:childData.width}" v-if="childData.dataType=='dict'" :required="childData.state == 'required'">
          <j-dict-select-tag :disabled="childData.state == 'readonly'" v-model="childData.value" :dictCode="childData.dictName" style="width:100%" :type="'radio'"/>
        </a-form-item>
      </div>
      <div v-else-if="childData.type=='checkbox'" :class="childData.class">
        <a-form-item :required="childData.state == 'required'" :label="childData.label" :style="{width:childData.width}">
          <template v-if="childData.dataType=='static'">
            <a-checkbox-group
              :disabled="childData.state == 'readonly'"
              :id="childData.id"
              :options="childData.items"
              v-model="childData.checkValue"
              style="width:100%"
            />
          </template>
          <template v-else>
            <j-multi-select-tag
              :disabled="childData.state == 'readonly'"
              :id="childData.id"
              v-model="childData.value"
              :dictCode="childData.dictName"
              :type="'checkbox'" style="width: 100%">
            </j-multi-select-tag>
          </template>
        </a-form-item>

      </div>
      <div v-else-if="childData.type=='select'" :class="childData.class">
        <a-form-item :label="childData.label" :required="childData.state == 'required'">
          &lt;!&ndash;多选&ndash;&gt;
          <div v-if="childData.isMulti" :style="{width:childData.width}">
            <template v-if="childData.dataType == 'static'">
              <j-multi-select-tag :id="childData.id"
                                  v-model="childData.value"
                                  :options="childData.items"
                                  style="width:100%"
                                  :placeholder="childData.placeholder"
                                  :disabled="childData.state == 'readonly'">
              </j-multi-select-tag>
            </template>
            <template v-else>
              <j-multi-select-tag :id="childData.id"
                                  v-model="childData.value" :dictCode="childData.dictName" style="width:100%"
                                  :placeholder="childData.placeholder"
                                  :disabled="childData.state == 'readonly'">
              </j-multi-select-tag>
            </template>

          </div>

          &lt;!&ndash;单选&ndash;&gt;
          <div v-else :style="{width:childData.width}">
            <template v-if="childData.dataType == 'static'">
              <a-select :placeholder="childData.placeholder" v-model="childData.value" :id="childData.id"
                        style="width:100%" :disabled="childData.state == 'readonly'">
                <a-select-option v-for="(item, key) in childData.items" :key="key" :value="item.value"
                                 :title=" item.text || item.label ">
                  {{ item.text || item.label }}
                </a-select-option>
              </a-select>
            </template>
            <template v-else>
              <j-dict-select-tag :disabled="childData.state == 'readonly'" :id="childData.id" v-model="childData.value" :dictCode="childData.dictName"
                                 :placeholder="childData.placeholder" style="width:100%"/>
            </template>

          </div>

        </a-form-item>
      </div>
      <div v-else-if="childData.type=='hText'" :class="childData.class">
        <div :id="childData.id" :style="{width:childData.width,'text-align':childData.textAlign,'font-size':childData.fontSize  + 'px',
           'font-weight': childData.fontWeight, 'font-style': childData.fontStyle, 'line-height': childData.lineHeight,
           'text-decoration': childData.decoration}">{{childData.value}}
        </div>
      </div>-->
      <div v-else-if="childData.type=='timePicker'">
        <a-form-item :label="childData.label">
          <a-time-range-picker v-if="childData.componentProps.range"
            v-model:value="childData.defaultValue" :name="childData.field"
            :style="{width:childData.componentProps.width}"
            :format="childData.componentProps.valueFormat"
            :showTime="childData.componentProps.showTime"
            :placeholder="[childData.componentProps.placeholder, childData.componentProps.placeholderend]"
          ></a-time-range-picker>
          <a-time-picker v-else
            :placeholder="childData.componentProps.placeholder"
            v-model:value="childData.defaultValue" :name="childData.field"
            :style="{width:childData.componentProps.width}"
            :format="childData.componentProps.valueFormat"
            :showTime="childData.componentProps.showTime"
          ></a-time-picker>
        </a-form-item>
      </div>
      <div v-else-if="childData.type=='datePicker'">
        <a-form-item :label="childData.label">
          <a-date-picker picker="year" v-if="childData.componentProps.type==1"
                         v-model:value="childData.defaultValue" :name="childData.field"
                         :style="{width:childData.componentProps.width}"
                         :format="childData.componentProps.format"
                         :placeholder="childData.componentProps.placeholder"
          ></a-date-picker>
          <a-date-picker picker="month" v-if="childData.componentProps.type==2"
               v-model:value="childData.defaultValue" :name="childData.field"
               :style="{width:childData.componentProps.width}"
               :format="childData.componentProps.format"
               :placeholder="childData.componentProps.placeholder"
          ></a-date-picker>
          <a-date-picker v-if="childData.componentProps.type==3 || childData.componentProps.type==4"
             v-model:value="childData.defaultValue" :name="childData.field"
             :style="{width:childData.componentProps.width}"
             :format="childData.componentProps.format"
             :showTime="childData.componentProps.type==4"
             :placeholder="childData.componentProps.placeholder"
          ></a-date-picker>

          <a-range-picker v-if="childData.componentProps.type==5 || childData.componentProps.type==6"
              v-model:value="childData.defaultValue" :name="childData.field"
              :style="{width:childData.componentProps.width}"
              :format="childData.componentProps.format"
              :showTime="childData.componentProps.type==6"
              :placeholder="[childData.componentProps.placeholder, childData.componentProps.placeholderend]"
          ></a-range-picker>
        </a-form-item>
      </div>

      <div v-else-if="childData.type=='switch'">
        <a-form-item :label="childData.label">
          <a-switch
            v-model:checked="childData.defaultValue" :name="childData.field"
            :unCheckedValue="childData.componentProps.unCheckedValue"
            :checkedValue="childData.componentProps.checkedValue"
          />
        </a-form-item>
      </div>

      <div v-else-if="childData.type=='hr'">
        <hr :style="{margin:(childData.componentProps.margin/2) +'px 0px',width:childData.componentProps.width}" />
      </div>

      <div v-else-if="childData.type=='text'">
        <div :style="{
          width:childData.componentProps.width,
          'text-align':childData.componentProps.align,
          'font-size':childData.componentProps.fontSize+'px',
          'font-style':childData.componentProps.italic?'italic':'',
          'font-weight':childData.componentProps.bold?'bold':'',
          'text-decoration':childData.componentProps.underline?'underline':(childData.componentProps.lineThrough?'line-through':''),
          'line-height':childData.componentProps.lineHeight + 'px',
        }">{{ childData.componentProps.content }}</div>
      </div>

      <div v-else-if="childData.type=='phone'">
        <a-form-item :label="childData.label">
          <a-input
            v-model:value="childData.defaultValue" :name="childData.field"
            :placeholder="childData.componentProps.placeholder"
            :style="{width:childData.componentProps.width}" />
        </a-form-item>
        <template  v-if="childData.componentProps.code">
          <a-form-item :style="{width:childData.componentProps.width}">
            <a-input v-model:value="childData.componentProps.codeValue" :name="childData.field" placeholder="验证码" style="width:calc(100% - 120px);"/>
            <a-button type="primary" style="margin-left:15px;">获取验证码</a-button>
          </a-form-item>
        </template>
      </div>

      <div v-else-if="childData.type=='email'">
        <a-form-item :label="childData.label">
          <a-input
            v-model:value="childData.defaultValue" :name="childData.field"
            :placeholder="childData.componentProps.placeholder"
            :style="{width:childData.componentProps.width}" />
        </a-form-item>
        <template :style="{width:childData.componentProps.width}" v-if="childData.componentProps.code">
          <a-form-item>
            <a-input v-model:value="childData.componentProps.codeValue" :name="childData.field" placeholder="验证码" style="width:calc(100% - 120px);"/>
            <a-button type="primary" style="margin-left:15px;">获取验证码</a-button>
          </a-form-item>
        </template>
      </div>

      <div v-else-if="childData.type=='upload'">
        <a-form-item :label="childData.label">
          <a-upload-dragger v-if="childData.componentProps.dragger"
            name="file"
            :style="{width:childData.componentProps.width}"
            :headers="headers"
            :action="uploadUrl"
            :multiple="childData.componentProps.multiple"
            :maxCount="childData.componentProps.maxCount"
            :file-list="childData.defaultValue"
            :data="{ biz: 'workOrder' }"
            @change="handleUploadChange"
          >
            <p class="ant-upload-drag-icon">
              <inbox-outlined></inbox-outlined>
            </p>
            <p class="ant-upload-text">{{ childData.componentProps.text }}</p>
          </a-upload-dragger>
          <a-upload v-else
            name="file"
            :style="{width:childData.componentProps.width}"
            :headers="headers"
            :action="uploadUrl"
            :multiple="childData.componentProps.multiple"
            :maxCount="childData.componentProps.maxCount"
            :file-list="childData.defaultValue"
            :data="{ biz: 'workOrder' }"
            @change="handleUploadChange"
          >
            <a-button>
              <upload-outlined></upload-outlined>
              {{ childData.componentProps.text }}
            </a-button>
          </a-upload>
        </a-form-item>
      </div>

      <div v-else-if="childData.type=='autoNumber'">
        <a-form-item :label="childData.label">
          <a-input v-model:value="childData.defaultValue" :name="childData.field" :style="{width:childData.componentProps.width}"/>
        </a-form-item>
      </div>
      <div  v-else-if="childData.type == 'table'">
        <a-form-item :label="childData.label">
          <a-table :dataSource="childData.dataSource" :columns="childData.columns" />
        </a-form-item>
      </div>

    <div v-else-if="childData.type == 'investigation'">
      <a-form-item :label="childData.label">
        <div :style="{width:childData.componentProps.width}">
          <a-row :gutter="[16,8]">
            <template v-for="(item,index) in childData.dataSource" :key="'investigation'+index">
              <a-col :span="5" style="max-width: none;">
                <div class="inves_div"></div>
              </a-col>
            </template>
            <a-col :span="4">
              <div class="inves_add">
                <div>
                  <Icon icon="ant-design:plus-outlined" size="24"/>
                </div>
                <div>Add</div>
              </div>
            </a-col>
          </a-row>
        </div>
      </a-form-item>
    </div>
    <div v-else-if="childData.type == 'riskCenter'">
      <a-form-item :label="childData.label">
        <div :style="{width:childData.componentProps.width}">
          <a-row :gutter="[16,8]">
            <template v-for="(item,index) in childData.dataSource" :key="'riskCenter'+index">
              <a-col :span="5" style="max-width: none;">
                <div class="inves_div"></div>
              </a-col>
            </template>
            <a-col :span="4">
              <div class="inves_add">
                <div>
                  <Icon icon="ant-design:plus-outlined" size="24"/>
                </div>
                <div>Add</div>
              </div>
            </a-col>
          </a-row>
        </div>
      </a-form-item>
    </div>
  </div>
</template>

<script name="TemplateComponents" lang="ts" setup>

import {defineExpose, onMounted, reactive, ref, watch} from 'vue'
import {Component} from "/@/views/workflow/setting/ts/Component";
import {uploadUrl} from "/@/api/common/api";
import {getToken} from "/@/utils/auth";
import {UploadChangeParam} from "ant-design-vue";
const headers = reactive({'X-Access-Token': getToken()});



const props = defineProps({
  value: {
    type: Object,
    default: {},
  },
  showType: {
    type: String,
    default: 'edit'
  }

});

let childData = ref<Component>({});
childData.value = props.value;
// if(!childData.value.style){
//   childData.value.style = {
//     borderLeft : 0,
//     borderRight : 0,
//     borderTop : 0,
//     borderBottom : 0,
//   }
// }

watch(()=>props.value,(n)=>{
  console.log('childData.value change:',props.value )
  childData.value = props.value;
},{deep:true})
const handleUploadChange = (info: UploadChangeParam) => {
  // fileList.value = [...info.fileList];
  // showUploadList.value = info.file.status == 'uploading';
  //
  // if (info.file.status === 'done') {
  //   showUploadList.value = false;
  //   console.log('handleUploadChange', info.file);
  //   console.log('handleUploadChange', info.fileList);
  //   filePath.value = info.file.response.message;
  //   fileName.value = info.file.name;
  // } else if (info.file.status === 'error') {
  //   createMessage.error(`${info.file.name} file upload failed.`);
  // }
};

</script>

<style lang="less" scoped>
@import "../less/template.less";

.ant-input-disabled {
  background-color: transparent !important;
  color: inherit !important;
  border: 0 !important;
}

.log_item {
  display: inline-block;
  padding: 0 5px;
}

.row-col {
  border: 1px dashed @border-color;
}

.grid-row {
  min-height: 100px;

}

.item-active {
  border-left: 5px solid @primary-color;
  background: @bg-color;

  .tool {
    display: block !important;
  }
}

.radio_block {
  display: flex;
  margin-left: 0;
}


.inves_div{
  height: 88px;
  width: 100%;
  border: 1px solid @border-color;
}

.inves_add {
  width: 88px;
  height: 88px;
  border: 1px solid @border-color;
}

.inves_add {
  width: 88px;
  height: 88px;
  border: 1px solid @border-color;
  border-radius: 8px;
  text-align: center;
  padding-top: 20px;
  font-size: 12px;
  cursor: pointer;
}
</style>
