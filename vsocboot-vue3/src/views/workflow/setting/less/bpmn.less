
/deep/ .djs-parent {
  --context-pad-entry-background-color: @dark-bg1 !important;
  --context-pad-entry-hover-background-color: @dark-bg3 !important;
  --shape-drop-allowed-fill-color: transprent !important;
  --popup-background-color: @dark-bg2 !important;
  --popup-border-color: @border-color !important;
  --palette-entry-color: @font-color-white;
  --palette-separator-color: @border-color;
  --color-blue-205-100-45-opacity-30: @border-color !important; //鼠标拖动垂直线颜色
  --common-stroke: @font-color-white;
  --common-stroke-line: @font-color-white; //连线颜色
  --common-fill: @font-color-white;
  --popup-entry-hover-color: @bg-color;
  --palette-background-color: @dark-bg3 !important;
  --palette-border-color: @border-color !important;
  --shape-connect-allowed-fill-color: rgba(48, 140, 255, 0.2) !important;
  --shape-drop-not-allowed-fill-color: rgba(48, 140, 255, 0.2) !important;

}

/deep/ [class^="bpmn-icon-"]:before, /deep/ [class*=" bpmn-icon-"]:before {
  font-family: "soc" !important;

}

.bpmn-canvas {
  height: 100%;
  flex: 1;

  /*background: url("data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PGRlZnM+PHBhdHRlcm4gaWQ9ImEiIHdpZHRoPSI0MCIgaGVpZ2h0PSI0MCIgcGF0dGVyblVuaXRzPSJ1c2VyU3BhY2VPblVzZSI+PHBhdGggZD0iTTAgMTBoNDBNMTAgMHY0ME0wIDIwaDQwTTIwIDB2NDBNMCAzMGg0ME0zMCAwdjQwIiBmaWxsPSJub25lIiBzdHJva2U9IiNlMGUwZTAiIG9wYWNpdHk9Ii4yIi8+PHBhdGggZD0iTTQwIDBIMHY0MCIgZmlsbD0ibm9uZSIgc3Ryb2tlPSIjZTBlMGUwIi8+PC9wYXR0ZXJuPjwvZGVmcz48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSJ1cmwoI2EpIi8+PC9zdmc+");*/

  :deep(.djs-container) {
    //background-image: linear-gradient(90deg,hsla(0,0%,78.4%,.15) 10%,transparent 0),linear-gradient(hsla(0,0%,78.4%,.15) 10%,transparent 0);
    //background-size: 10px 10px;
    .djs-palette-entries {
      .separator {

      }
    }

    .djs-palette {
      border: 0px;
      border-radius: 8px;
      width: 40px;
      justify-content: center;

      .bpmn-icon-lasso-tool, .bpmn-icon-space-tool,
      .bpmn-icon-intermediate-event-none, .bpmn-icon-subprocess-expanded,
      .bpmn-icon-data-store,
      .bpmn-icon-data-object,
      .bpmn-icon-group,
      .bpmn-icon-participant,
      .bpmn-icon-task {
        display: none;
      }

      .entry {
        width: 40px;
        height: 40px;
        line-height: 40px;
        font-size: 16px;
      }
    }

    .djs-context-pad {
      &.open {
        display: none;
      }

      .entry {
        font-size: 16px;
      }

      .bpmn-icon-screw-wrench, .bpmn-icon-intermediate-event-none, .bpmn-icon-text-annotation, .bpmn-icon-task {
        display: none;
      }
    }

    .djs-palette.two-column.open {
      width: 40px !important;
      top: 60px;
      background: @dark-bg3;
      border: 0px !important;

    }

  }
}

/deep/ [data-element-id^='Gateway'], [data-element-id^='Activity_'] {
  path {
    display: none !important;
  }
}

/deep/ [data-element-id^='Gateway'][data-element-id$='_label'] {
  display: none !important;
}

/deep/ [data-element-id^='Flow_'] {
  path {
    display: block !important;
  }
}

/deep/ [data-element-id^='TextAnnotation_'] {

  g.djs-visual {
    & > :nth-child(1) {
      stroke-width: 0 !important;
    }

    rect {
      stroke-width: 0px !important;
    }
  }
}

/deep/ .bjs-container {

  g.djs-visual {
    path {
      stroke: var(--common-stroke) !important;
      fill: var(--common-fill) !important;
      stroke-width: 2px !important;
      display: none;
    }

    text {
      fill: var(--common-fill) !important;
      stroke: none !important;
      font-size: 13px;

    }

    & > :nth-child(1):not(circle) { //shape
      fill: transparent !important;
      stroke: var(--common-stroke) !important;
    }

    & > :nth-child(1):not(text), & > :nth-child(1):not(path) { //shape
      display: block;
    }

    & > path:nth-child(1) { //line
      stroke: var(--common-stroke-line) !important;
    }

    & > path:first-child {
      display: none;
    }
  }

  .djs-direct-editing-parent {
    background-color: @dark-bg3 !important;
    border-width: 0 !important;
    border-radius: 10px;
    display: none;
  }

  .djs-connection-preview { //拖拽连线时虚线颜色
    path {
      stroke: @font-color-1 !important;
    }
  }

  .djs-popup-search {
    input {
      background: @bg-color;
      border: 1px solid @border-color;
    }

    path {
      fill: @font-color-white !important;
    }
  }
}

/deep/ .djs-connection {
  g.djs-visual {
    path {
      /* marker-start: url("#sequenceflow-arrow-normal");*/

    }

  }
}

/deep/ marker {
  transform: rotate(90deg);
}

/deep/ marker > path { //连线三角的颜色
  //stroke: var(--common-stroke-line) !important;
  //fill:var(--common-stroke-line) !important;
  stroke: @primary-color !important;
  fill: @primary-color !important;
  stroke-width: 2px !important;
}

/deep/ .bpmn-icon-hand-tool:before {
  content: "\e613";
}

/deep/ .bpmn-icon-connection-multi:before {
  content: "\e614";
}

/deep/ .bpmn-icon-start-event-none:before {
  content: "\e60f";
  color: #308CFF;
}

/deep/ .bpmn-icon-end-event-none:before {
  content: "\e612";
  color: #308CFF;
}

/deep/ .bpmn-icon-gateway-none:before {
  content: "\e615";
  color: #F8A556;
}

/deep/ .bpmn-icon-user-task:before {
  content: "\e610";
  color: #308CFF;
}

/deep/ .bpmn-icon-trash:before {
  font-family: bpmn !important;
}

/deep/ .bpmn-icon-user-task {
  font-family: soc !important;
  content: "\e610";
}

/deep/ .ax-bjq-note {
  color: #2ECF99;
}

circle:before {
  font-family: soc;
  content: "\e60f";
  color: #308CFF;
}

.taskMenu {
  width: 240px;
  height: auto;
  border-radius: 8px;
  background: @dark-bg3;
  box-sizing: border-box;
  border: 1px solid @border-color;
  position: absolute;
  z-index: 100;
  display: none;
  padding: 0px 12px 4px 12px;

  .menu {
    padding: 8px 0px;

    font-size: 12px;
    font-weight: normal;
    line-height: 16px;
    color: @font-color-default;
    cursor: pointer;
  }

  .menu-split {
    border-bottom: 1px solid @border-color;
    margin: 4px 0px;
  }
}

/deep/ [data-element-id^='Activity_'].djs-element.selected {

  .djs-outline {
    filter: blur(4px);
    x: -2 !important;
    y: -2 !important;
    width: 244px !important;
    rx: 0px !important;
    height: 70px !important;

  }

  g.djs-visual {
    rect[width="237"] {
      filter: blur(0px);
      stroke: #308CFF !important;
      stroke-width: 2px;
    }

    rect[width="232"] {
      fill: #1F324C !important;
    }


  }
}

/deep/ [data-element-id^='Gateway_'].djs-element.selected {

  .djs-outline {
    stroke: none;
  }

  g.djs-visual {
    polygon:nth-child(2) {
      fill: rgb(248, 165, 86) !important;
      filter: blur(4px);
      stroke: rgb(248, 165, 86) !important;
    }

    rect:nth-child(3) {
      fill: #47372A !important;
    }

    polygon:nth-child(4) {
      fill: rgb(248, 165, 86) !important;
      stroke: rgb(248, 165, 86) !important;
    }
  }
}

