import {computed, nextTick, ref} from "vue";
import {useMessage} from '/@/hooks/web/useMessage';
import {buildShortUUID2} from "/@/utils/uuid";
// 引入相关的依赖
import BpmnModeler from "bpmn-js/lib/Modeler";
import customModule from '../custom/ImportJS/onlyContextPad';
import "bpmn-js/dist/assets/diagram-js.css";
import "bpmn-js/dist/assets/bpmn-font/css/bpmn-embedded.css";
import "bpmn-js/dist/assets/bpmn-font/css/bpmn.css";
import "bpmn-js/dist/assets/bpmn-font/css/bpmn-codes.css";
import camundaModdleDescriptor from 'camunda-bpmn-moddle/resources/camunda.json'
// import {append as svgAppend, attr as svgAttr, create as svgCreate} from "tiny-svg";
// import {query as domQuery} from "min-dom";
import { useI18n } from '/@/hooks/web/useI18n';

const { t } = useI18n();

export let click_event = null;
//监听事件
const LISTENER_EVENTS = ['drag.move', 'shape.added', 'shape.move.end', 'shape.removed', 'connection.added', 'connect.end', 'connect.move', 'autoPlace.end', 'autoPlace']


export const ErrorMessageMap = {
  ERROR_1 : t('workflow.workflow.ERROR_1'),
  ERROR_2 : t('workflow.workflow.ERROR_2'),
  ERROR_3 : t('workflow.workflow.ERROR_3'),
  ERROR_4 : t('workflow.workflow.ERROR_4'),
  ERROR_5 : t('workflow.workflow.ERROR_5'),
  ERROR_6 : t('workflow.workflow.ERROR_6'),
  ERROR_7 : t('workflow.workflow.ERROR_7'),
  ERROR_8 : t('workflow.workflow.ERROR_8'),
  ERROR_9 : t('workflow.workflow.ERROR_9'),
  ERROR_10 : t('workflow.workflow.ERROR_10'),
  ERROR_11 : t('workflow.workflow.ERROR_11'),
  ERROR_12 : t('workflow.workflow.ERROR_12'),
  ERROR_13 : t('workflow.workflow.ERROR_13'),
  ERROR_14 : t('workflow.workflow.ERROR_14'),
  ERROR_15 : t('workflow.workflow.ERROR_15'),
}
export const redoShow = ref(false);
export const undoShow = ref(false);
export const scale = ref(1);
export const bpmnModeler = ref();
export const fileRef = ref();
export const bpmnXML = ref('');
export let elementSelector = [];
export const propertyActiveKey = ref('1');

export const clickShape = ref();//选中的node
export let processId = null;
export const isCloseCondition = ref(false);
//流程所有配置项信息
export const propertyData = ref({
  flowInfo: {},//流程基本信息
  nodeInfo: {//选中节点信息
    id: '',
    name: ''
  },
  approver: {
    /**
     * 审批人类型
     * 0：未指定
     * 1：办理人
     * 2：候选用户
     * 3：候选组
     */
    'camunda:type': 1,
    'camunda:candidateGroups': "",
    defaultFontSize: "",//选择的人
    'camunda:candidateUsers': '',//候选人

  },//审批人
  ruleConfig: {
    nextId: '',
    startId: '',
    nextConfig: []
  },//显示隐藏的规则
  conditionConfig: {},//分支条件
});
let undoNum = 0;
const DROP_MENU = 'contextMenu';
const {createMessage, notification} = useMessage();

export function initBpmn(container) {
  if (bpmnModeler.value) {
    removeListener();

    bpmnModeler.value.clear();
    bpmnModeler.value.destroy();
  }

  let containerEl = document.getElementById(container);
  bpmnModeler.value = new BpmnModeler({
    container: containerEl,
    keyboard: {
      bindTo: document
    },
    additionalModules: [customModule, {zoomScroll: ['value', '']}],
    moddleExtensions: {
      camunda: camundaModdleDescriptor,
    },
  });
  // 创建xml
  createNewDiagram();
}

export async function saveXML() {
  const res = await bpmnModeler.value.saveXML({format: true})
  bpmnXML.value = res.xml.toString();
}

export function updateXml(xml) {
  bpmnXML.value = xml;
  createNewDiagram();
}


export const minZoomShow = computed(() => {
  return scale.value > 0.1;
})
/**
 * 目前放大没有限制
 */
export const maxZoomShow = computed(() => {
  return scale.value > 1.2;
})

export function alignElements(position = 'center') {
  if (!elementSelector.length)
    return createMessage.warning('Please select an element!')
  const alignElements = bpmnModeler.value.get('alignElements')
  console.log(elementSelector)
  alignElements.trigger(elementSelector, position);
}

export function handlerZoom(radio) {
  scale.value = !radio ? 1.0 : scale.value + radio
  console.log(' scale.value:', scale.value)
  bpmnModeler.value.get('canvas').zoom(scale.value)
}

export function zoomFit() {
  bpmnModeler.value.get('canvas').zoom('fit-viewport');
}

export function redo() {
  bpmnModeler.value.get('commandStack').redo()
}

export function undo() {
  bpmnModeler.value.get('commandStack').undo()
}

export async function saveBPMN() {
  try {
    const result = await bpmnModeler.value.saveXML({format: true})
    const {xml} = result

    const xmlBlob = new Blob([xml], {
      type: 'application/bpmn20-xml;charset=UTF-8,'
    })

    const downloadLink = document.createElement('a')
    downloadLink.download = `bpmn-${+new Date()}.bpmn`
    downloadLink.innerHTML = 'Get BPMN SVG'
    downloadLink.href = window.URL.createObjectURL(xmlBlob)
    downloadLink.onclick = function (event) {
      document.body.removeChild(event.target)
    }
    downloadLink.style.visibility = 'hidden'
    document.body.appendChild(downloadLink)
    downloadLink.click()
  } catch (err) {
    console.log(err)
  }
}

export async function loadBPMN() {
  const file = fileRef.value.files[0]
  const reader = new FileReader()
  reader.readAsText(file)
  reader.onload = function () {
    // that.$emit('updateXml', this.result)
  }
}

export async function saveSVG() {
  try {
    const result = await bpmnModeler.value.saveSVG()
    const {svg} = result

    const svgBlob = new Blob([svg], {
      type: 'image/svg+xml'
    })

    const downloadLink = document.createElement('a')
    downloadLink.download = `bpmn-${+new Date()}.SVG`
    downloadLink.innerHTML = 'Get BPMN SVG'
    downloadLink.href = window.URL.createObjectURL(svgBlob)
    downloadLink.onclick = function (event) {
      document.body.removeChild(event.target)
    }
    downloadLink.style.visibility = 'hidden'
    document.body.appendChild(downloadLink)
    downloadLink.click()
  } catch (err) {
    console.log(err)
  }
}

/**
 * 获取初始化xml
 * @param uuid
 */
function getInitialDiagram(uuid) {
  const initialDiagram = `<?xml version="1.0" encoding="UTF-8"?>
    <bpmn:definitions xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:bpmn="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" id="Definitions_${uuid}" targetNamespace="http://bpmn.io/schema/bpmn">
    <bpmn:process id="${uuid}" isExecutable="true">
    </bpmn:process>
    <bpmndi:BPMNDiagram id="BPMNDiagram_1">
    <bpmndi:BPMNPlane id="BPMNPlane_1" bpmnElement="${uuid}">
    </bpmndi:BPMNPlane>
    </bpmndi:BPMNDiagram>
    </bpmn:definitions>`;
  return initialDiagram;
}

function createNewDiagram() {
  try {
    if (bpmnXML.value == "") {
      const uuid = buildShortUUID2('process');
      bpmnXML.value = getInitialDiagram(uuid);
      propertyData.value.flowInfo.processId = uuid;
      console.log(' propertyData.value.flowInfo.processId:', propertyData.value.flowInfo.processId)
    }
    bpmnModeler.value.importXML(bpmnXML.value).then(function (result) {
      const {warnings} = result;
      console.log('success import!', warnings);
      // createArrowSvg();
      zoomFit();
      addModelerListener();
      addEventBusListener();
      modifyBpmn();

    }).catch(function (err) {
      const {warnings, message} = err;
      console.log('something went wrong:', warnings, message);
    });
  } catch (err) {
    console.log('createNewDiagram error', err);
  }
}

function modifyBpmn() {
  const container = document.getElementsByClassName("bjs-container");
  if (container.length > 1) {
    container[0].remove();
  }

  // 删除 bpmn logo
  const Logo = document.querySelector(".bjs-powered-by");
  while (Logo.firstChild) {
    Logo.removeChild(Logo.firstChild);
  }
}

/**
 * 监听 modeler
 * shape.added 新增一个shape之后触发;
 * shape.move.end 移动完一个shape之后触发;
 * shape.removed 删除一个shape之后触发;
 */
function addModelerListener() {
  LISTENER_EVENTS.forEach(function (event) {
    bpmnModeler.value.on(event, e => {
      let elementRegistry = bpmnModeler.value.get('elementRegistry');
      let shape = e.element ? elementRegistry.get(e.element.id) : e.shape;
      // console.log('event name =============',event)
      // console.log('event shape =============',shape)
      clickShape.value = shape;
      if(shape.type == 'bpmn:ExclusiveGateway'){
        if(event === 'connect.move' || event === 'drag.move'|| event === 'shape.move'  ){
          isCloseCondition.value = true;
        }else if(event === 'connect.move.end' || event === 'connect.end'|| event === 'shape.move.end' || event === 'drag.cleanup' ){
          isCloseCondition.value = false;

        }
      }

      if (event === 'shape.added') {
        propertyActiveKey.value = '2';

        if (shape.type == 'bpmn:StartEvent' || shape.type == 'bpmn:EndEvent') {
          let res = validateAdd(shape);
          if (res != true) {
            createMessage.warning(ErrorMessageMap[res]);
            return;
          }
        }
        setPropertyData(shape.businessObject);
        //解决bpmn:TextAnnotation拖拽后是个小标签
        if (shape.type == "bpmn:TextAnnotation") {
          nextTick(() => {
            bpmnModeler.value.get('modeling').updateProperties(e.element, {
              text: 'This is an annotation'
            });
          })
        }

      } else if (event === 'shape.move.end') {
        closeContextPad();
      } else if (event === 'shape.removed') {
        // console.log('删除了shape:', shape)
        closeContextPad();
      } else if (event === 'connection.added') {
        let res = validateLine(shape);
        if (res != true) {
          createMessage.warning(ErrorMessageMap[res]);
          return;
        }

      } else if (event === 'autoPlace.end') {
        setTimeout(() => {
          //删除错误连线连接的节点
          if (shape.incoming.length == 0) {
            deleteErrorShape(shape);
          }
        }, 10)

      } else if (event === 'drag.move') {//解决拖拽后copy不能取消选中问题
        click_event = e;
      }


    })
  })
}

function validateLine(shape) {
  let source = shape.source;
  let target = shape.target;
  let error = '';
  if (target.type == 'bpmn:StartEvent') {//终点不能是起始节点
    error = 'ERROR_13';
  }//除了网关，不能有两条出线
  else if (source.type != 'bpmn:ExclusiveGateway' && source.outgoing.length > 1) {
    error = 'ERROR_12';
  // }//起点不能直接连接网关
  // else if (source.type == 'bpmn:StartEvent' && target.type == 'bpmn:ExclusiveGateway') {
  //   error = 'ERROR_11';
  } else if (target.outgoing.length > 0) {
    let lineId = target.id + '-' + source.id;
    target.outgoing.forEach(item => {
      let source2 = item.source;
      let target2 = item.target;
      let lineId2 = source2.id + '-' + target2.id;
      if (lineId == lineId2) {
        error = 'ERROR_14';
        return;
      }
    })

  }
  if (error) {
    deleteErrorShape(shape);
    return error;
  }
  return true;
}

/**
 * 组件添加校验，禁止添加不合理的组件
 * @param shape
 */
function validateAdd(shape) {
  const elementRegistry = bpmnModeler.value.get('elementRegistry');
  let error = '';
  if (shape.type == 'bpmn:StartEvent' || shape.type == 'bpmn:EndEvent') {
    const startEvent = elementRegistry.filter(item => item.type == shape.type);
    if (startEvent.length > 1) {
      error = 'bpmn:EndEvent' ? 'ERROR_4' : 'ERROR_2';
    }
  } else if (shape.type == 'bpmn:ExclusiveGateway') {
    // shape.incoming
  }

  if (error) {
    deleteErrorShape(shape);
    return error;
  }
  return true;

}

function deleteErrorShape(shape) {
  nextTick(() => {
    // const canvas = bpmnModeler.value.get("canvas");
    // canvas.removeShape(shape.id);
    // clickShape.value = null;
    bpmnModeler.value.get('modeling').removeElements([shape])
    // const element = elementRegistry.get(shape.id)
  })
}

/**
 * 创建element
 * @param elementType
 * @param properties
 * @param parent
 */
export function createModdleElement(elementType, properties, parent) {
  const moddle = bpmnModeler.value._moddle;
  const element = moddle.create(elementType, properties);
  parent && (element.$parent = parent);
  return element;
}

function addListener() {
  const listener = createModdleElement("camunda:ExecutionListener", {
    event: 'start',
    class: 'org.vsoc.modules.workflow.listener.MyExecutionListener',
  });

  var extensionElements = createModdleElement("bpmn:ExtensionElements", {values: [listener]});
  bpmnModeler.value.get('modeling').updateProperties(elementSelector[0], {
    extensionElements: extensionElements
  });
}

/**
 * 监听 element
 */
function addEventBusListener() {
  const eventBus = bpmnModeler.value.get('eventBus')
  const eventTypes = ['element.click'];

  eventTypes.forEach(function (eventType) {
    eventBus.on(eventType, function (e) {

      if (click_event == e) {//取消选中
        console.log('取消选中...')
        clickShape.value = null;
        click_event = null;
        return;
      }
      if (!e || e.element.type == 'bpmn:Process') {
        closeContextPad();
        propertyData.value.flowInfo.processId = e.element.id;
        return;
      }
      let shape = getShape(e.element.id);
      if(clickShape.value && clickShape.value.id == e.element.id){
        clickShape.value = JSON.parse(JSON.stringify(shape));
      }else{
        clickShape.value = shape;
      }

      if (eventType === 'element.click') {
        click_event = e;
        // isCloseCondition.value = false;
        console.log('click shape', shape)
        setPropertyData(shape.businessObject);

      }
    })
  });
  eventBus.on('selection.changed', function (e) {
    elementSelector = e.newSelection;
    if (elementSelector.length == 0) {
      clickShape.value = null;
      propertyActiveKey.value = '1';
      closeContextPad();
    } else {
      propertyActiveKey.value = '2';
      setTimeout(showContextPad, 100);
    }

  })
  //防止bpmn:TextAnnotation双击变一个小标签
  eventBus.on('element.dblclick', 10000, function (context) {
    var element = context.element;
    // if(element.type == 'bpmn:Task' || element.type == "bpmn:StartEvent" || element.type == "bpmn:IntermediateThrowEvent" || element.type == "bpmn:EndEvent" || element.type == "bpmn:ExclusiveGateway" || element.type == "bpmn:DataObjectReference" || element.type == "bpmn:DataStoreReference"){
    return false;
    // }
  });
}

export function closeContextPad() {
  document.getElementById(DROP_MENU).style.display = 'none';
}

export function removeListener() {
  const eventBus = bpmnModeler.value.get('eventBus');
  const eventTypes = ['element.click', 'element.changed', 'selection.changed'];
  eventTypes.forEach(function (eventType) {
    eventBus.off(eventType);
  });
  const events = LISTENER_EVENTS;
  events.forEach(function (event) {
    bpmnModeler.value.off(event);
  });
}

function elementChanged(eventType, e) {
  var shape = getShape(e.element.id)
  if (!shape) {
    // 若是shape为null则表示删除, 无论是shape还是connect删除都调用此处
    console.log('无效的shape')
    // 由于上面已经用 shape.removed 检测了shape的删除, 因此这里只判断是否是线
    if (isSequenceFlow(shape.type)) {
      console.log('删除了线')
    }
  }
  if (!isInvalid(shape.type)) {
    if (isSequenceFlow(shape.type)) {
      console.log('改变了线')
    }
  }
}

/**
 * 获取id
 * @param id
 */
export function getShape(id) {
  var elementRegistry = bpmnModeler.value.get('elementRegistry')
  return elementRegistry.get(id);
}

/**
 * 判断是否是无效的值
 * @param param
 */
function isInvalid(param) {
  return param === null || param === undefined || param === '';
}

/**
 *  判断是否是线
 * @param type
 */
function isSequenceFlow(type) {
  return type === 'bpmn:SequenceFlow';
}

function setPropertyData(data) {
  console.log('setPropertyData', data)
  propertyData.value.nodeInfo.name = data?.name || data?.text;
  // let attrs = data.$attrs;
  // if(data.$type == 'bpmn:UserTask'){
  //   console.log('attrs',attrs)
  //   propertyData.value.approver['camunda:type'] = attrs['camunda:type'];
  //   propertyData.value.approver['camunda:candidateUsers'] = attrs['camunda:candidateUsers'];
  //   propertyData.value.approver[defaultFontSize] = data.assignee;
  //   propertyData.value.approver['camunda:candidateGroups'] = data.candidateGroups;
  // }


}

export function validateActivity() {
  const elementRegistry = bpmnModeler.value.get('elementRegistry');
  const startEvent = elementRegistry.filter(item => item.type == 'bpmn:StartEvent');
  if (startEvent.length == 0) {
    return 'ERROR_1';
  } else if (startEvent.length > 1) {
    return 'ERROR_2';
  }
  const endEvent = elementRegistry.filter(item => item.type == 'bpmn:EndEvent');
  if (endEvent.length == 0) {
    return 'ERROR_3';
  } else if (endEvent.length > 1) {
    return 'ERROR_4';
  }
  const userTaskList = elementRegistry.filter(
    (item) => item.type === 'bpmn:UserTask'
  );
  if (!userTaskList || userTaskList.length == 0) {
    return 'ERROR_5';
  }
  const sequenceFlow = elementRegistry.filter(item => item.type == 'bpmn:SequenceFlow');
  const exclusiveGateway = elementRegistry.filter(item => item.type == 'bpmn:ExclusiveGateway');

  if (sequenceFlow.length < userTaskList.length + 1) {
    return 'ERROR_6';
  }
  for (let task of userTaskList) {
    if (task.incoming.length == 0 || task.outgoing.length == 0) {
      return 'ERROR_7';
    }
    if (!propertyData.value.ruleConfig[task.id] || (!propertyData.value.ruleConfig[task.id].users && !propertyData.value.ruleConfig[task.id].roles)) {
      return 'ERROR_9';
    }
  }
  if (exclusiveGateway.length > 0) {

    for (let e of exclusiveGateway) {
      if (e.incoming.length == 0 || e.outgoing.length == 0) {
        return 'ERROR_8';
      }
      if (!propertyData.value.ruleConfig[e.id] || !propertyData.value.ruleConfig[e.id].config) {
        return 'ERROR_10';
      }
    }
  }

  return true;
}

export function getStartEventId() {
  const elementRegistry = bpmnModeler.value.get('elementRegistry');
  const startEvent = elementRegistry.filter(item => item.type == 'bpmn:StartEvent');
  return startEvent[0].id;
}

export function getAllActivity() {
  const elementRegistry = bpmnModeler.value.get('elementRegistry');

  const startEvent = elementRegistry.filter(item => item.type == 'bpmn:StartEvent');
  //解决process id 对应不上问题 start
  const process = elementRegistry.filter(item => item.type == 'bpmn:Process');
  propertyData.value.flowInfo.processId = process[0].id;
  //解决process id 对应不上问题 end
  const data = getNextObject(startEvent[0]);
  console.log('startEvent data', data)
  return data;
}

export function getNextActivityId() {
  const elementRegistry = bpmnModeler.value.get('elementRegistry');
  const startEvent = elementRegistry.filter(item => item.type == 'bpmn:StartEvent');
  var outArr = startEvent[0].outgoing;
  console.log('outArr', outArr)
  // bpmnModeler.value.get('modeling').updateProperties(startEvent[0],{
  //   id :'StartEvent'
  // });
  return outArr[0].businessObject.targetRef.id;
}

function getNextObject(event) {
  var activityConfig = [];
  var outArr = event.outgoing;
  outArr.forEach(item => {
    var nextId = item.businessObject.targetRef.id;
    const nextEvent = propertyData.value.ruleConfig[nextId];
    console.log('nextEvent', nextEvent)
    if (nextEvent && nextEvent.isMultiInstance) {
      var data = {...nextEvent};
      data.id = nextId;
      console.log('startEvent data', data)
      activityConfig.push(setNextConfigData(data));
    }

  })
  return activityConfig;
}

function setNextConfigData(data) {
  if (data.type == 1) {
    if (Array.isArray(data.users)) {
      data.users = data.users.toString();
    }
    data.roles = "";
  } else if (data.type == 3) {
    if (data.roles) {
      data.roles = data.roles.toString();
    }
    data.user = "";
  }
  return data;
}

function showContextPad() {

  let el = document.getElementsByClassName("djs-tooltip-container")[0];
  let shape = getShape(elementSelector[0]?.id);
  var st = window.getComputedStyle(el, null)
  var tr = st.getPropertyValue("-webkit-transform") ||
    st.getPropertyValue("-moz-transform") ||
    st.getPropertyValue("-ms-transform") ||
    st.getPropertyValue("-o-transform") ||
    st.getPropertyValue("transform") ||
    "FAIL";
  let top = shape.height + shape.y + 200;
  let left = shape.x;
  if (shape.type == 'bpmn:SequenceFlow') {
    let y = Math.max(...shape.waypoints.map(item => item.y));
    top = y + 200;
    left = shape.waypoints[0].x;
  }
  if (tr != 'none') {
    var values = tr.split('(')[1].split(')')[0].split(',')
    let x = values[4].trim();
    let y = values[5].trim();
    top = top + new Number(y);
    left = left + new Number(x);
  }
  document.getElementById(DROP_MENU).style.left = left + 'px';
  document.getElementById(DROP_MENU).style.top = top + 'px';
  document.getElementById(DROP_MENU).style.display = 'block';


}


/**
 * 添加任务节点
 * @param startNode
 */
export function addShape(type) {
  console.log('elementSelector', elementSelector)
  const elementFactory = bpmnModeler.value.get("elementFactory");
  const autoPlace = bpmnModeler.value.get("autoPlace");
  const shape = elementFactory.createShape({type});
  autoPlace.append(elementSelector[0], shape);
  closeContextPad();
}

/**
 * 创建note
 */
export function createNote() {
  let shape = elementSelector[0];
  let attr = {
    type: 'bpmn:TextAnnotation',
    name: 'This is an annotation'
  }

  if (shape.type == 'bpmn:SequenceFlow') {
    let x = shape.waypoints[0].x;
    let y = Math.max(...shape.waypoints.map(item => item.y));
    attr.x = x + 100;
    attr.y = y - 20;
  } else {
    attr.x = Math.round(shape.x) + 100;
    attr.y = shape.y - 20;
  }

  createShape(attr);
  cancelSelected();
}

export function cancelSelected() {
  const eventBus = bpmnModeler.value.get('eventBus')
  console.log('cancelSelected click', clickShape.value)
  if (click_event) {
    console.log('cancelSelected', click_event)
    eventBus.fire('element.click', click_event);
  }
}

/**
 * 拷贝
 */
export function copyShape() {

  let shape = elementSelector[0];
  let attr = {
    x: shape.x + 50,
    y: shape.y + shape.height + 50,
    type: shape.type,
    name: shape?.businessObject?.name
  }
  let branchShape = createShape(attr);
  // clickShape.value = branchShape;
  let config = propertyData.value.ruleConfig[shape.id];
  if (config) {
    propertyData.value.ruleConfig[clickShape.value.id] = config;
    let newShape = getShape(branchShape.id);
    bpmnModeler.value.get('modeling').updateProperties(newShape, {
      defaultFontSize: config.users,
    });
  }
  cancelSelected();

}

function createShape(attr) {
  const elementFactory = bpmnModeler.value.get("elementFactory");
  const modeling = bpmnModeler.value.get("modeling")
  const rootElement = bpmnModeler.value.get('canvas').getRootElement()
  let branchShape = elementFactory.createShape({
    type: attr.type
  });
  branchShape.businessObject.name = attr?.name;
  return modeling.createShape(branchShape,
    {
      x: attr.x,
      y: attr.y
    },
    rootElement)
}

/**
 * 删除
 */
export function deleteShape() {
  if (elementSelector.length > 0) {
    console.log('deleteShape', elementSelector[0].id)

    if (propertyData.value.ruleConfig[elementSelector[0].id]) {
      console.log('delete config ！！！！！')
      Reflect.deleteProperty(propertyData.value.ruleConfig, elementSelector[0].id);
    }
    // 判断是否关联网关，关联要删除网关里得相应配置,不删除也没啥影响，就是数据冗余了
    if (elementSelector[0].type == 'bpmn:UserTask') {

    }
    bpmnModeler.value.get('modeling').removeElements(elementSelector);
    elementSelector = [];
    clickShape.value = null;
  }

}

function deleteGate() {

  let config = propertyData.value.ruleConfig;
}

export function getBeforeTask() {
  let beforeTask = [];
  const firstFlow = bpmnModeler.value.get('elementRegistry').find(function (item) {
    return item.id == clickShape.value.id;
  })?.incoming;
  console.log('firstFlow', firstFlow)

  if (firstFlow && firstFlow.length > 0) {
    let incoming = firstFlow;
    incoming.forEach(item => {
      let shape = getShape(item.id);
      if (shape?.source.type == 'bpmn:UserTask' || shape?.source.type == 'bpmn:StartEvent') {
        let task = getShape(shape.source.id);
        let taskConfig = propertyData.value.ruleConfig[task.id];
        beforeTask.push({id: task.id, name: task.businessObject.name, config: taskConfig})
      }
    })
  }
  return beforeTask;
}

export function getConditionTask() {

  if (elementSelector.length == 0) {
    return [];
  }
  let outgoing = elementSelector[0].outgoing;

  let taskArray = [];
  if (outgoing.length == 0) {
    return taskArray;
  }
  outgoing.forEach(item => {
    console.log('outgoing', outgoing)
    let shape = getShape(item.id);
    // console.log('shape',shape)
    let lineId = item.id;
    if (shape.target.type == 'bpmn:UserTask') {
      let task = getShape(shape.target.id);

      taskArray.push({id: task.id, name: task.businessObject.name, lineId: lineId});
    }else if (shape.target.type == 'bpmn:EndEvent') {
      let task = getShape(shape.target.id);
      taskArray.push({id: task.id, name: task.businessObject.name, lineId: lineId});
    }

  })
  // console.log('taskArray',taskArray)
  return taskArray;


}


// function createArrowSvg() {
//   console.log('=============this is custom createArrowSvg render=================')
//   const marker = svgCreate('marker');
//   const markerend = svgCreate('marker');
//   svgAttr(marker, {
//     id: 'sequenceflow-arrow-normal',
//     viewBox: '0 0 20 20',
//     refX: '11',
//     refY: '10',
//     markerWidth: '10',
//     markerHeight: '10',
//     orient: 'auto',
//   });
//   svgAttr(markerend, {
//     id: 'sequenceflow-arrow-normal-end',
//     viewBox: '0 0 20 20',
//     refX: '11',
//     refY: '10',
//     markerWidth: '10',
//     markerHeight: '10',
//     orient: 'auto-start-reverse',
//   });
//   const path = svgCreate('path');
//   const pathActivities = svgCreate('path');
//   svgAttr(pathActivities, {
//     d: 'M 1 5 L 11 10 L 1 15 Z',
//     style:
//       ' stroke-width: 1px; stroke-linecap: round; stroke-dasharray: 10000, 1; stroke: #fdb039;fill:#fdb039;',
//   });
//
//   svgAttr(path, {
//     d: 'M 1 5 L 11 10 L 1 15 Z',
//     style:
//       ' stroke-width: 1px; stroke-linecap: round; stroke-dasharray: 10000, 1; stroke: #cccccc;fill:#ccc;',
//   });
//   const defs = domQuery('defs');
//   svgAppend(marker, path);
//   svgAppend(defs, marker);
//   svgAppend(markerend, pathActivities);
//   svgAppend(defs, markerend);
// }





