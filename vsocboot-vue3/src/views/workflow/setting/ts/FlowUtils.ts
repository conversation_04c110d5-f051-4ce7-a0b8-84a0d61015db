import {ref} from "vue";
import { useI18n } from '/@/hooks/web/useI18n';

const { t } = useI18n();
export const stepList =  [
  {name: 'Information filling', title: 'Information filling                       '},
  {name: 'Flow customized', title: 'Flow customized'},
  {name: 'Permission Configuration', title: 'Permission Configuration'}
];

export const stepSettingList =  [
  {name: 'Information filling', title: 'Information filling'},
  {name: 'Form customization', title: 'Form customization'},
  {name: 'Flow customization', title: 'Flow customization'},
  {name: 'Permission Configuration', title: 'Permission Configuration'}
];


export const tabMenuNameList = [
  {id: '1', title: t('routes.workflowSetting.t1')},
  {id: '2', title: t('routes.workflowSetting.t2')},
  {id: '3', title: t('routes.workflowSetting.t3')},
  {id: '4', title: t('routes.workflowSetting.t4')},
  {id: '5', title: t('routes.workflowSetting.t6')},
];
export const tabMenuNameTenantList = [
  {id: '4', title: t('routes.workflowSetting.t5')},
  {id: '5', title: t('routes.workflowSetting.t6')},
];
