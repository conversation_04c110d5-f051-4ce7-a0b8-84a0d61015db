import {buildUUID} from '/@/utils/uuid';


const ComponentItems = [
  {

    title: 'Layout',
    value: 'layout',
    column: [
      {
        field: "",
        name: "Grid",
        //icon: 'appstore-outlined',
        icon: 'ax-bjq-grid',
        component: 'row',
        type: 'row',
        gutter: 8,
        col: [12, 12],
        align: 'top',
        justify: 'start',
        list: [{
          list: []
        }, {
          list: []
        }],

      }
    ]
  },
  {

    title: 'Text components',
    value: 'baseComponents',
    column: [
      {
        name: "Text box",
        label: 'Input',
        type: 'input',
        //icon: 'code',
        icon: 'ax-bjq-textbox',
        field: '',
        component: 'Input',
        colProps: {
          span: 24,
        },
        componentProps: {
          disabled: true,
          placeholder: 'Please enter value'
        },
        rules: [],
      },
      {
        name: "Number",
        label: 'InputNumber',
        type: 'inputNumber',
        // icon: 'field-binary-outlined',
        icon: 'ax-bjq-numberbox',
        field: '',
        component: 'InputNumber',
        colProps: {
          span: 24,
        },
        componentProps: {
          disabled: true,
          placeholder: 'Please enter value'
        },
        rules: [],
      },
      {
        name: "Text field",
        label: 'TextArea',
        type: 'textArea',
        // icon: 'container',
        icon: 'ax-bjq-textfield',
        field: '',
        component: 'InputTextArea',
        componentProps: {
          placeholder: 'Please enter value'
        },
        rules: [],
      },

      {
        name: "Check box",
        label: 'CheckboxGroup',
        type: 'checkboxGroup',
        // icon: 'check-square',
        icon: 'ax-bjq-checkbox',
        field: '',
        component: 'CheckboxGroup',
        componentProps: {
          width: '100%',
          type: "1",//1自定义，2字典
          staticData: [{
            label: 'Option1',
            value: buildUUID(),
          }, {
            label: 'Option2',
            value: buildUUID(),
          }]
        },
        layout: "radio_inline",
        items: []
      },

      {
        name: "Radio box",
        label: 'RadioGroup',
        type: 'radioGroup',
        // icon: 'check-circle',
        icon: 'ax-bjq-radiobox',
        field: '',
        component: 'RadioGroup',
        componentProps: {
          width: '100%',
          type: "1",//1自定义，2字典
          staticData: [{
            label: 'Option1',
            value: buildUUID(),
          }, {
            label: 'Option2',
            value: buildUUID(),
          }]
        },
        layout: "radio_inline",
        items: []
      },
      {
        name: "Drop-down box",
        label: 'Select',
        type: 'select',
        // icon: 'profile',
        icon: 'ax-bjq-dropdownbox',
        field: '',
        component: 'Select',
        componentProps: {
          width: '100%',
          placeholder: 'Please select',
          multiple: false,//是否多选
          showSearch: false,//是否可搜索
          type: "1",//1自定义，2字典
          staticData: [{
            label: 'Option1',
            value: buildUUID(),
          }, {
            label: 'Option2',
            value: buildUUID(),
          }]
        },
        items: []
      },
      {
        name: "Time",
        label: 'TimePicker',
        // icon: 'clock-circle',
        icon: 'ax-bjq-timebox',
        type: 'timePicker',
        field: '',
        component: 'TimePicker',
        colProps: {
          span: 24,
        },
        rules: [],
        componentProps: {
          showTime: true,
          range: false,
          valueFormat: "HH:mm:ss",
          placeholder:"",
          placeholderend:"",
          width: '100%',
          getPopupContainer: () => document.body,
        },
      },
      {
        name: "Date",
        label: 'DatePicker',
        // icon: 'clock-circle',
        icon: 'ax-bjq-datebox',
        type: 'datePicker',
        field: '',
        component: 'DatePicker',
        colProps: {
          span: 24,
        },
        rules: [],
        componentProps: {
          showTime: false,
          type: "3",
          format:"YYYY-MM-DD",
          valueFormat: "yyyy-MM-dd",
          placeholder:"",
          placeholderend:"",
          width: '100%',
        },
        option:[
          {text:"Year",value:"1",format:"YYYY",valueFormat:"yyyy"},
          {text:"Year-Month",value:"2",format:"YYYY-MM",valueFormat:"yyyy-MM"},
          {text:"Date(in days)",value:"3",format:"YYYY-MM-DD",valueFormat:"yyyy-MM-dd"},
          {text:"Date (in seconds)",value:"4",format:"YYYY-MM-DD HH:mm:ss",valueFormat:"yyyy-MM-dd HH:mm:ss"},
          {text:"Date range(in days)",value:"5",format:"YYYY-MM-DD",valueFormat:"yyyy-MM-dd"},
          {text:"Date range(in seconds)",value:"6",format:"YYYY-MM-DD HH:mm:ss",valueFormat:"yyyy-MM-dd HH:mm:ss"}
        ],
      },
      {
        name: "Switch",
        label: 'Switch',
        // icon: 'clock-circle',
        icon: 'ax-bjq-switch',
        type: 'switch',
        field: '',
        component: 'Switch',
        colProps: {
          span: 24,
        },
        rules: [],
        componentProps: {
          checkedValue:"Y",
          unCheckedValue:"N",
        },
      },
      {
        name: "Boundary",
        label: 'Hr',
        // icon: 'clock-circle',
        icon: 'ax-bjq-separator',
        type: 'hr',
        field: '',
        component: 'Hr',
        colProps: {
          span: 24,
        },
        rules: [],
        componentProps: {
          margin:20,
          width:"100%",
        },
      },
      {
        name: "Text",
        label: 'Text',
        // icon: 'clock-circle',
        icon: 'ax-bjq-text',
        type: 'text',
        field: '',
        component: 'Text',
        colProps: {
          span: 24,
        },
        rules: [],
        componentProps: {
          content:"this is content",
          align:"left",
          fontSize:16,
          lineHeight:null,
          width:"100%",
          italic:false,//斜体 font-style: italic;
          bold:false,//加粗 font-weight: bold;
          underline:false,//下划线 text-decoration: underline;
          lineThrough:false,//删除线 text-decoration: line-through; 删除线和下划线只能存在一个
        },
      },
      // {
      //   name: "Phone",
      //   label: 'Phone',
      //   icon: 'clock-circle',
      //   type: 'phone',
      //   field: '',
      //   component: 'Phone',
      //   colProps: {
      //     span: 24,
      //   },
      //   rules: [],
      //   componentProps: {
      //     width:"100%",
      //     code:false,
      //     codeValue:"",
      //     placeholder:"",
      //   },
      // },
      // {
      //   name: "Email",
      //   label: 'Email',
      //   icon: 'clock-circle',
      //   type: 'email',
      //   field: '',
      //   component: 'Email',
      //   colProps: {
      //     span: 24,
      //   },
      //   rules: [],
      //   componentProps: {
      //     width:"100%",
      //     code:false,
      //     codeValue:"",
      //     placeholder:"",
      //   },
      // },
      {
        name: "Upload files",
        label: 'Upload',
        // icon: 'clock-circle',
        icon: 'ax-bjq-uploadfile',
        type: 'upload',
        field: '',
        component: 'Upload',
        colProps: {
          span: 24,
        },
        rules: [],
        componentProps: {
          width:"100%",
          text:"Click to upload file",
          maxCount:1,
          multiple:false,
          dragger:false,
        },
      },
      /*{
        name: "自动编号",
        label: 'AutoNumber',
        icon: 'clock-circle',
        type: 'autoNumber',
        field: '',
        component: 'AutoNumber',
        colProps: {
          span: 24,
        },
        rules: [],
        componentProps: {
          width:"100%",
          dataList:[
            { type:'number',
              value:"自然数编号，不重置",
              label:"编号",
              base:{
                isUpd:false,
                type:"1",
                startVal:'1',
                startDisabled:false,
                reset:"1",
                digit:4,
                autoAdd:false,
              }
            }
          ],
          dateFormat:[
            {label:'yyyyMMdd',value:'YYYYMMDD'},
            {label:'yyyyMM',value:'YYYYMM'},
            {label:'MMdd',value:'MMDD'},
            {label:'yyyy',value:'YYYY'}
          ],

        },
      },*/


    ]
  },
  {

    title: 'Reference component',
    value: 'relation',
    column: [
      {
        name: "List",
        //icon: 'table',
        icon: 'ax-bjq-list',
        label: 'Reference list',
        field: '',//相当于datasource
        component: 'table',
        type: 'table',
        tableName : '',//默认自定义
        columns : [],
        checkColumns : [],//选中的列
        dataSource : [],
        componentProps: {
          width: '100%',

        },

      },
      {
        name: "Investigation",
        icon: 'ax-bjq-investgation',
        label: 'Investigation',
        field: 'investigation',//相当于datasource
        component: 'investigation',
        type: 'investigation',
        dataSource : [],
        componentProps: {
          width: '100%',
        },

      },{
        name: "Risk Center",
        icon: 'ax-bjq-investgation',
        label: 'Risk Center',
        field: 'riskCenter',//相当于datasource
        component: 'riskCenter',
        type: 'riskCenter',
        dataSource : [],
        componentProps: {
          width: '100%',
        },

      }
    ]
  },
];


const formConfig = {
  marginTop: 16,
  marginBottom: 16,
  marginRight: 16,
  marginLeft: 16,
  dimensions: 'big',
  popWidth: 1200,
  labelWidth: 100,
  labelAlign: 'top'
}



export const SPLIT_STR = '_';
export {ComponentItems, formConfig}
