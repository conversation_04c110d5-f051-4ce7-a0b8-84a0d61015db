import {BasicColumn} from "/@/components/Table";
import {useI18n} from "/@/hooks/web/useI18n";
import {ref,watch} from "vue";
const { t } = useI18n();
const investigationColumn: BasicColumn[] = [
  {
    title: t('routes.investigation.investigation'),
    dataIndex: 'investigation'
  },
  {
    title: t('routes.investigation.riskEvents'),
    dataIndex: 'riskEvents'
  },
  {
    title: t('routes.investigation.creator'),
    dataIndex: 'creator',
    slots: { customRender: 'userInfo' }
  },
  // {
  //   title: t('routes.investigation.members'),
  //   dataIndex: 'members',
  //   slots: { customRender: 'members' }
  // },
  {
    title: t('routes.investigation.status'),
    dataIndex: 'status',
    className:'table-cell-text-red',
  },
  {
    title: t('routes.investigation.creationTime'),
    dataIndex: 'creationTime'
  },
  {
    title: t('routes.investigation.severity'),
    dataIndex: 'severity',
    className:'table-cell-text-red',
    slots: { customRender: 'severity' }
  },
  // {
  //   title: t('routes.investigation.priority'),
  //   dataIndex: 'priority'
  // },
];
export const tableColumns = ref([]);

export const tableOption = [
 /* {value:"custom",label:"Custom"},//自定义*/
  {value:"investigation",label:"Investigation"}
]
const tableMap = {
  investigation : investigationColumn
}


export function changeTableName(value){
  if(value == 'custom'){
    tableColumns.value = [];
  }else{
    tableColumns.value = tableMap[value];
  }

}

export function getColumns(columns){
  let columnsArr = [];
  columns.forEach(item=>{
    columnsArr.push(tableColumns.value.find(d=>d.dataIndex == item));
  })
  return columnsArr;
}
