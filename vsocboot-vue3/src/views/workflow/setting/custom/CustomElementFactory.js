import BpmnElementFactory from 'bpmn-js/lib/features/modeling/ElementFactory';
import inherits from 'inherits';
import {getBusinessObject, getDi, is} from "bpmn-js/lib/util/ModelUtil";
import {isExpanded} from "bpmn-js/lib/util/DiUtil";

export default function CustomElementFactory(bpmnFactory, moddle, translate) {
BpmnElementFactory.call(this, bpmnFactory, moddle, translate);

var self = this;
// this.create = function(elementType, attrs) {
// var type = attrs.type;
// // ...
// // 排除自定义类型，exaple中自定义的图标类型是custom:triangle / custom:circle
// if (/^custom:/.test(type)) {
// // ...
// }
// // 真实创建在这里✨✨✨
// return self.createBpmnElement(elementType, attrs);
// }
}
inherits(CustomElementFactory, BpmnElementFactory);

CustomElementFactory.$inject = [
'bpmnFactory',
'moddle',
'translate'
];
// 重写bpmn中BpmnElementFactory的一些方法
CustomElementFactory.prototype.getDefaultSize = function(element, di) {
  var bo = getBusinessObject(element);
  di = di || getDi(element);

  if (is(bo, 'bpmn:SubProcess')) {
    if (isExpanded(bo, di)) {
      return { width: 350, height: 200 };
    } else {
      return { width: 100, height: 80 };
    }
  }

  if (is(bo, 'bpmn:Task')) {
    return { width: 100, height: 68 };
  }

  if (is(bo, 'bpmn:Gateway')) {
    return { width: 50, height: 64 };
  }

  if (is(bo, 'bpmn:Event')) {
    return { width: 36, height: 70 };
  }

  if (is(bo, 'bpmn:Participant')) {
    if (isExpanded(bo, di)) {
      return { width: 600, height: 250 };
    } else {
      return { width: 400, height: 60 };
    }
  }

  if (is(bo, 'bpmn:Lane')) {
    return { width: 400, height: 100 };
  }

  if (is(bo, 'bpmn:DataObjectReference')) {
    return { width: 36, height: 50 };
  }

  if (is(bo, 'bpmn:DataStoreReference')) {
    return { width: 50, height: 50 };
  }

  if (is(bo, 'bpmn:TextAnnotation')) {
    return { width: 200, height: 30 };
  }

  if (is(bo, 'bpmn:Group')) {
    return { width: 300, height: 300 };
  }

  return { width: 100, height: 80 };
};
