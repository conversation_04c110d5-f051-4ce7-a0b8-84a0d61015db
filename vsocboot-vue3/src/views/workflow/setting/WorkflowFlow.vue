<template>
  <div class="flex-v flow_design_wrapper" style="height: 100%">
    <div class="flex-h flex-wrap padding8-16 flex-content-left">
      <div class="font16 fcolor return-wraper">
        <span @click="goOut" style="cursor: pointer;">
          <Icon icon="ant-design:left-outlined" :size="16" style="margin-right: 5px;"></Icon>
           Back
        </span>
      </div>

    </div>
    <div class="flex-v flex-align-center" style="margin-top:-30px">
      <div class="steps_wraper flex-v flex-align-center">
        <div class="steps flex-h flex-wrap">
          <template v-for="(item,index) in stepList">
            <div :class="['font13','fcolor3','step','finish']" v-if="index < current">
              <img :src="okPng">
            </div>
            <div :class="['step',{current : index == current}]" v-else>
              {{ item.name }}
            </div>
          </template>
        </div>
        <div class="flex-h flex-content-left stepInfo">
          <div class="title font20 fcolor">Step{{ current + 1 }}：{{ stepList[current].name }}
          </div>
          <div class="flex-h step-btn">
            <a-button type="text" class="btn-back" @click="doBack" v-if="current > 0">
              <Icon icon="ant-design:left-outlined" :size="13">
              </Icon>
              Back
            </a-button>
            <a-button type="primary" class="font13 fcolor" @click="doNext" v-if="current < 2">
              Next
              <Icon icon="ant-design:right-outlined" :size="13">
              </Icon>
            </a-button>
            <a-button type="primary" class="font13 fcolor" @click="doSubmit"
                      v-if="(current == 2)">
              Comfirm
            </a-button>

          </div>
        </div>
      </div>
    </div>
    <!-- 步骤内容-->
    <div class="flex-h flex-1 bg2 mt16">
      <div class="flex flex-h flex-1 flex-content-center ">
        <FlowInformation v-if="current==0" ref="flowInformation"></FlowInformation>
        <FlowDesign v-if="current==1" ref="flowDesign"></FlowDesign>
        <FlowUser v-if="current==2" ref="flowUser"></FlowUser>
      </div>
    </div>
  </div>
</template>

<script lang="ts" name="WorkflowFlow" setup>
import {useRouter} from 'vue-router';
import {nextTick, onMounted, ref, unref} from "vue";
import okPng from "/@/assets/images/step/ok.png";
import {stepList} from './ts/FlowUtils';
import FlowInformation from "/@/views/workflow/setting/component/FlowInformation.vue";
import FlowDesign from "/@/views/workflow/setting/component/FlowDesign.vue";
import FlowUser from "/@/views/workflow/setting/component/FlowUser.vue";
import {saveOrUpdate} from "/@/views/workflow/setting/WorkflowInstance.api";
import {queryById} from "/@/views/workflow/setting/WorkflowInstance.api";
import {getTenantId, getTenantMode} from "/@/utils/auth";

const router = useRouter();
const {currentRoute} = useRouter();
const current = ref(0);//step current number
const flowInformation = ref();
const flowDesign = ref();
const flowUser = ref();
const flowInfo = ref({});
let designData = {};
let flowUserData = {};
let instId = '';

function goOut() {
  router.push({path: "/workflow/setting/WorkFlowSettingList", query: {activeKey: '2'}});
}

onMounted(() => {
  const param = currentRoute.value.query;
  if(param.id){
    showUpdateData(param.id);
  }
})
function showUpdateData(id){
  instId = id;
  queryById({id}).then((result)=>{
    console.log('result==',result)
    const {templateId,flowName,flowDesc,bpmnXml,processId} = result;
    flowInfo.value = { templateId,flowName,flowDesc};
    flowInformation.value.setFormData(flowInfo.value);
    let ruleConfig = JSON.parse(result.ruleConfig);
    designData = {ruleConfig,bpmnXml,processId};
    flowUserData = ruleConfig.flowUserData || {};
  })
}

function doSubmit() {
  let param = {...flowInfo.value};
  flowUserData = flowUser.value.getFlowUser();
  designData.ruleConfig.flowUserData = flowUserData;
  param.bpmnXml = designData.bpmnXml;
  param.ruleConfig = JSON.stringify(designData.ruleConfig);
  param.processId = designData.processId;
  param.isNow = 1;
  console.log('params-----',param)
  if(getTenantMode()){//租户模式下
    if(getTenantId()){//租户编辑
      param.tenantid = getTenantId();
      param.tenantType = 2;
    }else if( param.tenantType == 1){//MSSP的tenantId为空
      param.tenantid = '';
    }

  }
  if(instId!=''){
    param.id = instId;
    param.status = 0;
  }

  saveOrUpdate(param).then((res) => {
    console.log('saveOrUpdate-------------->res:', res)
    goOut();
  })
}

async function doBack() {
  let validateValue = true;
  if (current.value == 1) {
   await saveFlowDesign().then((value) => {
      console.log('doBack', value)
      validateValue = value;
    });
  }else if(current.value == 2){
    flowUserData = flowUser.value.getFlowUser();
    console.log('doBack flowUserData', flowUserData)
  }
  if (!validateValue) {
    return ;
  }
  current.value = current.value - 1;
  nextTick(() => {
    if (current.value == 0) {
      flowInformation.value.setFormData(flowInfo.value);
    } else if (current.value == 1) {
      flowDesign.value.setFlowInfo(flowInfo.value);
      if (designData.bpmnXml && designData.bpmnXml != "") {
        flowDesign.value.showUpdate(designData);
      }
    }
  })

}

async function doNext() {
  if (current.value == 0) {
    let values = await flowInformation.value.validate();
    flowInfo.value = values;
    console.log('values', values)
    current.value = current.value + 1;
    nextTick(() => {
      flowDesign.value.setFlowInfo(flowInfo.value);
      if (designData.bpmnXml && designData.bpmnXml != "") {
        flowDesign.value.showUpdate(designData);
      }
    })
  } else if (current.value == 1) {
    nextTick(() => {
       saveFlowDesign().then((value) => {
        console.log('doNext', value)
        if (value) {
          current.value = current.value + 1;
          nextTick(()=>{
            flowUser.value.setFlowUser(flowUserData);
          })
        }
      });
    })


  }

}

async function saveFlowDesign() {
  let flag = true;
  await flowDesign.value.doSave().then((data) => {
    console.log('values', data)
    if (data == false) {
      flag = false;
    } else {
      designData = unref(data);
      console.log('designData', designData)
    }


  });
  return flag;
}
</script>


<style lang="less" scoped>

@import './less/step.less';
@import './less/flow.less';
@import './less/bpmn.less';

</style>




