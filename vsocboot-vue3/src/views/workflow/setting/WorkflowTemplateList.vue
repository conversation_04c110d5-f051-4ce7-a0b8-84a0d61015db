<template>
  <div class="search_transparent">
    <!--引用表格-->
    <BasicTable @register="registerTable" :rowSelection="rowSelection" :isSearch="isSearch">
      <!--插槽:table标题-->
      <template #form-formFooter>
        <a-dropdown v-if="selectedRowKeys.length > 0">
          <template #overlay>
            <a-menu>
              <a-menu-item key="1" @click="batchHandleDelete">
                <Icon icon="ant-design:delete-outlined"></Icon>
                {{ t('common.delText') }}
              </a-menu-item>
            </a-menu>
          </template>
          <a-button>{{ t('common.batch') }}
             <span class="soc ax-com-Arrow-down"></span>
          </a-button>
        </a-dropdown>
<!--        <a-button :class="{'btn-checked': isSearch == true}" type="text" @click="isSearch=!isSearch"-->
<!--                  preIcon="ant-design:filter-outlined"> {{ t('common.filter') }}-->
<!--        </a-button>-->
        <a-button type="primary" @click="handleAdd"  >
          <span class="soc ax-com-Add ax-icon"></span>
          {{ t('common.add') }}
        </a-button>

      </template>
      <!--操作栏-->
      <template #action="{ record }">
        <TableAction :actions="getTableAction(record)"/>
      </template>
    </BasicTable>

    <!-- 表单区域 -->

    <WorkflowTemplatDesignModal @register="designModal"
                                @success="handleSuccess"></WorkflowTemplatDesignModal>
  </div>
</template>

<script lang="ts" name="workflow-workflowTemplate" setup>
import {ref} from 'vue';
import {BasicTable, TableAction} from '/@/components/Table';

import {useModal} from '/@/components/Modal';
import WorkflowTemplatDesignModal
  from "/@/views/workflow/template/modules/WorkflowTemplatDesignModal.vue";
import {useListPage} from '/@/hooks/system/useListPage'
import {columns, searchFormSchema} from './WorkflowTemplate.data';
import {batchDelete, deleteOne, getExportUrl, getImportUrl, list} from './WorkflowTemplate.api';
import {useI18n} from "/@/hooks/web/useI18n";
import {formLayout} from '/@/settings/designSetting';
import {usePermission} from "/@/hooks/web/usePermission";
import {queryInstList} from "/@/views/workflow/apply/WorkflowApply.api";
import {useMessage} from "/@/hooks/web/useMessage";
import {useRoute, useRouter} from "vue-router";

const {createMessage} = useMessage();
const {hasPermission} = usePermission();
const {t} = useI18n();
const isSearch = ref<boolean>(true);
const route = useRoute();
const router = useRouter();
//注册model

const [designModal, {openModal: openDesignModal}] = useModal();
//注册table数据
const {prefixCls, tableContext, onExportXls, onImportXls} = useListPage({
  tableProps: {
    searchInfo: {isNow: 1, isDel: 0},
    title: 'Workflow Template',
    api: list,
    columns,
    canResize: false,
    formConfig: {
      labelWidth: 140,
      schemas: searchFormSchema,
      autoSubmitOnEnter: true,
      showAdvancedButton: true,
      layout: formLayout,
      baseColProps:{
        lg: 12, // ≥992px
        xl: 4, // ≥1200px
        xxl: 4, // ≥1600px
      },
    },
    actionColumn: {
      width: 180,
    },
  },

  exportConfig: {
    name: "Workflow Template",
    url: getExportUrl,
  },
  importConfig: {
    url: getImportUrl
  },
})

const [registerTable, {reload}, {rowSelection, selectedRowKeys}] = tableContext

/**
 * 新增事件
 */
function handleAdd() {
  // openModal(true, {
  //   isUpdate: false,
  //   showFooter: true,
  // });
  router.push("/workflow/setting/modules/WorkflowTemplatDesign");
}



function handleDesign(record: Recordable) {
  // openDesignModal(true, {
  //   record
  // });
  router.push({path: "/workflow/setting/modules/WorkflowTemplatDesign", query: {id: record.id}});
}
/**
 * 删除事件
 */
async function handleDelete(record) {
  await queryInstList({templateId: record.id}, (result) => {
    console.log('result:', result)
    if (result.length > 0) {
      createMessage.warning('Template 【'+  record.templateName + '】In use and cannot be deleted！');
      return false;
    }
    deleteOne({id: record.id}, reload);
  })
}



/**
 * 批量删除事件
 */
/*async function batchHandleDelete() {
  let rows = rowSelection.selectedRows;
  for(let i in rows){
    handleDelete(rows[i]);
  }
  reload();
}*/
async function batchHandleDelete() {
  await batchDelete({ids: selectedRowKeys.value}, reload);
}


/**
 * 成功回调
 */
function handleSuccess({isUpdate, values}) {
  reload();
}

/**
 * 操作栏
 */
function getTableAction(record) {
  return [
    {
      label: t('common.viewText'),
      onClick: handleDesign.bind(null, record),
      ifShow: () => {
        return hasPermission('workflowTemplate:view');
      }
    },
    // {
    //   label: t('routes.workflow.design'),
    //   onClick: handleDesign.bind(null, record),
    //   ifShow: () => {
    //     return hasPermission('workflowTemplate:design');
    //   }
    // },
    {
      label: t('common.delText'),
      onClick: handleDelete.bind(null, record),
      // popConfirm: {
      //   title: t('common.delConfirmText'),
      //   confirm: handleDelete.bind(null, record),
      // },
      ifShow: () => {
        return hasPermission('workflowTemplate:delete');
      }
    }
  ]
}

</script>
<style scoped>

</style>
