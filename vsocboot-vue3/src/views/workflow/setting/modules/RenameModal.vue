<template>
  <a-modal v-model:visible="visible" :destroyOnClose="true"
           @ok="handleOk"
           title="Rename" >
  <a-row style="padding: 0 12px;margin-bottom: 10px">
    <a-input v-model:value="rename"/>
  </a-row>


  </a-modal>
</template>

<script lang="ts" setup>
import {defineEmits, defineExpose, ref, watch} from "vue";

const visible = ref(false);
const rename = ref('');
let value = '';

const emits = defineEmits(["ok"])


function handleOk() {
  // if(value != rename.value) {
    emits('ok', rename.value);
  // }

  handleCancel();
}

function handleCancel() {
  visible.value = false;

}

function show(data) {
  visible.value = true;
  value = data;
  rename.value = data;
}


defineExpose({
  show
});
</script>

<style lang="less" scoped>


</style>

