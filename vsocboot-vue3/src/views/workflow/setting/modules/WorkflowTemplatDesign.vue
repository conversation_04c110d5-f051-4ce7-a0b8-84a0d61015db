<template>
  <div class="design-template-modal" style="height:100%;">
    <div>
      <div class="font16 fcolor return-wraper" style="margin: 10px 0px 10px 10px;">
        <span  @click="goOut" style="cursor: pointer;">
          <Icon icon="ant-design:left-outlined" :size="16" style="margin-right: 5px;"></Icon>
           Back
        </span>
      </div>
      <a-form ref="formRef"  autocomplete="off"  class="antd-modal-form" name="basic" :labelCol="labelCol" :wrapperCol="wrapperCol" :model="formData" :layout="formLayout" style="padding-left: 25px;">
        <a-row :gutter="8">
          <a-col :span="4">
            <a-form-item label="Form Name" :rules="[{ required: true, message: t('common.inputText') },{ ...rules.limitationCheckRule(128)[0]}]" prop="templateName">
              <a-input
                placeholder="Please enter Form Name"
                v-model:value="formData.templateName"   />
            </a-form-item>
          </a-col>
          <!-- 系统为租户模式-->
          <template v-if="getTenantMode()">
            <!--  运维人员可见，租户不可见 start-->
            <a-col :span="2" v-if="!getTenantId()">
              <a-form-item label="User type" required >
                <a-radio-group v-model:value="formData.tenantType" @change="changeTenantType">
                  <a-radio-button value="1">MSSP</a-radio-button>
                  <a-radio-button value="2">Tenant</a-radio-button>
                </a-radio-group>
              </a-form-item>
            </a-col>
            <a-col :span="4" v-if="formData.tenantType == '2'"   >
              <a-form-item label="Template tenant"  required>
                <j-dict-select-tag  @change="changeFlowTypeCode" v-model:value="formData.tenantId"   dict-code="tenantActiveDict"></j-dict-select-tag>
              </a-form-item>
            </a-col>
            <!--  运维人员可见，租户不可见 end-->
          </template>

          <a-col :span="4">
            <a-form-item label="Flow Type" :rules="[{ required: true, message: t('common.inputText') }]" prop="templateType">
             <j-dict-select-tag  v-model:value="formData.templateType" placeholder="Please enter Template Type" :dict-code="flowTypeCode"></j-dict-select-tag>
            </a-form-item>
          </a-col>

          <a-col :span="3">
            <a-form-item label="Form Deseription" prop="templateDesc" :rules="[{ ...rules.limitationCheckRule(1000)[0]}]">
              <a-input
                v-model:value="formData.templateDesc"
                placeholder="Please enter Form Deseription"/>
            </a-form-item>
          </a-col>
          <Col :span="3">
            <a-form-item label="  ">
              <a-button type="text" @click="preView">
<!--                <a-icon type="eye"/>-->
                <span>Preview</span></a-button>
              <a-button @click="save" :disabled="disabled" type="primary">
<!--                <a-icon type="save"/>-->
                <span>Save</span></a-button>
            </a-form-item>
          </Col>
        </a-row>
      </a-form>
    </div>

    <div :class="['flex','flex-h','template-design_wraper']" style="background-color: #1A1B1F;">
      <!-- 组件-->
      <div class="flex flex-v padding16 components_wraper">
        <div class="font14 fcolor list-item-title" style="margin-bottom: 22px;">Components
          <Icon icon="ant-design:table-outlined" @click="showModel = !showModel" style="cursor: pointer;float: right;margin-top: 4px;"></Icon>
        </div>
        <div :key="index" v-for="(data,index) in ComponentItems" class="list-items">
          <div class="font12 fcolor list-item-title">{{ data.title }}</div>
          <template v-if="showModel">
            <div v-for="(liItem,index2) in data.column" :key="index2" :id="index + '-' + index2"
                 @dragstart="dragstart" draggable="true" @dragend="dragEnd" class="list-item">
              <div class="font13 ">
<!--                <a-icon :type="liItem.icon"/>-->
                <span :class="'soc '+liItem.icon"></span>
                <span>{{ liItem.name }}</span>
              </div>
            </div>
          </template>
          <template v-else>
            <a-row :gutter="[4,4]" style="text-align: center;">
              <a-col :span="8" class="icon-small-show-model font13 list-item"
                     v-for="(liItem,index2) in data.column" :key="index2"
                     @dragstart="dragstart" draggable="true" @dragend="dragEnd"
                     :id="index + '-' + index2">
                <span :class="'soc '+liItem.icon" :title="liItem.name"></span>
<!--                <a-icon :type="liItem.icon" :title="liItem.name"/>-->
              </a-col>
            </a-row>
          </template>
        </div>
      </div>
      <!-- 页面内容-->
      <div class="flex flex-v flex-1 template_wraper" style="padding:0px 120px;">
        <!-- 操作栏-->
        <div class="flex flex-h template-tool font16" style="justify-content: flex-start;padding: 15.5px 0px;">
          Form editing area
        </div>
        <!-- 模板-->
        <div class="flex flex-v flex-1" @drop.stop.prevent="drop" @dragover.prevent style="background-color: #06080C;">

          <a-form layout="vertical">
            <TemplateComponentList v-model:value="templateDesignData"
                                   ref="componentRef" @show="rowComponentField" @dropSub="dropSub"/>
          </a-form>
        </div>
      </div>
      <!-- 属性-->
      <div class="flex flex-v padding16 property_wraper">
        <div class="font14 fcolor list-item-title" style="margin-bottom: 22px;">Component setting
        </div>
        <FieldProperty v-model:value="fieldData"   ref="fieldPropertyRef" />

      </div>
    </div>

    <WorkflowTemplatePreviewModal ref="previewModal"/>
  </div>
</template>

<script lang="ts" name="WorkflowTemplateDesignModal" setup>
import WorkflowTemplatePreviewModal
  from "/@/views/workflow/setting/modules/WorkflowTemplatePreviewModal.vue";
import FieldProperty from "/@/views/workflow/setting/component/FieldProperty.vue";
import TemplateComponentList from "/@/views/workflow/setting/component/TemplateComponentList.vue";
import {ComponentItems, SPLIT_STR} from '/@/views/workflow/setting/ts/template.api';
import {nextTick, provide, reactive, ref, toRaw, watch} from "vue";
import {Col} from "ant-design-vue";
import {useModalInner} from "/@/components/Modal";
import {getTimeStr} from '/@/views/workflow/setting/ts/TemplateUtils'
import {useRefs} from "/@/hooks/core/useRefs";
import {useRoute, useRouter} from 'vue-router';
import {
  queryDesign,
  queryTemplate,
  saveOrUpdate
} from "/@/views/workflow/setting/WorkflowTemplate.api";
import {formLayout} from "/@/settings/designSetting";
import {useI18n} from "/@/hooks/web/useI18n";
import JDictSelectTag from "/@/components/Form/src/jeecg/components/JDictSelectTag.vue";
import {rules} from "/@/utils/helper/validator";
import {useMessage} from "/@/hooks/web/useMessage";
import {getTenantId, getTenantMode} from "/@/utils/auth";

const [refs, setRefs] = useRefs();
const { createMessage } = useMessage();
const activeKey = ref('2');
const currentClickId = ref('');
const currentItem = ref({});
const componentRef = ref();
const previewModal = ref();
const disabled = ref(false);
const emit = defineEmits(['success']);
const router = useRouter();
const route = useRoute();
const labelCol = ref<any>({ xs: { span: 24 }, sm: { span: 24 } });
const wrapperCol = ref<any>({ xs: { span: 24 }, sm: { span: 24 } });
const {t} = useI18n();
const formRef = ref();
const formData = reactive({
  id: '',
  templateName: '',
  templateType: '',
  templateDesc: '',
  designContent: '',
  tenantId: '',
  tenantType: '1',
  designConfig: ''
});
let showModel = ref(true);
const defaultFlowTypeCode = 'tbl_workflow_type_info,type_name,id,is_delete=0';
const flowTypeCode = ref('');
flowTypeCode.value = defaultFlowTypeCode;
/**
 * form property in the templateDesignData.formPoperty
 * field property in the templateDesignData.list
 */
const templateDesignData = ref({
  formPoperty: {},
  list: []
});
const fieldData = ref();
const fieldPropertyRef = ref();
const templateData = ref({});

//子孙通信
provide('clickId',currentClickId);
provide('clickItem',currentItem);
//自己调试用，上线前注释掉
watch(templateDesignData.value, () => {
  console.log('templateDesignData change', templateDesignData.value)
}, {deep: true, immediate: true})

//change click item，field content will change
watch(currentItem, () => {
  // console.log('currentItem change', currentItem.value)
  rowComponentField();
}, {deep: true })

function dropSub(p,j,data){

  console.log('parent:',p)
  console.log('dropSub:',data)
  console.log(j)
  let list = JSON.parse(JSON.stringify(templateDesignData.value.list));
  list.forEach(item=>{
    if(item.field == p.field){
      console.log(item.list[j])
      item.list[j].list.push(data);
    }
  })
  templateDesignData.value.list = list;
}



//表单赋值
const [registerModal, {setModalProps, closeModal}] = useModalInner(async (data) => {
  console.log('get row data', data.record)
  templateData.value = data.record;
  activeKey.value = '2';
  if(data.record.templateDesignId){
    queryDesign({id:data.record.templateDesignId}).then((result)=>{
      // console.log('get templateDesignId result', result)

      if(result.designContent){
        templateDesignData.value.list = JSON.parse(result.designContent);
      }
      if(result.designConfig){
        templateDesignData.value.formPoperty = JSON.parse(result.designConfig);
      }
    })
  }
});

if (route.query && route.query.id) {
  queryTemplate({id:route.query.id}).then((res)=>{
    console.log(res);
    formData['id'] = res['id'];
    formData['templateName'] = res['templateName'];
    formData['templateDesc'] = res['templateDesc'];
    formData['templateType'] = res['templateType'];
    if(res.designContent){
      templateDesignData.value.list = JSON.parse(res.designContent);
    }
    if(res.designConfig){
      templateDesignData.value.formPoperty = JSON.parse(res.designConfig);
    }
  });
}


/**
 * show field config change
 * @param data
 */
function rowComponentField(){
  activeKey.value = '1';
  fieldData.value = currentItem.value;
  fieldPropertyRef.value.show(currentItem.value);
}
async function save(){
  let str = JSON.stringify(templateDesignData.value.list);
  console.log(str.split("bisTitle"))
  if(str.split("bisTitle").length > 2){
    createMessage.warning('Duplicate Work Flow Title!');
    return false;
  }
  formRef.value
    .validate()
    .then(() => {
      disabled.value = true;
      console.log(formData);
      isAddInvestigation();//如果investigation类型没有组件，自动添加
      let params: any = toRaw(formData);
      console.log(params);
      if(getTenantMode()){//租户模式下
        if(getTenantId()){//租户编辑
          params.tenantid = getTenantId();
          params.tenantType = 2;
        }else if( params.tenantType == 1){//MSSP的tenantId为空
          params.tenantid = '';
        }

      }
      params['designContent'] = JSON.stringify(templateDesignData.value.list);
      params['designConfig'] = JSON.stringify(templateDesignData.value.formPoperty);
      saveOrUpdate(params, params.id?true:false).finally(()=>{
        console.log("finally");
        doCancel();
      });
    })
  /*return;
  await saveDesign({designContent: JSON.stringify(templateDesignData.value.list),
    designConfig:JSON.stringify(templateDesignData.value.formPoperty),
    templateId : templateData.value.id}).then((data)=>{
    emit('success', data);
    doCancel();
  });*/
}
function isAddInvestigation(){
  let str = JSON.stringify(templateDesignData.value.list);
  if(-1 == str.indexOf("investigation_") && formData.templateType == 'Investigation'){
    let item = ComponentItems[2].column.find(sub=>sub.name == 'Investigation');
    item.field = item.component + SPLIT_STR + getTimeStr();
    templateDesignData.value.list.push(item);
  }
}
function preView(){
  previewModal.value.showModal(templateDesignData.value);

}


/**
 * drag start
 * @param ev
 */
function dragstart(ev) {
  let group = ev.target.id.split("-")[0];
  let columnIndex = ev.target.id.split("-")[1];
  let info = {isDrop: true, item: ComponentItems[group].column[columnIndex]};
  //判断当前浏览器是否为火狐浏览器
  let userAgent = navigator.userAgent;
  let ifFirefox = userAgent.indexOf("Firefox");
  if (ifFirefox) {
    ev.dataTransfer.setData("imgInfo", info);
  }
  ev.dataTransfer.setData('Text', JSON.stringify(info));

};

/**
 * mouse drop
 * @param e
 * @param flag
 */
function drop(e) {
  let info = JSON.parse(e.dataTransfer.getData('Text'));
  if (info.isDrop) {
    let item = {...info.item};
    item.field = item.component + SPLIT_STR + getTimeStr();
    templateDesignData.value.list.push(item);
    nextTick(()=>{
      currentClickId.value = item.field;
      currentItem.value = item;
    })
  }

}

/**
 * drag end
 * @param event
 */
function dragEnd(event) {
  event.preventDefault();
  event.stopPropagation();
};

function doCancel(){
  // disabled.value = false;
  // currentClickId.value = '';
  // currentItem.value = null;
  // activeKey.value = '2';
  // templateDesignData.value = {
  //   formPoperty: {},
  //   list: []
  // };
  // for(let i in formData){
  //   formData[i] = null;
  // }
  // formRef.value.resetFields();
  // closeModal();
  router.push('/workflow/setting/WorkFlowSettingList')
}

function goOut(){
  router.push('/workflow/setting/WorkFlowSettingList')
}

/**
 * 根据tenantId过滤flowType
 * @param value
 */
function changeFlowTypeCode(value){
  flowTypeCode.value = 'tbl_workflow_type_info,type_name,id,is_delete=0'+' and tenant_id=' + value;
}

/**
 * 改变租户类型
 * @param value
 */
function changeTenantType(e){
  console.log('changeTenantType',e)
  formData.templateType = '';
  if(e.target.value == 1){
    flowTypeCode.value = defaultFlowTypeCode;
    formData.tenantId = '';
  }
}
</script>


<style lang="less" scoped>
@import "../less/template.less";
@import "../../../../assets/font/iconfont.css";

</style>




