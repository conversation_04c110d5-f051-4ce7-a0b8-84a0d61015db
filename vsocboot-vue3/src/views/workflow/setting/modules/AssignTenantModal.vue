<template>
  <a-modal v-model:visible="visible"   :destroyOnClose="true"
           @ok="handleOk"
           :maskClosable="false"  title="Assign"
          >
    <a-row style="padding: 12px;">
      <a-form layout="vertical" :model="propertyData" style="width: 100%">
        <a-form-item label="User Type" v-if="userTypeShow">
          <a-radio-group v-model:value="checkerData.userType">
            <a-radio-button value="1">MSSP</a-radio-button>
            <a-radio-button value="2">Tenant</a-radio-button>
          </a-radio-group>
        </a-form-item>
        <template v-if="userScopeShow">
          <a-form-item label="User Scope" >
            <a-radio-group v-model:value="checkerData.type">
              <a-radio-button value="1">assigner</a-radio-button>
              <a-radio-button value="3">roles</a-radio-button>
            </a-radio-group>
          </a-form-item>

          <template v-if="checkerData.type == 1">
            <a-form-item label="Assigner" >
              <j-select-user v-model:value="checkerData.users" v-model:params="param"></j-select-user>
            </a-form-item>
          </template>
          <template  v-if="checkerData.type==3">
            <a-form-item label="Candidate roles">
              <j-select-role v-model:value="checkerData.roles" v-model:params="param" @change="roleChange"></j-select-role>
            </a-form-item>
          </template>
        </template>


      </a-form>
    </a-row>
  </a-modal>
</template>

<script lang="ts" setup>

import {defineExpose, ref, watch, defineEmits, computed, inject} from "vue";
 import {propertyData} from "/@/views/workflow/setting/ts/BpmnUtils";
import JSelectUser from "/@/components/Form/src/jeecg/components/JSelectUser.vue";
import JSelectRole from "/@/components/Form/src/jeecg/components/JSelectRole.vue";
import {getTenantId, isAdministrator, isTenant} from "/@/utils/auth";
const emits = defineEmits(["ok"])
const visible = ref(false);
//1mssp内部，2 租户向mssp申请，3 mssp下发 ，4 租户内部工单，6 租户自己发布工单
const ticketType:any = inject('ticketType');
//默认数据
const defaultData = {
  userType: '',
  type : '',//1 用户 2 租户 3 角色
  users :"",
  roles : [],
  isMultiInstance : false,
  sign : '1'
}
//查询条件
const param = ref({
  socTenantId : "",
  tenantType : ""
})
/**
 * 新增User Type的选择
 * 1.MSSP管理员，只允许添加MSSP管理员类型的用户或角色
 * 2.租户，什么也不用选，自动根据申请人选择该节点的处理人
 * 3.租户用户：只允许Assign给租户用户或角色
 *
 * 在MSSP内部工单中，可选User Type只有MSSP管理员
 * 因此无需选择User Type
 */

const checkerData = ref({...defaultData})


//用户类型
const userTypeShow = computed(()=>{
  if(isAdministrator() &&  ticketType.value != '1' &&  ticketType.value != '4' ){
    return true;
  }
  return false;
})
//用户scope
const userScopeShow = computed(()=>{
   if(isTenant()){
     return true;
   }
   else if(isAdministrator() && (checkerData.value.userType == '2' || checkerData.value.userType == '')){
     return false;
   }
   return true;
})






/**
 * userScope 改变
 * @param value
 */
watch(()=>checkerData.value.type,(value)=>{
  console.log('userScope:',value)
  if(value != 2){//不是租户类型，可以选择人员或角色
    if(isTenant()){//根据租户id过滤
      // param.value.socTenantId = getTenantId();
    }else if(isAdministrator() && ticketType.value != '4'){//只能选择管理员
      param.value.tenantType = 1;
    }else if(isAdministrator() && ticketType.value== '4'){//只能选择租户
      param.value.tenantType = 2;
    }
  }

})


function getIsMultiInstance(){
  if(checkerData.value.userType == '2'){//租户
    return true;
  }
  var users = checkerData.value.users;
  if( checkerData.value.roles && checkerData.value.roles.length > 0){
    return true;
  }
  else if(users && Array.isArray(users) && users.length > 1){
    return  true
  }
  else if(users && !Array.isArray(users) && users.split(",").length > 1){
    return true;
  }
  return false;
}





function roleChange(e){
  checkerData.value.roles = e;
}
//保存
function handleOk(){
  if(checkerData.value.userType == '2'){//租户，什么也不用选，自动根据申请人选择该节点的处理人
    checkerData.value.type = '2';
  }
  checkerData.value.isMultiInstance = getIsMultiInstance();
  handleCancel();
  emits('ok',checkerData.value);

}

function handleCancel() {
  visible.value = false;


}
function reset(){
  console.log('-----------reset data:')
  checkerData.value = {...defaultData};

}
//回显
function show(data){
  visible.value = true;
  reset();
  if(data){
    console.log('show data:',data)
    checkerData.value = {...data};
  }else{//初次添加

    if(isAdministrator()){

      if(ticketType.value == 1){  // 在MSSP内部工单中,只选择只有MSSP管理员
        checkerData.value.userType = '1';
      }else if(ticketType.value == 4){//租户内部工单，只有租户用户
        checkerData.value.userType = '3';//租户用户
        param.value.tenantType = 2;
      }


    }
  }


}



defineExpose({
  show,

});
</script>

<style lang="less" scoped>


</style>

