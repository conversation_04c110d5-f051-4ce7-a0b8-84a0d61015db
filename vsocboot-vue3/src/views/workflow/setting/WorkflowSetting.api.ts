import {defHttp} from '/@/utils/http/axios';
import {Modal} from 'ant-design-vue';
import {useI18n} from "/@/hooks/web/useI18n";
const {t} = useI18n();
enum Api {
  list = '/workflow/workflowSetting/list',
  save='/workflow/workflowSetting/add',
  edit='/workflow/workflowSetting/edit',
  deleteOne = '/workflow/workflowSetting/delete',
  deleteBatch = '/workflow/workflowSetting/deleteBatch',
  queryById='/workflow/workflowSetting/queryById',
  publish='/workflow/workflowSetting/publish',
  batchPublish='/workflow/workflowSetting/batchPublish',
}

/**
 * 发布和取消发布
 * @param params
 * @param handleSuccess
 */
export const savePublish = (params,handleSuccess) => {
  return defHttp.post({url: Api.publish, params}).then(() => {
    handleSuccess();
  });
}
/**
 * 发布和取消发布
 * @param params
 * @param handleSuccess
 */
export const batchPublish = (params,handleSuccess) => {
  Modal.confirm({
    title: (params.status == 0 ? t('common.Unpublish') : t('common.Publish')),
    content: t('common.areyou') + ' ' + (params.status == 0 ? t('common.Unpublish') : t('common.Publish')) + ' ?',
    okText: t('common.okText'),
    cancelText: t('common.cancelText'),
    onOk: () => {
      return defHttp.post({url: Api.batchPublish, params}, {joinParamsToUrl: true}).then(() => {
        handleSuccess();
      });
    }
  });


}
/**
 * 查询详细
 * @param params
 */
export const queryById = (params) =>
  defHttp.get({url: Api.queryById, params});
/**
 * 列表接口
 * @param params
 */
export const list = (params) =>
  defHttp.get({url: Api.list, params});

/**
 * 删除单个
 * @param params
 * @param handleSuccess
 */
export const deleteOne = (params,handleSuccess) => {
  Modal.confirm({
    title: t('common.delText'),
    content: t('common.delConfirmText'),
    okText: t('common.delText'),
    cancelText: t('common.cancelText'),
    wrapClassName: 'delete_confirm',
    onOk: () => {
      return defHttp.delete({url: Api.deleteOne, params}, {joinParamsToUrl: true}).then(() => {
        handleSuccess();
      });
    }
  });

}
/**
 * 批量删除
 * @param params
 * @param handleSuccess
 */
export const batchDelete = (params, handleSuccess) => {
  Modal.confirm({
    title: t('common.delConfirmText'),
    content: t('common.delContent'),
    okText: t('common.delText'),
    cancelText: t('common.cancelText'),
    wrapClassName: 'delete_confirm',
    onOk: () => {
      return defHttp.delete({url: Api.deleteBatch, data: params}, {joinParamsToUrl: true}).then(() => {
        handleSuccess();
      });
    }
  });
}
/**
 * 保存或者更新
 * @param params
 * @param isUpdate 是否是更新数据
 */
export const saveOrUpdate = (params, isUpdate) => {
  const url = isUpdate ? Api.edit : Api.save;
  return defHttp.post({url: url, params});
}
