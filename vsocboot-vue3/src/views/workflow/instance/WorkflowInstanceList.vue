<template>
  <div class="search_transparent">
    <!--引用表格-->
   <BasicTable @register="registerTable" :rowSelection="rowSelection" :isSearch="isSearch">
     <!--插槽:table标题-->
          <template #form-formFooter>
             <a-dropdown v-if="selectedRowKeys.length > 0">
                 <template #overlay>
                    <a-menu>
                      <a-menu-item key="1" @click="batchHandleDelete">
                        <Icon icon="ant-design:delete-outlined"></Icon>
                        {{t('common.delText')}}
                      </a-menu-item>
                    </a-menu>
                  </template>
                  <a-button>{{t('common.batch')}}
                     <span class="soc ax-com-Arrow-down"></span>
                  </a-button>
            </a-dropdown>
<!--            <a-button  :class="{'btn-checked': isSearch == true}" type="text" @click="isSearch=!isSearch" preIcon="ant-design:filter-outlined"> {{ t('common.filter') }}</a-button>-->
            <a-button type="primary" v-if="hasPermission('workflowDesign:association')" @click="handleConfig" preIcon="ant-design:setting-outlined"> Configuration</a-button>
            <a-button type="primary"  @click="handleAdd"  >  <span class="soc ax-com-Add ax-icon"></span> {{t('common.add')}}</a-button>
          </template>

       <!--操作栏-->
      <template #action="{ record }">
        <TableAction :actions="getTableAction(record)"  />
      </template>
    </BasicTable>
    <ConfigModal ref="configModal" ></ConfigModal>
    <!-- 表单区域 -->
    <WorkflowInstanceModal @register="registerModal" @success="handleSuccess"></WorkflowInstanceModal>
  </div>
</template>

<script lang="ts" name="workflow-workflowInstance" setup>
import {ref} from 'vue';
import {BasicTable, TableAction} from '/@/components/Table';
import {useModal} from '/@/components/Modal';
import {useListPage} from '/@/hooks/system/useListPage'
import WorkflowInstanceModal from '/@/views/workflow/instance/modules/WorkflowInstanceModal.vue'
import {columns, searchFormSchema} from './WorkflowInstance.data';
import {
  batchDelete,
  deleteOne,
  getExportUrl,
  getImportUrl,
  list,
  savePublish
} from './WorkflowInstance.api';
import {useI18n} from "/@/hooks/web/useI18n";
import {formLayout} from '/@/settings/designSetting';
import {usePermission} from "/@/hooks/web/usePermission";
import ConfigModal from "/@/views/workflow/instance/modules/ConfigModal.vue";

const { t } = useI18n();
  const { hasPermission } = usePermission();
  const isSearch = ref<boolean>(true);
  //注册model
  const [registerModal, {openModal}] = useModal();
  //注册table数据
  const { prefixCls,tableContext,onExportXls,onImportXls } = useListPage({
       tableProps:{
            title: 'Workflow Instance',
            searchInfo:{isNow : 1,isDel:0},
            api: list,
            columns,
            canResize:false,
            formConfig: {
               labelWidth: 140,
               schemas: searchFormSchema,
               autoSubmitOnEnter:true,
               showAdvancedButton:true,
               layout: formLayout,
               baseColProps:{
                 lg: 12, // ≥992px
                 xl: 4, // ≥1200px
                 xxl: 4, // ≥1600px
               },
             },
            actionColumn: {
                width: 220,
             },
        },
        exportConfig: {
             name:"Workflow Instance",
             url: getExportUrl,
           },
           importConfig: {
             url: getImportUrl
           },
   })

  const [registerTable, {reload},{ rowSelection, selectedRowKeys }] = tableContext
  const configModal = ref();
   /**
    * 新增事件
    */
   function handleAdd() {
     openModal(true, {
       isUpdate: false,
       showFooter: true,
     });
   }

  /**
   * 配置工单关联
   */
  function handleConfig() {
    configModal.value.show();
  }
   /**
    * 编辑事件
    */
  function handleEdit(record: Recordable) {
     openModal(true, {
       record,
       isUpdate: true,
       showFooter: true,
     });
   }
   /**
    * 详情
   */
  function handleDetail(record: Recordable) {
     openModal(true, {
       record,
       isUpdate: true,
       showFooter: false,
     });
   }
   /**
    * 删除事件
    */
   async function handleDelete(record) {
     await deleteOne({id: record.id}, reload);
   }

  /**
   * 发布/取消发布
   * @param record
   */
   async function handlePublish(record){
     let status  = 1;
      if(record.status == 1){
        status = 0;
      }
    await savePublish({id: record.id,status : status}, reload);
   }
   /**
    * 批量删除事件
    */
   async function batchHandleDelete() {
     await batchDelete({ids: selectedRowKeys.value}, reload);
   }
   /**
    * 成功回调
    */
   function handleSuccess({isUpdate, values}) {
      reload();
   }
   /**
      * 操作栏
      */
   function getTableAction(record){
       return [
         {
           label: t('common.editText'),
           onClick: handleEdit.bind(null, record),
           ifShow: () => {
             return hasPermission('workflowDesign:edit');
           }
         }, {
           label: t('common.delText'),
           popConfirm: {
             title: t('common.delConfirmText'),
             confirm: handleDelete.bind(null, record),
           },
           ifShow: () => {
             return record.status != 1?hasPermission('workflowDesign:delete'):false;
           }
         }, {
           label: record.status == 1?'Unpublish' : 'Publish',
           popConfirm: {
             title: 'Are you sure to ' + (record.status == 1?'unpublish' : 'publish'),
             confirm: handlePublish.bind(null, record),
           },
           ifShow: () => {
             return record.status == 1?hasPermission('workflowDesign:unpublish') : hasPermission('workflowDesign:publish');
           }
         }
       ]
     }
     /**
       * 下拉操作栏
       */
    function getDropDownAction(record){
      return [
           {
             label: t('common.details'),
             onClick: handleDetail.bind(null, record),
           }
      ]
    }
</script>
<style scoped>

</style>
