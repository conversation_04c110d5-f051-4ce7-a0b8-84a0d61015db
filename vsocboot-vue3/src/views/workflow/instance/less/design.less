
.flex{
  display: flex;
}
.flex-1{
  flex : 1;
}
.flex-h{
  flex-direction: row;
}
.flex-v{
  flex-direction: column;
}

.flow_design_wrapper{
  display: flex;
  height: 100%;
  flex-direction: column;
  flex : 1;
  padding:16px 16px 0px 16px;
  .tools{
    height: 40px;
  }
  /deep/ .ant-tabs-content{
    height: 100%;
  }
}
/deep/ .djs-parent{
  --context-pad-entry-background-color:@dark-bg1!important;
  --context-pad-entry-hover-background-color:@dark-bg3!important;
  --shape-drop-allowed-fill-color:transprent!important;
  --popup-background-color:@dark-bg2!important;
  --popup-border-color:@border-color!important;
  --palette-entry-color: @font-color-white;
  --palette-separator-color: @border-color;
  --color-blue-205-100-45-opacity-30: @border-color!important;//鼠标拖动垂直线颜色
  --common-stroke: @font-color-white;
  --common-stroke-line: @font-color-white;//连线颜色
  --common-fill: @font-color-white;
  --popup-entry-hover-color:@bg-color;
  --palette-background-color:@dark-bg2!important;
  --palette-border-color:@border-color!important;
}
.bpmn-canvas{
  height: 100%;
  flex:1;

  /*background: url("data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PGRlZnM+PHBhdHRlcm4gaWQ9ImEiIHdpZHRoPSI0MCIgaGVpZ2h0PSI0MCIgcGF0dGVyblVuaXRzPSJ1c2VyU3BhY2VPblVzZSI+PHBhdGggZD0iTTAgMTBoNDBNMTAgMHY0ME0wIDIwaDQwTTIwIDB2NDBNMCAzMGg0ME0zMCAwdjQwIiBmaWxsPSJub25lIiBzdHJva2U9IiNlMGUwZTAiIG9wYWNpdHk9Ii4yIi8+PHBhdGggZD0iTTQwIDBIMHY0MCIgZmlsbD0ibm9uZSIgc3Ryb2tlPSIjZTBlMGUwIi8+PC9wYXR0ZXJuPjwvZGVmcz48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSJ1cmwoI2EpIi8+PC9zdmc+");*/
  :deep(.djs-container) {
    background-image: linear-gradient(90deg,hsla(0,0%,78.4%,.15) 10%,transparent 0),linear-gradient(hsla(0,0%,78.4%,.15) 10%,transparent 0);
    background-size: 10px 10px;

    .djs-palette{
      .bpmn-icon-lasso-tool,.bpmn-icon-space-tool,
      .bpmn-icon-intermediate-event-none,.bpmn-icon-subprocess-expanded,
      .bpmn-icon-data-store,
      .bpmn-icon-data-object,
      .bpmn-icon-group,
      .bpmn-icon-participant,

      .bpmn-icon-task{
        display: none;
      }
    }
    .djs-context-pad{
      .bpmn-icon-screw-wrench,.bpmn-icon-intermediate-event-none,.bpmn-icon-text-annotation,.bpmn-icon-task
      {
        display: none;
      }
    }
    .djs-palette.two-column.open {
      width: 48px!important;
      top:60px;
      background: @dark-bg3;
      border-color: @border-color;
    }

  }
}

/deep/ .bjs-container{
  g.djs-visual{
     path{
       stroke: var(--common-stroke) !important;
       fill:var(--common-fill) !important;
       stroke-width: 2px!important;
     }
     text{
       fill:var(--common-fill)!important;
       stroke: none !important;
       font-size: 13px;

     }
      & > :nth-child(1):not(text) {//shape
        fill: transparent !important;
        stroke: var(--common-stroke) !important;
      }
      & > path:nth-child(1){//line
        stroke: var(--common-stroke-line) !important;
      }
   }
  .djs-direct-editing-parent{
    background-color: @dark-bg3!important;
    border-width:0!important;
    border-radius: 10px;
  }
  .djs-connection-preview{//拖拽连线时虚线颜色
    path{
      stroke: @font-color-1 !important;
    }
  }

  .djs-popup-search {
    input {
      background: @bg-color;
      border: 1px solid @border-color;
    }
    path{
      fill: @font-color-white!important;
    }
  }
}
/deep/ marker > path{//连线三角的颜色
  stroke: var(--common-stroke-line) !important;
  fill:var(--common-stroke-line) !important;
  //stroke-width: 2px!important;
}
.panel{
  width:300px;
  height: 100%;
  background: @dark-bg2;
  position: absolute;
  right: 0px;
}
.code-editor{
  position: relative;
  width:100%;
  left:0px;
  height:100%;
  overflow:auto;
  .h-full{
    height:100%!important;
    /deep/ .relative{
      height:100%!important;
    }
  }
}

.overflow-hidden{
  overflow: hidden;
}
