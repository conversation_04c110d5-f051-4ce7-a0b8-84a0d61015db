import {BasicColumn} from '/@/components/Table';
import {FormSchema} from '/@/components/Table';
import {render} from "/@/utils/common/renderUtils";
import {initDictOptions} from "/@/utils/dict";
import {rules} from "/@/utils/helper/validator";
import {getTenantMode} from "/@/utils/auth";
import {USER_TYPE} from "/@/views/workflow/setting/WorkflowInstance.data";
export const FLOW_TYPE:any[] = [
  // {label: 'Test',
  // value: 1,
  // key: '1',}
];
const templateDictCode = "tbl_workflow_template,template_name,template_key,is_now=1 and is_del=0";

initDictOptions("tbl_workflow_type_info,type_name,id").then((res)=>{
  for(let i in res){
    FLOW_TYPE.push({
      label: res[i].text,
      value: res[i].value,
      key: res[i].value,
    });
  }
});

export const columns: BasicColumn[] = [
  {
    title: 'Flow Name',
    dataIndex: 'flowName'
  },
  // {
  //   title: 'Flow Type',
  //   dataIndex: 'flowType',
  //   customRender: ({text}) => {
  //     return render.renderDictNative(text, FLOW_TYPE);
  //     //return render.renderDict(text, "tbl_workflow_type_info,type_name,id");
  //   }
  // },
  // {
  //   title: '模板名称',
  //   dataIndex: 'templateName',
  //
  // },
  {
    title: 'Creator',
    dataIndex: 'createBy'
  },
  {
    title: 'Creation Time',
    dataIndex: 'createTime'
  },
  {
    title: 'Last Modified Person',
    dataIndex: 'updateBy'
  },
  {
    title: 'Last Modified Time',
    dataIndex: 'updateTime'
  },
  {
    title: 'Status',
    dataIndex: 'status',
    customRender: ({text}) => {
       if(text == 1){
         return 'Published';
       }
       return 'Unpublish';
    },
  },
];

export const searchFormSchema: FormSchema[] = [
  {
    label: 'Form Name',
    field: 'flowName',
    component: 'JInput'
  },
  {
    label: 'Creator',
    field: 'createBy',
    component: 'JDictSelectTag',
    componentProps: {
      showSearch:true,
      dictCode: 'sysUserNameDict',
    }
  },
  {
    label: 'Creation Time',
    field: 'startDate',
    component: 'RangeDate',
    componentProps:{
      datetime:true,
    },
    colProps: {
      lg: 12, // ≥992px
      xl: 5, // ≥1200px
      xxl: 5, // ≥1600px
    },
  },
  {
    label: 'Last Modified Person',
    field: 'updateBy',
    component: 'JDictSelectTag',
    componentProps: {
      showSearch:true,
      dictCode: 'sysUserNameDict',
    }
  },
  {
    label: 'Last Modified Time',
    field: 'updDate',
    component: 'RangeDate',
    componentProps:{
      datetime:true,
    },
    colProps: {
      lg: 12, // ≥992px
      xl: 5, // ≥1200px
      xxl: 5, // ≥1600px
    },
  },
];

export const formSchema: FormSchema[] = [
  // {
  //   label: 'process Id',
  //   field: 'processId',
  //   required: true,
  //   component: 'Input',
  // },
  {
    label: 'Flow Name',
    field: 'flowName',
    // required: true,
    component: 'Input',
    dynamicRules: ({ model, schema }) => {
      return [
        {required: true},
        { ...rules.limitationCheckRule(128)[0] },
      ];
    },
  },
  {
    label: 'Tenant type',
    field: 'tenantType',
    ifShow: getTenantMode(),
    component: 'RadioButtonGroup',
    required : true,
    defaultValue : 1,
    componentProps: {
      options:USER_TYPE,
    },
  },
  {
    label: 'Tenant',
    field: 'tenant',
    component: 'JDictSelectTag',
    required : true,
    ifShow: ({ values }) => {
      console.log('values',values)
      console.log('getTenantMode',getTenantMode())
      return getTenantMode() && values.tenantType == 2 ;
    },
    componentProps: {
      dictCode: 'tenantActiveDict',
      showChooseOption: false,
    },
  },
  {
    label: 'Template',
    field: 'templateId',
    required: true,
    component: 'JDictSelectTag',
    componentProps: {
      dictCode: 'workflowTemplateDict',
      showChooseOption: false,
    },
  },

  {
    label: 'Flow Description',
    field: 'flowDesc',
    component: 'InputTextArea',
    dynamicRules: ({ model, schema }) => {
      return [
        { ...rules.limitationCheckRule(1000)[0] },
      ];
    },
  },

];
