import { BasicColumn } from '/@/components/Table';
import { FormSchema } from '/@/components/Table';
import { useI18n } from '/@/hooks/web/useI18n';
import { FLOW_TYPE } from '/@/views/workflow/instance/WorkflowInstance.data';
import { render } from '/@/utils/common/renderUtils';
import { calcMillisecond } from '/@/utils/ckTable';
import {getTenantMode, isAdministrator} from '/@/utils/auth';
import { TICKET_TYPE_SELECT } from '/@/utils/valueEnum';
import {TICKET_TENANT_TYPE_OPTION, TICKET_TYPE_OPTION} from "/@/views/ticket/ts/option";

const { t } = useI18n();
export const columns: BasicColumn[] = [
  {
    title: 'Flow Name',
    dataIndex: 'flowName',
    customRender: (opt: any) => {
      return opt?.record?.bisTitle ?? opt.record?.flowName;
    },
  },
  {
    title: 'Flow Type',
    dataIndex: 'flowType',
    customRender: ({ text }) => {
      return render.renderDictNative(text, FLOW_TYPE);
    },
  },
  {
    title: 'Task Name',
    dataIndex: 'taskName',
  },
  {
    title: 'Create Time',
    dataIndex: 'createTime',
  },
  {
    title: 'Assignee',
    dataIndex: 'assignee',
  },
];

export const runColumns: BasicColumn[] = [
  {
    title: 'Flow Name',
    dataIndex: 'flowName',
    customRender: (opt: any) => {
      return opt.record.bisTitle ?? opt.record.flowName;
    },
  },
  {
    title: 'Flow Type',
    dataIndex: 'flowType',
    customRender: ({ text }) => {
      return render.renderDictNative(text, FLOW_TYPE);
    },
  },
  {
    title: 'Task Name',
    dataIndex: 'taskName',
  },
  {
    title: 'Create Time',
    dataIndex: 'createTime',
  },
  {
    title: 'Assignee',
    dataIndex: 'assignee',
  },
  {
    title: 'State',
    dataIndex: 'state',
    customRender: (opt: any) => {
      return opt.record.isSuspended == false ? 'Activate' : 'Suspended';
    },
  },
];

export const searchFormSchema: FormSchema[] = [
  {
    label: '',
    field: 'ticketName',
    component: 'JInput',
    componentProps: { search: true, placeholder: t('routes.workflowHistory.ticketName') },
  },
  {
    label: '',
    field: 'ticketType',
    component: 'Select',
    ifShow: getTenantMode(),
    componentProps: {
      options: isAdministrator() ? TICKET_TYPE_OPTION : TICKET_TENANT_TYPE_OPTION,
      placeholder: t('routes.workflowHistory.ticketType')
    },
  },
  {
    label: '',
    field: 'socTenantId',
    component: 'JSearchSelect',
    componentProps: { dict: 'tenantDict', placeholder: t('common.tenant') },
    ifShow: isAdministrator(),
  },
  {
    label: '',
    field: 'applyUser',
    component: 'JSearchSelect',
    componentProps: { dict: 'sysUserActiveDict', placeholder: t('routes.workflowHistory.applyUser') },
  },
];

export const historyColumns: BasicColumn[] = [
  {
    title: t('routes.workflowHistory.ticketName'),
    dataIndex: 'ticketName',
  },
  {
    title: t('routes.workflowHistory.ticketType'),
    dataIndex: 'ticketType',
    customRender(opt) {
      // 1mssp内部，2 mssp下发，3 租户提交 ，4 租户内部工单
      if (opt.value == 1) {
        return 'Admin tnternal ticket';
      } else if (opt.value == 2) {
        return 'Ticket issued to tenant';
      } else if (opt.value == 3) {
        return 'Ticket submitted by tenant';
      } else if (opt.value == 4) {
        return 'Tenant internal ticket';
      }
    },
  },
  {
    title: t('common.tenant'),
    dataIndex: 'socTenantId_dictText',
    ifShow: isAdministrator(),
  },
  {
    title: t('routes.workflowHistory.applyUser'),
    dataIndex: 'username',
  },
  {
    title: t('routes.workflowHistory.startTime'),
    dataIndex: 'startTime',
  },
  {
    title: t('routes.workflowHistory.handleTime'),
    dataIndex: 'handleTime',
  },
  {
    title: t('routes.workflowHistory.duration'),
    dataIndex: 'duration',
    customRender: ({ text }) => {
      return calcMillisecond(text * 1000, t);
    },
  },
];

export const searchTicketReportFormSchema: FormSchema[] = [
  {
    label: '',
    field: 'ticketName',
    component: 'JInput',
  },
  {
    label: '',
    field: 'ticketType',
    component: 'Select',
    componentProps: {
      options: TICKET_TYPE_SELECT,
    },
  },

  {
    label: '',
    field: 'applyUser',
    component: 'JSearchSelect',
    componentProps: {
      dict: 'sysUserActiveDict',
    },
  },
  {
    label: ' ',
    field: '-',
    component: 'Input',
    slot: 'search',
  },
];
