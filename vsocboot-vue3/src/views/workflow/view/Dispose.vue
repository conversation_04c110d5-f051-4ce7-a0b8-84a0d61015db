<template>
  <div class="flex flex-v padding16" style="height: 100%">
    <div class="font16 fcolor " @click="closeRetun">
    <span class="cursor">
      <Icon icon="ant-design:left-outlined" :size="16" style="margin-right: 8px;">
      </Icon>
      Back
    </span>
    </div>
    <div class="view-template_wraper flex flex-v flex-1"  >
      <div class="border-bottom padding24 font20 fcolor">
        {{ title }}
      </div>
      <div v-if="attention" class="padding16 font16 primaryColor">Attention：{{ attention }}</div>
      <div class="flex-1">
        <!-- template content-->
        <div class="form_content padding16 ">
          <HandleForm ref="handleRef"
                      @ok="showNext"
                      v-model:value="design"
                      v-model:variables="variables"
                      v-model:rules="rules"
                      v-model:isRead="isRead"
                      v-if="isShow"></HandleForm>
        </div>
        <div class="flex flex-h footer" v-if="!isSave && active == '2'">
          <a-button size="large" @click="save" type="primary">
            Save
          </a-button>
        </div>
        <!--        <div class="splitLine">
                  <div class="border-bottom "></div>
                </div>-->
        <div v-if="isSave " class="flow_content padding16">
          <Timeline>
            <template v-for="(item,index) in timeLines">
              <TimelineItem color="#308cff" v-if="item?.deleteReason=='completed'">
                <div class="font13 fcolor3 pt-3">
                  {{ activityMap[item?.taskDefinitionKey]?.activityName || 'Previous' }}
                </div>
                <div :class="['userList',{'userList-row' : active!=2}]">
                 <template v-for="(user,j) in activityMap[item?.taskDefinitionKey]?.users">
                    <User v-model:value="activityMap[item?.taskDefinitionKey].users[j].user" :event="false"
                          :isDo=" (activityMap[item?.taskDefinitionKey]?.users.length == 1 && user.endTime) || (user.endTime && user.user == item.assignee)"></User>
                  </template>
                </div>
              </TimelineItem>
            </template>
            <!-- current User-->
            <TimelineItem color="#308cff" v-if="(active == 2 && activityMap[currentActivityId]) || (nextUsers.length > 0)" >
              <div class="font13 fcolor3 pt-3"> {{activityMap[currentActivityId]?.activityName }}</div>
              <div :class="['userList',{'userList-row' : active!=2}]">
                <template v-for="(user,j) in activityMap[currentActivityId]?.users">
                  <User v-model:value="activityMap[currentActivityId].users[j].user" :event="false"></User>
                </template>
              </div>
            </TimelineItem>
            <!-- Next User-->
            <TimelineItem color="#308cff" v-if="active == '2' && nextUsers.length > 0">
              <div class="font13 fcolor3 pt-3"> Next</div>
              <div :class="['userList',{'userList-row' : active!=2}]">
                <User v-model:value="nextUsers[k]"
                      v-for="(u,k) in nextUsers" :event=" active==2"></User>
              </div>
            </TimelineItem>
            <!-- Copy User-->
            <TimelineItem color="#308cff" v-if="active == '2'">
              <div class="font13 fcolor3 pt-3"> Cc</div>
              <div class="ccList">
                <template v-if="ccUsers.length > 0">
                  <User v-model:value="ccUsers[index]" :event="false"
                        v-for="(item,index) in ccUsers"></User>
                </template>
                <div class="addCc font13 fcolor3 cursor" @click="openCheckUser">+</div>
              </div>
            </TimelineItem>
          </Timeline>
        </div>
      </div>


      <!-- footer-->
      <div class="flex flex-h footer" >
        <template v-if="(isSave && active == '2')">
          <a-button size="large" @click="workModal.openModal()">
            Hand over
          </a-button>
          <a-button type="primary" size="large" @click="submit" :disabled="disabled">
            Next
          </a-button>
        </template>
        <a-button type="primary" size="large" @click="recall" :disabled="disabled" v-else-if="active == '5' && instData.workflowStatus != 1 && instData.workflowStatus != 3">
          Recall
        </a-button>
      </div>
    </div>
  </div>
  <UserSelectModal rowKey="username" @register="registerSelUserModal"
                   @getSelectResult="onSelectUserOk"/>
  <UserSelectModal rowKey="username" @register="registerUserModal"
                   @getSelectResult="doWorkflowTransfer"  :isRadioSelection="true"/>
</template>

<script lang="ts" name="Dispose" setup>
import {nextTick, onMounted, ref, watch} from "vue";
import {useRouter} from "vue-router";
import {
  getHistoryTaskInfo,
  workflowTransfer,
  executionTask,
  getDisposeInfo,
  queryProcessInfo,
  getAllAcitivity,
  copyReadUpdate,
  getNextTask,
  workflowChangeStauts

} from "./WorkflowView.api"
import HandleForm from "/@/views/workflow/view/components/HandleForm.vue";
import {Modal, Timeline, TimelineItem} from "ant-design-vue";
import {queryUserNameByRoleIds} from "/@/views/system/user/user.api";
import User from './components/User.vue';
import UserSelectModal from "/@/components/Form/src/jeecg/components/modal/UserSelectModal.vue";
import {useModal} from "/@/components/Modal";
import {getAttention, handleNextUser, setNextConfigData} from "/@/views/workflow/view/ts/Utils";
import {useMessage} from "/@/hooks/web/useMessage";
import {useUserStore} from "/@/store/modules/user";
import {saveOrUpdate2} from "/@/views/risk/InvestigationRiskEventlogs.api";
import {useI18n} from "/@/hooks/web/useI18n";
const { t } = useI18n();
const [registerUserModal, workModal] = useModal();
const [registerSelUserModal, selUserModal] = useModal();
const { createMessage } = useMessage();
const {currentRoute} = useRouter();
const router = useRouter();
const userStore = useUserStore();
let query = {};
const disabled = ref(false);
const isRead = ref(false);
const title = ref('');
const instData = ref({});//tbl_workflow_process表数据
const nextConfig = ref([]);//下一步配置
const nextUsers = ref([]);//下一步处置人
const currentActivityId = ref('');//当前流程code
const nextActivityId = ref('');//下一步流程code
const ccUsers = ref([]);//抄送人
const finishCcUsers = ref([]);//工单结束抄送人
const rules = ref({});//form 规则
const isShow = ref(false);
const isSave = ref(false);//是否可更改form true不可，false可以
const variables = ref({});//form所有变量
const active = ref('');//当前显示菜单
const timeLines = ref([]);//时间线
// const nodeJson = ref([]);//所有节点
const currentTaskId = ref('');//当前任务id
const ruleConfig = ref({});//规则
const activityMap = ref({});//流程信息
const handleRef = ref();
let handleValues = [];//工单关联的数据
let attention = ref('');
const design = ref({
  title: 'dispose',
  formConfig: {},
  list: []
});
onMounted(() => {
  getData();

})
//初始化页面
function getData() {
  const param = currentRoute.value.query;
  query = {...param};
  active.value = param.active;
  if(param.active == null || param.active != 2){
    isRead.value = true;
  }
  if (active.value == '4') {//抄送
    copyReadUpdate({processInstanceId: param.instId});
  }
  //HistoricProcessInstance表数据
  getDisposeInfo({processInstanceId: param.instId}).then((data) => {
    variables.value = JSON.parse(data.variables);
    title.value = data.bisTitle || data.flowName;
    // console.log('variables.value:', variables.value)
    attention.value = getAttention(data);
    //tbl_workflow_process
    queryProcessInfo({processInstanceId: param.instId}).then((result) => {
      instData.value = result;
      // nodeJson.value = JSON.parse(instData.value.nodeJson);
      ruleConfig.value = JSON.parse(instData.value.ruleConfig);
      console.log('ruleConfig',ruleConfig.value)
      if (result.designContent) {
        design.value.list = JSON.parse(result.designContent);
      }
      if (result.designConfig) {
        design.value.formConfig = JSON.parse(result.designConfig);
      }

      getHistoryTaskInfo({processInstanceId: param.instId}, (result) => {
        timeLines.value = result;
        console.log('getHistoryTaskInfo',result)
      })

      getAllAcitivity({processInstanceId: param.instId}, (result) => {
        getCurrentActivity(result);
        handleTimeline(result);
        nextTick(()=>{
          isShow.value = true;
        })
      })


    });
  });


}

function setRuleConfig() {
  let config = JSON.parse(instData.value.ruleConfig) ?? {};

  let data = config[currentActivityId.value] ?? {};
  // console.log('setRuleConfig',data)
  if(data.config){
    data = data.config;
    for(let i in data){
      if(data[i].setting){
        rules.value[i] = data[i].setting;
      }
    }
  }
  let flowUserData = config.flowUserData;
  // console.log('flowUserData',flowUserData)
  if(flowUserData.ccList && flowUserData.ccList.length > 0){
    finishCcUsers.value = flowUserData.ccList;
  }
}

function getCurrentActivity(result) {
  var current = result.find(item => item.taskId != null && null == item.endTime);
  if (current) {
    currentActivityId.value = current.activityId;
    currentTaskId.value = current.taskId;
    setRuleConfig();
  }

}

function handleTimeline(result) {
  // console.log('all ctivity=============>', result)
  let map = {};
  result.forEach((item) => {
    let obj = {user : item.taskAssignee,canceled : item.canceled,endTime : item.endTime};
    if(map[item.activityId]){
      let repeat = map[item.activityId].users.filter(cc=>cc.user == item.taskAssignee)
      if(repeat.length == 0){//去掉多次执行重复的数据
        map[item.activityId].users.push(obj);
      }
    }else{
      item.users = [obj];
      map[item.activityId] = item;
    }
  })
  activityMap.value = map;
  // console.log('==================activityMap',map)

  if (active.value != 2) {
    isSave.value = true;
  }


}


function recall(){
  Modal.confirm({
    title: 'Recall',
    content: 'Are you sure to Recall' + "?",
    okText: t('common.okText'),
    cancelText: t('common.cancelText'),
    onOk: () => {
      const param = currentRoute.value.query;
      workflowChangeStauts({state: 'Suspend',processInstanceId:param.instId}).then(()=>{
        closeRetun();
      });

    }
  });

}


async function submit() {
  disabled.value = true;
  const param = currentRoute.value.query;
  //下一步审批配置，为了提前给下一步分配人员用
  console.log('nextActivityId.value',nextActivityId.value)
  nextConfig.value = nextActivityId.value ? getNextConfig() : [];
  console.log('nextConfig', nextConfig)
  //用来保存下一步的Attention
  variables.value.nextUsers = nextUsers.value;
  variables.value.nextId = nextActivityId.value;//用来判断是否已完成
  //保存数据
  let queryParam = {
    vals: JSON.stringify(variables.value),//存到act_ru_variable中
    nextConfig: JSON.stringify(nextConfig.value),
    processInstanceId: param.instId,
    options : 'agree',//评论，目前没用,
    taskId : currentTaskId.value
  };
  //===========抄送start================
  let userNames = [];
  //抄送人
  if(ccUsers.value.length > 0){
     userNames = ccUsers.value.map(item=>{
      return item.user;
    })
    console.log('userNames',userNames)

  }
  //工单结束抄送人
  if(!nextActivityId.value){
    finishCcUsers.value.forEach(item=>{
      if(userNames.length > 0 && userNames.findIndex(v=>item.value == v) > 0){
        userNames.push(item.value);
      }else{
        userNames.push(item.value);
      }
    })
  }
  if(userNames.length > 0){
    queryParam.ccUsers = userNames.toString();
  }
  //===========抄送end================
  console.log('executionTask:', queryParam)
  try{
    let res = await executionTask(queryParam);
    console.log('executionTask res',res);
    closeRetun();
  }catch (e){
    disabled.value = false;
    isRead.value = false;
    isSave.value = false;
    console.log('=====================',e)
  }


}

function getNextConfig() {
  var activityConfig = [];
  let nextId = nextActivityId.value;
  let nextData = ruleConfig.value[nextId];
  if (nextData && nextData.isMultiInstance) {
    var data = setNextConfigData(nextData);
    data.id = nextId;
    activityConfig.push(data);
  }

  return activityConfig;

}

function closeRetun() {
  const param = currentRoute.value.query;
  if (param.pageSource == "inve") {
    router.go(-1)
  } else if(param.active){
    router.push({path: "/workflow/view/WorkFlowViewList", query: {active: query.active}});
  }
  else {
    router.go(-1)
  }
}

function doWorkflowTransfer(options, userIdList){

  if(userIdList.length == 0){
    createMessage.warning('Please check user!');
    return;
  }
  workflowTransfer({taskId:currentTaskId.value,assignee:userIdList[0]}).then((result)=>{
    createMessage.success(result);
    closeRetun();
  });




}
// 选择用户成功
async function onSelectUserOk(options, userIdList) {
  if (userIdList.length == 0) {
    return;
  }

  userIdList.forEach(item => {
    ccUsers.value.push({user: item});
  })
}

function openCheckUser() {
  selUserModal.openModal();
}

function save() {
  handleRef.value.validate();
}

async function showNext() {
  // console.log('currentActivityId.value:',currentActivityId.value)
  // console.log('ruleConfig.value:',ruleConfig.value)
  const param = currentRoute.value.query;
  await getNextTask( {vals : JSON.stringify(variables.value),processInstanceId: param.instId,
    currentActivityId : currentActivityId.value}).then((data)=>{
      console.log('getNextTask data',data)
    if(data.length > 0){
      let item = data[0];//目前数组只有一条
      if(data[0].name != "结束"){
        //下一步审批节点id
        nextActivityId.value = item.key;
        //下一步审批人
        let config = ruleConfig.value[item.key];
        handleNextUser(config).then((users)=>{
          nextUsers.value = users;
        });
      }
      isSave.value = true;
      isRead.value = true;
    }else{
      createMessage.warning('No next steps found,Please check the data or process configuration!');
    }


  })

}


</script>

<style scoped lang="less">
@import "./less/view.less";

</style>
