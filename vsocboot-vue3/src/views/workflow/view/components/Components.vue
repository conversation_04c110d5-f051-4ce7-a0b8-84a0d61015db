<template>
  <div class="row" v-show="isShow">
    <!-- input-->
    <a-form-item v-if="childData.type == 'input'" :label="childData.label" :name="childData.field">
      <a-input
        :id="childData.field"
        v-model:value="form[childData.field]"
        :style="{ width: childData.componentProps.width }"
        :placeholder="childData.componentProps.placeholder"
        :disabled="isRead"
        autoComplete="off"
      />
    </a-form-item>
    <!-- inputNumber-->
    <a-form-item :label="childData.label" :name="childData.field" v-else-if="childData.type == 'inputNumber'">
      <a-input-number
        :id="childData.field"
        v-model:value="form[childData.field]"
        :style="{ width: childData.componentProps.width }"
        :disabled="isRead"
        :min="childData.componentProps.min"
        :max="childData.componentProps.max"
        :step="childData.componentProps.step"
      />
    </a-form-item>
    <!-- textArea-->
    <a-form-item :label="childData.label" :name="childData.field" v-else-if="childData.type == 'textArea'">
      <a-textarea
        :id="childData.field"
        v-model:value="form[childData.field]"
        :placeholder="childData.componentProps.placeholder"
        :disabled="isRead"
        :style="{ width: childData.componentProps.width }"
      />
    </a-form-item>
    <!-- radioGroup-->
    <a-form-item :label="childData.label" :name="childData.field" v-else-if="childData.type == 'radioGroup'">
      <a-radio-group :id="childData.field" v-model:value="form[childData.field]" :style="{ width: childData.componentProps.width }">
        <template v-for="(group, index) in childData.items" :key="'radio_' + index">
          <a-radio :value="group.value" :class="childData.layout" :disabled="isRead">{{ group.label }}</a-radio>
        </template>
      </a-radio-group>
    </a-form-item>
    <!-- checkboxGroup-->
    <a-form-item :label="childData.label" :name="childData.field" v-else-if="childData.type == 'checkboxGroup'">
      <a-checkbox-group :id="childData.field" v-model:value="arrayValue" :style="{ width: childData.componentProps.width }">
        <template v-for="(group, index) in childData.items" :key="'checkbox_' + index">
          <a-checkbox :value="group.value" :class="childData.layout" :disabled="isRead">{{ group.label }}</a-checkbox>
        </template>
      </a-checkbox-group>
    </a-form-item>
    <!-- select-->
    <a-form-item :label="childData.label" :name="childData.field" v-else-if="childData.type == 'select'">
      <a-select
        v-if="childData.componentProps.multiple"
        v-model:value="arrayValue"
        :id="childData.field"
        :style="{ width: childData.componentProps.width }"
        :mode="childData.componentProps.multiple ? 'multiple' : ''"
        :showSearch="childData.componentProps.showSearch"
        optionFilterProp="label"
        :disabled="isRead"
        :placeholder="childData.componentProps.placeholder"
      >
        <template v-for="(group, index) in childData.items" :key="'select' + index">
          <a-select-option :label="group.label" :value="group.value">{{ group.label }}</a-select-option>
        </template>
      </a-select>

      <a-select
        v-else
        v-model:value="form[childData.field]"
        :id="childData.field"
        :style="{ width: childData.componentProps.width }"
        :mode="childData.componentProps.multiple ? 'multiple' : ''"
        :showSearch="childData.componentProps.showSearch"
        optionFilterProp="label"
        :disabled="isRead"
        :placeholder="childData.componentProps.placeholder"
      >
        <template v-for="(group, index) in childData.items" :key="'select' + index">
          <a-select-option :label="group.label" :value="group.value">{{ group.label }}</a-select-option>
        </template>
      </a-select>
    </a-form-item>
    <!-- timePicker-->
    <template v-else-if="childData.type == 'timePicker'">
      <a-form-item :label="childData.label" :name="childData.field">
        <a-time-range-picker
          v-if="childData.componentProps.range"
          v-model:value="form[childData.field]"
          :disabled="isRead"
          :name="childData.field"
          :id="childData.field"
          :style="{ width: childData.componentProps.width }"
          :format="childData.componentProps.valueFormat"
          :valueFormat="childData.componentProps.valueFormat"
          :showTime="childData.componentProps.showTime"
          :placeholder="[childData.componentProps.placeholder, childData.componentProps.placeholderend]"
        ></a-time-range-picker>
        <a-time-picker
          v-else
          :placeholder="childData.componentProps.placeholder"
          v-model:value="form[childData.field]"
          :id="childData.field"
          :name="childData.field"
          :disabled="isRead"
          :style="{ width: childData.componentProps.width }"
          :format="childData.componentProps.valueFormat"
          :valueFormat="childData.componentProps.valueFormat"
          :showTime="childData.componentProps.showTime"
        ></a-time-picker>
      </a-form-item>
    </template>
    <!-- datePicker-->
    <template v-else-if="childData.type == 'datePicker'">
      <a-form-item :label="childData.label" :name="childData.field">
        <a-date-picker
          picker="year"
          v-if="childData.componentProps.type == 1"
          v-model:value="form[childData.field]"
          :id="childData.field"
          :name="childData.field"
          :style="{ width: childData.componentProps.width }"
          :format="childData.componentProps.format"
          :placeholder="childData.componentProps.placeholder"
          :valueFormat="childData.componentProps.format"
          :disabled="isRead"
        ></a-date-picker>
        <a-date-picker
          picker="month"
          v-if="childData.componentProps.type == 2"
          v-model:value="form[childData.field]"
          :id="childData.field"
          :name="childData.field"
          :style="{ width: childData.componentProps.width }"
          :format="childData.componentProps.format"
          :placeholder="childData.componentProps.placeholder"
          :valueFormat="childData.componentProps.format"
          :disabled="isRead"
        ></a-date-picker>
        <a-date-picker
          v-if="childData.componentProps.type == 3 || childData.componentProps.type == 4"
          v-model:value="form[childData.field]"
          :id="childData.field"
          :name="childData.field"
          :style="{ width: childData.componentProps.width }"
          :format="childData.componentProps.format"
          :valueFormat="childData.componentProps.format"
          :placeholder="childData.componentProps.placeholder"
          :showTime="childData.componentProps.type == 4"
          :disabled="isRead"
        ></a-date-picker>

        <a-range-picker
          v-if="childData.componentProps.type == 5 || childData.componentProps.type == 6"
          v-model:value="form[childData.field]"
          :id="childData.field"
          :name="childData.field"
          :style="{ width: childData.componentProps.width }"
          :format="childData.componentProps.format"
          :showTime="childData.componentProps.type == 6"
          :disabled="isRead"
          :valueFormat="childData.componentProps.format"
          :placeholder="[childData.componentProps.placeholder, childData.componentProps.placeholderend]"
        ></a-range-picker>
      </a-form-item>
    </template>
    <!-- switch-->
    <template v-else-if="childData.type == 'switch'">
      <a-form-item :label="childData.label" :name="childData.field">
        <a-switch
          v-model:checked="form[childData.field]"
          :id="childData.field"
          :name="childData.field"
          :unCheckedValue="childData.componentProps.unCheckedValue"
          :checkedValue="childData.componentProps.checkedValue"
          :disabled="isRead"
        />
      </a-form-item>
    </template>
    <!-- hr-->
    <template v-else-if="childData.type == 'hr'">
      <hr :style="{ margin: childData.componentProps.margin / 2 + 'px 0px', width: childData.componentProps.width }" />
    </template>
    <!-- text-->
    <template v-else-if="childData.type == 'text'">
      <div
        :style="{
          width: childData.componentProps.width,
          'text-align': childData.componentProps.align,
          'font-size': childData.componentProps.fontSize + 'px',
          'font-style': childData.componentProps.italic ? 'italic' : '',
          'font-weight': childData.componentProps.bold ? 'bold' : '',
          'text-decoration': childData.componentProps.underline ? 'underline' : childData.componentProps.lineThrough ? 'line-through' : '',
          'line-height': childData.componentProps.lineHeight + 'px',
        }"
        >{{ childData.componentProps.content }}</div
      >
    </template>
    <!-- phone-->
    <!--      <template v-else-if="childData.type=='phone'">
        <a-form-item :label="childData.label"  :name="childData.field" >
          <a-input
            v-model:value="form[childData.field]"
            :id="childData.field"
            :placeholder="childData.componentProps.placeholder"
            :style="{width:childData.componentProps.width}" />
        </a-form-item>
        <template  v-if="childData.componentProps.code">
          <a-form-item :style="{width:childData.componentProps.width}">
            <a-input v-model:value="childData.componentProps.codeValue"
                     :id="childData.field" placeholder="Identifying Code" style="width:calc(100% - 120px);"/>
            <a-button type="primary" style="margin-left:15px;">Obtain verification code</a-button>
          </a-form-item>
        </template>
      </template>-->
    <!-- email-->
    <!--      <template v-else-if="childData.type=='email'">
        <a-form-item :label="childData.label"  :name="childData.field" >
          <a-input
            v-model:value="form[childData.field]"
            :id="childData.field"
            :placeholder="childData.componentProps.placeholder"
            :style="{width:childData.componentProps.width}" />
        </a-form-item>
        <template :style="{width:childData.componentProps.width}" v-if="childData.componentProps.code">
          <a-form-item>
            <a-input v-model:value="childData.componentProps.codeValue" :name="childData.field" placeholder="验证码" style="width:calc(100% - 120px);"/>
            <a-button type="primary" style="margin-left:15px;">获取验证码</a-button>
          </a-form-item>
        </template>
      </template>-->
    <!-- upload-->
    <template v-else-if="childData.type == 'upload'">
      <a-form-item :label="childData.label" :name="childData.field">
        <a-upload-dragger
          v-if="childData.componentProps.dragger"
          name="file"
          :style="{ width: childData.componentProps.width }"
          :headers="headers"
          :action="uploadContent"
          :multiple="childData.componentProps.multiple"
          :maxCount="childData.componentProps.maxCount"
          :file-list="form[childData.field]"
          :data="{ biz: 'workOrder' }"
          @change="handleUploadChange"
          :disabled="isRead"
          :showUploadList="false"
        >
          <p class="ant-upload-drag-icon">
            <inbox-outlined></inbox-outlined>
          </p>
          <p class="ant-upload-text">{{ childData.componentProps.text }}</p>
        </a-upload-dragger>
        <JUpload
          v-else
          :text="childData.componentProps.text"
          :multiple="childData.componentProps.multiple"
          :maxCount="childData.componentProps.maxCount || 1"
          @change="handleUploadChange"
          :return-url="false"
          :disabled="isRead"
          bizPath="workOrder"
          v-model:value="form[childData.field]"
          :showUploadList="false"
        ></JUpload>

        <div class="uploadFiles" v-if="form[childData.field]" v-for="(item, index) in form[childData.field]">
          <span class="blob" @click.stop="downloadFileBlob(item.filePath, item.fileName)"
            >{{ item.fileName
            }}<Icon v-if="!isRead" icon="ant-design:close-outlined" :size="12" @click.stop="deleteUploadFiles(item.filePath, index)" />
          </span>
        </div>
      </a-form-item>
    </template>
    <!-- autoNumber-->
    <template v-else-if="childData.type == 'autoNumber'">
      <a-form-item :label="childData.label" :name="childData.field">
        <a-input v-model:value="form[childData.field]" :id="childData.field" readonly :style="{ width: childData.componentProps.width }" />
      </a-form-item>
    </template>
    <template v-else-if="childData.type == 'table'">
      <a-form-item :label="childData.label" :name="childData.field">
        <a-table :dataSource="childData.dataSource" :columns="childData.columns">
          <!--            <template #title v-if="!isRead">
              <a-button type="primary" @click="handleAdd" preIcon="ant-design:plus-outlined">
                New
              </a-button>
            </template>-->

          <template #userInfo="{ text, record }">
            <tableUser v-model:value="record.creator"></tableUser>
          </template>
          <template #severity="{ text, record }">
            <a-tag color="red" v-if="text">{{ text }}</a-tag>
          </template>
          <template #members="{ text, record }">
            <div class="user_wraper" v-if="record.userList && record.userList.length > 0">
              <template v-for="(item, index) in record.userList">
                <a-tooltip placement="bottomRight">
                  <template #title> {{ item.userName }}</template>
                  <UserName :record="{ avatar: item.avatar, username: item.userName }" :unShowUsername="true" style="margin-left: 3px"></UserName>
                </a-tooltip>
              </template>
            </div>
          </template>
          <!--操作栏-->
          <!--            <template #action="{ record }" v-if="!isRead">
              <a-button @click="handleOpen(record)" style="margin-right: 5px;">Open</a-button>
              <a-button @click="handleDelete(record)">Delete</a-button>
            </template>-->
        </a-table>
      </a-form-item>
    </template>

    <template v-else-if="childData.type == 'investigation'">
      <a-form-item :label="childData.label" :name="childData.field">
        <div :style="{ width: childData.componentProps.width }">
          <a-row :gutter="[16, 8]">
            <template v-for="(item, index) in childData.dataSource" :key="'investigation' + index">
              <a-col :span="5" class="inves_div_parent" style="max-width: none">
                <div class="inves_div">
                  <div class="inves_del" v-if="!isRead" @click="delInvestigation(index)">
                    <img src="../../../../assets/images/del.png" />
                  </div>
                  <div class="inves_div__name">
                    {{ item.investigation }}
                  </div>
                  <div class="inves_div__severity">
                    <span
                      :class="{
                        CriticalClass: item.severity == 'Critical',
                        HighClass: item.severity == 'High',
                        MediumClass: item.severity == 'Medium',
                        LowClass: item.severity == 'Low',
                        InformationClass: item.severity == 'Information',
                      }"
                    >
                      <Icon icon="ant-design:warning-filled" v-if="item.severity != 'Information'" />
                      <Icon icon="ant-design:exclamation-circle-filled" v-if="item.severity == 'Information'" />
                      {{ item.severity }}
                    </span>
                  </div>
                </div>
              </a-col>
            </template>
            <a-col :span="4" v-if="!isRead">
              <div class="inves_add" @click="showAddInvestigation">
                <div>
                  <Icon icon="ant-design:plus-outlined" size="24" />
                </div>
                <div>Add</div>
              </div>
            </a-col>
          </a-row>
        </div>
      </a-form-item>
    </template>
    <template v-else-if="childData.type == 'riskCenter'">
      <a-form-item :label="childData.label" :name="childData.field">
        <div :style="{ width: childData.componentProps.width }" class="flex-row">
          <template v-for="(item, index) in childData.dataSource" :key="'riskCenter' + index">
            <div class="riskcenter_wrapper" @click.prevent.stop="openRiskCenterDetail(item)">
              <div class="del_btn" v-if="!isRead" @click="delRiskCenter(index)">
                <img src="../../../../assets/images/del.png" />
              </div>
              <!--                    <div class="font14 fcolor3">
                      {{eventTypeMap[item.type]}}
                    </div>-->
              <div class="font13 fcolor ellipsis">
                {{ item.name }}
              </div>
              <div class="font12 flex-between">
                <span
                  :class="{
                    CriticalClass: item?.severity?.toLowerCase() == 'critical',
                    HighClass: item?.severity?.toLowerCase() == 'high',
                    MediumClass: item?.severity?.toLowerCase() == 'medium',
                    LowClass: item?.severity?.toLowerCase() == 'low',
                    InformationClass: item?.severity?.toLowerCase() == 'information',
                  }"
                >
                  <Icon icon="ant-design:exclamation-circle-filled" v-if="item.severity?.toLowerCase() == 'information'" />
                  <Icon icon="ant-design:warning-filled" v-else />
                  {{ item.severity }}
                </span>
                <span :class="['status', 'status-' + statusMap[item.type][item.status]]">{{ statusMap[item.type][item.status] }}</span>
              </div>
            </div>
          </template>
          <a-col :span="4" v-if="!isRead">
            <div class="inves_add" @click="showAddRiskCenter">
              <div>
                <Icon icon="ant-design:plus-outlined" size="24" />
              </div>
              <div>Add</div>
            </div>
          </a-col>
        </div>
      </a-form-item>
    </template>
  </div>
  <WorkflowInvestigation ref="workflowInvestigationRef" @ok="investigationSuccess" />
  <WorkflowRiskcenter ref="riskCenterRef" @ok="addRiskCenter"></WorkflowRiskcenter>
  <a-modal v-model:visible="riskCenterVisible" :title="t('common.details')" centered :footer="null">
    <RiskCenterModal :data="riskCenterData" />
  </a-modal>
</template>

<script name="WorkflowComponents" lang="ts" setup>
  import RiskCenterModal from './event/RiskCenterModal.vue';
  import { deleteFile, downloadFileBlob } from '/@/utils/common/renderUtils';
  import dayjs from 'dayjs';
  import { defineEmits, reactive, ref, watch } from 'vue';
  import { Component } from '/@/views/workflow/template/ts/Component';
  import { uploadContent } from '/@/api/common/api';
  import { getToken } from '/@/utils/auth';
  import { useMessage } from '/@/hooks/web/useMessage';
  import UserName from '/@/components/vsoc/UserName.vue';
  import WorkflowInvestigation from '/@/views/workflow/view/components/WorkflowInvestigation.vue';
  import JUpload from '/@/components/Form/src/jeecg/components/JUpload/JUpload.vue';
  import WorkflowRiskcenter from '/@/views/workflow/view/components/WorkflowRiskcenter.vue';
  import { statusMap } from '/@/views/workflow/view/ts/TicketUtils';
  import { queryById } from '/@/views/aggregationRiskEventView/RiskEventView.api';
  import {useI18n} from "/@/hooks/web/useI18n";
  const {t} = useI18n()
  const emit = defineEmits(['update:form', 'ok']);
  const headers = reactive({ 'X-Access-Token': getToken() });
  const uploadFiles = ref([]);
  const filePath = ref('');
  const isShow = ref(true);
  const isRead = ref(false);
  const riskCenterVisible = ref(false);
  const riskCenterData = ref()
  const arrayValue = ref([]);
  const riskCenterRef = ref();

  const props = defineProps({
    value: {
      type: Object,
      default: {},
    },
    ruleConfig: {
      type: Object,
      default: {},
    },
    form: {
      type: String,
      default: '{}',
    },
    type: {
      type: String,
      default: '',
    },
    isRead: {
      type: Boolean,
      default: false,
    },
  });
  const { createMessage } = useMessage();
  const showUploadList = ref<boolean>(false);
  let childData = ref<Component>({});
  childData.value = props.value;
  let form = ref({});
  form.value = props.form;
  const type = props.type;
  if (type == 'apply') {
    form.value[childData.value.field] = childData.value.defaultValue;
    if (childData.value.component == 'table' && childData.value.dataSource.length > 0) {
      form.value[childData.value.field] = childData.value.dataSource;
    }
  }
  // console.log('props.ruleConfig======>',props.ruleConfig)
  // console.log('props.form======>',form.value)

  if (props.ruleConfig && props.ruleConfig[childData.value.field]) {
    if (props.ruleConfig[childData.value.field] == 'hide') {
      isShow.value = false;
    } else if (props.ruleConfig[childData.value.field] == 'readonly') {
      isRead.value = true;
    } else {
      isRead.value = props.ruleConfig[childData.value.field].readonly;
      isShow.value = !props.ruleConfig[childData.value.field].hide;
    }
  }

  if (props.isRead == true) {
    isRead.value = true;
  }
  if (childData.value.type == 'upload') {
    if (!form.value[childData.value.field]) {
      form.value[childData.value.field] = [];
    } else {
      let uploadData = form.value[childData.value.field];
      console.log('uploadData', uploadData);
    }
  } else if (childData.value.type == 'switch') {
    if (!form.value[childData.value.field]) {
      form.value[childData.value.field] = childData.value.componentProps.unCheckedValue;
    }
  } else if (childData.value.type == 'checkboxGroup') {
    if (form.value[childData.value.field]) {
      if (!Array.isArray(form.value[childData.value.field])) {
        arrayValue.value = form.value[childData.value.field].split(',');
      } else {
        arrayValue.value = form.value[childData.value.field];
      }
    } else if (childData.value.defaultValue) {
      arrayValue.value = childData.value.defaultValue;
    } else {
      arrayValue.value = [];
    }
  } else if (childData.value.type == 'radioGroup') {
    if (!form.value[childData.value.field] && childData.value.defaultValue) {
      form.value[childData.value.field] = childData.value.defaultValue;
    }
  } else if (childData.value.type == 'select' && childData.value.componentProps.multiple) {
    if (form.value[childData.value.field]) {
      arrayValue.value = form.value[childData.value.field].split(',');
    } else {
      arrayValue.value = [];
    }
  } else if (childData.value.component == 'DatePicker' && form.value[childData.value.field]) {
    console.log('DatePicker value', form.value[childData.value.field]);
    form.value[childData.value.field] = dayjs(form.value[childData.value.field], childData.value.componentProps.format);
  } else if (childData.value.component == 'TimePicker' && form.value[childData.value.field]) {
    form.value[childData.value.field] = dayjs(form.value[childData.value.field], childData.value.componentProps.valueFormat);
  } else if (childData.value.component == 'table' && form.value[childData.value.field]) {
    childData.value.dataSource = form.value[childData.value.field];
  } else if ((childData.value.component == 'investigation' || childData.value.component == 'riskCenter') && form.value[childData.value.field]) {
    childData.value.dataSource = form.value[childData.value.field];
  }
  watch(
    () => arrayValue.value,
    (n) => {
      // console.log('value change',n)
      if (n.length > 0) {
        form.value[childData.value.field] = n.toString();
      } else {
        form.value[childData.value.field] = '';
      }
    }
  );
  watch(
    () => form.value,
    () => {
      emit('update:form', form.value);
    },
    { deep: true }
  );

  watch(
    () => props.isRead,
    (n) => {
      // console.log('component isRead:',n)
      isRead.value = n;
    }
  );

  function windowOpen(url, fileName) {
    var xhr = new XMLHttpRequest();
    xhr.open('GET', url, true);
    xhr.responseType = 'blob';
    xhr.setRequestHeader('token', getToken());
    xhr.onload = function (res) {
      if (this.status === 200) {
        var type = xhr.getResponseHeader('Content-Type');
        var blob = new Blob([this.response], { type: type });
        if (typeof window.navigator.msSaveBlob !== 'undefined') {
          /*
           * For IE
           * >=IE10
           */
          window.navigator.msSaveBlob(blob, fileName);
        } else {
          /*
           * For Non-IE (chrome, firefox)
           */
          var URL = window.URL || window.webkitURL;
          var objectUrl = URL.createObjectURL(blob);
          if (fileName) {
            var a = document.createElement('a');
            if (typeof a.download === 'undefined') {
              window.location = objectUrl;
            } else {
              a.href = objectUrl;
              a.download = fileName;
              document.body.appendChild(a);
              a.click();
              a.remove();
            }
          } else {
            window.location = objectUrl;
          }
        }
      }
    };
    xhr.send();
  }
  /**
   * delete upload files
   */
  function deleteUploadFiles(path, index) {
    console.log('deleteUploadFiles', path);
    console.log('deleteUploadFiles index', index);
    deleteFile(path, (data) => {
      console.log('deleteUploadFiles data', data);
      if (-1 != data.indexOf('success')) {
        form.value[childData.value.field].splice(index, 1);
      }
    });
  }

  const handleUploadChange = (info) => {
    console.log('handleUploadChange', info);
    console.log('handleUploadChange uploadFiles', uploadFiles);

    // form.value[childData.value.field] = [...info.fileList];
    // showUploadList.value = info.file.status == 'uploading';
    //
    // if (info.file.status === 'done') {
    //   showUploadList.value = false;
    //   form.value[childData.value.field] = [];
    //   info.fileList.forEach(item=>{
    //     let path = item.response.message;
    //     form.value[childData.value.field].push({
    //       name : item.name,
    //       path : path
    //     })
    //   })
    //
    // } else if (info.file.status === 'error') {
    //   createMessage.error(`${info.file.name} file upload failed.`);
    // }
  };
  function handleOpen() {}

  function handleAdd() {}
  function handleDelete() {}

  //---------------Investigation start ======================================
  const workflowInvestigationRef = ref();

  function showAddInvestigation() {
    if (isRead.value == true) {
      return;
    }
    //需要添加校验，多租户的情况下不能弹出
    if (form.value.tenantId && form.value.tenantId.split(',').length > 1) {
      createMessage.warn('Multiple tenants cannot choose investigation');
      return;
    }
    let list = childData.value.dataSource;

    let ids: any = [];
    for (let i in list) {
      ids.push(list[i].id);
    }

    workflowInvestigationRef.value.show(ids.join(','), form.value.tenantId);
  }

  function delInvestigation(index) {
    childData.value.dataSource.splice(index, 1);
  }

  function investigationSuccess(list) {
    console.log(list);
    let data: any = childData.value.dataSource || [];
    console.log(data);
    for (let i in list) {
      data.push({
        investigation: list[i].investigation,
        severity: list[i].severity,
        id: list[i].id,
      });
    }
    childData.value.dataSource = data;
    form.value[childData.value.field] = childData.value.dataSource;
  }

  //---------------Investigation end ======================================
  //---------------risk center start ======================================
  function addRiskCenter(list) {
    let data: any = childData.value.dataSource || [];
    childData.value.dataSource = [...data, ...list];
    form.value[childData.value.field] = childData.value.dataSource;
  }
  function delRiskCenter(index) {
    childData.value.dataSource.splice(index, 1);
  }
  function showAddRiskCenter() {
    riskCenterRef.value.show();
  }
  // 打开risk center 详情页
  function openRiskCenterDetail(item) {
    queryById({ id: item.id }).then((data) => {
      console.log(data);
      riskCenterData.value = data
      riskCenterVisible.value = true;

    });
    console.log(item);
  }

  //---------------risk center end============================================
</script>

<style lang="less" scoped>
  @import '../../template/less/template.less';
  .ant-input-disabled {
    background-color: transparent !important;
    color: inherit !important;
    border: 0 !important;
  }
  .radio_block {
    display: flex;
    margin-left: 0;
  }
  .log_item {
    display: inline-block;
    padding: 0 5px;
  }

  .row-col {
    border: 1px dashed @border-color;
  }
  .grid-row {
    min-height: 100px;
  }
  .item-active {
    border-left: 5px solid @primary-color;
    background: @bg-color;
    .tool {
      display: block !important;
    }
  }
  .uploadFiles {
    padding: 5px 0px;
    cursor: pointer;
    .app-iconify {
      font-size: 12px;
      margin-left: 10px;
    }
  }

  .inves_div_parent {
    flex: 0 0 200px;
    max-width: none;
  }

  .inves_div {
    height: 88px;
    width: 200px;
    border: 1px solid @border-color;
    border-radius: 8px;
    padding: 16px;
    position: relative;
    display: flex;
    flex-direction: column;
    .inves_del {
      position: absolute;
      right: -8px;
      top: -8px;
      line-height: 16px;
      cursor: pointer;
    }

    .inves_div__name {
      font-size: 13px;
      flex: 1;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
      word-break: keep-all;
    }

    .inves_div__severity {
      margin-top: 8px;
      font-size: 12px;
    }
  }

  .inves_add {
    width: 88px;
    height: 88px;
    border: 1px solid @border-color;
    border-radius: 8px;
    text-align: center;
    padding-top: 20px;
    font-size: 12px;
    cursor: pointer;
  }
  .flex-row {
    display: flex;
    flex-direction: row;
    gap: 12px;
    flex-wrap: wrap;
  }
  .riskcenter_wrapper {
    width: 200px;
    border: 1px solid @border-color;
    border-radius: 8px;
    padding: 16px;
    position: relative;
    display: flex;
    flex-direction: column;
    gap: 12px;
    .del_btn {
      position: absolute;
      right: -8px;
      top: -8px;
      line-height: 16px;
      cursor: pointer;
    }

    .ellipsis {
      flex: 1;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
      word-break: keep-all;
    }

    .flex-between {
      display: flex;
      flex: 1;
      flex-direction: row;
      justify-content: space-between;
      justify-items: center;
      font-size: 12px;
    }
    .status {
      color: red;
    }
    .status-Closed {
      color: greenyellow;
    }
  }
</style>
