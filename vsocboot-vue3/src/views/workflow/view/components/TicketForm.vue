<template>
  <div class="handle_workflow_order" >

      <a-form ref="formRef" :model="form" :rules="validatorRules"  layout="vertical" >
        <ComponentList v-model:value="design" v-model:form="form"
                       v-model:isRead="isRead"         v-model:ruleConfig="ruleConfig"       ref="componentRef" ></ComponentList>
      </a-form>


  </div>

</template>

<script lang="ts" name="TicketForm" setup>
import ComponentList from "./ComponentList.vue";
import {defineEmits, defineProps, ref, watch, unref, defineExpose} from "vue";
const emit = defineEmits(['update:value','update:variables','ok']);
const formRef = ref();
const form = ref({});
const ruleConfig = ref({});
const isShow = ref(false);
const isRead = ref(false);
const design = ref({
  list : []
});
const validatorRules = ref({});
const props = defineProps({
  value: {
    type: Object,
    default: { },
  },
  variables: {
    type: Object,
    default: {},
  },
  isRead: {
    type: Boolean,
    default: false,
  },
  rules: {
    type: Object,
    default: {},
  },
})
design.value = props.value;
form.value = props.variables;
isRead.value = props.isRead;
watch(
  () =>  props.value,
  () => {
    isShow.value = true;
    design.value = props.value;
    console.log('props.rules :',props.rules )
    if(props.rules && JSON.stringify(props.rules) != '{}'){
      validatorRules.value = {};
      let map = props.rules;
      for(let i in map){
        if(map[i] == 'required'){
           validatorRules.value[i] = [{ required: true, message: 'please enter the value!', trigger: 'blur' }];
        }
      }
      ruleConfig.value = map;


      console.log('validatorRules.value',validatorRules.value)
    }
  },
  { deep: true ,immediate:true}
)

watch(()=>props.isRead,(n)=>{
  isRead.value = n;
  console.log('isRead----------->',n)
})

watch(form,(n,o) => {
    console.log('change form ',n)
    emit('update:variables',n);
  },
  { deep: true }
)
async function validate(){
  console.log('validate============ validate')
 let values =  await formRef.value.validateFields();
 emit('ok');
}
function close(){
  design.value = {
    list : []
  };
  form.value = {};
}

defineExpose({
  validate,

});
</script>

<style lang="less" scoped>
.handle_workflow_order{
  .row-col {
    border: 0px!important;
  }
}
</style>
