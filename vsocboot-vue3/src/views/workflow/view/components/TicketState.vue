<template>
  <div :class="['state','border-right',{'state-active' : (state == 2 || state == 0)},stateColor[state]]">
      <div> <Icon :icon="stateIcon[state]" size="22"/></div>
      <div :class="['font12']">{{stateMap[state]}}</div>
  </div>
</template>

<script lang="ts" name="WorkState" setup>

import {computed, defineProps, unref} from "vue";

const props = defineProps({
  value: String
});
const stateMap = {
  2:'Processing',
  0:'Processing',
  1:'Close',
  3:'Recall'
}
const stateColor = {
  2:'fcolor',
  0:'fcolor',
  1:'primaryColor',
  3:'fcolor3'
}
const stateIcon = {
  0:'ant-design:clock-circle-filled',
  2:'ant-design:clock-circle-filled',
  1:'ant-design:check-circle-filled',
  3:'ant-design:minus-circle-filled'
}
const state = computed(() => {
    let value = props.value;

    return value;
});

</script>

<style scoped lang="less">
.state{
  display: flex;
  width: 80px;
  text-align: center;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  border-radius: 8px 0px 0px 8px;
}
.state-active{
  background: @primary-color;
}
</style>
