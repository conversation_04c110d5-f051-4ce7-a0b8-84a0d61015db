<template>

  <div>
    <BasicTable @register="registerTable" :isSearch="isSearch"  :rowSelection="rowSelection as any"   >
      <!--插槽:table标题-->
<!--      <template #form-formFooter>
        <a-button :class="{'btn-checked': isSearch == true}" type="text" @click="isSearch=!isSearch"
                  preIcon="ant-design:filter-outlined"> {{ t('common.filter') }}
        </a-button>
      </template>-->


      <template #severity="{ text, record }">
        <span class="severity1" v-if="record?.severity?.toLowerCase() == 'critical'">
          {{ record?.severity }}
        </span>
        <span class="severity2"
              v-if="text?.severity?.toLowerCase() == 'high'">{{ record?.severity }}</span>
        <span class="severity3"
              v-if="text?.severity?.toLowerCase() == 'medium'">{{ record?.severity }}</span>
        <span class="severity4"
              v-if="text?.severity?.toLowerCase() == 'low'">{{ record?.severity }}</span>
      </template>

    </BasicTable>
  </div>

</template>

<script lang="ts" name="riskcenter-badActors" setup>
import {BasicTable} from '/@/components/Table';
import {useListPage} from '/@/hooks/system/useListPage'
import {useI18n} from "/@/hooks/web/useI18n";
import {formLayout} from '/@/settings/designSetting';
import {defineExpose, ref} from 'vue';
import {
  badactorsColumns,
  badactorsFormSchema
} from "/@/views/workflow/view/components/event/RiskCenter.data";
import {badactorEventList} from "/@/views/workflow/view/components/event/RiskCenter.api";

const {t} = useI18n();
const isSearch = ref<boolean>(true);




const {tableContext} = useListPage({
  tableProps: {
    title: 'bad actors',
    api: badactorEventList,
    columns: badactorsColumns,
    canResize: false,
    formConfig: {
      labelWidth: 120,
      baseColProps: {
        lg: 6, // ≥992px
        xl: 4, // ≥1200px
        xxl: 4, // ≥1600px
      },
      schemas: badactorsFormSchema,
      autoSubmitOnEnter: true,
      showAdvancedButton: true,
      layout: formLayout,
    },
    defSort: {
      column: "attackLatestTime",
      order: "desc"
    },
    actionColumn: {
      defaultHidden:true,
    },
    rowSelection: {
      columnWidth: 10
    },
    afterFetch: dataSource
  },

})

const [registerTable, {reload}, {rowSelection,selectedRows}] = tableContext;

function dataSource(a) {
  for (let i = 0; i < a.length; i++) {
    if (a[i].attackWay) {
      let attackWays = a[i].attackWay.split(",");
      if (attackWays.length > 2) {
        attackWays = [attackWays[0], attackWays[1]]
      }
      a[i]['attackWays'] = attackWays;
    }
  }
}

function getSelected() {
  return rowSelection.selectedRows;
}

defineExpose({
  getSelected
})
</script>
<style scoped lang="less">
.search-left {
  top: 15px
}

.pageHeaderContainer {
  padding: 10px;

  span {
    font-size: 20px
  }
}

.chartContainer {

  .chartCol {
    border-left: 1px solid #1A1B1F;
    border-top: 1px solid #1A1B1F;
    border-bottom: 1px solid #1A1B1F;
    padding: 12px 16px;

    &:first-child {
      border-left: 0px
    }
  }

}

.bad_actor_main {
  padding: 5px 0px;
}


</style>
<style lang="less">

.tenant_div {
  .ant-table-tbody .ant-table-row:hover > td {
    background: transparent !important;
  }

  .ant-table-tbody > tr > td.ant-table-cell.ant-table-cell-row-hover {
    background: transparent !important;
  }

  .tenant_table .ant-table-tbody > tr:not(.ant-table-measure-row) > td.ant-table-cell {
    padding: 8px !important;
  }

  .tenant_table .ant-table-thead > tr > th {
    padding: 8px !important;
  }

  .tenant_table .ant-table-tbody .ant-table-placeholder {
    display: none;
  }

  .ant-table-summary {
    font-size: 13px;
  }

  .other_action {
    margin-bottom: 4px;
    width: 100%;

    .ant-space-item {
      width: 100%;
      text-align: center;
    }
  }
}


</style>
