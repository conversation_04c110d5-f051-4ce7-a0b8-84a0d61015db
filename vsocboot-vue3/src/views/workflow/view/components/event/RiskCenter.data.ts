import {FormSchema} from "/@/components/Form";
import {isAdministrator} from "/@/utils/auth";
import {EVENT_SEVERITY} from "/@/views/correlationevent/CorrelationEvent.data";
import {useI18n} from "/@/hooks/web/useI18n";
import {BasicColumn} from "/@/components/Table";
import {createVNode} from "vue";
import dayjs from "dayjs";

const {t} = useI18n();
export const CorrelationEventSearchFormSchema = ():FormSchema[] => [
  {
    label: t('common.tenantName'),
    field: 'socTenantId',
    component: 'JDictSelectTag',
    componentProps: {
      showSearch: true,
      dictCode: 'tenantDict',
    },
    ifShow: isAdministrator()
  },
  {
    label: 'Threat Name',
    field: 'eventName',
    component: 'Input'
  },
  {
    label: 'Tactic',
    field: 'eventTacticId',
    //component: 'JInput'
    component: 'JDictSelectTag',
    componentProps: {
      showSearch: true,
      dictCode: 'tacticsDict',
    }
  },
  {
    label: 'Technique',
    field: 'techniqueText',
    component: 'Input'
  },
  {
    label: 'Severity',
    field: 'eventSeverity',
    component: 'Select',
    componentProps: {
      options: EVENT_SEVERITY,
      stringToNumber: true,
    }
  },
  {
    label: 'Status',
    field: 'eventStatus',
    component: 'Select',
    componentProps: {
      options: [{
        label: 'New',
        value: '0',
        key: '0',
      }, {
        label: 'Pending',
        value: '1',
        key: '1',
      }, {
        label: 'Investigating',
        value: '2',
        key: '2',
      }, {
        label: t('common.Closed'),
        value: '3',
        key: '3',
      }],
    },
  },
  {
    label: 'Last Detected',
    field: 'startDate',
    component: 'RangeDate',
    componentProps: {
      datetime: true,
    },
    colProps: {
      lg: 12, // ≥992px
      xl: 6, // ≥1200px
      xxl: 6, // ≥1600px
    },
  },
];
export const CorrelationEventColumns = ():BasicColumn[]=>{
  return [
    {
      title: t('common.tenantName'),
      dataIndex: 'socTenantId_dictText',
      ifShow: isAdministrator()
    },
    {
      title: t('routes.CorrelationEvent.eventName'),
      dataIndex: 'eventName',
    },
    {
      title: t('routes.CorrelationEvent.tactic'),
      dataIndex: 'eventTacticId_dictText',
    },
    {
      title: t('routes.CorrelationEvent.techniques'),
      sorter: true,
      dataIndex: 'techniques',
      width: 120
    },
    {
      title: t('routes.CorrelationEvent.subTechniques'),
      sorter: true,
      dataIndex: 'subTechniques',
      width: 140
    },
    {
      title: t('routes.CorrelationEvent.updateTime'),
      dataIndex: 'updateTime',
    },
    {
      title: t('routes.CorrelationEvent.createTime'),
      dataIndex: 'createTime',
    },
    {
      title: t('routes.CorrelationEvent.severity'),
      dataIndex: 'eventSeverity',
      slots: {customRender: 'severity'}
    },
    {
      title: t('routes.CorrelationEvent.owner'),
      dataIndex: 'owner_dictText',
      customRender: ({text,record}) => {
        const data: any = record;
        if (data?.owner === 'unassign') {
          return createVNode('div', {class: 'redClass'}, [
            t('common.Unassigned')
          ]);
        }
        return text
      }
    },
    {
      title: t('routes.CorrelationEvent.triage'),
      dataIndex: 'dismiss',
      customRender: ({text}) => {
        if (text === 0) {
          return t('common.Untriaged')
        } else if (text === 1) {
          return createVNode('div', {class: 'ax-label '}, [
            t('common.TurePositive')
          ]);
        } else if (text === 2) {
          return createVNode('div', {class: 'greenClass'}, [
            t('common.FalsePositive')
          ]);
        } else if (text === 3) {
          return createVNode('div', {class: 'greenClass'}, [
            'Unknow'
          ]);
        }
      }
    },
    {
      title: t('routes.CorrelationEvent.eventStatus'),
      dataIndex: 'eventStatus',
      customRender: ({text}) => {
        if (text === 0) {
          return createVNode('div', {class: 'redClass'}, [
            t('common.Unclosed')
          ]);
        } else if (text === 1) {
          return createVNode('div', {class: 'greenClass'}, [
            t('common.Closed')
          ]);
        }
      }
    },
  ]
};



export const riskEventColumns = (): BasicColumn[] => [
  {
    title: t('common.tenantName'),
    dataIndex: 'socTenantId_dictText',
    ifShow: isAdministrator()
  },
  {
    title: t('routes.RiskEventLogView.eventName'),
    dataIndex: 'eventName'
  },
  {
    title: t('routes.RiskEventLogView.updateTime'),
    dataIndex: 'updateTime'
  },
  {
    title: t('routes.RiskEventLogView.logCount'),
    dataIndex: 'mergeCount'
  },
  {
    title: t('routes.RiskEventLogView.severity'),
    dataIndex: 'severity',
    slots: {customRender: 'severity'}
  },
  {
    title: t('routes.RiskEventLogView.srcIp'),
    dataIndex: 'srcIp'
  },
  {
    title: t('routes.RiskEventLogView.dstIp'),
    dataIndex: 'dstIp'
  },
  {
    title: t('routes.RiskEventLogView.fromIp'),
    dataIndex: 'fromIp'
  },
  {
    title: t('routes.RiskEventLogView.owner'),
    dataIndex: 'owner_dictText',
    customRender: ({text,record}) => {
      const data: any = record;
      if (data?.owner === 'unassign') {
        return createVNode('div', {class: 'redClass'}, [
          t('common.Unassigned')
        ]);
      }
      return text
    }
  },
  {
    title: t('routes.RiskEventLogView.triage'),
    dataIndex: 'triageStatus',
    // slots: {customRender: 'triageStatus'},
    customRender: ({text}) => {
      if (text === 0) {
        return createVNode('div', {class: 'ax-color-yellow'}, [
          t('common.Untriaged')
        ]);
      } else if (text === 1) {
        return createVNode('div', {class: 'ax-color-red'}, [
          t('common.TurePositive')
        ]);
      } else if (text === 2) {
        return createVNode('div', {class: 'ax-color-cyan'}, [
          t('common.FalsePositive')
        ]);
      } else if (text === 3) {
        return createVNode('div', {class: 'ax-color-yellow'}, [
          t('routes.RiskEventLogView.other')
        ]);
      }
    }
  },
  {
    title: t('routes.RiskEventLogView.eventStatus'),
    dataIndex: 'eventStatus',
    customRender: ({text}) => {
      if (text === 1) {
        return createVNode('div', {class: 'ax-color-red'}, [
          t('common.Unclosed')
        ]);
      } else if (text === 2) {
        return createVNode('div', {class: 'ax-color-cyan'}, [
          t('common.Closed')
        ]);
      }
    }
  },

];

export const riskEventSearchFormSchema = (): FormSchema[] => [
  {
    label: t('common.tenantName'),
    field: 'socTenantId',
    component: 'JSearchSelect',
    componentProps: {
      dict: "tenantDict"
    },
    ifShow: isAdministrator()
  },
  {
    label: t('routes.RiskEventLogView.eventName'),
    field: 'eventName',
    component: 'JInput',
  },
  {
    label: t('routes.RiskEventLogView.eventType'),
    field: 'type',
    component: 'Select',
    componentProps: {
      options: [
        {label: 'Risk by security device', value: '1'},
        {label: 'Risk by endpoint', value: '2'},
      ],
    },
  },
  {
    label: t('routes.RiskEventLogView.severity'),
    field: 'severityStr',
    component: 'JSelectMultiple',
    componentProps: {
      options: [
        {label: 'Low', value: 'Low'},
        {label: 'Medium', value: 'Medium'},
        {label: 'High', value: 'High'},
        {label: 'Critical', value: 'Critical'},
      ],
    },
  },
  {
    label: t('routes.RiskEventLogView.owner'),
    field: 'owner',
    component: 'JSearchSelect',
    componentProps: {
      dict: "sysUserDict"
    }
  },
  {
    label: t('routes.RiskEventLogView.triage'),
    field: 'triageStatus',
    component: 'Select',
    componentProps: {
      options: [
        {label: 'Untriaged', value: 0},
        {label: t('common.TurePositive'), value: 1},
        {label: t('common.FalsePositive'), value: 2},
      ],
    },
  },
  {
    label: t('routes.RiskEventLogView.eventStatus'),
    field: 'eventStatus',
    component: 'Select',
    componentProps: {
      options: [
        {label: t('common.Closed'), value: 2},
        {label: t('common.Unclosed'), value: 1},
      ],
    },
  },

  {
    label: t('routes.RiskEventLogView.srcIp'),
    field: 'srcIp',
    component: 'JInput',
  },
  {
    label: t('routes.RiskEventLogView.dstIp'),
    field: 'dstIp',
    component: 'JInput',
  },
  {
    label: t('routes.RiskEventLogView.fromIp'),
    field: 'fromIp',
    component: 'JInput',
  },
  {
    label: t('routes.RiskEventLogView.updateTime'),
    field: 'updateTimeStr',
    component: 'RangeDate',
    componentProps: {
      datetime: true
    },
    colProps: {
      lg: 12, // ≥992px
      xl: 6, // ≥1200px
      xxl: 5, // ≥1600px
    },
    defaultValue: dayjs().subtract(7, 'day').format('YYYY-MM-DD HH:mm:ss') + ',' + dayjs().format('YYYY-MM-DD HH:mm:ss')
  },


];


export const mlColumns = (): BasicColumn[] => [
  {
    title: t('common.tenantName'),
    dataIndex: 'socTenantId_dictText',
    ifShow: isAdministrator()
  },
  {
    title: t('routes.MlEvent.ruleName'),
    dataIndex: 'ruleName'
  },
  {
    title: t('routes.MlEvent.urgency'),
    dataIndex: 'urgency',
    slots: {customRender: 'severity'},
  },
  {
    title: t('routes.MlEvent.alarmTime'),
    dataIndex: 'alarmTime'
  },
  {
    title: t('routes.MlEvent.owner'),
    dataIndex: 'owner_dictText',
    customRender: ({text, record}) => {
      const data: any = record;
      if (data?.owner === 'unassign') {
        return createVNode('div', {class: 'redClass'}, [
          t('common.Unassigned')
        ]);
      }
      return text
    }
  },
  {
    title: t('routes.MlEvent.triage'),
    dataIndex: 'triageStatus',
    customRender: ({text}) => {
      if (text === 0) {
        return t('common.Untriaged')
      } else if (text === 1) {
        return createVNode('div', {class: 'redClass'}, [
          t('common.TurePositive')
        ]);
      } else if (text === 2) {
        return createVNode('div', {class: 'greenClass'}, [
          t('common.FalsePositive')
        ]);
      }
    }
  },
  {
    title: t('routes.MlEvent.riskStatus'),
    dataIndex: 'riskStatus',
    customRender: ({text}) => {
      if (text === 1) {
        return createVNode('div', {class: 'redClass'}, [
          t('common.Unclosed')
        ]);
      } else if (text === 2) {
        return createVNode('div', {class: 'greenClass'}, [
          t('common.Closed')
        ]);
      }
    }
  },
];

export const mlFormSchema: FormSchema[] = [
  {
    label: t('routes.MlEvent.ruleName'),
    field: 'ruleName',
    component: 'JInput'
  },
  {
    label: t('routes.MlEvent.urgency'),
    field: 'urgency',
    component: 'Select',
    componentProps: {
      options: [
        {label: "Critical", value: 1},
        {label: "High", value: 2},
        {label: "Middle", value: 3},
        {label: "Low", value: 4},
        {label: "Information", value: 5},
      ]
    }
  },
  {
    label: t('routes.MlEvent.riskStatus'),
    field: 'riskStatus',
    component: 'Select',
    componentProps: {
      options: [
        {label: "Unclosed", value: 1},
        {label: "Closed", value: 2},
      ]
    }
  },
  {
    label: t('routes.MlEvent.alarmTime'),
    field: 'startDate',
    component: 'RangeDate',
    componentProps: {
      datetime: true
    },
    colProps: {
      lg: 12, // ≥992px
      xl: 6, // ≥1200px
      xxl: 5, // ≥1600px
    },
  },
];


export const suspiciousColumns: BasicColumn[] = [
  {
    title: t('routes.SuspiciousProcessesCollect.deviceIp'),
    dataIndex: 'deviceIp',
    width: 200,
  },
  // {
  //   title: t('routes.SuspiciousProcessesCollect.processCount'),
  //   dataIndex: 'processCount'
  // },
  {
    title: t('routes.SuspiciousProcessesCollect.firstDetected'),
    dataIndex: 'firstDetected',
    width: 200,
  },
  {
    title: t('routes.SuspiciousProcessesCollect.lastDetected'),
    dataIndex: 'lastDetected',
    width: 200,
  },
  {
    title: t('routes.SuspiciousProcessesCollect.status'),
    align: 'center',
    children: [
      {
        title: t('routes.SuspiciousProcessesCollect.status0'),
        dataIndex: 'num',
        width: 100,
        align: 'center',
      },
      {
        title: t('routes.SuspiciousProcessesCollect.status1'),
        dataIndex: 'num1',
        width: 100,
        align: 'center',
      },
      {
        title: t('routes.SuspiciousProcessesCollect.status2'),
        dataIndex: 'num2',
        width: 200,
        align: 'center',
      },
      {
        title: t('routes.SuspiciousProcessesCollect.status3'),
        dataIndex: 'num2',
        width: 200,
        align: 'center',
      },
    ],
    minWidth: 600,
  }
];

export const suspiciousFormSchema: FormSchema[] = [
  {
    label: t('routes.SuspiciousProcessesCollect.deviceIp'),
    field: 'deviceIp',
    component: 'Input'
  },
  {
    label: t('routes.SuspiciousProcessesCollect.processCount'),
    field: 'processName',
    component: 'Input'
  },
  {
    label: t('routes.SuspiciousProcessesCollect.firstDetected'),
    field: 'firstDetectedStr',
    component: 'RangeDate',
    componentProps: {
      datetime: true
    },
    colProps: {
      lg: 10, // ≥992px
      xl: 5, // ≥1200px
      xxl: 5, // ≥1600px
    },
  },
  {
    label: t('routes.SuspiciousProcessesCollect.lastDetected'),
    field: 'lastDetectedStr',
    component: 'RangeDate',
    componentProps: {
      datetime: true
    },
    colProps: {
      lg: 10, // ≥992px
      xl: 5, // ≥1200px
      xxl: 5, // ≥1600px
    },
  },
  {
    label: t('routes.SuspiciousProcessesCollect.status'),
    field: 'status',
    component: 'Select',
    componentProps: {
      options: [
        {label: "New", value: "1"},
        {label: "Pending", value: "2"},
        {label: "Closed", value: "3"}
      ]
    }
  },
];
export const badactorsColumns: BasicColumn[] = [
  {
    title: t('routes.badActors.badActor'),
    dataIndex: 'ip',
    slots: {customRender: 'ip'},
    width: 120
  },
  {
    title: 'Severity',
    dataIndex: 'severity',
    slots: {customRender: 'severity'},
    width: 100
  },
  {
    title: t('routes.badActors.members'),
    dataIndex: 'members',
    width: 290,
    customRender: ({record}) => {
      const data: any = record;
      return data?.attackFirstTime + " " + data?.attackLatestTime
    },
  },
  {
    title: t('routes.badActors.attackLatestTime'),
    dataIndex: 'attackLatestTime',
    width: 180
  },
  {
    title: t('routes.badActors.targetNum'),
    dataIndex: 'targetNum',
    width: 100
  },
  {
    title: t('routes.badActors.attackNum'),
    dataIndex: 'attackNum',
    width: 120
  },
  {
    title: t('routes.badActors.attackWayNum'),
    dataIndex: 'attackWayNum',
    width: 140
  },
  {
    title: t('routes.badActors.owner'),
    dataIndex: 'owner_dictText',
    customRender: ({text, record}) => {
      const data: any = record;
      if (data?.owner === 'unassign') {
        return createVNode('div', {class: 'redClass'}, [
          t('common.Unassigned')
        ]);
      }
      return text
    },
    width: 120
  },
  {
    title: t('routes.badActors.triage'),
    dataIndex: 'triageStatus',
    customRender: ({text}) => {
      if (text === 0) {
        return t('common.Untriaged')
      } else if (text === 1) {
        return createVNode('div', {class: 'redClass'}, [
          t('common.TurePositive')
        ]);
      } else if (text === 2) {
        return createVNode('div', {class: 'greenClass'}, [
          t('common.FalsePositive')
        ]);
      }
    },
    width: 130
  },
  {
    title: t('routes.badActors.status'),
    dataIndex: 'status',
    customRender: ({text}) => {
      if (text === 0) {
        return createVNode('div', {class: 'redClass'}, [
          t('common.Unclosed')
        ]);
      } else if (text === 1) {
        return createVNode('div', {class: 'greenClass'}, [
          t('common.Closed')
        ]);
      }
    },
    width: 110
  },
  {
    title: t('routes.badActors.ownScore'),
    dataIndex: 'threatScore',
    width: 120
  },
  {
    title: t('routes.badActors.totalScore'),
    dataIndex: 'totalScore',
    width: 120
  },
];

export const badactorsFormSchema: FormSchema[] = [
  {
    label: 'Bad Actor',
    field: 'ip',
    component: 'Input',
    colProps: {span: 4},
  },
  {
    label: 'Severity',
    field: 'severity',
    component: 'Select',
    colProps: {span: 4},
    componentProps: {
      options: [
        {label: 'Critical', value: 'Critical'},
        {label: 'High', value: 'High'},
        {label: 'Medium', value: 'Medium'},
        {label: 'Low', value: 'Low'},
      ],
    },
  },
  {
    label: 'Status',
    field: 'status',
    component: 'Select',
    colProps: {span: 4},
    componentProps: {
      options: [
        {label: t('common.Unclosed'), value: 0},
        {label: t('common.Closed'), value: 1},
      ],
    },
  },
  {
    label: 'Last Alert Time',
    field: 'attackLatestTimeRange',
    component: 'RangeDate',
    componentProps: {
      datetime: true,
    },
    colProps: {
      lg: 12, // ≥992px
      xl: 5, // ≥1200px
      xxl: 5, // ≥1600px
    },
  },
];
