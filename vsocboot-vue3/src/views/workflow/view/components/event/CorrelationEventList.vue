<template>
  <div class="">

    <BasicTable @register="registerTable" rowKey="eventId"
                :rowSelection="rowSelection as any"    :isSearch="isSearch">

      <template #severity="{ text }">
        <span class="severity1" v-if="text == '4'">Critical</span>
        <span class="severity2" v-if="text== '3'">High</span>
        <span class="severity3" v-if="text == '2'">Medium</span>
        <span class="severity4" v-if="text == '1'">Low</span>
      </template>
    </BasicTable>

  </div>
</template>

<script lang="ts" name="correlationevent-correlationEvent" setup>
import {defineExpose, ref, toRaw} from 'vue';
import {BasicTable,} from '/@/components/Table';
import {useListPage} from '/@/hooks/system/useListPage'
import {formLayout} from '/@/settings/designSetting';
import {seList} from "/@/views/workflow/view/components/event/RiskCenter.api";
import {
  CorrelationEventColumns,
  CorrelationEventSearchFormSchema
} from "/@/views/workflow/view/components/event/RiskCenter.data";

const isSearch = ref<boolean>(true);

//注册table数据
const {tableContext} = useListPage({
  tableProps: {
    api: seList,
    columns: CorrelationEventColumns(),
    rowKey: 'eventId',
    canResize: false,
    defSort: {
      column: 'updateTime',
      order: 'desc'
    },
    formConfig: {
      baseColProps: {
        lg: 5, // ≥992px
        xl: 3, // ≥1200px
        xxl: 3, // ≥1600px
      },
      labelWidth: 120,
      schemas: CorrelationEventSearchFormSchema(),
      autoSubmitOnEnter: true,
      showAdvancedButton: false,
      layout: formLayout,
    },
    actionColumn :{
      defaultHidden : true
    }
  },
})

const [registerTable, {reload}, {rowSelection,selectedRows}] = tableContext;


function getSelected() {
  console.log(rowSelection)
  console.log(rowSelection.selectedRows)
  return rowSelection.selectedRows;
}

defineExpose({
  getSelected
})



</script>
<style lang="less" scoped>

</style>
