<template>
  <div>
    <!--引用表格-->
    <BasicTable @register="registerTable" :isSearch="isSearch" :rowSelection="rowSelection as any"  >
      <!--插槽:table标题-->
<!--      <template #form-formFooter>
        <a-button :class="{'btn-checked': isSearch == true}" type="text" @click="isSearch=!isSearch"
                  preIcon="ant-design:filter-outlined"> {{ t('common.filter') }}
        </a-button>
      </template>-->
      <template #severity="{ text }">
        <span class="severity1" v-if="text == 1">Critical</span>
        <span class="severity2" v-if="text == 2">High</span>
        <span class="severity3" v-if="text == 3">Middle</span>
        <span class="severity4" v-if="text == 4">Low</span>
        <span class="severity4" v-if="text == 5">Information</span>
      </template>
    </BasicTable>

  </div>

</template>

<script lang="ts" name="riskcenter-mlEvent" setup>
import {defineExpose, ref, toRaw} from 'vue';
import {BasicTable} from '/@/components/Table';
import {useListPage} from '/@/hooks/system/useListPage'


import {useI18n} from "/@/hooks/web/useI18n";
import {formLayout} from '/@/settings/designSetting';
import {mlEventList} from "/@/views/workflow/view/components/event/RiskCenter.api";
import {mlColumns, mlFormSchema} from "/@/views/workflow/view/components/event/RiskCenter.data";


const {t} = useI18n();
const isSearch = ref<boolean>(true);
//注册table数据
const {tableContext} = useListPage({
  tableProps: {
    title: 'ML View',
    api: mlEventList,
    columns: mlColumns(),
    canResize: false,
    formConfig: {
      labelWidth: 120,
      schemas: mlFormSchema,
      autoSubmitOnEnter: true,
      showAdvancedButton: true,
      layout: formLayout,
    },
    defSort: {
      column: 'alarmTime',
      order: 'desc',
    },
    beforeFetch: (params) => {
      let startDate = params.startDate
      if (startDate) {
        let array = startDate.split(',')
        params.alarmTime_begin = array[0]
        params.alarmTime_end = array[1]
      }
    },
    actionColumn: {
      defaultHidden:true
    },
  },
})

const [registerTable, {reload}, {rowSelection,selectedRows}] = tableContext

function getSelected() {
  return rowSelection.selectedRows;
}

defineExpose({
  getSelected
})


</script>
<style scoped>

</style>
