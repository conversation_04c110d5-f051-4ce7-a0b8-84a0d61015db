<template>
  <a-modal v-model:visible="visible" :destroyOnClose="true" title="Investigtion" @ok="handleOk" :maskClosable="false" width="1300px">
    <div class="p-[16px] all-body">
      <BasicTable @register="registerTable" :rowSelection="rowSelection as any" :isSearch="isSearch">

        <template #userInfo="{ record }">
          <UserName :record="record" />
        </template>
        <template #severity="{ text }">
          <span
            :class="{
              CriticalClass: text == 'Critical',
              HighClass: text == 'High',
              MediumClass: text == 'Medium',
              LowClass: text == 'Low',
              InformationClass: text == 'Information',
            }"
          >
            <Icon icon="ant-design:warning-filled" v-if="text != 'Information'" />
            <Icon icon="ant-design:exclamation-circle-filled" v-if="text == 'Information'" />
            {{ text }}
          </span>
        </template>
        <template #status="{ text }">
          <span :class="{ CriticalClass: text == 'Pending', textColorClass: text == 'Processing', closedClass: text == 'Closed' }">
            {{ text }}
          </span>
        </template>
      </BasicTable>
    </div>
  </a-modal>
</template>

<script lang="ts" setup>
  import UserName from '/@/components/vsoc/UserName.vue';
  import { BasicTable } from '/@/components/Table';
  import { ref, toRaw, defineExpose, defineEmits } from 'vue';
  import { useI18n } from '/@/hooks/web/useI18n';
  import { useListPage } from '/@/hooks/system/useListPage';
  import { list } from '/@/views/investigation/InvestigationVO.api';
  import { getColumns, searchFormSchema } from '/@/views/investigation/InvestigationVO.data';
  import { formLayout } from '/@/settings/designSetting';

  const { t } = useI18n();
  const isSearch = ref(true);
  const visible = ref(false);

  const emits = defineEmits(['ok']);

  const notIds = ref('');
  const socTenantId = ref('');
  //注册table数据
  const { tableContext } = useListPage({
    tableProps: {
      api: list,
      columns: getColumns(),
      canResize: false,
      formConfig: {
        baseColProps: {
          lg: 6, // ≥992px
          xl: 4, // ≥1200px
          xxl: 3, // ≥1600px
        },
        labelCol: {
          xs: 24,
          sm: 8,
          md: 24,
          lg: 24,
          xl: 24,
          xxl: 24,
        },
        schemas: searchFormSchema,
        autoSubmitOnEnter: true,
        showAdvancedButton: true,
        layout: formLayout,
      },
      beforeFetch: (params) => {
        params.notIds = notIds.value;
        params.socTenantId = socTenantId.value;
        return params;
      },
      showActionColumn: false,
    },
  });

  const [registerTable, {}, { rowSelection }] = tableContext;

  function handleOk() {
    visible.value = false;
    emits('ok', toRaw(rowSelection.selectedRows));
  }

  function show(ids, tenantId) {
    notIds.value = ids;
    visible.value = true;
    isSearch.value = true;
    socTenantId.value = tenantId;
  }

  defineExpose({
    show,
  });
</script>

<style scoped lang="less">
  .all-body {
    :deep(.searchForm) {
      background-color: transparent !important;

    }
    :deep(.ant-btn-text.btn-checked) {
      background: transparent !important;
      border-color: #06080c
    }
  }
</style>
