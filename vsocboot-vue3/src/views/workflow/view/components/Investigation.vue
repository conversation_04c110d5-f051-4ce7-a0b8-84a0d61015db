<template>
  <div class="font16 fcolor pb-16">Investigation [{{dataList.length}}]</div>
  <a-row :gutter="[16,8]">
    <template v-for="(inve,index) in dataList"
              :key="'investigation'+index">
      <a-col :span="5" class="inves_div_parent" style="max-width: none;">
        <div class="inves_div">
          <div class="inves_div__name ellipsis">
            {{ inve.investigation }}
          </div>
          <div class="inves_div__severity">
                              <span
                                :class="{
                                  'CriticalClass':inve.severity=='Critical',
                                  'HighClass':inve.severity=='High',
                                  'MediumClass':inve.severity=='Medium',
                                  'LowClass':inve.severity=='Low',
                                  'InformationClass':inve.severity=='Information'
                                }">
                                <Icon icon="ant-design:warning-filled"
                                      v-if="inve.severity != 'Information'"/>
                                <Icon icon="ant-design:exclamation-circle-filled"
                                      v-if="inve.severity == 'Information'"/>
                                {{ inve.severity }}
                              </span>
          </div>
        </div>
      </a-col>
    </template>
  </a-row>
</template>

<script lang="ts" name="Investigation" setup>
import {defineProps, ref, watch} from "vue";
import {getFormDataByType} from "/@/views/workflow/view/ts/TicketUtils";

const dataList = ref([]);
const props = defineProps({
  value: Object,
});
watch(()=>props.value,(n)=>{
  dataList.value = getFormDataByType(n,'investigation_');
},{deep:true,immediate:true})

</script>

<style lang="less" scoped>
@import "../less/event.less";
</style>
