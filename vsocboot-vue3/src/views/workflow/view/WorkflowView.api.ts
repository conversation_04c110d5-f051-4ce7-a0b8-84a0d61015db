import {defHttp} from '/@/utils/http/axios';


enum Api {
  queryTemplateDesign = '/workflow/workflowDesign/queryTemplateDesign',
  queryTemplateDesignInfo = '/workflow/workflowDesign/queryTemplateDesignInfo',
  queryInstList = '/workflow/workflowInstance/queryInstList',
  queryCopyList = '/workflow/workflowView/queryCopyList',
  queryPending = '/workflow/workflowView/queryPending',
  querySubmitList = '/workflow/workflowView/querySubmitList',
  queryProcessedList = '/workflow/workflowView/queryProcessedList',
  getNextTask = '/workflow/workflowView/getNextTask',
  apply = '/workflow/workflowInstance/apply',
  getDisposeInfo = '/workflow/workflowInstance/getDisposeInfo',
  queryProcessInfo = '/workflow/workflowInstance/queryProcessInfo',
  getAllAcitivity  = '/workflow/workflowInstance/getAllAcitivity',
  copyReadUpdate = '/workflow/workflowCopy/update',
  executionTask='/workflow/workflowInstance/executionTask',
  workflowTransfer  = '/workflow/workflowInstance/workflowTransfer',
  getHistoryTaskInfo  = '/workflow/workflowInstance/getHistoryTaskInfo',
  workflowChangeStauts  = '/workflow/workflowInstance/workflowChangeStauts',
  getWorkflowTemplateList  = '/workflow/workflowTemplate/getWorkflowTemplateList',
  entryTickets  = '/workflow/workflowSetting/queryEntryTicketList',

}

/**
 * 模板设计
 * @param params
 */
export const queryTemplateDesign = (params,handleSuccess) =>{
  return defHttp.get({url: Api.queryTemplateDesign, params}).then((result) => {
    handleSuccess(result);
  });
}

export const queryInstList = (params,handleSuccess) =>{
  defHttp.get({ url: Api.queryInstList ,params : params}).then((result)=>{
    handleSuccess(result);
  })
}
export const queryCopyList = (params,handleSuccess) =>{
  defHttp.get({ url: Api.queryCopyList ,params : params}).then((result)=>{
    handleSuccess(result);
  })
}
export const queryPending = (params,handleSuccess) =>{
  defHttp.get({ url: Api.queryPending ,params : params}).then((result)=>{
    handleSuccess(result);
  })
}
export const querySubmitList = (params,handleSuccess) =>{
  defHttp.get({ url: Api.querySubmitList ,params : params}).then((result)=>{
    handleSuccess(result);
  })
}
export const queryProcessedList = (params,handleSuccess) =>{
  defHttp.get({ url: Api.queryProcessedList ,params : params}).then((result)=>{
    handleSuccess(result);
  })
}

export const apply = (params,handleSuccess) =>{
  return defHttp.post({url: Api.apply, params}).then((result) => {
    handleSuccess(result);
  });
}





export const getDisposeInfo = (params) =>{
  return defHttp.get({url: Api.getDisposeInfo, params});
}
export const queryTemplateDesignInfo = (params,handleSuccess) =>{
  return defHttp.get({url: Api.queryTemplateDesignInfo, params}).then((result)=>{
    handleSuccess(result)
  });
}
export const queryProcessInfo = (params) =>
  defHttp.get({url: Api.queryProcessInfo, params});
export const getHistoryTaskInfo = (params,handleSuccess) =>{
  defHttp.get({url: Api.getHistoryTaskInfo, params}).then((data) => {
    handleSuccess(data);
  });
}

export const getAllAcitivity = (params,handleSuccess) =>{
  defHttp.get({url: Api.getAllAcitivity, params}).then((data) => {
    handleSuccess(data);
  });
}
export const copyReadUpdate = (params) => {
  return defHttp.post({url: Api.copyReadUpdate, params});
}
export const getWorkflowTemplateList = (params) => {
  return defHttp.get({url: Api.getWorkflowTemplateList, params});
}

export const executionTask = (params) => {
  return defHttp.get({url: Api.executionTask, params});
}
export const getNextTask = (params) => {
  return defHttp.get({url: Api.getNextTask, params});
}
export const workflowTransfer = (params) => {
  return defHttp.get({url: Api.workflowTransfer,params});
}
export const workflowChangeStauts = (params) => {
  return defHttp.get({url: Api.workflowChangeStauts, params});
}
export const getEntryTickets = (params?) =>
  defHttp.get({url: Api.entryTickets, params});
