<template>
  <div class="flex flex-v padding16" style="height: 100%">
    <div class="font16 fcolor " @click="closeRetun" v-if="initFlag">
    <span class="cursor">
      <Icon icon="ant-design:left-outlined" :size="16" style="margin-right: 8px;">
      </Icon>
      Back
    </span>
    </div>
    <div class="view-template_wraper flex flex-v flex-1"  >
      <div class="border-bottom padding24 font20 fcolor">
        {{ instData.ticketName }}
      </div>
      <div v-if="attention" class="padding16 font16 primaryColor">Attention：{{ attention }}</div>
      <div class="flex-1">
        <!-- template content-->
        <div class="form_content padding16 ">
          <TicketForm ref="handleRef"
                      @ok="showNext"
                      v-model:value="design"
                      v-model:variables="variables"
                      v-model:rules="rules"
                      v-model:isRead="isRead"
                      v-if="isShow"></TicketForm>
        </div>
        <div class="flex flex-h footer" v-if="!isSave && active == '2'">
          <a-button size="large" @click="save" type="primary">
            Save
          </a-button>
        </div>

        <div v-if="isSave" class="flow_content padding16">
          <Timeline>
            <template v-for="(item,index) in timeLines">
              <TimelineItem color="#308cff" v-if="item?.deleteReason=='completed'">
                <div class="font13 fcolor3 pt-3">
                  {{ activityMap[item?.taskDefinitionKey]?.activityName || 'Previous' }}
                </div>
                <div :class="['userList',{'userList-row' : active!=2}]">
                 <template v-for="(user,j) in activityMap[item?.taskDefinitionKey]?.users">
                    <User v-model:value="activityMap[item?.taskDefinitionKey].users[j].user" :event="false"
                          :isDo=" (activityMap[item?.taskDefinitionKey]?.users.length == 1 && user.endTime) || (user.endTime && user.user == item.assignee)"></User>
                  </template>
                </div>
              </TimelineItem>
            </template>
            <!-- current User-->
            <TimelineItem color="#308cff" v-if="(active == 2 && activityMap[currentActivityId]) || (nextUsers.length > 0)" >
              <div class="font13 fcolor3 pt-3"> {{activityMap[currentActivityId]?.activityName }}</div>
              <div :class="['userList',{'userList-row' : active!=2}]">
                <template v-for="(user,j) in activityMap[currentActivityId]?.users">
                  <User v-model:value="activityMap[currentActivityId].users[j].user" :delUserShow="false" :event="false"></User>
                </template>
              </div>
            </TimelineItem>
            <!-- Next User-->
            <TimelineItem color="#308cff" v-if="active == '2' && nextUsers.length > 0">
              <div class="font13 fcolor3 pt-3"> Next</div>
              <div :class="['userList',{'userList-row' : active!=2}]">
                <User v-model:value="nextUsers[k]"
                      v-for="(u,k) in nextUsers" :event=" active==2" :delUserShow="false"></User>
              </div>
            </TimelineItem>
            <!-- Copy User-->
            <TimelineItem color="#308cff" v-if="active == '2'">
              <div class="font13 fcolor3 pt-3"> Cc</div>
              <div class="ccList">
                <template v-if="ccUsers.length > 0">
                  <User v-model:value="ccUsers[index]" :event="false"
                        v-for="(item,index) in ccUsers"></User>
                </template>
                <div class="addCc font13 fcolor3 cursor" @click="openCheckUser">+</div>
              </div>
            </TimelineItem>
          </Timeline>
        </div>
      </div>


      <!-- footer-->
      <div class="flex flex-h footer" >
        <template v-if="(isSave && active == '2')">
          <a-button size="large" @click="handOver">
            Hand over
          </a-button>
          <a-button type="primary" size="large" @click="submit" :disabled="disabled">
            Next
          </a-button>
        </template>
        <a-button type="primary" size="large" @click="recall" :disabled="disabled" v-else-if="active == '5' && instData.workflowStatus != 1 && instData.workflowStatus != 3">
          Recall
        </a-button>
      </div>
    </div>
  </div>
  <!--  抄送人-->
  <UserSelectModal rowKey="username" @register="registerSelUserModal"
                   @getSelectResult="onSelectUserOk"  v-model:params="copyParams"/>
  <!--  转让人-->
  <UserSelectModal rowKey="username" @register="registerUserModal"
                   @getSelectResult="doWorkflowTransfer"  :isRadioSelection="true"
                   v-model:params="transferParams"/>
</template>

<script lang="ts" name="Dispose" setup>
import {nextTick, onMounted, ref,inject} from "vue";
import {useRouter} from "vue-router";

import TicketForm from "/@/views/workflow/view/components/TicketForm.vue";
import {Modal, Timeline, TimelineItem} from "ant-design-vue";
import User from './components/User.vue';
import UserSelectModal from "/@/components/Form/src/jeecg/components/modal/UserSelectModal.vue";
import {useModal} from "/@/components/Modal";
import {getAttention, handleNextUser, setNextConfigData} from "/@/views/workflow/view/ts/TicketUtils";
import {useMessage} from "/@/hooks/web/useMessage";
import {useI18n} from "/@/hooks/web/useI18n";
import {getHistoryVariables, queryTicketVO,executionTask,
  copyReadUpdate,
  getHistoryAcivity,
  getHistoryTaskInfo,
  getNextTask,
  workflowChangeStatus,
  workflowTransfer} from "/@/views/workflow/view/WorkflowViewTenant.api";
import {getTenantId, getTenantMode, isTenant} from "/@/utils/auth";

const { t } = useI18n();
const [registerUserModal, workModal] = useModal();
const [registerSelUserModal, selUserModal] = useModal();
const { createMessage } = useMessage();
const {currentRoute} = useRouter();
const router = useRouter();
const disabled = ref(false);
const isRead = ref(false);
const copyParams = ref({});//抄送人查询参数
const transferParams = ref({});//转让人查询参数
const instData:any = ref({ticketName : ''});//tbl_workflow_data表数据
const nextConfig:any = ref([]);//下一步配置
const nextUsers:any = ref([]);//下一步处置人
const currentActivityId = ref('');//当前流程code
const nextActivityId = ref('');//下一步流程code
const ccUsers:any = ref([]);//抄送人
const finishCcUsers:any = ref([]);//工单结束抄送人
const rules = ref({});//form 规则
const isShow = ref(false);
const isSave = ref(false);//是否可更改form true不可，false可以
const variables:any = ref({});//form所有变量
const active:any = ref('');//当前显示菜单
const timeLines = ref([]);//时间线
const currentTaskId = ref('');//当前任务id
const ruleConfig:any = ref({});//规则
const activityMap = ref({});//流程信息
const processInstanceId:any = ref('');//工单实例id
const handleRef = ref();
let attention = ref('');
const emits = defineEmits(["close"])
const design = ref({
  title: 'dispose',
  formConfig: {},
  list: []
});
//1mssp内部，2 租户向mssp申请，3 mssp下发 ，4 租户内部工单，6 租户自己发布工单
const ticketType = history.state.ticketType;
const props = defineProps({
  initFlag: {
    type: Boolean,
    default: true
  }
})
if(getTenantMode()){
  if(ticketType == 1 || ticketType == 3 ){
    copyParams.value.tenantType = 1;
  }else if(ticketType == 4 || ticketType == 2){
    copyParams.value.tenantType = 2;
  }
  // if(ticketType == 4){
  //   copyParams.value.socTenantId = getTenantId();
  // }
}


onMounted(() => {
  if(!props.initFlag){
    return
  }
  const param = currentRoute.value.query;
  active.value = param.active;
  processInstanceId.value = param.instId;
  if(param.active != '2'){
    isRead.value = true;
  }
  getData();

})
/**
 * 事件 entry 入口
 * @param id
 * @param isOnlyView
 */
function entryInit(id:string,isOnlyView:boolean) {
  active.value = isOnlyView ? 3 : 2;
  processInstanceId.value = id;
  //申请工单数据
  queryTicketVO({id: processInstanceId.value},initTicket);

  //获取历史表单变量(HistoricProcessInstance表数据)
  getHistoryVariables({processInstanceId: processInstanceId.value},setVariables)
}
/**
 * 历史变量【表单数据】
 * @param data
 */
function setVariables(data) {
  variables.value = data;
  console.log('variables.value',data)
  attention.value = getAttention(data);
}

/**
 * 工单时间线
 */
function setTimeLines(result) {
  timeLines.value = result;
  getHistoryAcivity({processInstanceId: processInstanceId.value}, (result) => {
    //解析当前Activity
    getCurrentActivity(result);
    //组装所有Activity
    handleTimeline(result);
    nextTick(()=>{
      isShow.value = true;
    })
  })
}

/**
 * 工单信息
 */
function initTicket(result) {

  if(result.ruleConfig){
    ruleConfig.value = JSON.parse(result.ruleConfig);
    console.log('ruleConfig.value->',ruleConfig.value)
  }
  if (result.designContent) {
    design.value.list = JSON.parse(result.designContent);
  }
  if(result.permissionConfig){
    let flowUserData = JSON.parse(result.permissionConfig);
    //工单提交后抄送人处理
    if(flowUserData.ccList && flowUserData.ccList.length > 0){
      finishCcUsers.value = flowUserData.ccList;
    }
  }

  instData.value = result;
  //工单结束
  if(result.workflowStatus == 1 && !props.initFlag){
    isSave.value = true;
    active.value = 3;
  }
  //工单历史线
  getHistoryTaskInfo({processInstanceId: processInstanceId.value}, setTimeLines)
}

/**
 * 初始化数据
 */
function getData() {

  //申请工单数据
  queryTicketVO({id: processInstanceId.value},initTicket);

  //获取历史表单变量(HistoricProcessInstance表数据)
  getHistoryVariables({processInstanceId: processInstanceId.value},setVariables)

  //抄送 更新已读状态
  if (active.value == '4') {
    copyReadUpdate({processInstanceId: processInstanceId.value,id : history.state.id});
  }
}

/**
 * 设置表单规则
 */
function setRuleConfig() {
  let config = JSON.parse(instData.value.ruleConfig) ?? {};
  let data = config[currentActivityId.value] ?? {};
 //表单规则
  if(data.config){
    data = data.config;
    for(let i in data){
      if(data[i].setting){
        rules.value[i] = data[i].setting;
      }
    }
  }

}

/**
 * 获取当前任务
 * @param result
 */
function getCurrentActivity(result) {
  var current = result.find(item => item.taskId != null && null == item.endTime);
  if (current) {
    currentActivityId.value = current.activityId;
    currentTaskId.value = current.taskId;
    setRuleConfig();
  }

}

/**
 * activityMap
 * @param result
 */
function handleTimeline(result) {
  // console.log('all ctivity=============>', result)
  let map = {};
  result.forEach((item) => {
    let obj = {user : item.taskAssignee,canceled : item.canceled,endTime : item.endTime};
    if(map[item.activityId]){
      let repeat = map[item.activityId].users.filter(cc=>cc.user == item.taskAssignee)
      if(repeat.length == 0){//去掉多次执行重复的数据
        map[item.activityId].users.push(obj);
      }
    }else{
      item.users = [obj];
      map[item.activityId] = item;
    }
  })
  activityMap.value = map;
  // console.log('==================activityMap',map)

  if (active.value != 2) {
    isSave.value = true;
  }


}

/**
 * 挂起工单
 */
function recall(){
  Modal.confirm({
    title: 'Recall',
    content: 'Are you sure to Recall' + "?",
    okText: t('common.okText'),
    cancelText: t('common.cancelText'),
    onOk: () => {
      workflowChangeStatus({workflowStatus: 3,processInstanceId:processInstanceId.value}).then(()=>{
        closeRetun();
      });

    }
  });

}


/**
 * 提交
 */
async function submit() {
  disabled.value = true;
  //下一步审批配置，为了提前给下一步分配人员用
  console.log('nextActivityId.value',nextActivityId.value)
  nextConfig.value = nextActivityId.value ? getNextConfig() : [];
  console.log('nextConfig', nextConfig)
  console.log('nextUsers.value', nextUsers.value)
  //用来保存下一步的Attention
  variables.value.nextUsers = nextUsers.value;
  variables.value.nextId = nextActivityId.value;//用来判断是否已完成
  //保存数据
  let queryParam:any = {
    vals: JSON.stringify(variables.value),//存到act_ru_variable中
    nextConfig: JSON.stringify(nextConfig.value),
    processInstanceId: processInstanceId.value,
    taskId : currentTaskId.value
  };
  //===========抄送start================
  let userNames:any = [];
  //抄送人
  if(ccUsers.value.length > 0){
     userNames = ccUsers.value.map(item=>{
      return item.user;
    })
    console.log('userNames',userNames)

  }
  //每步处置都要抄送给抄送人------2024-07-26
  // if(!nextActivityId.value){
  //   finishCcUsers.value.forEach(item=>{
  //     if(userNames.length == 0 || (userNames.length > 0 && !userNames.includes(item.value)))
  //       userNames.push(item.value);
  //     })
  // }
  if(userNames.length > 0){
    queryParam.ccUsers = userNames.toString();
  }
  //===========抄送end================
  console.log('executionTask:', queryParam)
  try{
    let res = await executionTask(queryParam);
    console.log('executionTask res',res);
    closeRetun();
  }catch (e){
    disabled.value = false;
    isRead.value = false;
    isSave.value = false;
    console.log('=====================',e)
  }


}

/**
 * 获取下一步配置
 */
function getNextConfig() {
  let activityConfig:any = [];
  let nextId = nextActivityId.value;
  let nextData:any = ruleConfig.value[nextId];
  if (nextData && nextData.isMultiInstance) {
    let data:any = setNextConfigData(nextData);
    data.id = nextId;
    activityConfig.push(data);
  }

  return activityConfig;

}

/**
 * 返回
 */
function closeRetun() {
  if (!props.initFlag) {
    emits("close")
    return;
  }
  const param = currentRoute.value.query;

  if (param.pageSource == "inve") {
    router.go(-1)
  } else if(param.active){
    router.push({path: "/workflow/view/WorkFlowView", query: {active: param.active},state:{ticketType : ticketType}});
  }
  else {
    router.go(-1)
  }
}

/**
 * 委派
 * @param options
 * @param userIdList
 */
function doWorkflowTransfer(options, userIdList){

  if(userIdList.length == 0){
    createMessage.warning('Please check user!');
    return;
  }
  workflowTransfer({taskId:currentTaskId.value,assignee:userIdList[0]}).then((result)=>{
    closeRetun();
  });




}
// 选择用户成功
async function onSelectUserOk(options, userIdList) {
  if (userIdList.length == 0) {
    return;
  }

  userIdList.forEach(item => {
    ccUsers.value.push({user: item});
  })
}

/**
 * 选择抄送人
 */
function openCheckUser() {

  selUserModal.openModal();
}

/**
 * hand over
 */
function handOver() {

  workModal.openModal();
}
/**
 * 保存校验
 */
function save() {
  handleRef.value.validate();
}

/**
 * 显示下一步
 */
async function showNext() {
  await getNextTask( {vals : JSON.stringify(variables.value),processInstanceId: processInstanceId.value,
    currentActivityId : currentActivityId.value}).then((data)=>{
      console.log('getNextTask data',data)
    if(data.length > 0){
      let item = data[0];//目前数组只有一条
      if(data[0].name != "结束"){
        //下一步审批节点id
        nextActivityId.value = item.key;
        //下一步审批人
        let config = ruleConfig.value[item.key];

        if(config.type == 2){
          config.socTenantId = variables.value.tenantId;
          config.tenants = variables.value.tenantId;
        }

        handleNextUser(config).then((users)=>{
          nextUsers.value = users;
        });
      }
      isSave.value = true;
      isRead.value = true;
    }else{
      createMessage.warning('No next steps found,Please check the data or process configuration!');
    }


  })

}

defineExpose({
  entryInit
})
</script>

<style scoped lang="less">
@import "./less/view.less";

</style>
