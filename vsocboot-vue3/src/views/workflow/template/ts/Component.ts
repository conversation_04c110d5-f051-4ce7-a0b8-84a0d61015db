import {Rule} from "/@/components/Form";

export interface Component {
  field?: string;//组件id
  label?: string;//标题
  component?: string;//Input等组件类型
  defaultValue?: any;// 默认值
  value?: any;// 值
  show?: boolean;//是否显示
  required?: boolean;//是否必填
  rules?: Rule[];// Validation rules
  componentProps?: any;//其他属性
  colProps?: object;//列占比{span: 24}
  col?: any[],
  list?: any[],
  items?: any[],
  type?: any//组件类型
}
