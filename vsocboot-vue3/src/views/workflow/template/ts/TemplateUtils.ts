import {SPLIT_STR} from "/@/views/workflow/template/ts/template.api";

export function getTimeStr(){
  let time = new Date().getTime()
  return time;
};

export function delItem(list,index){
  list.splice(index,1);
}
export function copyItem(list,index,item) {
  let data = {...item};
  data.field = data.component + SPLIT_STR + getTimeStr();
  data.oldField = data.field;

  console.log('copy data',data)
  if(data.list && data.list.length > 0){
    data.list = JSON.parse(JSON.stringify(data.list));
    console.log('changeSub data')
    changeSub(data);


  }
  list.splice(index+1, 0, data);
  console.log('list',list);
  return data;
}
function changeSub(data){
  if(data.component){
    data.field = data.component + SPLIT_STR + getTimeStr();
  }
  data.list.forEach((sub,i)=>{
    console.log(i,sub)

    if(sub.component){
      sub.field = sub.component + SPLIT_STR + getTimeStr();
      console.log('sub.field',sub.field)
      if(sub.list && sub.list.length > 0){
        changeId(sub);
      }
    }else if(sub.list && sub.list.length > 0){
      changeId(sub)
    }
  })
}
function changeId(data){
  console.log('changeId',data.list)
    if(data.list){
      changeSub(data);
      // data.list.forEach((sub,i)=>{
      //   console.log(i,sub)
      //   if(sub.list && sub.list.length > 0){
      //     changeId(sub.list)
      //   }else if(sub.component){
      //     sub.field = sub.component + SPLIT_STR + getTimeStr();
      //     console.log('sub.field',sub.field)
      //     if(sub.list && sub.list.length > 0){
      //       changeId(sub);
      //     }
      //   }
      // })
    }




}


