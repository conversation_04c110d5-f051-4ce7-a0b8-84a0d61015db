.flex{
  display: flex;
}
.flex-1{
  flex : 1;
}
.flex-h{
  flex-direction: row;
}
.flex-v{
  flex-direction: column;
}
.flex-align-center{
  align-items: center;
  justify-content: center;
}
.template-design_wraper{
  height: 100%;
  border-top: 1px solid @border-color;
}
.template-design_content{
  min-height: 100px;
}
/* component style */
.components_wraper{
  width:200px;
  position: fixed;
  .list-items{
    margin-bottom:0.5vh;
    .list-item-title{
      margin-bottom:0.5vh;
    }

    .list-item{
      padding:8px;
      color:@font-color-default;
      span{
        margin-left: 6px;
      }
      &:hover{
        cursor: move;
        border:1px dashed @primary-color;
      }

    }


  }
}
/* property style */
.property_wraper{
  width: 300px;
  :deep(.ant-tabs-content-holder){
    overflow: auto;
  }
}
/* template style */
.design-template-modal{
  .template_wraper{
    border-left: 1px solid @border-color;
    border-right: 1px solid @border-color;
    height: 100%;
    margin-left: 200px;
    .template-tool{
      padding:8px;
      align-items: center;
      justify-content: flex-end;
      border-bottom: 1px solid @border-color;
    }
    .item-active{
      border-left: 5px solid @primary-color;
      background: @bg-color;
    }
    .item-widget.item-active{
      .tool{
        display: block;
      }
      .item-widget{
        .tool{
          display: none!important;
        }
      }
    }
    .item-widget{
      padding: 8px 8px 8px 13px;
      position: relative;
      .tool{
        display: none;
        position: absolute;
        bottom:0px;
        right:0px;
        margin-top:5px;
        button{
          margin-left: 5px;
          background: @bg-color;
          z-index: 100;
        }
        .btn-copy{
          background-color: rgba(32,128,252,0.2)!important;
        }
        .btn-del{
          background-color: rgba(227,61,48,0.2);
        }

      }

    }
  }
  .ant-input-disabled {
    background-color: transparent !important;
    color: inherit !important;
    border : 0 !important;
  }


  .row-col{
    border: 1px dashed @border-color;
    padding-bottom: 30px;
  }

  .item-active{
    border-left: 5px solid @primary-color;
    background: @bg-color;
  }
}



/** preview template */
.preview-template-modal{
  .preview_wrapper{
    padding:16px
  }
  .tool{
    display: none;
  }
}

/deep/ .ant-input-number{
  width:100%
}

.view-template{
  .preview_wrapper{
    padding:16px
  }
  .tool{
    display: none;
  }
}

hr{
  border: 1px solid @border-color;
}

/*.grid-row{
  border: 1px solid  @border-color;
  .item-widget{
    border:1px solid red;
  }
}*/

.preview_wrapper{
  .borderLeft{
    border-left: 1px solid @border-color;
  }
  .borderRight{
    border-right: 1px solid @border-color;
  }
  .borderTop{
    border-top: 1px solid @border-color;
  }
  .borderBottom{
    border-bottom: 1px solid @border-color;
  }
  .item-widget{
    .ant-form-item{
      padding: 12px;
      margin-bottom: 0px;
    }

  }
}


