<template>
  <BasicModal v-bind="$attrs" @register="registerModal"
              :title="templateData.templateName + ' design'"
              :destroyOnClose="true" defaultFullscreen
              :footer="null" wrapClassName="design-template-modal"
  @cancel="doCancel">

    <div :class="['flex','flex-h','template-design_wraper']">
      <!-- 组件-->
      <div class="flex flex-v padding16 components_wraper">
        <div :key="index" v-for="(data,index) in ComponentItems" class="list-items">
          <div class="font14 fcolor list-item-title">{{ data.title }}</div>
          <div v-for="(liItem,index2) in data.column" :key="index2" :id="index + '-' + index2"
               @dragstart="dragstart" draggable="true" @dragend="dragEnd" class="list-item">
            <div class="font13 ">
              <a-icon :type="liItem.icon"/>
              <span>{{ liItem.name }}</span>
            </div>
          </div>
        </div>
      </div>
      <!-- 页面内容-->
      <div class="flex flex-v flex-1 template_wraper">
        <!-- 操作栏-->
        <div class="flex flex-h template-tool">
          <a-button type="text" @click="doCancel">
            <a-icon type="close"/>
            <span>Close</span></a-button>
          <a-button type="text" @click="save" :disabled="disabled">
            <a-icon type="save"/>
            <span>Save</span></a-button>
          <a-button type="text" @click="preView">
            <a-icon type="eye"/>
            <span>Preview</span></a-button>
        </div>
        <!-- 模板-->
        <div class="flex flex-v flex-1" @drop.stop.prevent="drop" @dragover.prevent>

          <a-form layout="vertical">
              <TemplateComponentList v-model:value="templateDesignData"
              ref="componentRef" @show="rowComponentField" @dropSub="dropSub"/>
          </a-form>
        </div>
      </div>
      <!-- 属性-->
      <div class="flex flex-v padding16 property_wraper">
        <a-tabs v-model:activeKey="activeKey">
          <a-tab-pane key="1" tab="Field" force-render>
            <FieldProperty v-model:value="fieldData"   ref="fieldPropertyRef" />
          </a-tab-pane>
          <a-tab-pane key="2" tab="Form Properties" force-render>
            <FormProperty v-model:value="templateDesignData.formPoperty" ref="formPropertyRef"/>
          </a-tab-pane>

        </a-tabs>


      </div>
    </div>

    <WorkflowTemplatePreviewModal ref="previewModal"/>
  </BasicModal>

</template>

<script lang="ts" name="WorkflowTemplateDesignModal" setup>
import WorkflowTemplatePreviewModal
  from "/@/views/workflow/template/modules/WorkflowTemplatePreviewModal.vue";
import FieldProperty from "/@/views/workflow/template/component/FieldProperty.vue";
import TemplateComponentList from "/@/views/workflow/template/component/TemplateComponentList.vue";
import {ComponentItems, formConfig, SPLIT_STR} from '../ts/template.api';
import {computed, nextTick, onMounted, ref, reactive, watch, getCurrentInstance, provide} from "vue";
import {Modal} from "ant-design-vue";
import {BasicModal, useModal, useModalInner} from "/@/components/Modal";
import {getTimeStr,delItem,copyItem} from '../ts/TemplateUtils'
import FormProperty from "/@/views/workflow/template/component/FormProperty.vue";
import {useRefs} from "/@/hooks/core/useRefs";
import {
  queryDesign,
  saveDesign
} from "/@/views/workflow/template/WorkflowTemplate.api";
const [refs, setRefs] = useRefs();
const activeKey = ref('2');
const currentClickId = ref('');
const currentItem = ref({});
const componentRef = ref();
const formPropertyRef = ref();
const previewModal = ref();
const disabled = ref(false);
const emit = defineEmits(['success']);
/**
 * form property in the templateDesignData.formPoperty
 * field property in the templateDesignData.list
 */
const templateDesignData = ref({
  formPoperty: {},
  list: []
});
const fieldData = ref();
const fieldPropertyRef = ref();
const templateData = ref({});
//子孙通信
provide('clickId',currentClickId);
provide('clickItem',currentItem);
//自己调试用，上线前注释掉
watch(templateDesignData.value, () => {
  console.log('templateDesignData change', templateDesignData.value)
}, {deep: true, immediate: true})

//change click item，field content will change
watch(currentItem, () => {
  // console.log('currentItem change', currentItem.value)
  rowComponentField();
}, {deep: true })

function dropSub(p,j,data){

  console.log('parent:',p)
  console.log('dropSub:',data)
  console.log(j)
  let list = JSON.parse(JSON.stringify(templateDesignData.value.list));
  list.forEach(item=>{
    if(item.field == p.field){
      console.log(item.list[j])
      console.log(22222222222222222222)
      item.list[j].list.push(data);
    }
  })
  console.log(list);
  templateDesignData.value.list = list;
}



//表单赋值
const [registerModal, {setModalProps, closeModal}] = useModalInner(async (data) => {
  console.log('get row data', data.record)
  templateData.value = data.record;
  activeKey.value = '2';
  if(data.record.templateDesignId){
    queryDesign({id:data.record.templateDesignId}).then((result)=>{
      // console.log('get templateDesignId result', result)

      if(result.designContent){
        templateDesignData.value.list = JSON.parse(result.designContent);
      }
      if(result.designConfig){
        templateDesignData.value.formPoperty = JSON.parse(result.designConfig);
      }
    })
  }
});


/**
 * show field config change
 * @param data
 */
function rowComponentField(){
  activeKey.value = '1';
  fieldData.value = currentItem.value;
  fieldPropertyRef.value.show(currentItem.value);
}
async function save(){
  disabled.value = true;
  await saveDesign({designContent: JSON.stringify(templateDesignData.value.list),
    designConfig:JSON.stringify(templateDesignData.value.formPoperty),
  templateId : templateData.value.id}).then((data)=>{
    emit('success', data);
    doCancel();
  });
}

function preView(){
  previewModal.value.showModal(templateDesignData.value);

}


/**
 * drag start
 * @param ev
 */
function dragstart(ev) {
  let group = ev.target.id.split("-")[0];
  let columnIndex = ev.target.id.split("-")[1];
  let info = {isDrop: true, item: ComponentItems[group].column[columnIndex]};
  //判断当前浏览器是否为火狐浏览器
  let userAgent = navigator.userAgent;
  let ifFirefox = userAgent.indexOf("Firefox");
  if (ifFirefox) {
    ev.dataTransfer.setData("imgInfo", info);
  }
  ev.dataTransfer.setData('Text', JSON.stringify(info));

};

/**
 * mouse drop
 * @param e
 * @param flag
 */
function drop(e) {
  let info = JSON.parse(e.dataTransfer.getData('Text'));
  if (info.isDrop) {
    let item = {...info.item};
    item.field = item.component + SPLIT_STR + getTimeStr();
    templateDesignData.value.list.push(item);
    nextTick(()=>{
      currentClickId.value = item.field;
      currentItem.value = item;
    })
  }

}

/**
 * drag end
 * @param event
 */
function dragEnd(event) {
  event.preventDefault();
  event.stopPropagation();
};

function doCancel(){
  disabled.value = false;
  currentClickId.value = '';
  currentItem.value = null;
  activeKey.value = '2';
  templateDesignData.value = {
    formPoperty: {},
    list: []
  };
  closeModal();
}
</script>


<style lang="less" scoped>
@import "../less/template.less";

</style>




