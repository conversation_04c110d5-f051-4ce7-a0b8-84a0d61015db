<template>
  <a-modal :title="title" :width="width" :visible="visible" @ok="handleOk"
           @cancel="handleCancel">
    <a-row :gutter="24" style="padding:12px;">
      <a-col :span="24" style="padding: 20px;">
        <a-form :layout="formLayout"
                ref="closeForm"
                :model="closeBase">

          <a-form-item
             label="编号方式"
             prop="type" >
            <a-radio-group v-model:value="closeBase.type" button-style="solid" @change="typeChange">
              <a-radio-button value="1">自然数编号</a-radio-button>
              <a-radio-button value="2">指定位数编号</a-radio-button>
            </a-radio-group>
          </a-form-item>
          <a-form-item v-show="closeBase.type==2"
            label="位数"
            prop="startVal">
            <a-input-number :min="2" :max="6" v-model:value="closeBase.digit" style="width:300px;"/>
            <template #extra>
              <a-checkbox v-model:checked="closeBase.autoAdd">编号超出位数后继续递增</a-checkbox>
              <a-tooltip placement="topLeft" title="勾选时，超出位数继续递增； 取消勾选时，超出位数从0开始编号">
                <Icon icon="ant-design:question-circle-filled"></Icon>
              </a-tooltip>
            </template>
          </a-form-item>
          <a-form-item
            label="开始值"
            prop="startVal">
            <a-input-number v-show="closeBase.isUpd==false" :min="1" v-model:value="closeBase.startVal" :disabled="closeBase.startDisabled"  style="width:300px;"/>
            <a-input-number v-show="closeBase.isUpd==true" :min="1" v-model:value="closeBase.startFVal" :disabled="closeBase.startDisabled"  style="width:300px;"/>
            <template #extra>
              修改后将使用新的初始值重新进行编号<a @click="updFunc">{{ closeBase.isUpd?'取消修改':'修改' }}</a>
            </template>
          </a-form-item>
          <a-form-item
            label="重置周期"
            prop="startVal">
            <a-select
              v-model:value="closeBase.reset"
              style="width: 100%"
            >
              <a-select-option value="1">不重置</a-select-option>
              <a-select-option value="2">每天重置</a-select-option>
              <a-select-option value="3">每月重置</a-select-option>
              <a-select-option value="4">每年重置</a-select-option>
            </a-select>
          </a-form-item>
        </a-form>

      </a-col>
    </a-row>

    <template #footer>
      <a-button key="back" @click="handleCancel">Return</a-button>
      <a-button key="submit" type="primary" :disabled="isClose" :loading="loading" @click="handleOk">Submit</a-button>
    </template>

  </a-modal>
</template>

<script lang="ts" setup>
  import {ref, nextTick, defineExpose,reactive} from 'vue';
  import {useI18n} from "/@/hooks/web/useI18n"
  import {Modal} from 'ant-design-vue';
  import { formLayout } from '/@/settings/designSetting';
  import {updateStatus} from "/@/views/risk/RiskEvent.api";

  const {t} = useI18n();

  const title = ref<string>('编号设置');
  const width = ref<number>(800);
  const visible = ref<boolean>(false);
  const closeBase = reactive({});
  const emit = defineEmits(['ok']);
  let loading = ref(false);
  let isClose = ref(false);
  let oldDis = ref(false);


  function init(record){
    console.log("init");
    for(let i in record){
      closeBase[i] = record[i];
    }
    if(closeBase['startDisabled']){
      oldDis.value = true;
    }
    dispositionChange(record.disposition);
    console.log(record,closeBase);
  }


  /**
   * 确定按钮点击事件
   */
  function handleOk() {
    visible.value = false;
    let resetArr = ["","不重置","每天重置","每月重置","每年重置"];
    let text = "自然数编号";
    if(closeBase.type==2){
      text = closeBase.digit+"位数";
    }
    closeBase['startDisabled'] = true;
    let value = text+"，"+resetArr[closeBase.reset];
    emit("ok",closeBase,value);
    handleCancel();
  }

  /**
   * 取消按钮回调事件
   */
  function handleCancel() {
    visible.value = false;
  }

  function dispositionChange(val){
    if(val=='1'){
      isClose.value = true;
    }else{
      isClose.value = false;
    }

  }

  function typeChange(val){
    console.log(val);
  }

  function updFunc(){
    if(!closeBase.isUpd){
      closeBase.startFVal = closeBase.startVal;
      closeBase['startDisabled'] = false;
    }
    closeBase.isUpd = !closeBase.isUpd;
    if(closeBase.isUpd && oldDis.value){
      closeBase['startDisabled'] = true;
    }
  }

  defineExpose({
    visible,init
  });
</script>

<style>
</style>
