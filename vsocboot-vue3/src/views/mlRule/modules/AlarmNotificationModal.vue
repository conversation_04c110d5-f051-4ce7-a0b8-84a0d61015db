<template>
  <a-modal :footer="null" @cancel="handleCancel()" :title="t('routes.MlRuleVO.notification')" width="800px" :visible="visible" :destroyOnClose="true">
    <template #closeIcon>
      <div class="ax-icon-button ax-icon-large">
        <span class="soc ax-com-Close ax-icon"></span>
      </div>
    </template>

    <AlarmNotification v-model:value="ruleId" ruleType="2"></AlarmNotification>
  </a-modal>
</template>

<script lang="ts" name="AlarmNotificationModal" setup>
  import { defineExpose, ref } from 'vue';
  import AlarmNotification from '/@/views/notification/components/AlarmNotification.vue';
  import { useI18n } from '/@/hooks/web/useI18n';

  const { t } = useI18n();
  const visible = ref(false);
  const ruleId = ref('');
  function show(data) {
    console.log('ruleId:', data.id);
    ruleId.value = data.id;
    visible.value = true;
  }
  /**
   * 取消
   */
  function handleCancel() {
    visible.value = false;
  }
  defineExpose({
    show,
  });
</script>

<style scoped></style>
