<template>
  <div ref="chartRef" style="width: 120vh; height: 100vh" v-show="ifShow"></div>
</template>
<script lang="ts">
  import { defineComponent, PropType, ref, Ref, reactive, watchEffect } from 'vue';
  import { useECharts } from '/@/hooks/web/useECharts';
  export default defineComponent({
    name: 'bar',
    props: {
      chartData: {
        type: Array,
        default: () => [],
      },
      option: {
        type: Object,
        default: () => ({}),
      },
      width: {
        type: String as PropType<string>,
        default: '100%',
      },
      height: {
        type: String as PropType<string>,
        default: '100%',
      },
      seriesColor: {
        type: String,
        default: '#1890ff',
      },
      selectKeys: {
        type: Array as PropType<string[]>,
        default: () => [],
      },
      selectValues: {
        type: Array as PropType<string[]>,
        default: () => [],
      },
      selectData: {
        type: Array as PropType<string[]>,
        default: () => [],
      },
      ifSelect: {
        type: Boolean,
        default: false,
      },
      compareLine: {
        type: Array as any,
        default: () => [],
      },
    },
    setup(props, { expose }) {
      const ifShow = ref(true);
      const chartRef = ref<HTMLDivElement | null>(null) as any;
      const { setOptions, echarts } = useECharts(chartRef as Ref<HTMLDivElement>);
      let option = reactive({
        tooltip: {
          trigger: 'axis',
        },
        legend: {
          data: [],
        },
        calculable: true,
        xAxis: [
          {
            type: 'category',
            data: [],
            axisLabel: {
              formatter: function (value) {
                // 使用\n手动折行文本
                return value.split(',').join('\n');
              },
            },
          },
        ],
        yAxis: [
          {
            type: 'value',
          },
        ],
        series: [
          {
            name: '',
            type: 'bar',
            data: [],
            label: {
              show: true, // 显示标签
              position: 'top', // 标签位置
            },
            markLine: {
              data: [
                {
                  yAxis: 0, // 设置虚线的固定值
                  lineStyle: {
                    type: 'dashed', // 设置虚线样式
                  },
                },
              ],
            },
          },
        ],
      }) as any;

      watchEffect(() => {
        props.selectKeys && props.selectValues && initCharts();
      });

      function initCharts() {
        console.log('props.selectKeys', props.selectKeys);
        console.log('props.selectValues', props.selectValues);
        console.log('props.ifSelect', props.ifSelect);
        console.log('props.compareLine', props.compareLine);
        if (props.ifSelect) {
          // 确保 xAxis 数据填充
          let xAxisData = props.selectKeys;
          if (xAxisData && Array.isArray(xAxisData)) {
            option.xAxis[0].data = xAxisData;
          } else {
            console.error('Invalid xAxisData');
          }

          let seriesData: any = props.selectValues;
          option.series = [];
          // 初始化 series 配置并填充数据
          let seriesName = seriesData[0]?.map((e) => e.key) || [];
          option.legend.data = seriesName;
          seriesName.forEach((seriesName: any, index) => {
            option.series[index] = {
              name: seriesName,
              type: 'bar',
              data: [],
              label: {
                show: true, // 显示标签
                position: 'top', // 标签位置
              },
              markLine: {
                data: [
                  {
                    yAxis: 0, // 设置虚线的固定值
                    lineStyle: {
                      type: 'dashed', // 设置虚线样式
                    },
                  },
                ],
              },
            };
            // 为每个 series 填充 data
            console.log('seriesDataseriesData', seriesData);
            console.log('seriesNameseriesName', seriesName);
            seriesData.forEach((itemArray) => {
              const value = itemArray.find((item) => item.key === seriesName)?.value || 0;
              option.series[index].data.push(value);
              if (props.compareLine.length > 0) {
                let arr = [] as any;
                arr = props.compareLine.filter((item) => seriesName.includes(item.field_name));
                if (seriesName === 'Count_of_logs') {
                  const customNumItem = props.compareLine.filter((item) => item.field_name === 'custom_num');
                  arr = customNumItem;
                }
                console.log('arr', arr);

                if (arr.length > 0) {
                  option.series[index].markLine.data = [] as any;
                  arr.forEach((element) => {
                    option.series[index].markLine.data.push({
                      yAxis: element.value,
                      lineStyle: {
                        type: 'dashed', // 设置虚线样式
                      },
                    });
                  });
                }
              }
            });
          });
        } else {
          let xAxisData = props.selectKeys;
          option.xAxis[0].data = xAxisData;
          let seriesData: any = props.selectValues;
          option.legend.data = xAxisData;
          option.series[0].name = xAxisData[0] || [];
          option.series[0].data = seriesData[0];
          if (props.compareLine.length > 0) {
            option.series[0].markLine.data[0].yAxis = props.compareLine[0].value;
          }
        }

        console.log('option', option);
        setOptions(option);
        ifShow.value = true;
      }

      function delInitCharts() {
        ifShow.value = false;
      }

      expose({
        initCharts,
        delInitCharts,
        ifShow,
      });
      return { chartRef, ifShow };
    },
  });
</script>
