<template>
  <div class="flex" v-for="(item,num) in TriggerList" :key="'Trigger'+num"
       style="margin-bottom: 10px;">
    <div class="border condition_title" style="position: relative;word-break: break-all;">
      {{ item?.condition_name }}
      <Icon icon="ant-design:form-outlined"
            style="position:absolute;right: 10px;top: 10px;cursor: pointer;"
            @click="editTitle(item.condition_name,num)"/>
    </div>
    <div class="border content">
      <div style="display: none;">
        <div style="margin-bottom: 10px;">{{ tp('datasetComparisonTriggers') }}</div>
        <div class="flex" v-for="(item2,index) in item.trigger_dataset_list" :key="'tda'+index">
          <div v-if="index > 0" style="width: 100%;margin-bottom: 5px;">AND</div>
          <div style="width: 150px;margin-bottom: 5px;">
            <a-select v-model:value="item2.dataset_key" style="width: 100%;"
                      class="rule_required" @blur="checkedElRuleRequired"
                      :options="datasetList" @change="datasetChange(item2)"/>
          </div>
          <div style="width: 150px;margin-left: 5px;margin-bottom: 5px;">
            <a-select v-model:value="item2.field_name" style="width: 100%"
                      :options="fieldDataList[item2.dataset_key]" class="rule_required"
                      @blur="checkedElRuleRequired" dropdownClassName="autoWidth"
                      :showSearch="true" optionFilterProp="label"/>
          </div>
          <div class="flex" style="width: calc(100% - 360px)">
            <div v-for="(item3,index3) in item2.dataset_trigger" :key="'dt'+index3" class="flex"
                 style="margin-bottom: 5px;">
              <div style="padding: 0 10px;" class="label" v-if="index3 > 0">OR</div>
              <div style="width: 115px;margin-left: 5px;">
                <a-select v-model:value="item3.compare" style="width: 100%;">
                  <a-select-option value="eq">{{ t('common.compare.equal') }}</a-select-option>
                  <a-select-option value="noteq">{{ t('common.compare.notEqual') }}
                  </a-select-option>
                  <a-select-option value="gt">
                    {{ t('common.compare.larger') }}
                  </a-select-option>
                  <a-select-option value="lt">
                    {{ t('common.compare.smaller') }}
                  </a-select-option>
                </a-select>
              </div>
              <div style="width: 150px;margin-left: 5px;">
                <a-select v-model:value="item3.dataset_key" style="width: 100%;"
                          class="rule_required" @blur="checkedElRuleRequired"
                          :options="datasetList" @change="datasetChange(item3)"/>
              </div>
              <div style="width: 150px;margin-left: 5px;">
                <a-select v-model:value="item3.field_name" style="width: 100%"
                          :options="fieldDataList[item3.dataset_key]" class="rule_required"
                          @blur="checkedElRuleRequired" dropdownClassName="autoWidth"
                          :showSearch="true" optionFilterProp="label"/>
              </div>
              <div v-if="index3 > 0">
                <a-button shape="circle" class="w-btn" style="margin-left: 2px;"
                          @click="delFilter(index3,item2)">
                  <Icon icon="ant-design:delete-outlined"/>
                </a-button>
              </div>
            </div>
            <div style="margin-left: 10px;margin-bottom: 5px;">
              <a-button shape="circle" class="w-btn" @click="addFilter(item2)">+</a-button>
              <a class="w-btn" style="margin-left: 5px;" @click="delDataset(index,item)">
                {{ t('common.delText') }}
              </a>
            </div>
          </div>

        </div>
        <div>
          <a-button type="primary" @click="addDataset(num,item)">
            {{ t('common.add') }}
          </a-button>
        </div>
      </div>
      <!--      <div class="border_top" style="margin-top: 15px;padding-top: 10px;">-->
      <div>
        <div style="margin-bottom: 10px;">{{ t('routes.MlRuleVO.statisticTrigger') }}</div>
        <div class="font12" style="margin-bottom: 10px;">
          {{ t('routes.MlRuleVO.statisticTriggerDesc') }}
        </div>
        <div class="flex" v-for="(item2,index) in item.trigger_statistic_list" :key="'tda'+index">
          <div v-if="index > 0" style="width: 100%;margin-bottom: 5px;padding-top: 24px;">AND</div>
          <div style="width: 150px;margin-bottom: 5px;padding-top: 24px;">
            <a-select v-model:value="item2.statistic_key" style="width: 100%;"
                      :options="statisticList[index]"
                      class="rule_required" @blur="checkedElRuleRequired"
                      @change="statisticChange(index,item2)"/>
          </div>
          <div style="width: 150px;margin-left: 5px;margin-bottom: 5px;padding-top: 24px;">
            <a-select v-model:value="item2.field_name_str" style="width: 100%"
                      :options="statisticFieldDataList[item2.statistic_key]" class="rule_required"
                      @blur="checkedElRuleRequired" dropdownClassName="autoWidth"
                      :showSearch="true" optionFilterProp="label"/>
          </div>
          <div class="flex" style="width: calc(100% - 360px)">
            <div v-for="(item3,index3) in item2.statistic_trigger" :key="'st'+index3"
                 style="margin-bottom: 5px;">
              <div class="flex" style="padding-top: 24px;">
                <div style="padding: 0 10px;" class="label" v-if="index3 > 0">OR</div>
                <div style="width: 115px;margin-left: 5px;position: relative;">
                  <div class="flex" style="position:absolute;top:-24px;width: 150px;display: none;">
                    <div class="tabs_div" :class="{'active':item3.type == 'value'}"
                         @click="item3.type = 'value'">{{ tp('value') }}
                    </div>
                    <div class="tabs_div" :class="{'active':item3.type == 'statistic'}"
                         @click="item3.type = 'statistic'">{{ tp('statistic') }}
                    </div>
                  </div>
                  <a-select v-model:value="item3.compare" style="width: 100%;">
                    <a-select-option value="eq">{{ t('common.compare.equal') }}</a-select-option>
                    <a-select-option value="gt">
                      {{ t('common.compare.larger') }}
                    </a-select-option>
                    <a-select-option value="lt">
                      {{ t('common.compare.smaller') }}
                    </a-select-option>
                  </a-select>
                </div>
                <template v-if="item3.type == 'statistic'">
                  <div style="width: 150px;margin-left: 5px;">
                    <a-select v-model:value="item3.statistic_key" style="width: 100%;"
                              class="rule_required" @blur="checkedElRuleRequired"
                              :options="statisticList[index]"/>
                  </div>
                  <div style="width: 150px;margin-left: 5px;">
                    <a-select v-model:value="item3.field_name_str" style="width: 100%"
                              :options="statisticFieldDataList[item3.statistic_key]"
                              class="rule_required"
                              @blur="checkedElRuleRequired" dropdownClassName="autoWidth"
                              :showSearch="true" optionFilterProp="label"/>
                  </div>
                </template>
                <template v-if="item3.type == 'value'">
                  <div style="width: 150px;margin-left: 5px;">
                    <a-input-number v-model:value="item3.value" class="rule_required"
                                    style="width: 100%;"
                                    @focusout="checkedElRuleRequired"/>
                  </div>
                </template>

                <div v-if="index3 > 0">
                  <a-button shape="circle" class="w-btn" style="margin-left: 2px;"
                            @click="delStatisticFilter(index3,item2)">
                    <Icon icon="ant-design:delete-outlined"/>
                  </a-button>
                </div>
              </div>

            </div>
            <div style="margin-left: 10px;margin-bottom: 5px;padding-top: 24px;">
              <a-button shape="circle" class="w-btn" @click="addStatisticFilter(item2)">+</a-button>
              <a class="w-btn" style="margin-left: 5px;" @click="delStatistic(index,item)">
                {{ t('common.delText') }}

              </a>
            </div>
          </div>
        </div>
        <div>
          <a-button type="primary" @click="addStatistic(num,item)">
            {{ t('common.add') }}
          </a-button>
        </div>
      </div>

    </div>
    <div class="delete">
      <div style="width: 100%;text-align: center">
        <Icon icon="ant-design:delete-outlined" size="30" style="cursor: pointer;"
              @click="delTrigger(num)"/>
      </div>
    </div>
  </div>

  <a-button type="primary" @click="addTrigger">
    {{ t('routes.MlRuleVO.addACondition') }}
  </a-button>

  <a-modal v-model:visible="conditionNameVisible" :title="t('routes.MlRuleVO.editStatisticTitle')"
           @ok="conditionNameOk">
    <div style="padding: 10px 16px;">
      <a-input v-model:value="conditionName"/>
    </div>
  </a-modal>

</template>

<script setup lang="ts">
import {defineExpose, defineProps, ref, watch} from 'vue'
import {checkedElRuleRequired} from "/@/utils/checkedRule";
import {getCountFieldText, getTabField, getTabFieldList} from "/@/utils/ckTable";
import {useI18n} from "/@/hooks/web/useI18n";

const {t} = useI18n();

function tp(name) {
  return t('routes.MlRuleVO.' + name);
}

const datasetList = ref<any[]>([])
const statisticList = ref<any>({})
const fieldDataList = ref<any>({})
const statisticFieldDataList = ref<any>({})
const TriggerList = ref<any[]>([{
  condition_name: tp('condition') + '#1',
  trigger_dataset_list: [],
  trigger_statistic_list: []
}])

const props = defineProps({
  data: Object
})
watch(() => props.data, () => {
  console.log(props.data)
  if (props.data) {
    TriggerList.value = JSON.parse(JSON.stringify(props.data))
  }
})
const conditionNameVisible = ref(false)
const conditionName = ref("")
let editTitleIndex = -1

function editTitle(title, num) {
  conditionName.value = title
  editTitleIndex = num
  conditionNameVisible.value = true
}

function conditionNameOk() {
  conditionNameVisible.value = false
  if (editTitleIndex >= 0) {
    TriggerList.value[editTitleIndex].condition_name = conditionName.value
  }
}

let datasetData: any = {}
let statisticData: any = {}
let defaultDatasetKey = ""
let defaultStatisticKey = ""

function init(data1, data2) {
  datasetList.value = []
  datasetData = {}
  statisticData = {}
  for (let i = 0; i < data1.length; i++) {
    datasetData[data1[i].key] = data1[i]
    if (i === 0) {
      defaultDatasetKey = data1[i].key
    }
  }
  for (let i = 0; i < data2.length; i++) {
    statisticData[data2[i].key] = data2[i]
    if (i === 0) {
      defaultStatisticKey = data2[i].key
    }
  }

  //fieldMap记录dataset对应的表的字段，为编辑时便判断是否修改了表需要清空原来数据准备
  let fieldMap: any = {}
  for (let i = 0; i < data1.length; i++) {
    datasetList.value.push({
      label: data1[i].title,
      value: data1[i].key
    })
    fieldMap[data1[i].key] = {}
    fieldDataList.value[data1[i].key] = []
    const dataField = getTabFieldList(data1[i].table_name)
    for (let j = 0; j < dataField.length; j++) {
      if (dataField[j].fieldValue == "ck_enter_date" || dataField[j].fieldValue == "enter_date") {
        continue
      }
      fieldMap[data1[i].key][dataField[j].fieldValue] = true
      fieldDataList.value[data1[i].key].push({
        label: dataField[j].fieldName,
        value: dataField[j].fieldValue
      })
    }
  }

  for (let i = 0; i < TriggerList.value.length; i++) {
    let list = TriggerList.value[i].trigger_dataset_list
    //编辑校验数据
    for (let j in list) {
      if (!datasetData[list[j].dataset_key]) {
        list[j].field_name = ""
        list[j].dataset_key = ""
      } else {
        if (!fieldMap[list[j].dataset_key][list[j].field_name]) {
          list[j].field_name = ""
        }
      }

      let list2 = list[j].dataset_trigger
      for (let k in list2) {
        if (!datasetData[list2[k].dataset_key]) {
          list2[k].field_name = ""
          list2[k].dataset_key = ""
        } else {
          if (!fieldMap[list2[k].dataset_key][list2[k].field_name]) {
            list2[k].field_name = ""
          }
        }
      }
    }

    let list2 = TriggerList.value[i].trigger_statistic_list
    console.log(list2)
    for (let j = 0; j < list2.length; j++) {
      if (!statisticData[list2[j].statistic_key]) {
        list2[j].statistic_key = ""
        list2[j].field_name_str = ""
      }

      let list3 = list2[j].statistic_trigger
      for (let k in list3) {
        if (!statisticData[list3[k].statistic_key]) {
          list3[k].statistic_key = ""
          list3[k].field_name_str = ""
        }
      }

      initStatisticList(j, list2[j])
    }
  }


}

function addTrigger() {
  let index = TriggerList.value.length
  TriggerList.value.push({
    condition_name: tp('condition') + '#' + (index + 1),
    trigger_dataset_list: [],
    trigger_statistic_list: []
  })
}

function delTrigger(num) {
  TriggerList.value.splice(num, 1)
}


function addDataset(num, item) {
  item.trigger_dataset_list.push({
    dataset_key: defaultDatasetKey,
    field_name: '',
    dataset_trigger: [{
      compare: 'eq',
      dataset_key: defaultDatasetKey,
      field_name: '',
    }]
  })
}

function delDataset(index, item) {
  item.trigger_dataset_list.splice(index, 1)
}

function datasetChange(item2) {
  item2.field_name = ""
}


function addFilter(data) {
  data.dataset_trigger.push({
    compare: 'eq',
    dataset_key: defaultDatasetKey,
    field_name: '',
  })
}

function delFilter(index, data) {
  data.dataset_trigger.splice(index, 1)
}

function initStatisticList(index, data) {

  console.log(datasetData)
  console.log(statisticData)
  console.log(data)
  let table_name = datasetData[statisticData[data.statistic_key].dataset_key].table_name
  const fieldMap = getTabField(table_name)
  statisticList.value[index] = []
  for (let key in statisticData) {
    statisticList.value[index].push({
      label: statisticData[key].title,
      value: key,
      disabled: data.statistic_key === key
    })

    let fieldList: any = [{
      value: statisticData[key].dataset_key + "_@_total",
      label: t('routes.MlRuleVO.totalLogNumberOf') + datasetData[statisticData[key].dataset_key].title
    }]
    let step = statisticData[key].step
    for (let j = 0; j < step.length; j++) {
      let options: any = []
      let fields = step[j].statistic_fields
      for (let k in fields) {
        options.push({
          value: step[j].key + '_@_' + fields[k],
          label: getCountFieldText(fieldMap, fields[k])//"Count of " + getHump(fields[k])
        })
      }
      if (j === 0) {
        options.push({
          value: step[j].key + '_@_' + 'custom_num',
          label: tp('countOfLogs')
        })
      }

      fieldList.push({
        label: tp('Step') + '#' + (j + 1),
        options: options
      })
    }
    statisticFieldDataList.value[key] = fieldList
  }
  let flag2 = false
  console.log(data)
  console.log(statisticFieldDataList.value[data.statistic_key])

  for (let k in statisticFieldDataList.value[data.statistic_key]) {
    let d = statisticFieldDataList.value[data.statistic_key][k]
    if (d.value) {
      if (d.value == data.field_name_str) {
        flag2 = true
      }
    } else {
      for (let j in d.options) {
        if (d.options[j].value == data.field_name_str) {
          flag2 = true
        }
      }
    }
  }
  if (!flag2) {
    data.field_name_str = ""
  }
}


function addStatistic(num, item) {
  item.trigger_statistic_list.push({
    statistic_key: defaultStatisticKey,
    field_name: '',
    field_name_str: '',
    statistic_trigger: [{
      type: 'value',
      compare: 'eq',
      statistic_key: '',
      field_name: '',
      field_name_str: '',
      value: ''
    }]
  })
  let index = item.trigger_statistic_list.length - 1;
  initStatisticList(index, item.trigger_statistic_list[index])
}

function delStatistic(index, item) {
  item.trigger_statistic_list.splice(index, 1)
}

function statisticChange(index, item2) {

  item2.statistic_trigger = [{
    type: 'value',
    compare: 'eq',
    statistic_key: '',
    field_name: '',
    field_name_str: '',
    value: ''
  }]
  item2.field_name_str = ""
  let value = item2.statistic_key
  let list = statisticList.value[index]
  for (let i = 0; i < list.length; i++) {
    if (list[i].value === value) {
      list[i].disabled = true
    } else {
      list[i].disabled = false
    }
  }
  statisticList.value[index] = list
}


function addStatisticFilter(item2) {
  item2.statistic_trigger.push({
    type: 'value',
    compare: 'eq',
    statistic_key: '',
    field_name: '',
    field_name_str: '',
    value: ''
  })
}

function delStatisticFilter(index3, item2) {
  item2.statistic_trigger.splice(index3, 1)
}

function getTriggerData() {
  for (let i = 0; i < TriggerList.value.length; i++) {
    let list = TriggerList.value[i].trigger_statistic_list
    for (let j = 0; j < list.length; j++) {
      let fieldName = list[j].field_name_str
      if (fieldName) {
        let arr = fieldName.split("_@_")
        list[j].field_name = arr[1]
        list[j].field_type_value = arr[0]
        if (arr[0].indexOf('dataset') > -1) {
          list[j].field_type = 'dataset'
        } else {
          list[j].field_type = 'step'
        }
      }
      let list2 = list[j].statistic_trigger
      for (let k = 0; k < list2.length; k++) {
        list2[k].value = list2[k].value + ""
        if (list2[k].type == 'statistic') {
          let fieldName = list2[k].field_name_str
          if (fieldName) {
            let arr = fieldName.split("_@_")
            list2[k].field_name = arr[1]
            list2[k].field_type_value = arr[0]
            if (arr[0].indexOf('dataset') > -1) {
              list2[k].field_type = 'dataset'
            } else {
              list2[k].field_type = 'step'
            }
          }
        }
      }
    }
  }

  return JSON.parse(JSON.stringify(TriggerList.value))
}

defineExpose({
  init,
  getTriggerData
})

</script>

<style scoped lang="less">

.flex {
  display: flex;
  flex-flow: wrap;
}

.label {
  line-height: 32px;
}

.cursor {
  cursor: pointer;
}

.border {
  border: 1px solid @border-color;
}

.condition_title {
  display: flex;
  align-items: center;
  width: 120px;
  padding: 10px;
}

.content {
  padding: 10px;
  border-left: 0px !important;
  width: calc(100% - 180px);
}

.delete {
  display: flex;
  align-items: center;
  width: 60px;
  padding: 10px;
}

.border_top {
  border-top: 1px solid @border-color;
}

.tabs_div {
  padding: 0px 10px;
  cursor: pointer;
}

.active {
  background-color: @m-text-color;
}
</style>
