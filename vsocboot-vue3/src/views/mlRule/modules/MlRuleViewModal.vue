<template>
  <div style="padding: 16px 0">
    <div v-if="pageSource != 'mlView'">
      <div class="ax-step-back" style="display: flex; align-items: center">
        <div class="ax-icon-button" @click="goBack">
          <span class="soc ax-com-Arrow-left ax-icon"></span>
        </div>
        <div class="font16 fcolor">
          {{ t('common.goBack') }}
        </div>
        <a-button type="primary" @click="toEdit" style="position: absolute; right: 10px">
          {{ t('common.editText') }}
        </a-button>
      </div>
    </div>
    <div :style="pageSource !== 'mlView' ? 'padding: 0 20%; position: relative;' : ''">
      <div class="rule-details" :style="pageSource !== 'mlView' ? 'margin: 20px;padding: 20px;' : ''">
        <div class="section" style="background: rgba(255, 255, 255, 0.04)">
          <div class="label">{{ tp('ruleNames') }}</div>
          <div class="value" v-if="dataInfo.ruleName">
            <span>{{ dataInfo.ruleName }}</span>
          </div>
        </div>
        <div class="section">
          <div class="label">{{ t('routes.MlRuleVO.severity') }}</div>
          <div class="value critical" v-if="dataInfo.rules?.basic_info?.urgency">
            <Severity2 :value="dataInfo.rules?.basic_info?.urgency" type="number" />
          </div>
        </div>
        <div class="section" style="background: rgba(255, 255, 255, 0.04)">
          <div class="label">{{ tp('Executionfrequency') }}</div>
          <div class="value" style="background: rgba(255, 255, 255, 0.1)" v-if="dataInfo.timeVal">{{
            dataInfo.timeVal + ' ' + timeTypeMap[dataInfo.timeType]
          }}</div>
        </div>
        <div class="section">
          <div class="label">{{ tp('Creator') }}</div>
          <div class="value" v-if="dataInfo.createBy">
            <UserColumn :value="getUserRecord()" />
          </div>
        </div>
        <div class="section" style="background: rgba(255, 255, 255, 0.04)">
          <div class="label">{{ tp('Creationtime') }}</div>
          <div class="value" style="background: rgba(255, 255, 255, 0.1)" v-if="dataInfo.createTime">{{ dataInfo.createTime }}</div>
        </div>
        <div class="section">
          <div class="label">{{ tp('updatetime') }}</div>
          <div class="value" style="background: rgba(255, 255, 255, 0.1)" v-if="dataInfo.updateTime">{{ dataInfo.updateTime }}</div>
        </div>
        <!--        <div class="section" style="background: rgba(255, 255, 255, 0.04)">-->
        <!--          <div class="label">{{ tp('LogType') }}</div>-->
        <!--          <div class="value" style="background: rgba(255, 255, 255, 0.1)" v-if="dataInfo.rules?.dataset?.table_name_type">{{-->
        <!--            tableNameMap[dataInfo.rules?.dataset?.table_name_type]-->
        <!--          }}</div>-->
        <!--        </div>-->
        <div class="section">
          <div class="label">{{ tp('periodoffocus') }}</div>
          <div class="value" style="background: rgba(255, 255, 255, 0.1)" v-if="dataInfo.rules?.dataset.additional_time?.start_time">{{
            dataInfo.rules?.dataset.additional_time?.start_time + '-' + dataInfo.rules?.dataset.additional_time?.end_time
          }}</div>
        </div>
        <div class="section">
          <div class="label">{{ tp('Searchcondition') }}</div>
          <div class="value" style="background: transparent" v-if="dataInfo.rules?.dataset.queryStr">{{ dataInfo.rules?.dataset.queryStr }}</div>
        </div>
        <div class="section">
          <div class="label">{{ tp('Summaryfields') }}</div>
          <div class="value" style="background: rgba(255, 255, 255, 0.1)" v-for="item in dataInfo.summary" :key="item"> {{ item }}</div>
        </div>
        <div class="section">
          <div class="label" style="background: rgba(48, 140, 255, 0.2); width: 100%">Dimension#1</div>
        </div>

        <div class="section" style="background: #18191d; margin-top: -10px">
          <div class="label">
            <img :src="yaxisImg" alt="groupby" class="groupby" />
            <span style="font-size: 12px; font-weight: normal; line-height: 16px; letter-spacing: 0px">{{ t('routes.MlRuleVO.groupby') }}</span>
          </div>
          <div class="value" style="background: rgba(255, 255, 255, 0.1); margin: 5px" v-for="item in dataInfo.groupBy" :key="item">{{ item }}</div>
        </div>

        <div class="section" style="background: rgba(255, 255, 255, 0.04); margin-top: -10px">
          <div class="label">
            <img :src="yaxisImg" alt="statisticfield" class="statisticfield" />
            <span style="font-size: 12px; font-weight: normal; line-height: 16px; letter-spacing: 0px">{{
              t('routes.MlRuleVO.statisticfield')
            }}</span>
          </div>
          <div
            class="value"
            style="background: rgba(255, 255, 255, 0.1); margin: 5px"
            v-for="item in dataInfo?.rules?.dimension?.statistic_fields"
            :key="item"
            >{{ item }}</div
          >
        </div>

        <div class="section" style="background: #18191d; margin-top: -10px">
          <div class="label">
            <img :src="yaxisImg" alt="statisticfieldvalue" class="statisticfield" />
            <span style="font-size: 12px; font-weight: normal; line-height: 16px; letter-spacing: 0px">{{
              t('routes.MlRuleVO.statisticfieldvalue')
            }}</span>
          </div>
          <div
            class="value"
            style="background: rgba(255, 255, 255, 0.1); padding: 4px 8px"
            v-for="item in dataInfo?.statistic_fields_value"
            :key="item"
            >{{ item }}</div
          >
        </div>

        <div class="section" style="background: rgba(255, 255, 255, 0.04); margin-top: -10px">
          <div class="label">
            <img :src="yaxisImg" alt="statisticlogvolume" class="statisticfield" />
            <span style="font-size: 12px; font-weight: normal; line-height: 16px; letter-spacing: 0px">{{
              t('routes.MlRuleVO.statisticlogvolume')
            }}</span>
          </div>
          <div class="value" style="background: rgba(255, 255, 255, 0.1); padding: 4px 8px" v-if="dataInfo.rules?.dimension?.statistic_log_volume">{{
            openOrClose[dataInfo.rules?.dimension?.statistic_log_volume]
          }}</div>
        </div>

        <div class="section" style="background: rgba(255, 255, 255, 0.04); margin-top: -10px">
          <div class="label">
            <img src="./Compare.svg" alt="statisticlogvolume" style="margin-right: 8px; width: 24px; height: 24px" />
            <span style="font-size: 12px; font-weight: normal; line-height: 16px; letter-spacing: 0px">
              {{ t('routes.MlRuleVO.compareCondition') }}
            </span>
          </div>
          <div style="display: flex; flex-wrap: wrap">
            <div
              class="value"
              style="background: rgba(255, 255, 255, 0.1); padding: 4px 8px; white-space: nowrap"
              v-for="item in dataInfo?.rules?.dimension?.filter_list"
              :key="item"
              >{{ 'Count of ' + item.field_name + ' ' + compareMap[item.compare] + ' ' + item.value }}</div
            >
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { useRoute, useRouter } from 'vue-router';
  import { ref, defineProps } from 'vue';
  import { queryById } from '/@/views/mlRule/MlRuleVO.api';
  import Severity2 from '/@/components/Severity/Severity2.vue';
  import { useI18n } from '/@/hooks/web/useI18n';
  import UserColumn from '/@/components/vsoc/UserColumn.vue';
  import yaxisImg from '/@/assets/images/posture/yaxis.png';
  import dayjs from 'dayjs';

  const { t } = useI18n();
  const tp = (name) => {
    return t('routes.MlEvent.' + name);
  };

  const router = useRouter();
  const route = useRoute();
  const props = defineProps({
    pageSource: String,
    dataJson: Object,
    mlId: String,
    record: Object,
  });
  let mlRuleId = '';
  console.log(props);

  // const datasetMap = ref<any>({});
  // const statistictMap = ref<any>({});
  const dataInfo = ref<any>({});

  if (props.pageSource) {
    console.log('data', props.dataJson);
    mlRuleId = props.mlId as string;
    loadMlRuleInfo();
  } else {
    mlRuleId = route.query.id as string;
    loadMlRuleInfo();
  }

  const urgencyMap = {
    1: t('common.Critical'),
    2: t('common.High'),
    3: t('common.Middle'),
    4: t('common.Low'),
    5: t('common.Information'),
  };

  const timeTypeMap = {
    1: t('common.min'),
    2: t('common.hour'),
  };

  const tableNameMap = {
    1: t('common.SecurityLog'),
    2: t('common.HostLog'),
    3: t('common.NetworkLog'),
    4: t('common.OperationLog'),
  };

  const compareMap = {
    '=': t('common.compare.equal'),
    '<>': t('common.compare.notEqual'),
    '>=': t('common.compare.startsWith'),
    '<=': t('common.compare.endsWith'),
    '>': t('common.compare.larger'),
    '<': t('common.compare.smaller'),
  };

  const openOrClose = {
    1: t('common.openBtn'),
    2: t('common.closeBtn'),
  };

  function toEdit() {
    sessionStorage.setItem('MlRuleVOModal_id', mlRuleId);
    router.push('/mlRule/modules/MlRuleVOModal');
  }

  function loadMlRuleInfo() {
    queryById({ id: mlRuleId }).then((data) => {
      if (props.record && props.pageSource == 'mlView') {
        console.log('props.record', props.record);
        dataInfo.value = data;
        dataInfo.value.rules = JSON.parse(props.record.ruleInfo);
        dataInfo.value.timeVal = dataInfo.value.rules.basic_info?.time_val;
        dataInfo.value.timeType = dataInfo.value.rules.basic_info?.time_type;
        dataInfo.value.summary = dataInfo.value.rules?.summary;
        dataInfo.value.groupBy = dataInfo.value.rules?.dimension?.group_fields?.map((item) => {
          return item.value;
        });
        dataInfo.value.statistic_fields_value = dataInfo.value.rules?.dimension?.statistic_fields_value?.map((item) => {
          return item.value;
        });
        dataInfo.value.createBy = dataInfo.value.rules.basic_info?.createBy ? dataInfo.value.rules.basic_info?.createBy : data?.createBy;
        dataInfo.value.createTime = dataInfo.value.rules.basic_info?.createTime
          ? dayjs(dataInfo.value.rules.basic_info?.createTime).format('YYYY-MM-DD HH:mm:ss')
          : data?.createTime;
        dataInfo.value.updateTime = dataInfo.value.rules.basic_info?.updateTime
          ? dayjs(dataInfo.value.rules.basic_info?.updateTime).format('YYYY-MM-DD HH:mm:ss')
          : data?.updateTime;
      } else {
        console.log('data', data);
        // isEdit.value = getIsEdit(data);
        dataInfo.value = data;
        dataInfo.value.rules = JSON.parse(data.ruleJson);
        dataInfo.value.summary = dataInfo.value.rules?.summary;
        dataInfo.value.groupBy = dataInfo.value.rules?.dimension?.group_fields?.map((item) => {
          return item.value;
        });
        dataInfo.value.statistic_fields_value = dataInfo.value.rules?.dimension?.statistic_fields_value?.map((item) => {
          return item.value;
        });

        console.log('dataInfo.value', dataInfo.value);
      }
    });
  }

  function getUserRecord() {
    return { username: dataInfo.value.createBy, avatar: null, email: null };
  }

  function goBack() {
    router.go(-1);
  }
</script>

<style scoped lang="less">
  .border {
    border: 1px solid @border-color;
    border-radius: 8px 8px 0px 0px;
    padding: 10px;
    margin: 10px 0px;
  }

  .rule-details {

    color: #ffffff;
    background-color: #030306;
  }

  .section {
    display: flex;
    margin-bottom: 10px;
    height: 40px;
  }

  .sectionCompare {
    display: flex;
    margin-bottom: 10px;
  }

  .section.full {
    flex-direction: column;
  }

  .label {
    min-width: 234.85px;

    font-size: 12px;
    font-weight: normal;
    line-height: 16px;
    letter-spacing: 0px;
    color: rgba(255, 255, 255, 0.8);
    display: flex;
    flex-direction: row;
    align-items: center;
    padding: 16px;
  }

  .value {
    flex: 1;

    font-size: 12px;
    font-weight: normal;
    letter-spacing: 0px;
    color: rgba(255, 255, 255, 0.8);
    display: flex;
    flex-direction: row;
    align-items: center;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 4px;
    padding: 4px 8px;
    max-width: fit-content;
    margin: 5px
    // padding: 16px;
  }

  .groupby {
    margin-right: 8px;
    width: 24px;
    height: 24px;
    transform: rotate(-180deg);
    border-top: 8px solid #308cff;
  }

  .statisticfield {
    margin-right: 8px;
    width: 24px;
    height: 24px;
    transform: rotate(-90deg);
    border-top: 8px solid #308cff;
  }
</style>
