<template>
  <div class="statis-div">
    <div class="head-div">
      {{ tPrefix('title') }}
      <div class="time_div">{{ nowDateTime }}</div>
    </div>
    <a-row style="height: calc(70% - 80px);">
      <a-col :span="18" class="left-div">
        <a-row class="left-div_div1">
          <a-col style="height: 100%;position: relative;">
            <div ref="chartMapRef" style="height: 100%;"></div>
            <div class="mapRight_div" v-if="mapData.length > 0">
              <div class="col-div">
                <div class="half-ll border-w2"></div>
                <div class="half-rr border-w2"></div>
                <div class="half-rb border-w2"></div>
                <div class="half-lt border-w2"></div>
                <div class="block-container">
                  <div class="title_div">{{ mapData[0] }}</div>
                  <div class="soc_div">
                    <span class="soc ax-zc-safetyequipment"></span>
                    {{ tPrefix('Security_Device') }}
                    <CountTo class="textRight" :startVal="0" :endVal="mapData[1]"/>
                  </div>
                  <div class="soc_div">
                    <span class="soc ax-zc-router"></span>
                    {{ tPrefix('Router') }}
                    <CountTo class="textRight" :startVal="0" :endVal="mapData[2]"/>
                  </div>
                  <div class="soc_div">
                    <span class="soc ax-zc-exchangeboard"></span>
                    {{ tPrefix('Switch') }}
                    <CountTo class="textRight" :startVal="0" :endVal="mapData[3]"/>
                  </div>
                  <div class="soc_div">
                    <span class="soc ax-zc-server"></span>
                    {{ tPrefix('Server') }}
                    <CountTo class="textRight" :startVal="0" :endVal="mapData[4]"/>
                  </div>
                  <div class="soc_div">
                    <span class="soc ax-zc-cloudserver"></span>
                    {{ tPrefix('Cloud_Server') }}
                    <CountTo class="textRight" :startVal="0" :endVal="mapData[5]"/>
                  </div>
                  <div class="soc_div">
                    <span class="soc ax-zc-databaseserver"></span>
                    {{ tPrefix('Database_Server') }}
                    <CountTo class="textRight" :startVal="0" :endVal="mapData[6]"/>
                  </div>
                </div>
              </div>

              <div class="col-div" style="margin-top: 20px;">
                <div class="half-ll border-w2"></div>
                <div class="half-rr border-w2"></div>
                <div class="half-rb border-w2"></div>
                <div class="half-lt border-w2"></div>
                <div class="block-container" style="min-height: 50px;">
                  <!--                  防火墙（Firewall）-->
                  <!--                  入侵检测系统（IDS）-->
                  <!--                  入侵防御系统（IPS）-->
                  <!--                  防病毒网关（GAV）-->
                  <!--                  扩展检测和响应（XDR）-->
                  <!--                  终端防护中心（EDR）-->
                  <!--                  垃圾邮件检测系统（SDS）-->
                  <div class="title_div">{{ tPrefix('Attack_Detected') }}</div>
                  <div class="soc_div">
                    <span class="soc ax-aqcp-firewall"></span>
                    {{ tPrefix('Firewall') }}
                    <CountTo class="textRight" :startVal="0" :endVal="mapData[7]"/>
                  </div>
                  <div class="soc_div">
                    <span class="soc ax-aqcp-IDS"></span>
                    {{ tPrefix('IDS') }}
                    <CountTo class="textRight" :startVal="0" :endVal="mapData[8]"/>
                  </div>
                  <div class="soc_div">
                    <span class="soc ax-aqcp-IPS"></span>
                    {{ tPrefix('IPS') }}
                    <CountTo class="textRight" :startVal="0" :endVal="mapData[9]"/>
                  </div>
                  <div class="soc_div">
                    <span class="soc ax-aqcp-GAV"></span>
                    {{ tPrefix('GAV') }}
                    <CountTo class="textRight" :startVal="0" :endVal="mapData[10]"/>
                  </div>
                  <div class="soc_div">
                    <span class="soc ax-aqcp-XDR"></span>
                    {{ tPrefix('XDR') }}
                    <CountTo class="textRight" :startVal="0" :endVal="mapData[11]"/>
                  </div>
                  <div class="soc_div">
                    <span class="soc ax-aqcp-EDR"></span>
                    {{ tPrefix('EDR') }}
                    <CountTo class="textRight" :startVal="0" :endVal="mapData[12]"/>
                  </div>
                  <div class="soc_div">
                    <span class="soc ax-aqcp-SDS"></span>
                    {{ tPrefix('SDS') }}
                    <CountTo class="textRight" :startVal="0" :endVal="mapData[13]"/>
                  </div>
                </div>
              </div>

            </div>
          </a-col>
        </a-row>
      </a-col>
      <a-col :span="6" class="right-div">
        <a-row class="right-div_div1">
          <a-col :span="8" class="count-item">
            <div style="margin-right: 10px;position: relative;padding: 4px;">
              <div class="lt border-w"></div>
              <div class="rt border-w"></div>
              <div class="rb border-w"></div>
              <div class="lb border-w"></div>
              <div class="count-item__div">
                <div class="number-div">{{ totData.num }}</div>
                <div>{{ tPrefix('Attacking_Situation') }}</div>
              </div>

            </div>
          </a-col>
          <a-col :span="8" class="count-item">
            <div style="margin-right: 10px;position: relative;padding: 4px;">
              <div class="lt border-w"></div>
              <div class="rt border-w"></div>
              <div class="rb border-w"></div>
              <div class="lb border-w"></div>
              <div class="count-item__div">
                <div class="number-div">{{ totData.num2 }}</div>
                <div>{{ tPrefix('Attackings') }}</div>
              </div>
            </div>

          </a-col>
          <a-col :span="8" class="count-item">
            <div style="margin-right: 10px;position: relative;padding: 4px;">
              <div class="lt border-w"></div>
              <div class="rt border-w"></div>
              <div class="rb border-w"></div>
              <div class="lb border-w"></div>
              <div class="count-item__div">
                <div class="number-div">{{ totData.num3 }}</div>
                <div>{{ tPrefix('Victims') }}</div>
              </div>
            </div>
          </a-col>
        </a-row>
        <a-row class="right-div_div2">
          <a-col class="col-div">
            <div class="half-ll border-w2"></div>
            <div class="half-rr border-w2"></div>
            <div class="half-rb border-w2"></div>
            <div class="half-lt border-w2"></div>
            <div class="block-container">
              <div class="title_div">{{ tPrefix('Attacking_Type') }}</div>
              <div class="content_div" id="content_div">
                <div v-for="(item,index) in AttackingTypeList" :key="index"
                     style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%);">
                  <div class="wordDiv" :id="'wordDiv'+index"
                       :style="{'left':wordDivData[index]?.left,'top':wordDivData[index]?.top}">
                    <div
                      style="position: absolute; left:50%; top: 50%; font: 14px 微软雅黑; white-space: nowrap; transform: translate(-50%, -50%) rotate(0rad); transition: all 1500ms ease 0ms;">
                      <div class="text-item"> {{ item?.category }} [{{ item?.num }}]</div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </a-col>
        </a-row>
        <a-row class="right-div_div3">
          <a-col class="col-div">
            <div class="half-ll border-w2"></div>
            <div class="half-rr border-w2"></div>
            <div class="half-rb border-w2"></div>
            <div class="half-lt border-w2"></div>
            <div class="block-container">
              <div class="title_div">{{ tPrefix('Collected_Logs') }}</div>
              <div class="content_div">
                <div ref="chart1Ref" style="height: 100%;"></div>
              </div>
            </div>
          </a-col>
        </a-row>
      </a-col>


    </a-row>
    <a-row style="height: calc(30% - 20px);">
      <a-col :span="6" style="padding: 0 10px;">
        <div class="col-div">
          <div class="half-ll border-w2"></div>
          <div class="half-rr border-w2"></div>
          <div class="half-rb border-w2"></div>
          <div class="half-lt border-w2"></div>
          <div class="block-container">
            <div class="title_div">{{ tPrefix('Access_Risk_IP') }}</div>
            <div class="content_div">
              <table class="ew-table">
                <thead>
                <tr class="ew-header-row">
                  <th style="width: 60%">{{ tPrefix('IP') }}</th>
                  <th style="width: 40%">{{ tPrefix('Counts') }}</th>
                </tr>
                </thead>
                <tbody id="list-3">
                <tr v-for="(item,index) in accListData" :key="'tr1'+index"
                    :class="{'odd-row':index==0 || index % 2==0 }">
                  <td>
                    <div class="ellipsis">{{ item?.srcIp }}</div>
                  </td>
                  <td>
                    <div class="ellipsis">{{ item?.num }}</div>
                  </td>
                </tr>
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </a-col>
      <a-col :span="6" style="padding: 0 10px;">
        <div class="col-div">
          <div class="half-ll border-w2"></div>
          <div class="half-rr border-w2"></div>
          <div class="half-rb border-w2"></div>
          <div class="half-lt border-w2"></div>
          <div class="block-container">
            <div class="title_div">{{ tPrefix('Network_Risk_IP') }}</div>
            <div class="content_div">
              <table class="ew-table">
                <thead>
                <tr class="ew-header-row">
                  <th style="width: 60%">{{ tPrefix('IP') }}</th>
                  <th style="width: 40%">{{ tPrefix('Counts') }}</th>
                </tr>
                </thead>
                <tbody id="list-3">
                <tr v-for="(item,index) in spyListData" :key="'tr2'+index"
                    :class="{'odd-row':index==0 || index % 2==0 }">
                  <td>
                    <div class="ellipsis">{{ item?.srcIp }}</div>
                  </td>
                  <td>
                    <div class="ellipsis">{{ item?.num }}</div>
                  </td>
                </tr>
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </a-col>
      <a-col :span="6" style="padding: 0 10px;">
        <div class="col-div">
          <div class="half-ll border-w2"></div>
          <div class="half-rr border-w2"></div>
          <div class="half-rb border-w2"></div>
          <div class="half-lt border-w2"></div>
          <div class="block-container">
            <div class="title_div">{{ tPrefix('Threat_Risk_IP') }}</div>
            <div class="content_div">
              <table class="ew-table">
                <thead>
                <tr class="ew-header-row">
                  <th style="width: 60%">{{ tPrefix('IP') }}</th>
                  <th style="width: 40%">{{ tPrefix('Counts') }}</th>
                </tr>
                </thead>
                <tbody id="list-3">
                <tr v-for="(item,index) in secListData" :key="'tr3'+index"
                    :class="{'odd-row':index==0 || index % 2==0 }">
                  <td>
                    <div class="ellipsis">{{ item?.srcIp }}</div>
                  </td>
                  <td>
                    <div class="ellipsis">{{ item?.num }}</div>
                  </td>
                </tr>
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </a-col>
      <a-col :span="6">
        <div class="col-div">
          <div class="half-ll border-w2"></div>
          <div class="half-rr border-w2"></div>
          <div class="half-rb border-w2"></div>
          <div class="half-lt border-w2"></div>
          <div class="block-container">
            <div class="title_div">{{ tPrefix('Audi_Risk_IP') }}</div>
            <div class="content_div">
              <table class="ew-table">
                <thead>
                <tr class="ew-header-row">
                  <th style="width: 60%">{{ tPrefix('IP') }}</th>
                  <th style="width: 40%">{{ tPrefix('Counts') }}</th>
                </tr>
                </thead>
                <tbody id="list-3">
                <tr v-for="(item,index) in oprListData" :key="'tr4'+index"
                    :class="{'odd-row':index==0 || index % 2==0 }">
                  <td>
                    <div class="ellipsis">{{ item?.srcIp }}</div>
                  </td>
                  <td>
                    <div class="ellipsis">{{ item?.num }}</div>
                  </td>
                </tr>
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </a-col>
    </a-row>
  </div>

</template>

<script lang="ts" setup>
import {nextTick, onMounted, Ref, ref} from 'vue'
import {useECharts} from "/@/hooks/web/useECharts";
import {
  loadAttackingTrend,
  loadLeftTopNum,
  loadLogIpList,
  loadLogLatLng,
  loadLogTypeData,
  loadProject
} from './statisIndex.api'
import {formatToDateTime} from "/@/utils/dateUtil";
import axios from 'axios';
import {CountTo} from "/@/components/CountTo";
import {useI18n} from "/@/hooks/web/useI18n";

const {t} = useI18n();
const tPrefix = (name) => {
  return t('posture.attack.' + name)
}
const nowDateTime = ref(formatToDateTime())
setInterval(() => {
  nowDateTime.value = formatToDateTime()
}, 1000)

onMounted(() => {
  loadTopNumData();
  loadLogIpListData("sec")
  loadLogIpListData("opr")
  loadLogIpListData("spy")
  loadLogIpListData("acc")

  loadAttackingTrendData()

  loadAttackingTypeData()

  loadProject().then((data) => {
    console.log(data)
    // data = 'egypt'
    if (data == "egypt") {
      loadMapData()
    } else {
      loadWorldMapData()
    }

  })

})


const totData = ref({
  num: 0,
  num2: 0,
  num3: 0
});
const loadTopNumData = () => {
  loadLeftTopNum().then((data) => {
    totData.value.num = data.num
    totData.value.num2 = data.num2
    totData.value.num3 = data.num3
  })
}

const accListData = ref<any[]>([])
const spyListData = ref<any[]>([])
const oprListData = ref<any[]>([])
const secListData = ref<any[]>([])
const loadLogIpListData = (type) => {
  loadLogIpList({flag: type}).then((data) => {
    console.log(data)
    if (type == "acc") {
      accListData.value = data
    } else if (type == "spy") {
      spyListData.value = data
    } else if (type == "opr") {
      oprListData.value = data
    } else if (type == "sec") {
      secListData.value = data
    }
  })
}

const chart1Ref = ref<HTMLDivElement | null>(null);
const {setOptions: setOptions1, echarts} = useECharts(chart1Ref as Ref<HTMLDivElement>);
const loadAttackingTrendData = () => {
  loadAttackingTrend().then((data) => {
    console.log(data)
    let list: any = [];
    for (let key in data) {
      list.push({
        time: key,
        value: data[key]
      })
    }
    list.sort((a, b) => {
      if (a.time > b.time) {
        return 1
      } else {
        return -1
      }
    })

    let series: any = [];
    let xData: any = [];
    for (let i in list) {
      xData.push(list[i].time)
      series.push(list[i].value)
    }

    let option: any = {
      color: ['#28E5B3'],
      tooltip: {
        trigger: 'axis',
        formatter: '{b} <br/> {c}'
      },
      grid: {
        top: '10',
        left: '10',
        right: '10',
        bottom: '0',
        containLabel: true
      },
      xAxis: {
        type: 'category',
        boundaryGap: false,
        data: xData,
        axisLabel: {
          formatter: function (param) {
            return param.substring(10)
          }
        },

      },
      yAxis: {
        type: 'value'
      },
      series: [{
        data: series,
        type: 'bar',
        // smooth: true, //是否平滑曲线显示
        // showAllSymbol: false,
        // symbol: "none",
        // areaStyle: { //区域填充样式
        //   //线性渐变，前4个参数分别是x0,y0,x2,y2(范围0~1);相当于图形包围盒中的百分比。如果最后一个参数是‘true’，则该四个值是绝对像素位置。
        //   color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{
        //     offset: 0,
        //     color: '#1B897D'
        //   },
        //     {
        //       offset: 1,
        //       color: '#113E4E'
        //     }
        //   ], false),
        //   opacity: 0.8
        // },
      }]
    };
    setOptions1(option);
  })
}
const mapData = ref<any>([])
const chartMapRef = ref<HTMLDivElement | null>(null);
const {setOptions: setOptions2, getInstance} = useECharts(chartMapRef as Ref<HTMLDivElement>);
const loadMapData = () => {
  axios.get("/map/Egypt.json").then((res) => {
    console.log(res.data)
    echarts.registerMap('egypt', res.data);
    loadLogLatLng().then((data) => {
      let seriesData: any = [];
      let scatterData: any = [];
      let cityMap = {};
      console.log(data)
      for (let i in data) {
        // seriesData.push([[data[i].srcLat, data[i].srcLng], [data[i].dstLat, data[i].dstLng]])
        seriesData.push([[data[i].srcLng, data[i].srcLat], [data[i].dstLng, data[i].dstLat]])
        if (null == cityMap[data[i].srcCountry]) {
          cityMap[data[i].srcCountry] = [data[i].srcLng, data[i].srcLat];
        }
        if (null == cityMap[data[i].dstCountry]) {
          cityMap[data[i].dstCountry] = [data[i].dstLng, data[i].dstLat];
        }
      }
      if (null != cityMap) {
        for (let key in cityMap) {
          let obj = {
            "name": key,
            "value": cityMap[key],
            "itemStyle": {
              "color": '#E75B5E',
            }
          }
          scatterData.push(obj);
        }
      }

      let point: any = []
      let city = [
        ['Cairo', 31.233155, 30.052684],
        ['Alexandria', 29.931799, 31.11692],
        ['Giza', 31.213536, 30.022732],
        ['Shubra el-Kheima', 31.245444, 30.121558],
        ['Port Said', 32.256758, 31.178237],
        ['Suez', 32.541341, 29.977889],
        ['El Mansoura', 31.385904, 31.025798],
        ['Mahalla el-Kubra', 31.172273, 30.972625],
        ['Tanta', 30.997068, 30.781825],
        ['Asyut', 31.182508, 27.173725],
        ['Fayoum', 30.845823, 29.309377],
      ]
      for (let i in city) {
        point.push({
          name: city[i][0],
          value: [city[i][1], city[i][2]]
        })
      }
      // seriesData.push([[city[0][1], city[0][2]], [city[1][1], city[1][2]]])

      console.log(seriesData)
      console.log(point)
      let option: any = {
        tooltip: {
          trigger: 'item',
          formatter: function (params) {
            return params.name;
          }
        },
        geo: {
          map: 'egypt',
        },
        roam: false,
        series: [
          {
            type: 'map',
            map: 'egypt',
            selectedMode: 'single', // 只能单选
            itemStyle: {
              areaColor: '#93F7FD',
              borderColor: '#111'
            },
            // data: [
            //   {
            //     name: "Aswān",
            //     value: 10,
            //     itemStyle: {
            //       areaColor: '#a8dc67',
            //       color: '#a8dc67',
            //     }
            //   },
            // ],
            // markPoint: {
            //   symbol: "pin",
            //   symbolSize: 20,
            //   data: point
            // }
          },
          {
            type: 'scatter',
            coordinateSystem: 'geo',
            symbol: 'pin',
            itemStyle: {
              color: '#00a668',
              opacity: 0.8
            },
            symbolSize: 20,
            label: {
              show: false,
              position: 'top',
              formatter: (params) => {
                return params.name
              },
              textStyle: {
                color: '#fff'
              }
            },
            data: point,
          }
        ]
      }
      setOptions2(option);


      let dataJson = {
        "Alexandria": [5, 8, 2, 5, 8, 5],
        "Aswan": [5, 8, 2, 5, 8, 5],
        "Asyut": [5, 8, 2, 5, 8, 5],
        "Beheira": [5, 8, 2, 5, 8, 5],
        "Beni Suef": [5, 8, 2, 5, 8, 5],
        "Cairo": [5, 8, 2, 5, 8, 5],
        "Dakahlia": [5, 8, 2, 5, 8, 5],
        "Damietta": [5, 8, 2, 5, 8, 5],
        "Faiyum": [5, 8, 2, 5, 8, 5],
        "Gharbia": [5, 8, 2, 5, 8, 5],
        "Giza": [5, 8, 2, 5, 8, 5],
        "Ismailia": [5, 8, 2, 5, 8, 5],
        "Kafr El Sheikh": [5, 8, 2, 5, 8, 5],
        "Matroh": [5, 8, 2, 5, 8, 5],
        "Minya": [5, 8, 2, 5, 8, 5],
        "Monufia": [5, 8, 2, 5, 8, 5],
        "New Valley": [5, 8, 2, 5, 8, 5],
        "North Sinai": [5, 8, 2, 5, 8, 5],
        "Port Said": [5, 8, 2, 5, 8, 5],
        "Qalyubiyya": [5, 8, 2, 5, 8, 5],
        "Qena": [5, 8, 2, 5, 8, 5],
        "Red Sea": [5, 8, 2, 5, 8, 5],
        "Sharqia": [5, 8, 2, 5, 8, 5],
        "Sohag": [5, 8, 2, 5, 8, 5],
        "South Sinai": [5, 8, 2, 5, 8, 5],
        "Suez": [5, 8, 2, 5, 8, 5],
      }
      let instance = getInstance();
      instance?.on("click", (params) => {
        if (params.seriesType == 'map') {
          let name = params.name
          mapData.value = [
            name,
            Math.floor(Math.random() * 50 + 50),
            Math.floor(Math.random() * 50 + 50),
            Math.floor(Math.random() * 50 + 50),
            Math.floor(Math.random() * 50 + 50),
            Math.floor(Math.random() * 50 + 50),
            Math.floor(Math.random() * 50 + 50),
            Math.floor(Math.random() * 50 + 50),
            Math.floor(Math.random() * 50 + 50),
            Math.floor(Math.random() * 50 + 50),
            Math.floor(Math.random() * 50 + 50),
            Math.floor(Math.random() * 50 + 50),
            Math.floor(Math.random() * 50 + 50),
            Math.floor(Math.random() * 50 + 50),
          ]
          console.log(mapData.value)
        }
      })
    })
  })
}

function loadWorldMapData() {
  axios.get("/map/world.json").then((res) => {
    console.log(res.data)
    echarts.registerMap('world', res.data);


    loadLogLatLng().then((data) => {
      let seriesData: any = [];
      let scatterData: any = [];
      let cityMap = {};
      console.log(data)
      for (let i in data) {
        // seriesData.push([[data[i].srcLat, data[i].srcLng], [data[i].dstLat, data[i].dstLng]])
        seriesData.push([[data[i].srcLng, data[i].srcLat], [data[i].dstLng, data[i].dstLat]])
        if (null == cityMap[data[i].srcCountry]) {
          cityMap[data[i].srcCountry] = [data[i].srcLng, data[i].srcLat];
        }
        if (null == cityMap[data[i].dstCountry]) {
          cityMap[data[i].dstCountry] = [data[i].dstLng, data[i].dstLat];
        }
      }
      if (null != cityMap) {
        for (let key in cityMap) {
          let obj = {
            "name": key,
            "value": cityMap[key],
            "itemStyle": {
              "color": '#E75B5E'
            }
          }
          scatterData.push(obj);
        }
      }
      console.log(seriesData)
      console.log(scatterData)
      let option: any = {
        tooltip: {
          trigger: 'item',
          formatter: function (params) {
            return params.name;
          }
        },
        geo: {
          name: 'Attack Situation',
          type: 'map',
          map: 'world',
          itemStyle: {
            areaColor: '#93F7FD',
            borderColor: '#111'
          },
          emphasis: {
            itemStyle: {
              areaColor: '#406A79'
            }
          }
        },
        roam: false,
        series: [
          {
            type: 'lines',
            zlevel: 2,
            effect: {
              show: true,
              period: 6,
              trailLength: 0.1,
              color: '#fff',
              symbolSize: 2
            },
            lineStyle: {
              color: '#E75B5E',
              width: 1,
              opacity: 0.01,
              curveness: 0.2
            },
            data: seriesData
          },
          {
            type: 'scatter',
            coordinateSystem: 'geo',
            // symbolSize: function (val) {
            //   return val[2] / 15;
            // },
            itemStyle: {
              color: '#E75B5E',
              opacity: 0.2
            },
            data: scatterData
          }
        ]
      }
      setOptions2(option);
    })
  })
}

const wordDivData = ref<any>([])
const AttackingTypeList = ref<any[]>([])
const loadAttackingTypeData = () => {
  loadLogTypeData().then((data) => {
    console.log(data)
    for (let i = 0; i < data.length; i++) {
      if (i > 9) {
        break
      }
      AttackingTypeList.value.push(data[i])
    }
    nextTick(() => {
      let div = document.getElementById("content_div")
      console.log(div)
      let wordH = div?.scrollHeight ? div?.scrollHeight : 0;
      let wordW = div?.scrollWidth ? div?.scrollWidth : 0;
      console.log(wordH, wordW)

      wordDivData.value[0] = {left: -wordW * 0.05 + "px", top: 10 + 'px'};
      wordDivData.value[1] = {left: -wordW * 0.25 + "px", top: wordH * 0.3 + 'px'};
      wordDivData.value[2] = {left: -wordW * 0.18 + "px", top: -wordH * 0.15 + 'px'};
      wordDivData.value[3] = {left: -wordW * 0.25 + "px", top: -wordH * 0.32 + 'px'};
      wordDivData.value[4] = {left: wordW * 0.25 + "px", top: wordH * 0.3 + 'px'};
      wordDivData.value[5] = {left: wordW * 0.3 + "px", top: -wordH * 0.35 + 'px'};
      wordDivData.value[6] = {left: wordW * 0.25 + "px", top: -wordH * 0.1 + 'px'};
      wordDivData.value[7] = {left: -wordW * 0.07 + "px", top: -wordH * 0.40 + 'px'};
      wordDivData.value[8] = {left: wordW * 0.07 + "px", top: wordH * 0.40 + 'px'};

      setInterval(() => {
        for (let i in wordDivData.value) {
          let left = parseInt(wordDivData.value[i].left.replace("px"))
          let top = parseInt(wordDivData.value[i].top.replace("px"))
          wordDivData.value[i].left = -left + "px"
          wordDivData.value[i].top = -top + "px"
        }
      }, 5000)
    })

  })
}

</script>


<style scoped lang="less">


.statis-div {
  height: 100vh;
  width: 100vw;
  position: fixed;
  top: 0;
  left: 0;
  z-index: 999;
  background: url("../../assets/images/threat-background.png");
  padding: 0 10px;
  color: #51f2ff;
  font-size: 12px;
}

.head-div {
  position: relative;
  height: 80px;
  width: 100%;
  text-align: center;
  font-size: 30px;
  line-height: 60px;
  background: url(data:image/png;base64,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) no-repeat bottom;
}


.left-div, center-div, right-div {
  height: 100%;
}

.left-div_div1 {
  padding: 10px;
  text-align: center;
  margin-bottom: 10px;
  height: 100%;
}


.content_div {
  height: calc(100% - 35px);
  overflow: hidden;
}


.right-div_div1 {
  text-align: center;
  margin-bottom: 10px;
}

.right-div_div2 {
  height: calc(calc(100% - 93px) / 2);
  margin-bottom: 10px;
}

.right-div_div3 {
  height: calc(calc(100% - 93px) / 2);
  margin-bottom: 10px;
}


.title_div {
  padding-left: 30px;
  font-size: 18px;
  line-height: 33px;
  position: relative;
}

.title_div:before {
  background: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABwAAAAOCAYAAAA8E3wEAAACW0lEQVQ4jXXUyWtXBxAH8M8vNiHua4OmxH3DpdWi0uLaCoogHjx5EA/iQT2JB0G8xJvrPyC92LO23iqKYEsQQ0tARS20datrm6pJTa2t8Sfzc4LPZzLweDBv3nxnvt+Zqazprn6JbdiFP7y1ediKw3hQ8DdjA77Fo4J/GBbhEp4YwOqwDJ/hZIL02QLMxqEKcwv+yao+whZML/jHYiSWYPxAgIOm7W39Hg+xBuvxO37FZdzOYpbiKW7WvldqMTMxC/8lA134E00Yl/m7+gOM91VcwCqsy847cAc/4RMsxmBcx1/4GS2YiiG4i39wHyMwGo0JWi0DSj1OZ/LVNeq4WKETbZiIhapaVGoF/o1rmXgSPsQ9vMhc9Unx0IztLQOG9eC71OMLfIofcwja0aBS0zao/AXPkv5XWdCEpPXf/Kc3uw3Q53hZBpRBbUnFSixPGjsL74+9GaQHqW109JjaMDVnIT2Fpw/0/7qBpil1rEv+qyW/fvyVfMr+d2I/6AeoAXtSx9CoNWkKoI34PAfk6xweuUJzssv2wnQ25fMiN+F5GbApF30GzuBA6hFLvR1TcAXHU5P6LKw5i/gh4+tyekehG7eCznKHMSBHMBzH8FXSENO6O0Fjik/kkIzJizMk16ot9W/MA9KQl+tGxisCbsK+bH0vzqZ/BXZkoijgfPpnJFg1fR3pjyKC3rDfco/f0TMA92NzXpidqVtYnK4oJPQ7WPDH1Vmbu/VNJg6LXQywoDTuaWj2ngXgubyJcbwjeZ/FhZmPo6WfAyD0OlU69rEusXtxEmNV3je8BtTnrDzTkrBMAAAAAElFTkSuQmCC) no-repeat;
  position: absolute;
  left: 0px;
  top: 10px;
  content: "";
  width: 28px;
  height: 30px;
}


.time_div {
  position: absolute;
  top: 0px;
  right: 20px;
  font-size: 14px;
}

.count-item {

}

.count-item .border-w {
  width: 12px;
  height: 12px;
  position: absolute;
}

.border-w2 {
  position: absolute;
}

.lt {
  top: 0;
  left: 0;
  border-top: 2px solid #65dada;
  border-left: 2px solid #65dada;
}

.lb {
  left: 0;
  bottom: 0;
  border-bottom: 2px solid #65dada;
  border-left: 2px solid #65dada;
}

.rb {
  bottom: 0;
  right: 0;
  border-bottom: 2px solid #65dada;
  border-right: 2px solid #65dada;
}

.rt {
  top: 0;
  right: 0;
  border-top: 2px solid #65dada;
  border-right: 2px solid #65dada;
}

.number-div {
  height: 30px;
  line-height: 30px;
  font-size: 24px;
}

.count-item__div {
  padding: 2px;
  border: 1px solid rgba(101, 218, 218, 0.2);
  background: rgba(81, 242, 255, 0.1);
}

.half-ll:before {
  content: "";
  display: inline-block;
  width: 4px;
  height: 4px;
  border-radius: 50%;
  background-color: #fff;
  position: absolute;
  top: -2px;
  left: -2px;
  -webkit-box-shadow: 0 0 4px 2px #fff;
  box-shadow: 0 0 4px 2px #fff;
  z-index: 2;
}

.half-ll {
  background-image: linear-gradient(180deg, #65dada, rgba(101, 218, 218, 0));
}

.half-rr {
  background-image: linear-gradient(0deg, #65dada, rgba(101, 218, 218, 0));
}

.half-rb {
  background-image: linear-gradient(270deg, #65dada, rgba(101, 218, 218, 0));
}

.half-lt {
  background-image: linear-gradient(90deg, #65dada, rgba(101, 218, 218, 0));
}

.half-ll, .half-lt {
  top: 0;
  left: 0;
}

.half-rb, .half-rr {
  bottom: 0;
  right: 0;
}

.half-ll, .half-rr {
  width: 2px;
  height: 50%;
}

.half-lt, .half-rb {
  width: 50%;
  height: 2px;
}

.col-div {
  height: 100%;
  padding: 10px;
  position: relative;
}

.block-container {
  background: rgba(101, 218, 218, 0.1);
  padding: 0 10px;
  height: 100%;
}

.ew-table {
  width: 100%;
  height: 100%;

  td, th {
    padding: 5px 10px;
  }
}


.ew-header-row {
  text-align: left;
  color: #adf9ff;
}

.odd-row {
  background: rgba(81, 242, 255, 0.08);
}

.ellipsis {
  overflow: hidden;
  display: -webkit-box;
  text-overflow: ellipsis;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
  word-break: break-all;
  white-space: normal;
}

.wordDiv {
  position: absolute;
  transition: all 2s;
  -webkit-transition: all 2s; /* Safari */
  animation-duration: 1500ms;
  animation-timing-function: ease;
  animation-delay: 0ms;
}

.wordDiv .text-item {
  color: #51f2ff !important;
  cursor: default;
  -webkit-transition: all .5s !important;
  transition: all .5s !important;
  border-radius: 5px;
  border: 1px solid rgba(81, 242, 255, .35);
  padding: 0.26vw 0.31vw;
  line-height: 1 !important;
  -webkit-text-size-adjust: none;
  background: rgba(81, 242, 255, .25);
}

.mapRight_div {
  position: absolute;
  left: 0px;
  top: 0px;
  min-width: 100px;
  text-align: left;
  font-size: 12px;

  .soc_div {
    position: relative;
    padding-right: 40px;
  }

  .textRight {
    position: absolute;
    right: 0;
    line-height: 31px;
  }

  .soc {
    font-size: 20px;
  }
}
</style>
