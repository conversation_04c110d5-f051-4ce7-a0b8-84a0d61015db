import {defHttp} from '/@/utils/http/axios';

enum Api {
  getTicketsRadar = '/workflow/workflowView/getTicketsRadar',
  getTicketsProcessed = '/workflow/workflowView/getTicketsProcessed',
  getUsedTicketsTemplate = '/workflow/workflowView/getUsedTicketsTemplate',
  getTicketsMeanTime = '/workflow/workflowView/getTicketsMeanTime',
  getTicketsPendingNum = '/workflow/workflowView/getTicketsPendingNum',
  getTicketsAVGMeanTime = '/workflow/workflowView/getTicketsAVGMeanTime',
  getInvestigationCloseAvgTime = '/statistics/getInvestigationCloseAvgTime',
  getInvestigationCloseNum = '/statistics/getInvestigationCloseNum',
  getManagementPostureInvestigationBar = '/statistics/getManagementPostureInvestigationBar',
  getUsersMeanTimeCloseInvestigation = '/statistics/getUsersMeanTimeCloseInvestigation',
  getUnClosedUserNumTop10 = '/statistics/getUnClosedUserNumTop10',
  getManagementPostureSeverityPie = '/statistics/getManagementPostureSeverityPie',

}
export const getManagementPostureSeverityPie = (params) =>
  defHttp.get({url: Api.getManagementPostureSeverityPie, params});
export const getUnClosedUserNumTop10 = (params) =>
  defHttp.get({url: Api.getUnClosedUserNumTop10, params});
export const getUsersMeanTimeCloseInvestigation = (params) =>
  defHttp.get({url: Api.getUsersMeanTimeCloseInvestigation, params});


export const getInvestigationCloseAvgTime = (params) =>
  defHttp.get({url: Api.getInvestigationCloseAvgTime, params});

export const getInvestigationCloseNum = (params) =>
  defHttp.get({url: Api.getInvestigationCloseNum, params});
export const getTicketsAVGMeanTime = (params) =>
  defHttp.get({url: Api.getTicketsAVGMeanTime, params});

export const getTicketsPendingNum = (params) =>
  defHttp.get({url: Api.getTicketsPendingNum, params});

export const getTicketsRadar = (params) =>
  defHttp.get({url: Api.getTicketsRadar, params});

export const getTicketsProcessed = (params) =>
  defHttp.get({url: Api.getTicketsProcessed, params});

export const getUsedTicketsTemplate = (params) =>
  defHttp.get({url: Api.getUsedTicketsTemplate, params});

export const getTicketsMeanTime = (params) =>
  defHttp.get({url: Api.getTicketsMeanTime, params});
export const getManagementPostureInvestigationBar = (params) =>
  defHttp.get({url: Api.getManagementPostureInvestigationBar, params});
