<template>

  <RiskPostureMssp v-if="showFlag" @ok="change"/>
  <RiskPostureTenant ref="tenantRef" v-else @ok="change2"/>

</template>

<script setup lang="ts">
import RiskPostureTenant from '/@/views/statistics/RiskPostureTenant.vue'
import RiskPostureMssp from '/@/views/statistics/RiskPostureMssp.vue'
import {getTenantId, getTenantMode, isAdministrator, isTenant} from "/@/utils/auth";
import {ref, nextTick} from 'vue'

const showFlag = ref(false)
const tenantRef = ref()
if (!getTenantMode()) {
  nextTick(() => {
    tenantRef.value.init()
  })
} else if (isAdministrator()) {
  showFlag.value = true
} else if (isTenant()) {
  showFlag.value = false
  let id = getTenantId()
  nextTick(() => {
    tenantRef.value.init(id)
  })
}

function change(id) {
  console.log(id)
  showFlag.value = false
  nextTick(() => {
    tenantRef.value.init(id)
  })
}

function change2() {
  showFlag.value = true
}

</script>

<style scoped lang="less">
</style>
