<template>
  <div class="page_title">Management Posture</div>
  <div class="risk_posture">
    <div class="title">
      <div class="chart_filter">
        <a-select v-model:value="dateFilter" style="float: right;width:150px"
                  @change="allDateFilterChange" class="font12">
          <a-select-option value="1h">{{ t('common.last1Hours') }}</a-select-option>
          <a-select-option value="24h">{{ t('common.last24Hours') }}</a-select-option>
          <a-select-option value="7d">{{ t('common.last7Days') }}</a-select-option>
          <a-select-option value="30d">{{ t('common.last30Days') }}</a-select-option>
          <a-select-option value="6m">{{ t('common.last6Months') }}</a-select-option>
          <a-select-option value="1y">{{ t('common.last1Year') }}</a-select-option>
        </a-select>
      </div>
    </div>
    <div style="display: flex;padding: 0 10px">
      <div class="top_div top_color1">
        <span class="soc ax-nav-riskevents"></span>
        <div class="top_div__text">
          <div class="top_div__num">35</div>
          <div>Investigations are not closed</div>
        </div>
      </div>
      <div class="top_div top_color2">
        <span class="soc ax-nav-mlevents"></span>
        <div class="top_div__text">
          <div class="top_div__num">
            27
            <span class="top_div__unit">Mins</span>
          </div>
          Mean time to close investigation
        </div>
      </div>
      <div class="top_div top_color3">
        <span class="soc ax-nav-correlationevents"></span>
        <div class="top_div__text">
          <div class="top_div__num">80</div>
          Tickets pending
        </div>
      </div>
      <div class="top_div top_color4">
        <span class="soc ax-nav-badactors"></span>
        <div class="top_div__text">
          <div class="top_div__num">
            45
            <span class="top_div__unit">Mins</span>
          </div>
          Mean time to close Tickets
        </div>
      </div>
    </div>

    <a-row :gutter="[16,0]" style="margin: 10px">
      <a-col :span="24">
        <div class="title title2">
          Investigation
        </div>
        <div class="border">
          <div ref="chart4Ref" style="height: 250px"></div>
        </div>
      </a-col>
    </a-row>

    <a-row :gutter="[16,0]" style="margin: 10px">
      <a-col :span="8">
        <div class="title_2 title2">
          Percentage of severity
        </div>
        <div class="border">
          <PieLegend :chartData="chartPieData as any" height="300px" width="50%"
                     :rightDiv="true"/>
        </div>
      </a-col>
      <a-col :span="8">
        <div class="title_2 title2">
          Top10 users with investigations
        </div>
        <div class="border">
          <div ref="chart8Ref" style="height: 300px"></div>
        </div>
      </a-col>
      <a-col :span="8">
        <div class="title_2 title2">
          Top10 users with mean time from Pending to Processing
        </div>
        <div class="border">
          <div ref="chart7Ref" style="height: 300px"></div>
        </div>
      </a-col>
    </a-row>


    <a-row :gutter="[16,0]" style="margin: 10px">
      <a-col :span="24" class="title title2">
        Tickets
      </a-col>
      <a-col :span="12">
        <div class="title_2 title2">
          Top10 most used tickets
        </div>
        <div class="border">
          <div ref="chart5Ref" style="height: 300px"></div>
        </div>
      </a-col>
      <a-col :span="12">
        <div class="title_2 title2">Top10 users with the most tickets processed</div>
        <div class="border">
          <div ref="chart9Ref" style="height: 300px"></div>
        </div>
      </a-col>
    </a-row>

    <a-row :gutter="[16,0]" style="margin: 10px">
      <a-col :span="12">
        <div class="title_2 title2">
          User Tickets participation radar
        </div>
        <div class="border">
          <div ref="chart6Ref" style="height: 300px"></div>
<!--          <template  v-for="(item,index) in leftDownSource">-->
<!--            <div style="height: 40px;padding: 5px;float: left;">-->
<!--              <div :style="{width: '60px',height: '30px',border:'3px solid rgb(121, 121, 121)',float:'left','background-color':item.color}"></div>-->
<!--              <div style="width: 60px;height: 30px;float:left;line-height: 30px;padding-left:12px;">{{item.text}}</div>-->
<!--            </div>-->
<!--            <div v-if="index%4==0"></div>-->
<!--          </template>-->
        </div>
      </a-col>

      <a-col :span="12">
        <div class="title_2 title2">Top10 users with mean time from Pending to Processed</div>
        <div class="border">
          <div ref="chart10Ref" style="height: 300px"></div>
        </div>
      </a-col>
    </a-row>

  </div>

</template>

<script setup lang="ts">
import {onMounted, ref, Ref} from 'vue'
import {useECharts} from "/@/hooks/web/useECharts";
import {EChartsOption} from "echarts";
import dayjs from "dayjs";
import PieLegend from "/@/components/chart/PieLegend.vue";
import {useI18n} from "/@/hooks/web/useI18n";

const {t} = useI18n();
const dateFilter = ref('24h')

const chart1Ref = ref<HTMLDivElement | null>(null);
const {setOptions: setOptions1} = useECharts(chart1Ref as Ref<HTMLDivElement>);
const chart2Ref = ref<HTMLDivElement | null>(null);
const {setOptions: setOptions2} = useECharts(chart2Ref as Ref<HTMLDivElement>);
const chart3Ref = ref<HTMLDivElement | null>(null);
const {setOptions: setOptions3} = useECharts(chart3Ref as Ref<HTMLDivElement>);
const chart4Ref = ref<HTMLDivElement | null>(null);
const {setOptions: setOptions4} = useECharts(chart4Ref as Ref<HTMLDivElement>);
const chart5Ref = ref<HTMLDivElement | null>(null);
const {setOptions: setOptions5} = useECharts(chart5Ref as Ref<HTMLDivElement>);
const chart6Ref = ref<HTMLDivElement | null>(null);
const {setOptions: setOptions6} = useECharts(chart6Ref as Ref<HTMLDivElement>);
const chart7Ref = ref<HTMLDivElement | null>(null);
const {setOptions: setOptions7} = useECharts(chart7Ref as Ref<HTMLDivElement>);
const chart8Ref = ref<HTMLDivElement | null>(null);
const {setOptions: setOptions8} = useECharts(chart8Ref as Ref<HTMLDivElement>);
const chart9Ref = ref<HTMLDivElement | null>(null);
const {setOptions: setOptions9} = useECharts(chart9Ref as Ref<HTMLDivElement>);
const chart10Ref = ref<HTMLDivElement | null>(null);
const {setOptions: setOptions10} = useECharts(chart10Ref as Ref<HTMLDivElement>);


const chartPieData = ref<any>([])
const leftDownSource = ref<any>([])

function loadPieChart() {

  chartPieData.value = [
    {value: 51, name: 'Critical'},
    {value: 46, name: 'High'},
    {value: 50, name: 'Medium'},
    {value: 44, name: 'Low'},
  ]
}

function allDateFilterChange(value) {
  console.log(value)
  loadEcahrt1()
  loadEcahrt2()
  loadEcahrt3()
  loadEchart4()
  loadEchart5()
  loadEchart6()
  loadTableData()
  loadPieChart()
  loadEchart7()
  loadEchart8()
  loadEchart9()
  loadEchart10()
  loadLeftDownSource()
}

onMounted(() => {
  loadEcahrt1()
  loadEcahrt2()
  loadEcahrt3()
  loadEchart4()
  loadEchart5()
  loadEchart6()
  loadTableData()
  loadPieChart()
  loadEchart7()
  loadEchart8()
  loadEchart9()
  loadEchart10()
  loadLeftDownSource()
})


function loadEcahrt1() {
  let xData: any = [];
  let series: any = [];
  for (let i = 360; i >= 0; i--) {
    xData.push(dayjs().subtract(i, 'second').format("DD HH:mm:ss"))
    series.push(Math.floor(Math.random() * 100) + 50)
  }

  let option: EChartsOption = {
    tooltip: {
      trigger: 'axis',
      formatter: '{b} <br/> {c}'
    },
    grid: {
      top: '10',
      left: '15',
      right: '30',
      bottom: '10',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: xData,
    },
    yAxis: {
      type: 'value'
    },
    series: [{
      data: series,
      type: 'line',
      smooth: true
    }]
  };
  setOptions1(option);
}


function loadEcahrt2() {
  let option: EChartsOption = {
    tooltip: {
      trigger: 'item'
    },
    legend: {
      top: '5%',
      left: 'center'
    },
    series: [
      {
        type: 'pie',
        radius: ['40%', '70%'],
        center: ['50%', '55%'],
        avoidLabelOverlap: false,
        itemStyle: {
          borderRadius: 10,
          borderColor: '#06080C',
          borderWidth: 2
        },
        label: {
          show: false,
          position: 'center'
        },
        labelLine: {
          show: false
        },
        data: [
          {value: 1048, name: 'Critical'},
          {value: 735, name: 'High'},
          {value: 580, name: 'Medium'},
          {value: 484, name: 'Low'},
          {value: 300, name: 'Information'}
        ]
      }
    ]
  };
  setOptions2(option);
}

function loadEcahrt3() {
  let option: EChartsOption = {
    tooltip: {
      trigger: 'item'
    },
    legend: {
      top: '5%',
      left: 'center'
    },
    series: [
      {
        type: 'pie',
        radius: ['40%', '70%'],
        center: ['50%', '55%'],
        avoidLabelOverlap: false,
        itemStyle: {
          borderRadius: 10,
          borderColor: '#06080C',
          borderWidth: 2
        },
        label: {
          show: false,
          position: 'center'
        },
        labelLine: {
          show: false
        },
        data: [
          {value: 1048, name: 'Critical'},
          {value: 735, name: 'High'},
          {value: 580, name: 'Medium'},
          {value: 484, name: 'Low'},
          {value: 300, name: 'Information'}
        ]
      }
    ]
  };
  setOptions3(option);
}

function loadEchart4() {
  let xAxisData: any = []
  let seriesData1: any = []
  let seriesData2: any = []
  for (let i = 30; i >= 0; i--) {
    xAxisData.push(dayjs().subtract(i, 'day').format("MM-DD"))
    seriesData1.push(Math.floor(Math.random() * 20))
    seriesData2.push(Math.floor(Math.random() * 20))
  }
  let option: EChartsOption = {
    color: ['rgb(250, 205, 145)', 'rgb(236, 128, 141)'],
    tooltip: {
      trigger: 'axis',
    },
    legend: {
      show: true,
      data: ['Processing', 'Pending'],
      orient: 'vertical',
      left: 0,
      top: 'middle'
    },
    grid: {
      top: '20',
      left: '120',
      right: '20',
      bottom: '20',
      containLabel: true
    },
    xAxis: [
      {
        type: 'category',
        show: false,
        data: xAxisData
      }
    ],
    yAxis: [
      {
        type: 'value'
      }
    ],
    series: [
      {
        name: 'Processing',
        type: 'bar',
        stack: 'Ad',
        data: seriesData1
      },
      {
        name: 'Pending',
        type: 'bar',
        stack: 'Ad',
        data: seriesData2
      },
    ]
  };
  setOptions4(option);
}


function loadEchart5() {
  let data = [];
  let series = [];
  for(let i = 0 ; i < 10 ; i++ ){
    data.push("Tickets#"+(i+1));
    series.push(150-(i*i));
  }
  let option: EChartsOption = {
    tooltip: {
      trigger: 'item'
    },
    xAxis: {
      type: 'category',
      data: data
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    yAxis: {
      type: 'value'
    },
    series: [
      {
        data: series,
        type: 'bar',
        barWidth:24,
        showBackground: true,
        backgroundStyle: {
          color: 'rgba(180, 180, 180, 0.2)'
        }
      }
    ]
  };
  setOptions5(option);
}

function loadEchart6() {
  let username = [
    'Habib',
    'Eadoin',
    'Cachi',
    'Saabir',
    'Xakery',
    'Aafke',
    'Yahuah',
    'Lacole',
    'Tablita',
    'Haakon',
    'Okan',
    'Aalilyanna'
  ]
  let option: EChartsOption = {
    tooltip: {
      trigger: 'item'
    },
    legend: {
      show : true,
      type: 'scroll',
      bottom: 0,
      data:  username
    },
    visualMap: {
      show : false,
      top: 'middle',
      right: 10,
      color: ['red', 'yellow'],
      calculable: true
    },
    radar: {
      center:['50%','46%'],
      axisNameGap:5,
      indicator: [
        {name: 'Processed', max: 400},
        // {name: 'Close', max: 400},
        {name: 'Carbon copy', max: 400},
        {name: 'Submitted', max: 400},
        {name: 'Pending', max: 400},
      ]
    },
    series: (function () {
      let series: any = [];
      for (let i = 1; i <= username.length; i++) {
        series.push({
          type: 'radar',
          symbol: 'none',

          lineStyle: {
            width: 1
          },
          emphasis: {
            areaStyle: {
              color: 'rgba(0,250,0,0.3)'
            }
          },
          data: [
            {
              value: [
                (40 - i) * 10,
                (38 - i) * 6 ,
                i * 5 + 10,
                i * 9

              ],
              name:  username[i]
            }
          ]
        });
      }
      return series;
    })()
  };
  setOptions6(option)
}

function loadEchart7() {
  let option: EChartsOption = {
    tooltip: {
      trigger: 'item'
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    yAxis: {
      type: 'category',
      data: ['Aafke', 'Xakery', 'Saabir', 'Cachi', 'Eadoin']
    },
    xAxis: {
      type: 'value'
    },
    series: [
      {
        data: [80, 100, 110, 115, 120],
        type: 'bar',
        showBackground: true,
        backgroundStyle: {
          color: 'rgba(180, 180, 180, 0.2)'
        }
      }
    ]
  };
  setOptions7(option);
}

function loadEchart8() {
  let option: EChartsOption = {
    tooltip: {
      trigger: 'item'
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    yAxis: {
      type: 'category',
      data: ['Xakery', 'Habib', 'Cachi', 'Aafke', 'Eadoin']
    },
    xAxis: {
      type: 'value'
    },
    series: [
      {
        name:"Processing",
        data: [80, 100, 110, 115, 120],
        type: 'bar',
        stack: 'total',
        showBackground: true,
        backgroundStyle: {
          color: 'rgba(180, 180, 180, 0.2)'
        }
      },
      {
        name:"Pending",
        data: [80, 100, 110, 115, 120],
        type: 'bar',
        stack: 'total',
        showBackground: true,
        backgroundStyle: {
          color: 'rgba(180, 180, 180, 0.2)'
        }
      }
    ]
  };
  setOptions8(option);
}

function loadEchart9() {
  let option: EChartsOption = {
    tooltip: {
      trigger: 'item'
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    yAxis: {
      type: 'category',
      data: ['Habib','Eadoin', 'Cachi', 'Saabir', 'Xakery', 'Aafke']
    },
    xAxis: {
      type: 'value'
    },
    series: [
      {
        data: [80, 100, 110, 115, 120,140],
        type: 'bar',
        showBackground: true,
        backgroundStyle: {
          color: 'rgba(180, 180, 180, 0.2)'
        }
      }
    ]
  };
  setOptions9(option);
}

function loadEchart10() {
  let option: EChartsOption = {
    tooltip: {
      trigger: 'item'
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    yAxis: {
      type: 'category',
      data: ['Xakery','Cachi', 'Aafke', 'Saabir', 'Eadoin', 'Habib']
    },
    xAxis: {
      type: 'value'
    },
    series: [
      {
        data: [80, 100, 110, 115, 120,140],
        type: 'bar',
        showBackground: true,
        backgroundStyle: {
          color: 'rgba(180, 180, 180, 0.2)'
        }
      }
    ]
  };
  setOptions10(option);
}

function loadLeftDownSource(){
  let color = ["rgba(217, 0, 27, 1)","rgba(217, 0, 27, 0.8588235294117647)","rgba(217, 0, 27, 0.6980392156862745)","rgba(217, 0, 27, 0.6980392156862745)",
               "rgba(217, 0, 27, 0.5529411764705883)","rgba(245, 154, 35, 1)","rgba(245, 154, 35, 0.8509803921568627)","rgba(245, 154, 35, 0.8509803921568627)",
               "rgba(245, 154, 35, 0.5529411764705883)","rgba(255, 255, 0, 1)","rgba(255, 255, 0, 0.7450980392156863)","rgba(255, 255, 0, 0.49019607843137253)"
  ];
  let username = [
    'Habib',
    'Eadoin',
    'Cachi',
    'Saabir',
    'Xakery',
    'Aafke',
    'Yahuah',
    'Lacole',
    'Tablita',
    'Haakon',
    'Okan',
    'Aalilyanna'
  ];
  for(let i=0;i<color.length;i++){
    leftDownSource.value.push({color:color[i],text:username[i]});
  }
}

const dataSource = ref<any[]>([])
const dataSource2 = ref<any[]>([])
const dataSource3 = ref<any[]>([])
const columns = [
  {
    title: 'Rule name',
    dataIndex: 'name'
  },
  {
    title: 'Severity',
    dataIndex: 'severity'
  },
  {
    title: 'Count',
    dataIndex: 'count'
  },
  {
    title: 'Sparkline',
    dataIndex: 'sparkline'
  },
]

const columns2 = [
  {
    title: 'IP address',
    dataIndex: 'ip'
  },
  {
    title: 'Country',
    dataIndex: 'country'
  },
  {
    title: 'Count',
    dataIndex: 'count'
  },
  {
    title: 'Sparkline',
    dataIndex: 'sparkline'
  },
]

function loadTableData() {
  let rule = [
    'Roles Assigned Outside PIM',
    'Stale Accounts In A Privileged Role',
    'Azure AD Threat Intelligence',
    'Primary Refresh Token Access Attempt',
    'ESXi VM Kill Via ESXCLI',
    'ESXi VM List Discovery Via ESXCLI',
    'Atypical Travel',
    'Anomalous User Activity',
    'Unfamiliar Sign-In Properties',
    'LOL-Binary Copied From System Directory'
  ]
  for (let i = 0; i < 10; i++) {
    let sparkline: any = []
    for (let j = 0; j < 5; j++) {
      sparkline.push({
        name: 'line',
        value: Math.floor(Math.random() * 10 + 1)
      })
    }
    dataSource.value.push({
      name: rule[i],
      severity: 'High',
      count: 15,
      sparkline: sparkline
    })
  }
  let json = [
    {'ip': '************', 'count': 3358, type: 'host'},
    {'ip': '**************', 'count': 3355, type: 'host'},
    {'ip': '************', 'count': 3354, type: 'host'},
    {'ip': '*************', 'count': 3353, type: 'host'},
    {'ip': '*************', 'count': 3353, type: 'host'},
    {'ip': '**************', 'count': 3351, type: 'host'},
    {'ip': '************', 'count': 3350, type: 'host'},
    {'ip': '************', 'count': 3349, type: 'host'},
    {'ip': '**************', 'count': 3349, type: 'host'},
    {'ip': '**************', 'count': 3349, type: 'host'}
  ]
  let country = [
    'United States',
    'China',
    'Egypt',
    'Australia',
    'Hong Kong',
    'China',
    'Australia',
    'Egypt',
    'Egypt',
    'Australia',
  ]
  for (let i = 0; i < json.length; i++) {
    let sparkline: any = []
    for (let j = 0; j < 5; j++) {
      sparkline.push({
        name: 'line',
        value: Math.floor(Math.random() * 10 + 1)
      })
    }
    dataSource2.value.push({
      ip: json[i].ip,
      country: country[i],
      count: json[i].count,
      sparkline: sparkline
    })
  }
  rule = [
    'DDOS Attack',
    'Worm',
    'Virus Attack',
    'Suspicious File access',
    'Webshell attack',
    'Brute-Force',
    'Botnet',
    'Mining',
    'CSRF',
    'XXE'
  ]
  for (let i = 0; i < 10; i++) {
    let sparkline: any = []
    for (let j = 0; j < 5; j++) {
      sparkline.push({
        name: 'line',
        value: Math.floor(Math.random() * 10 + 1)
      })
    }
    dataSource3.value.push({
      name: rule[i],
      severity: 'Low',
      count: 10,
      sparkline: sparkline
    })
  }

}


</script>

<style scoped lang="less">
.risk_posture {

  .title {

    font-size: 20px;
    font-weight: 600;
    line-height: 32px;
    padding: 8px 16px;

    .chart_filter {
      font-weight: normal;
      float: right;
      font-size: 13px;
      color: @font-color-1 !important;

      span {
        color: @font-color-1 !important;
        font-size: 12px
      }

      span:first-child {
        margin-right: 8px;
      }

      span:last-child {
        margin-left: 8px;
      }
    }

  }

  .title_2 {

    font-size: 16px;
    line-height: 24px;
    padding: 4px 16px;
  }

  .title2 {
    padding-left: 0;
  }

  .top_div {
    display: flex;
    width: 25%;
    margin: 0 5px;
    border: 1px solid @border-color;
    padding: 10px;
    font-size: 14px;

    .soc {
      font-size: 64px;
      line-height: 64px;
    }
  }

  .top_div__text {
    padding-left: 10px;

    font-size: 14px;
    font-weight: 600;
    line-height: 24px;
  }

  .top_div__num {

    font-size: 32px;
    font-weight: 600;
    line-height: 40px;
  }
  .top_div__unit{

    font-size: 14px;
    font-weight: 600;
    line-height: 24px;
  }

  .top_color1 .soc, .top_color1 .top_div__num {
    color: #F75555;
  }
  .top_color2 .soc, .top_color2 .top_div__num {
    color: #308CFF;
  }
  .top_color3 .soc, .top_color3 .top_div__num {
    color: #F8A556;
  }
  .top_color4 .soc, .top_color4 .top_div__num {
    color: #945DF8;
  }
  .top_color5 .soc, .top_color5 .top_div__num {
    color: #F6C84D;
  }

  :deep(.legend_wraper) {
    width: 60% !important;
  }

  .table-wraper {
    padding: 16px
  }

  .table-title {
    padding: 16px;
  }

  .table-row {
    display: flex;
    border-bottom: 1px solid @border-color;
    align-items: center;

    .table-col {
      display: flex;
      flex: 1;
      padding: 14px 16px;
    }

    .table-th {
      display: flex;
      flex: 1;
      padding: 12px 16px;
    }
  }

  .border {
    border: 1px solid @border-color;
  }
}
</style>
