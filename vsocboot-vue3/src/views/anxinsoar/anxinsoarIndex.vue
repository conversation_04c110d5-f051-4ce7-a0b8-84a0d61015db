<template>
  <div>
    <WujieVue width="100%" height="100%" name="workflows"
              :url="`${soarUrl}/workflows`"
              :props="{userName: userName }"/>
  </div>
</template>

<script setup lang="ts">
import {ref} from 'vue'
import {useUserStore} from "/@/store/modules/user";
import {getAuthCache} from "/@/utils/auth";
import {LOCAL_SOAR_KEY} from "/@/enums/cacheEnum";

const soarUrl = getAuthCache(LOCAL_SOAR_KEY);
console.log("soarUrl", soarUrl)

const userStore = useUserStore();
console.log("userInfo", userStore.userInfo?.username)
const userName = ref(userStore.userInfo?.username)


</script>

<style scoped>

</style>
