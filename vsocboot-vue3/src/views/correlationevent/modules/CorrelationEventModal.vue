<!--
 * @Author: fanglei =
 * @Date: 2023-07-20 10:03:20
 * @LastEditors: fanglei =
 * @LastEditTime: 2023-07-28 19:28:36
 * @FilePath: \vsoc-vue3\vsoc-vue3\vsocboot-vue3\src\views\risk\RiskEvent.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <BasicTable @register="registerTable"  :indexColumnProps="indexColumnProps">

  </BasicTable>
</template>

<script lang="ts" setup>
//ts语法
import {ref, computed, unref, Ref, onMounted} from 'vue';
import { BasicTable, useTable, TableAction } from '/@/components/Table';
import {useI18n} from "/@/hooks/web/useI18n";
import {JsonPreview} from "/@/components/CodeEditor";
import {
  queryTechniqueRelationshipsSoftwareRequest
} from "/@/views/correlationevent/CorrelationEvent.api";
const {t} = useI18n();
const emit = defineEmits(['success']);

const props = defineProps({
  param: {
    type: Object, default: {},
  }
})
/**
 * 序号列配置
 */
const indexColumnProps = {
  title: '#',
  width: '50px',
};
const columns = ref([
  {
    title: 'Technique',
    dataIndex: 'tname',
  },
  {
    title: 'Sub Technique',
    dataIndex: 'sub_tname',
    customRender: ({record}) => {
      if(!record.target_id)return record.sub_tname;
      if(record.target_id.indexOf(".")>-1)return record.sub_tname;
      else return "";
    }
  },
  {
    title: 'Tools/Malware',
    dataIndex: 'name'
  },
  {
    title: 'Type',
    dataIndex: 'type'
  },
]);
const [registerTable,methods] = useTable({
  api: queryTechniqueRelationshipsSoftwareRequest,
  title: '',
  titleHelpMessage: [],
  columns: columns,
  rowKey: 'relationId',
  canResize: false,
  pagination : { pageSize: 10 },
  searchInfo: props.param,
});

query();

function query(){
  console.log("query");
  console.log(props);
}


</script>

<style scoped lang="less">
.t_text {
  padding: 10px;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  word-break: keep-all;
}

.borderBottom {
  border-bottom: 1px solid @border-color;
}

.borderRight {
  border-right: 1px solid @border-color;
}
</style>
