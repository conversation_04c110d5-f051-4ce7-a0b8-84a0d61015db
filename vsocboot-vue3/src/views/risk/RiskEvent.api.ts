import {defHttp} from '/@/utils/http/axios';
import {Modal} from 'ant-design-vue';

enum Api {
  list = '/riskevent/riskEvent/list',
  save='/riskevent/riskEvent/add',
  edit='/riskevent/riskEvent/edit',
  deleteOne = '/riskevent/riskEvent/delete',
  deleteBatch = '/riskevent/riskEvent/deleteBatch',
  importExcel = '/riskevent/riskEvent/importExcel',
  exportXls = '/riskevent/riskEvent/exportXls',
  query24MonthData = '/riskevent/riskEvent/query24MonthData',
  updateStatus = '/riskevent/riskEvent/updateStatus',
  queryEventLogRelationPageList = '/riskevent/riskEvent/queryEventLogRelationPageList',
  queryEventLogRelationOldTableList = '/riskevent/riskEvent/queryEventLogRelationPageOldTableList',
  queryEventLogRelationNewTableList = '/riskevent/riskEvent/queryEventLogRelationPageNewTableList',
  queryCHEventLogRelationPageNewTableList = '/riskevent/riskEvent/queryCHEventLogRelationPageNewTableList',
  queryCHEventLogRelationHostPageList = '/riskevent/riskEvent/queryCHEventLogRelationHostPageList',
  queryRiskEventVSLogNotEventId = '/riskevent/riskEvent/queryRiskEventVSLogNotEventId',
  queryRiskObjectAll = '/riskevent/riskEvent/queryRiskObjectAll',
  queryRiskEventGroupByRuleUrgency = '/riskevent/riskEvent/queryRiskEventGroupByRuleUrgency',
  queryRiskEventGroupByRiskType = '/riskevent/riskEvent/queryRiskEventGroupByRiskType',
  queryRiskEventGroupByRiskTypeAlarmTime = '/riskevent/riskEvent/queryRiskEventGroupByRiskTypeAlarmTime',
  queryRiskLogGroupTime = '/riskevent/riskEvent/queryRiskLogGroupTime',
  queryRiskVSLogGroupbyDstIP = '/riskevent/riskEvent/queryRiskVSLogGroupbyDstIP',
  queryRiskVSLogGroupbySrcIP = '/riskevent/riskEvent/queryRiskVSLogGroupbySrcIP',
  queryRiskEventStatisical = '/riskevent/riskEvent/queryRiskEventStatisical',
  queryRiskEventStatisicalGroupOwner = '/riskevent/riskEvent/queryRiskEventStatisicalGroupOwner',
  queryRiskEventStatisicalInvGroupOwner = '/riskevent/riskEvent/queryRiskEventStatisicalInvGroupOwner',
  queryRiskEventStatisicalRoleAvg = '/riskevent/riskEvent/queryRiskEventStatisicalRoleAvg',
  queryRiskEventStatisicalOwner4hour = '/riskevent/riskEvent/queryRiskEventStatisicalOwner4hour',
  saveRiskEventLog = '/riskevent/riskEventLog/saveRiskEventLog',
  queryRiskEventLogPage = '/riskevent/riskEventLog/list',
  queryIndexStatisicalNum13 = '/riskevent/riskEvent/queryIndexStatisicalNum13',
}
/**
 * 导出api
 * @param params
 */
export const getExportUrl = Api.exportXls;
/**
 * 导入api
 */
export const getImportUrl = Api.importExcel;
/**
 * 列表接口
 * @param params
 */
export const list2 = (params) =>
  defHttp.get({url: Api.list, params});

/**
 * 删除单个
 * @param params
 * @param handleSuccess
 */
export const deleteOne = (params,handleSuccess) => {
  return defHttp.delete({url: Api.deleteOne, params}, {joinParamsToUrl: true}).then(() => {
    handleSuccess();
  });
}
/**
 * 批量删除
 * @param params
 * @param handleSuccess
 */
export const batchDelete = (params, handleSuccess) => {
  Modal.confirm({
    title: '确认删除',
    content: '是否删除选中数据',
    okText: '确认',
    cancelText: '取消',
    onOk: () => {
      return defHttp.delete({url: Api.deleteBatch, data: params}, {joinParamsToUrl: true}).then(() => {
        handleSuccess();
      });
    }
  });
}
/**
 * 保存或者更新
 * @param params
 * @param isUpdate 是否是更新数据
 */
export const saveOrUpdate = (params, isUpdate) => {
  let url = isUpdate ? Api.edit : Api.save;
  return defHttp.post({url: url, params});
}

export const query24MonthDataRequest = (params) => {
  return defHttp.get({url: Api.query24MonthData, params});
}

export const updateStatus = (params) => {
  return defHttp.post({url: Api.updateStatus, params});
}

export const updateStatusNotMessage = (params) => {
  return defHttp.post({url: Api.updateStatus, params});
}

export const queryEventLogRelationPageListRequest = (params) =>
  defHttp.get({url: Api.queryEventLogRelationPageList, params});

export const queryEventLogRelationOLdTableListRequest = (params) =>
  defHttp.get({url: Api.queryEventLogRelationOldTableList, params});

export const queryEventLogRelationNewTableListRequest = (params) =>
  defHttp.get({url: Api.queryEventLogRelationNewTableList, params});

export const queryCHEventLogRelationPageNewTableListRequest = (params) =>
  defHttp.get({url: Api.queryCHEventLogRelationPageNewTableList, params});

export const queryCHEventLogRelationHostPageListRequest = (params) =>
  defHttp.get({url: Api.queryCHEventLogRelationHostPageList, params});


export const queryRiskEventVSLogNotEventIdRequest = (params) =>
  defHttp.get({url: Api.queryRiskEventVSLogNotEventId, params});

export const queryRiskObjectAllRequest = (params) =>
  defHttp.get({url: Api.queryRiskObjectAll, params});

export const queryRiskEventGroupByRuleUrgencyRequest = (params) =>
  defHttp.get({url: Api.queryRiskEventGroupByRuleUrgency, params});

export const queryRiskEventGroupByRiskTypeRequest = (params) =>
  defHttp.get({url: Api.queryRiskEventGroupByRiskType, params});

export const queryRiskEventGroupByRiskTypeAlarmTimeRequest = (params) =>
  defHttp.get({url: Api.queryRiskEventGroupByRiskTypeAlarmTime, params});

export const queryRiskLogGroupTimeRequest = (params) =>
  defHttp.get({url: Api.queryRiskLogGroupTime, params});


export const queryRiskVSLogGroupbyDstIPRequest = (params) =>
  defHttp.get({url: Api.queryRiskVSLogGroupbyDstIP, params});

export const queryRiskVSLogGroupbySrcIPRequest = (params) =>
  defHttp.get({url: Api.queryRiskVSLogGroupbySrcIP, params});

export const queryRiskEventStatisicalRequest = (params) =>
  defHttp.get({url: Api.queryRiskEventStatisical, params});

export const queryRiskEventStatisicalGroupOwnerRequest = (params) =>
  defHttp.get({url: Api.queryRiskEventStatisicalGroupOwner, params});

export const queryRiskEventStatisicalInvGroupOwnerRequest = (params) =>
  defHttp.get({url: Api.queryRiskEventStatisicalInvGroupOwner, params});

export const queryRiskEventStatisicalRoleAvgRequest = (params) =>
  defHttp.get({url: Api.queryRiskEventStatisicalRoleAvg, params});

export const queryRiskEventStatisicalOwner4hourRequest = (params) =>
  defHttp.get({url: Api.queryRiskEventStatisicalOwner4hour, params});

export const saveRiskEventLogRequest = (params) => {
  return defHttp.post({url: Api.saveRiskEventLog, params});
}

export const queryRiskEventLogPageRequest = (params) =>
  defHttp.get({url: Api.queryRiskEventLogPage, params});

export const queryIndexStatisicalNum13Request = (params) =>
  defHttp.get({url: Api.queryIndexStatisicalNum13, params});





