import {defHttp} from '/@/utils/http/axios';
import {Modal} from 'ant-design-vue';

enum Api {
  list = '/riskevent/investigationRiskEventlogs/list',
  list2 = '/riskevent/investigationRiskEventlogs/list2',
  listDstIpAll = '/riskevent/investigationRiskEventlogs/listDstIpAll',
  listSrcIpAll = '/riskevent/investigationRiskEventlogs/listSrcIpAll',
  listSrcDstIpAll = '/riskevent/investigationRiskEventlogs/listSrcDstIpAll',
  save='/riskevent/investigationRiskEventlogs/add',
  edit='/riskevent/investigationRiskEventlogs/edit',
  deleteOne = '/riskevent/investigationRiskEventlogs/delete',
  deleteBatch = '/riskevent/investigationRiskEventlogs/deleteBatch',
  importExcel = '/riskevent/investigationRiskEventlogs/importExcel',
  exportXls = '/riskevent/investigationRiskEventlogs/exportXls',
  saveBatch = '/riskevent/investigationRiskEventlogs/addBatch',
  deleteByIds = '/riskevent/investigationRiskEventlogs/deleteByIds',
  queryList = '/riskevent/investigationRiskEventlogs/queryList',
  loadEventList = '/riskevent/riskEvent/list',
}
/**
 * 导出api
 * @param params
 */
export const getExportUrl = Api.exportXls;
/**
 * 导入api
 */
export const getImportUrl = Api.importExcel;
/**
 * 列表接口
 * @param params
 */
export const list = (params) =>
  defHttp.get({url: Api.list, params});

export const list2 = (params) =>
  defHttp.get({url: Api.list2, params});

export const listDstIpAll = (params) =>
  defHttp.get({url: Api.listDstIpAll, params});

export const listSrcIpAll = (params) =>
  defHttp.get({url: Api.listSrcIpAll, params});

export const listSrcDstIpAll = (params) =>
  defHttp.post({url: Api.listSrcDstIpAll, params});

export const loadEventList = (params) =>
  defHttp.get({url: Api.loadEventList, params});

/**
 * 删除单个
 * @param params
 * @param handleSuccess
 */
export const deleteOne = (params,handleSuccess) => {
  return defHttp.delete({url: Api.deleteOne, params}, {joinParamsToUrl: true}).then(() => {
    handleSuccess();
  });
}
/**
 * 批量删除
 * @param params
 * @param handleSuccess
 */
export const batchDelete = (params, handleSuccess) => {
  Modal.confirm({
    title: '确认删除',
    content: '是否删除选中数据',
    okText: '确认',
    cancelText: '取消',
    onOk: () => {
      return defHttp.delete({url: Api.deleteBatch, data: params}, {joinParamsToUrl: true}).then(() => {
        handleSuccess();
      });
    }
  });
}
/**
 * 保存或者更新
 * @param params
 * @param isUpdate 是否是更新数据
 */
export const saveOrUpdate2 = (params, isUpdate) => {
  let url = isUpdate ? Api.edit : Api.save;
  return defHttp.post({url: url, params});
}

export const saveOrUpdate3 = (params) => {
  return defHttp.post({url: Api.saveBatch, params});
}

export const deleteByIds = (params,handleSuccess) => {
  return defHttp.delete({url: Api.deleteByIds, params}, {joinParamsToUrl: true}).then(() => {
    handleSuccess();
  });
}

export const queryLIstRequest = (params) =>
  defHttp.get({url: Api.queryList, params});
