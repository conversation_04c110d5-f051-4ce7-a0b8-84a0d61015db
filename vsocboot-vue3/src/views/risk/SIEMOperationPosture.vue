<!--
 * @Author: fanglei =
 * @Date: 2023-07-21 11:13:16
 * @LastEditors: fanglei =
 * @LastEditTime: 2023-07-21 16:53:59
 * @FilePath: \vsoc-vue3\vsocboot-vue3\src\views\risk\RiskEventPosture.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <div>
    <div class="title">
      SIEM Operation Posture
      <div class="chart_filter">
        <a-select default-value="30d" v-model:value="dateFilter" style="float: right;width:150px"
                  @change="allDateFilterChange"  class="font12">
          <a-select-option value="24h">Last 24 hours</a-select-option>
          <a-select-option value="7d">Last 7 days</a-select-option>
          <a-select-option value="30d">Last 30 days</a-select-option>
          <a-select-option value="3m">Last 3 months</a-select-option>
          <a-select-option value="1y">Last 1 year</a-select-option>
        </a-select>
      </div>
    </div>
    <div class="flex-row border-top border-bottom">
      <div class="flex-col ax-border-right">
        <div class="flex-col-left">
          <img :src="img1">
        </div>
        <div class="flex-col-right">
          <div class="number2 "><span style="cursor: pointer;" @click="jumpFunction('/risk/riskEvent',{owner:'unassign'})">{{ topNumBase.num1 }}</span></div>
          <div class="info">Risk Events are not assigned</div>
        </div>
      </div>
      <div class="flex-col ax-border-right">
        <div class="flex-col-left ">
          <img :src="img2">
        </div>
        <div class="flex-col-right">
          <div class="number2 "><span style="cursor: pointer;" @click="jumpFunction('/investigation/InvestigationVOList',{statusStr:'In Process,Pause'})">{{ topNumBase.num2 }}</span></div>
          <div class="info">Investigations have not been closed</div>
        </div>
      </div>
      <div class="flex-col ax-border-right">
        <div class="flex-col-left ">
          <img :src="img3">
        </div>
        <div class="flex-col-right">
          <div class="number2 ">{{ formatSeconds(topNumBase.num3) }} </div>
          <div class="info">Mean time for Risk Events processing</div>
        </div>
      </div>
      <div class="flex-col ax-border-right">
        <div class="flex-col-left ">
          <img :src="img4">
        </div>
        <div class="flex-col-right">
          <div class="number color-purple">{{ formatSeconds(topNumBase.num4) }} </div>
          <div class="info">Mean time for Investigations processing</div>
        </div>
      </div>
    </div>

    <div class="flex-row border-top border-bottom">
      <div class="flex-col_chart">
        <div class="chart_title">Statistical Risk Events by assignment</div>
        <ColumnStack :chartData="chartDataColumn" height="240px"></ColumnStack>
      </div>
      <div class="flex-col_chart" style="position: relative;">
        <div class="chart_title">TOP10 Statistical assignment by user</div>
        <div class="chart_content2">
          <PieLegend :chartData="pieChart" height="240px"  width="50%"  :rightDiv="true"></PieLegend>
        </div>
      </div>
    </div>
    <div class="flex-row border-bottom">
      <div class="flex-col_chart">
        <div class="chart_title">TOP10 Statistical Risk Events processing by user</div>
        <BarBg :chartData="chartDataBar" :option="chartDataBarOption" height="240px"></BarBg>
      </div>
      <div class="flex-col_chart" style="position: relative;">
        <div class="chart_title">TOP10 Statistical Investigations processing by user</div>
        <div class="chart_content2">
          <PieLegend :chartData="pieChart2" height="240px"  width="50%"  :rightDiv="true"></PieLegend>
        </div>

      </div>

    </div>

    <div class="table-wraper" :style="{'height': (dataSource.length * 42 + 150) + 'px'}">

      <BasicTable title=""
                  :canResize="false"
                  :showIndexColumn="true"
                  :indexColumnProps="indexColumnProps"
                  :columns="columns"
                  :data-source="dataSource"
                  :pagination="false">
        <template #form-formFooter>
          <div style="width:100%;" class="padding16">Top 10 rules with the highest false positives</div>
        </template>
        <template #userInfo="{ text, record }">
          <tableUser v-model:value="record.createBy"></tableUser>
        </template>
        <template #expandedRowRender="{ record }">
          <a-row :gutter="[20,0]">
            <a-col :span="8" style="border-right: 1px solid rgba(255,255,255,0.08)">
              <a-form  :model="record" layout="vertical">
                <a-row>
                  <a-col :span="24">
                    <a-form-item :label="t('routes.aggregationrule.ruleName')">
                      <a-input v-model:value="record.ruleName" :disabled="true"></a-input>
                    </a-form-item>
                  </a-col>
                </a-row>
                <a-row :gutter="8">
                  <a-col :span="12">
                    <a-form-item :label="t('routes.aggregationrule.urgency')">
                      <a-select  v-model:value="record.urgency" :options="RULE_URGENCY_LEVEL" :disabled="true">></a-select>
                    </a-form-item>
                  </a-col>
                  <a-col :span="12">
                    <a-form-item :label="t('routes.aggregationrule.riskType')">
                      <a-select  v-model:value="record.riskType" :options="RULE_RISK_TYPE" :disabled="true">></a-select>
                    </a-form-item>
                  </a-col>
                </a-row>
                <a-row>
                  <a-col :span="24">
                    <a-form-item :label="t('routes.aggregationrule.riskDesc')">
                      <a-textarea v-model:value="record.riskDesc" :rows="4" :disabled="true">></a-textarea>
                    </a-form-item>
                  </a-col>
                </a-row>
              </a-form>
            </a-col>
            <a-col :span="16">
              <a-form  :model="record" layout="vertical">
                <a-row :gutter="8">
                  <a-col :span="6">
                    <a-form-item :label="t('routes.aggregationrule.minLimits')">
                      <a-input-number v-model:value="record.minLimits" style="width:100%" :disabled="true">></a-input-number>
                    </a-form-item>
                  </a-col>
                  <a-col :span="6">
                    <a-form-item :label="t('routes.aggregationrule.maxLimits')">
                      <a-input-number v-model:value="record.maxLimits" style="width:100%" :disabled="true">></a-input-number>
                    </a-form-item>
                  </a-col>
                  <a-col :span="6">
                    <a-form-item :label="t('routes.aggregationrule.timeThreshold')">
                      <a-input-number v-model:value="record.timeThreshold" style="width:100%" :disabled="true">></a-input-number>
                    </a-form-item>
                  </a-col>

                  <a-col :span="6">
                    <a-form-item :label="t('routes.aggregationrule.timeThresholdType')">
                      <a-select v-model:value="record.timeThresholdType" :options="RULE_THRESHOLD_TYPE" :disabled="true">></a-select>
                    </a-form-item>
                  </a-col>

                </a-row>
                <a-row>
                  <a-col :span="24">
                    <a-form-item :label="t('routes.aggregationrule.riskGroupby')">
                      <a-select :value="getGroupBy(record)"  mode="multiple"  :options="RULE_FIELD" :disabled="true">>
                      </a-select>
                    </a-form-item>
                  </a-col>
                </a-row>
                <a-row>
                  <a-col :span="24">
                    <a-form-item :label="t('routes.aggregationrule.advancedRules')">
                      <RuleSelect v-model:value="record.advancedRules" :isShow="true" :options="RULE_FIELD"></RuleSelect>
                    </a-form-item>
                  </a-col>
                </a-row>
              </a-form>
            </a-col>
          </a-row>


        </template>
      </BasicTable>
    </div>


  </div>
</template>

<script lang="ts" setup>

import {ref, onMounted, reactive} from 'vue';
import img1 from "/@/assets/images/4.png";
import img2 from "/@/assets/images/5.png";
import img3 from "/@/assets/images/6.png";
import img4 from "/@/assets/images/7.png";
import {BasicColumn, BasicTable} from "/@/components/Table";
import {render} from "/@/utils/common/renderUtils";
import {useI18n} from "/@/hooks/web/useI18n";
import {
  RULE_URGENCY_LEVEL,
  RULE_RISK_TYPE, RULE_THRESHOLD_TYPE, RULE_FIELD,
} from "/@/views/rule/aggregation/AggregationRule.data";
import {
  list,
} from '../rule/aggregation/AggregationRule.api';
import BarBg from "/@/components/chart/BarBg.vue";
import ColumnStack from "/@/components/chart/ColumnStack.vue";
import PieLegend from "/@/components/chart/PieLegend.vue";
import tableUser from '/@/components/vsoc/tableUser.vue';
import {
  queryRiskEventStatisicalRequest,
  queryRiskEventStatisicalInvGroupOwnerRequest,
  queryRiskEventStatisicalGroupOwnerRequest,
  queryRiskEventStatisicalRoleAvgRequest,
  queryRiskEventStatisicalOwner4hourRequest,
} from "/@/views/risk/RiskEvent.api";
import {axisPointerBg} from "/@/components/chart/ChartColor";
import {primaryColor} from "../../../build/config/themeConfig";
import {RuleSelect} from "/@/components/Form";
import {useRouter} from "vue-router";
const {t} = useI18n();
const router = useRouter();
const dateFilter = ref("30d");
const chartDataBar = ref([]);
const chartDataBarOption = reactive({
  tooltip: {
    // trigger: 'axis',
    formatter: '{b}: {c} second',
    axisPointer: {
      type: 'shadow',
      label: {
        show: false,
        backgroundColor: axisPointerBg,
      },
    },
  },
  grid: {
    top:10,
    bottom:20,
    left:100,
    right:10,
  },
  xAxis: {
    type: 'value',
    axisLine : {
      show : false
    },
    splitLine : {
      show : false
    },
    axisTick: {
      show : false
    }
  },
  yAxis: {
    type: 'category',
    data: [],
    axisLine : {
      show : false
    },
    splitLine : {
      show : false
    },
    axisTick: {
      show : false
    },
    axisLabel: {
      formatter: function (value) {
        var label1 = value.split(" ")[0];
        var label2 = value.split(" ")[1] || '';
        return '{a|'+ label1 + '}\n{b|' + label2 + '}';
      },
      rich: {
        a: {
          color: 'rgba(255, 255, 255, 0.8)',
          fontSize: 13,
          lineHeight: 20,
          align: 'right'
        },

        b: {
          color: primaryColor,
          fontSize: 13,
          fontWeight: 'bold',
          lineHeight: 20
        },

      }
    }
  },
  series: [
    {
      name: 'bar',
      type: 'bar',
      barMaxWidth:'48',
      showBackground: true,
      backgroundStyle: {
        color: 'rgba(255, 255, 255, 0.08)'
      },
      data: [],
    },
  ],
});
const chartDataColumn = ref( {});
const pieChart = ref([]);
const pieChart2 = ref([]);
/**
 * 序号列配置
 */
const indexColumnProps: BasicColumn = {
  title: '#',
  width: '50px',
};
/**
 * 列表项
 */
const columns: BasicColumn[] = [
  {
    title: t('routes.aggregationrule.ruleName'),
    align: 'left',
    dataIndex: 'ruleName'
  },
  {
    title: t('routes.aggregationrule.riskType'),
    dataIndex: 'riskType',
    align: 'left',
    customRender: ({text}) => {
      return render.renderDictNative(text, RULE_RISK_TYPE);
    },
  },
  {
    title: t('routes.aggregationrule.urgency'),
    dataIndex: 'urgency',
    align: 'left',
    customRender: ({text}) => {
      return render.renderDictNative(text, RULE_URGENCY_LEVEL);
    }
  },

  {
    title: t('routes.aggregationrule.createBy'),
    dataIndex: 'createBy',
    align: 'left',
    slots: {customRender: 'userInfo'}
  },
  {
    title: t('routes.aggregationrule.createTime'),
    align: 'left',
    dataIndex: 'createTime'
  },
  {
    title: t('routes.aggregationrule.falseRate'),
    align: 'left',
    dataIndex: 'falseRate',
    customRender: function(t,r,index){
      if(t.record.falseRate)return (parseFloat(t.record.falseRate)).toFixed(1)+"%";
      return "";
    }
  },
  {
    title: t('routes.aggregationrule.falseCount'),
    align: 'left',
    dataIndex: 'falseCount',
  },
];
let dataSource = ref([]);
let topNumBase = reactive({
  num1:0,
  num2:0,
  num3:0,
  num4:0
});

async function tableData() {
  const res = await list({dateFilter:dateFilter.value});

  if (res) {
    console.log(res.records);
    dataSource.value = res.records;
    console.log(dataSource.value);
  }

}

onMounted(() => {
  tableData();

  queryTopNumData();

  queryPer1Data();

  queryPer2Data();

  queryBar1Data();

  queryBar2Data();
});

function allDateFilterChange(val){
  console.log("val=",val);
  tableData();

  queryTopNumData();

  queryPer1Data();

  queryPer2Data();

  queryBar1Data();

  queryBar2Data();
}

async function queryTopNumData(){
  const res = await queryRiskEventStatisicalRequest({dateFilter:dateFilter.value});
  if (res) {
    console.log(res)
    topNumBase['num1'] = res[0]?.num;
    topNumBase['num2'] = res[1]?.num;
    topNumBase['num3'] = res[2]?.num;
    topNumBase['num4'] = res[3]?.num;

  }
}

async function queryPer1Data(){
  const res = await queryRiskEventStatisicalGroupOwnerRequest({dateFilter:dateFilter.value});
  if (res) {
    var source = [];
    for(let i=0;i<res.length;i++){
      source.push({value:res[i].num,name:res[i].owner});
    }
    pieChart.value = source;
  }
}

async function queryPer2Data(){
  const res = await queryRiskEventStatisicalInvGroupOwnerRequest({dateFilter:dateFilter.value});
  if (res) {
    var source = [];
    for(let i=0;i<res.length;i++){
      source.push({value:res[i].num,name:res[i].owner});
    }
    pieChart2.value = source;
  }
}

function queryBar1Data(){
  queryRiskEventStatisicalOwner4hourRequest({dateFilter:dateFilter.value}).then(res => {
    let base = {
      stack : ['Assigned','Unassigned'],
      xAxis : res.xData,
      Assigned : res.use,
      Unassigned : res.un
    };
    console.log(base);
    chartDataColumn.value = base;
    console.log(chartDataColumn.value);
  });
}

async function queryBar2Data(){
  const res = await queryRiskEventStatisicalRoleAvgRequest({dateFilter:dateFilter.value});
  if  (res) {
    var source = [];
    for(let i=0;i<res.length;i++){
      source.push({value:(res[i].cha/res[i].num),name:res[i].username});
    }
    console.log(source);
    chartDataBar.value = source;
  }
}

function formatSeconds(value){
  let theTime = value;
  let theTime1 = 0;
  let theTime2 = 0;
  let theTime3 = 0;
  if(theTime > 60){
    theTime1 = parseInt(theTime/60);
    theTime = parseInt(theTime%60);
    if(theTime1 > 60){
      theTime2 = parseInt(theTime1/60);
      theTime1 = parseInt(theTime1%60);
      if(theTime2 > 24){
        theTime3 = parseInt(theTime2/24);
        theTime2 = parseInt(theTime2%24);
      }
    }
  }
  let result = parseInt(theTime) + "second";
  if(theTime1 > 0){
    result = parseInt(theTime1) + "min";// + result;
  }
  if(theTime2 > 0){
    result = parseInt(theTime2) + "hour";// + result;
  }
  if(theTime3 > 0){
    result = parseInt(theTime3) + "day" + result;
  }
  return result;
}
/**
 * 获取group by
 * @param record
 */
function getGroupBy(record) {
  if(record.riskGroupby){
    return record.riskGroupby.split(",");
  }
  return [];
}

function jumpFunction(url,param){
  console.log(url,param);
  router.push({
    path: url,
    query: param
  });
}

</script>

<style scoped lang="less">
@import './less/content.less';
.number2 {

  font-size: 24px!important;
  font-weight: 600;
  line-height: 32px!important;

}
.flex-col-right{
  justify-content: space-around!important;
}
/*.title {

  font-size: 20px;
  font-weight: 600;
  line-height: 32px;
  padding: 8px 16px;

  .chart_filter {
    font-weight: normal;
    float: right;
    font-size: 13px;

    span:first-child {
      margin-right: 10px;
    }

    span:last-child {
      margin-left: 10px;
    }
  }
}

.border-top {
  border-top: 1px solid @border-color;

}

.border-bottom {
  border-bottom: 1px solid @border-color;
}

.flex-row {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: top;


  .flex-col {
    flex: 1;
    display: flex;
    flex-direction: row;
    justify-content: center;
    padding: 14px 0px 14px 40px;

    &:not(:last-child) {
      border-right: 1px solid @border-color;
    }

    .flex-col-left {
      width: 64px;
      height: 64px;
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 32px;
      margin-right: 16px;
      border: 1px solid @border-color;

      img {
        height: 32px;
        width: 32px;
      }
    }

    .flex-col-right {
      flex: 1;
      display: flex;
      flex-direction: column;
      justify-content: space-between;

      .number {

        font-size: 32px;
        font-weight: 600;
        line-height: 40px;
        margin-top: -5px;
      }

      .info {

        font-size: 14px;
        line-height: 24px;
        color: @font-color-default;

      }

    }
  }

  .flex-col_chart {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    padding: 16px;

    &:not(:last-child) {
      border-right: 1px solid @border-color;
    }

    .chart_title {

      font-size: 14px;
      font-weight: 600;
      line-height: 24px;
      color: @font-color-default;
    }

    .chart_content {
      height: 200px;
      width: 100%;
    }


  }
}

.table-title {

  font-size: 14px;
  font-weight: 600;
  height: 60px;
  color: @font-color-default;
  line-height: 60px;
  padding-left: 10px;
}

.table-wraper {
  min-height: 200px;
  margin-top: 10px;

  .soc-basic-table-header__tableTitle {
    justify-content: flex-start;
  }
}*/
</style>
