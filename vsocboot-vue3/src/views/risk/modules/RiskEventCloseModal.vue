<template>
  <a-modal :title="title" :width="width" :visible="visible" @ok="handleOk"
           @cancel="handleCancel">
    <a-row :gutter="24">
      <a-col :span="24" style="padding: 20px;">
        <a-form :layout="formLayout"
                ref="closeForm"
                :model="closeBase"
        >

          <a-form-item label="disposition" prop="disposition"
                       :rules="[{ required: true, message: 'Please select Disposition!' }]"
          >
            <a-select v-model:value="closeBase.disposition" placeholder="please select your zone" @change="dispositionChange">
              <a-select-option :value="1">Unknown</a-select-option>
              <a-select-option :value="2">False Position</a-select-option>
              <a-select-option :value="3">Ture Position</a-select-option>
            </a-select>
          </a-form-item>
          <a-form-item label="Handle" prop="remark">
            <a-textarea
              v-model:value="closeBase.remark"
              placeholder="please writ Handle"
              :rows="3"
            />
          </a-form-item>
        </a-form>

      </a-col>
    </a-row>

    <template #footer>
      <a-button key="back" @click="handleCancel">Return</a-button>
      <a-button key="submit" type="primary" :disabled="isClose" :loading="loading" @click="handleOk">Submit</a-button>
    </template>

  </a-modal>
</template>

<script lang="ts" setup>
  import {ref, nextTick, defineExpose,reactive} from 'vue';
  import {useI18n} from "/@/hooks/web/useI18n"
  import {Modal} from 'ant-design-vue';
  import { formLayout } from '/@/settings/designSetting';
  import {updateStatus} from "/@/views/risk/RiskEvent.api";

  const {t} = useI18n();

  const title = ref<string>('Close');
  const width = ref<number>(800);
  const visible = ref<boolean>(false);
  const closeBase = reactive({remark:''});
  const emit = defineEmits(['ok']);
  let loading = ref(false);
  let isClose = ref(false);



  function init(record){
    console.log("init");
    for(let i in record){
      closeBase[i] = record[i];
    }
    dispositionChange(record.disposition);
    console.log(record,closeBase);
  }


  /**
   * 确定按钮点击事件
   */
  function handleOk() {
    console.log(closeBase);
    updateStatus({
      id:closeBase.id,
      ruleId:closeBase.ruleId,
      status:'4',
      disposition:closeBase.disposition,
      remark:closeBase.remark
    }).then(() => {
      visible.value = false;
      emit("ok");
    })
    handleCancel();
  }

  /**
   * 取消按钮回调事件
   */
  function handleCancel() {
    visible.value = false;
  }

  function dispositionChange(val){
    if(val=='1'){
      isClose.value = true;
    }else{
      isClose.value = false;
    }

  }

  defineExpose({
    visible,init
  });
</script>

<style>
</style>
