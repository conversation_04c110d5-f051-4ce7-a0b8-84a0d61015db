const RouteMap = [
//   {
//     path: '/setting',
//     name: 'UMP System Monitoring',
//     root: true,
//     children: [
//       {
//         path: '/setting/monitor',
//         name: 'System Status',
//       },
//       {
//         path: '/setting/monitor',
//         name: 'Proxy Configuration',
//       },
//       {
//         path: '/setting/monitor',
//         name: 'License Management',
//       },
//       {
//         path: '/setting/monitor',
//         name: 'Field Management',
//       },
//     ],
//   },
  {
    path: '/setting',
    name: 'MDPS Management',
    root: true,
    children: [
      {
        path: '/setting/monitor',
        name: 'MDPS Linkage',
      },
      {
        path: '/setting/monitor',
        name: 'Agent Status',
      },
    ],
  },
  {
    path: '/setting',
    name: 'Risk Rule Management',
    root: true,
    children: [
      {
        path: '/setting/monitor',
        name: 'Event by content',
      },
      {
        path: '/setting/monitor',
        name: 'Event by statistic',
      },
      {
        path: '/setting/monitor',
        name: 'Event by order',
      },
      {
        path: '/setting/monitor',
        name: 'Whitelist',
      },
    ],
  },
  {
    path: '/setting',
    name: 'Asset Management',
    root: true,
    children: [
      {
        path: '/setting/monitor',
        name: 'Integration Management',
      },
      {
        path: '/setting/monitor',
        name: 'Credential Management',
      },
      {
        path: '/setting/monitor',
        name: 'Asset discovery task',
      },
    ],
  },
  {
    path: '/setting',
    name: 'Tickets Management',
    root: true,
    children: [
      {
        path: '/setting/monitor',
        name: 'Tickets Setting',
      },
      {
        path: '/setting/monitor',
        name: 'History Tickets',
      },
    ],
  },
  {
    path: '/setting',
    name: 'Tenant Management',
    root: true,
    children: [
      {
        path: '/setting/monitor',
        name: 'Tenant Management',
      },
    ],
  },
  {
    path: '/setting',
    name: 'User Management',
    root: true,
    children: [
      {
        path: '/setting/monitor',
        name: 'Role Management',
      },
      {
        path: '/setting/monitor',
        name: 'User Management',
      },
    ],
  },
  {
    path: '/setting',
    name: 'Notification',
    root: true,
    children: [
      {
        path: '/setting/monitor',
        name: 'Email Notification',
      },
    ],
  },
];
export default RouteMap;
// console.log(RouteMap.length);
