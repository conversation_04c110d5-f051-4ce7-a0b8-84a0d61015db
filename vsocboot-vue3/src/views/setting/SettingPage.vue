<template>
  <div class="setting">
    <div class="setting-left">
      <ScrollContainer ref="scrollRef">
        <template v-for="(item, index) in RouteMap" :key="index">
          <!-- /six下是租户菜单，租户菜单在非租户模式下不显示  -->
          <div class="menu" v-if="item.path !== '/six' || (isAdmin && item.path === '/six')">
            <div
              class="subMenu"
              @click="changeRoute(subItem.path)"
              :class="{ active: currentRoute == subItem.path }"
              v-for="(subItem, subIndex) in item.children"
              :key="subIndex"
            >
              {{ subItem.name }}
            </div>
            <div class="split"
                 :style="{ display: index === RouteMap.length - 1 ? 'none' : '' }"></div>
          </div>
        </template>
      </ScrollContainer>
    </div>
    <div class="setting-right">
      <router-view/>
    </div>
  </div>
</template>

<script setup lang="ts">
import {ScrollContainer, ScrollActionType} from '/@/components/Container/index';
import { ref, watchEffect} from 'vue';
import {useRouter, useRoute} from 'vue-router';
import {usePermissionStore} from '/@/store/modules/permission';
import {isAdministrator} from "/@/utils/auth";

const router = useRouter();
const route = useRoute();

const isAdmin = isAdministrator();
const permission = usePermissionStore();
const scrollRef = ref<Nullable<ScrollActionType>>(null);
const path = ref('/system/setting');
const RouteMap = ref();
const currentRoute = ref();



const route1 = parseCurrentRouteInfo(path.value, permission.backMenuList);
RouteMap.value = route1?.children || [];
// 监听路由变化
watchEffect(() => {
  if (route.path !== '/system/setting') {
    currentRoute.value = route.path;
    if (String(route.path) == '/workflow/setting/WorkFlowSetting') {
      console.log('----------------');
    }
    router.push({
      path: route.path,
      query: route.query,
    });
  } else {
    router.push(route1?.children[0].children[0].path);
    currentRoute.value = route1?.children[0].children[0].path;
  }
});

const changeRoute = (path: string) => {
  currentRoute.value = path;
  router.push(path);
};

// 解析当前的路由信息
function parseCurrentRouteInfo(path, routeList) {
  const routeInfo = routeList.find((item) => item.path === path);
  return routeInfo;
}

function go(item) {
  router.push(item.path);
}
</script>

<style scoped lang="less">
.setting {
  height: 100vh;
  width: 100%;
  display: flex;
  --padding-l: calc((100% - 84%) / 2);
  
  &-left {
    min-width: 230px;
    background-color: #18191D;
    height: 100%;
    padding: 0;
    //border-right: 1px solid #333333;
    flex-shrink: 0;
    
    .menu {
      // margin-bottom: 15px;
      // margin-right: 9px;
      padding: 16px 8px 0 8px;
      
      &:last-child {
        margin-bottom: 0;
        // border-bottom: 0
      }
      
      //margin-bottom: 15px;
      // text-align: center;
    }
    
    .menu-title {
      border-bottom: 1px solid rgba(255, 255, 255, 0.1);
      padding-bottom: 4px;
      // color: #6095fe;
      margin-bottom: 4px;
      font-size: 13px;
      font-weight: 700;
    }
    
    .subMenu {
      cursor: pointer;
      transition: all 0.3s;
      border-radius: 6px;
      padding: 8px 16px;
      color: @font-color-default;
      
      &:not(.active):hover {
        background: @menu-hover-bg;
      }
      
      &.active {
        position: relative;
        // 添加背景渐变层
        &::before {
          content: '';
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background: @menu-active-bg;
          border-radius: 6px;
        }
        
        // 文字渐变样式
        color: transparent;
        background-image: linear-gradient(90deg, #5098fc 0%, #f6a058 100%);
        -webkit-background-clip: text;
        background-clip: text;
        font-weight: 600;
      }
    }
  }
  
  &-right {
    flex: 1;
    height: 100%;
    overflow: hidden;
  }
  
  .active {
    background: linear-gradient(90deg, #5098fc 30%, #f6a058 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    text-fill-color: transparent;
    border-radius: 4px;
    font-size: 13px;
    font-weight: 600;
  }
  
  .split {
    height: 1px;
    background: rgba(255, 255, 255, 0.1);
    margin: 16px 16px 0 16px;
  }
}
</style>
