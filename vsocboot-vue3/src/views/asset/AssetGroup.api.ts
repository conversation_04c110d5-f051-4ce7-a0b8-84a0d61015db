import {defHttp} from '/@/utils/http/axios';
import {Modal} from 'ant-design-vue';

enum Api {
  list = '/asset/assetGroup/list',
  save='/asset/assetGroup/add',
  edit='/asset/assetGroup/edit',
  moveAssets='/asset/assetGroup/moveAssets',
  findGroupTree='/asset/assetGroup/findGroupTree',
  findGroupAsset='/asset/assetGroup/findGroupAsset',
  queryById='/asset/assetGroup/queryById',
  rename='/asset/assetGroup/rename',
  deleteOne = '/asset/assetGroup/delete',
  deleteBatch = '/asset/assetGroup/deleteBatch',
  importExcel = '/asset/assetGroup/importExcel',
  exportXls = '/asset/assetGroup/exportXls',
}
export const renameGroup = (params,handleSuccess) =>
  defHttp.get({url: Api.rename, params}).then(() => {
    handleSuccess();
  });
export const moveGroupAssets = (params,handleSuccess) =>
  defHttp.get({url: Api.moveAssets, params}).then(() => {
    handleSuccess();
  });
export const findGroupAsset = (params) =>
  defHttp.get({url: Api.findGroupAsset, params});
export const findGroupTree = (params) =>
  defHttp.get({url: Api.findGroupTree, params});
export const queryById = (params) =>
  defHttp.get({url: Api.queryById, params});

/**
 * 导出api
 * @param params
 */
export const getExportUrl = Api.exportXls;
/**
 * 导入api
 */
export const getImportUrl = Api.importExcel;
/**
 * 列表接口
 * @param params
 */
export const list = (params) =>
  defHttp.get({url: Api.list, params});

/**
 * 删除单个
 * @param params
 * @param handleSuccess
 */
export const deleteOne = (params,handleSuccess) => {
  return defHttp.delete({url: Api.deleteOne, params}, {joinParamsToUrl: true}).then(() => {
    handleSuccess();
  });
}
/**
 * 批量删除
 * @param params
 * @param handleSuccess
 */
export const batchDelete = (params, handleSuccess) => {
  Modal.confirm({
    title: '确认删除',
    content: '是否删除选中数据',
    okText: '确认',
    cancelText: '取消',
    onOk: () => {
      return defHttp.delete({url: Api.deleteBatch, data: params}, {joinParamsToUrl: true}).then(() => {
        handleSuccess();
      });
    }
  });
}
/**
 * 保存或者更新
 * @param params
 * @param isUpdate 是否是更新数据
 */
export const saveOrUpdate = (params, isUpdate) => {
  let url = isUpdate ? Api.edit : Api.save;
  return defHttp.post({url: url, params});
}
