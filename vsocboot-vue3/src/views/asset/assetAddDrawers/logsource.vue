<template>
  <div class="log-config-container">
    <!-- Log Source Switch -->
    <div class="log-source-switch">
      <a-switch v-model:checked="logform.ifLogSource" :checkedValue="1" :unCheckedValue="2" @change="changeIfLogSource" />
      <div style="display: flex; flex-direction: column; gap: 8px">
        <span class="switch-label">{{ tPrefix('logsourceswitch') }}</span>
        <span class="switch-description">{{ tPrefix('logsourcetext1') }}</span>
      </div>
    </div>

    <!-- Access Log way -->
    <a-form
      ref="logFormRef"
      name="form"
      :model="logform"
      :label-col="{ span: 24 }"
      :wrapper-col="{ span: 24 }"
      autocomplete="off"
      v-if="logform.ifLogSource == 1"
    >
      <a-card class="access-log-way-card">
        <template #title>
          <div>
            <span>{{ t('routes.asset.accessLogway') }}</span>
          </div>
        </template>
        <a-row gutter="16">
          <a-col :span="8">
            <a-form-item name="selectedLogWay">
              <a-select
                v-model:value="logform.selectedLogWay"
                class="input-field"
                :options="logWayList"
                @change="selectLogWay(logform.selectedLogWay)"
              />
            </a-form-item>
          </a-col>
          <a-col :span="1">
            <MyTooltip v-if="logform.selectedLogWay == 'syslog'">
              <template #title>
                {{ tPrefix('logsourcetext3') }}
              </template>
              <template #default>
                <Icon icon="ant-design:question-circle-outlined" style="margin-top: 10px" />
              </template>
            </MyTooltip>
            <MyTooltip v-if="logform.selectedLogWay == 'api'">
              <template #title>
                {{ tPrefix('logsourcetext2') }}
              </template>
              <template #default>
                <Icon icon="ant-design:question-circle-outlined" style="margin-top: 10px" />
              </template>
            </MyTooltip>
          </a-col>
        </a-row>
        <a-row gutter="16">
          <!-- Right Side - Log Configuration -->
          <a-col :span="24">
            <div v-if="logform.selectedLogWay == 'syslog' && logform.ifLogSource == 1">
              <a-row gutter="16">
                <a-col :span="8">
                  <a-form-item name="logSourceIp" :label="tPrefix('logsourceip')">
                    <a-select
                      v-model:value="logform.logsource.logSourceIp"
                      class="input-field"
                      :options="ipList"
                      :fieldNames="{ label: 'ipv4', value: 'ipv4' }"
                      style="display: flex; align-items: center; justify-content: center"
                    >
                    </a-select>
                  </a-form-item>
                </a-col>
                <a-col :span="1">
                  <MyTooltip>
                    <template #title>
                      {{ tPrefix('logsourcetext4') }}
                    </template>
                    <template #default>
                      <Icon icon="ant-design:question-circle-outlined" style="margin-top: 50px" />
                    </template>
                  </MyTooltip>
                </a-col>
                <a-col :span="8">
                  <a-form-item name="logCharset" :label="tPrefix('logencodeformat')">
                    <a-select v-model:value="logform.logsource.logCharset" class="input-field" :options="encodeList"></a-select>
                  </a-form-item>
                </a-col>
                <a-col :span="7" v-if="!logSourceFlag && !isUpdate">
                  <a-button type="primary" @click="selectLogSource" style="margin-top: 40px">
                    {{ t('routes.asset.selectLogSource') }}
                  </a-button>
                </a-col>
              </a-row>
              <a-row gutter="16">
                <a-col :span="16">
                  <a-form-item name="timeoutThreshold" :label="tPrefix('timeThreshold')">
                    <div style="display: inline-flex; width: 100%; flex: 1; margin-right: 8px">
                      <a-col :span="12" style="flex: 1; margin-right: 8px">
                        <a-input-number
                          id="inputNumber"
                          class="input-field"
                          v-model:value="logform.logsource.timeThreshold"
                          :min="timeMin"
                          :max="timeMax"
                        />
                      </a-col>
                      <a-col :span="4" style="flex: 1">
                        <a-select v-model:value="logform.logsource.timeType" class="input-field" :allowClear="true">
                          <a-select-option value="Min">{{ t('routes.asset.min') }}</a-select-option>
                          <a-select-option value="Hour">{{ t('routes.asset.hour') }}</a-select-option>
                          <a-select-option value="Day">{{ t('routes.asset.day') }}</a-select-option>
                        </a-select>
                      </a-col>
                    </div>
                  </a-form-item>
                </a-col>
              </a-row>
              <a-row gutter="16">
                <a-col :span="10">
                  <a-form-item name="keywords" :label="t('routes.sysmenu.configure.log_source_manager.dropPolicy')">
                    <div v-for="(item, index) in logform.logsource.keywordsList" :key="index" style="display: flex; gap: 8px; margin-bottom: 8px">
                      <a-input v-model:value="item.name" @change="changeKeywordsList" :disabled="selectSourceFlag" />
                      <div class="ax-icon-button" v-if="index == 0 && !selectSourceFlag">
                        <Icon
                          icon="ant-design:plus-outlined"
                          style="cursor: pointer; display: flex; align-items: center; justify-content: center"
                          @click="addDrop(index)"
                          class="ax-icon"
                        >
                        </Icon>
                      </div>
                      <div class="ax-icon-button" v-if="index > 0 && !selectSourceFlag">
                        <Icon
                          icon="ant-design:delete-outlined"
                          style="cursor: pointer; display: flex; align-items: center; justify-content: center"
                          @click="delDrop(index)"
                          class="ax-icon"
                        />
                      </div>
                    </div>
                  </a-form-item>
                </a-col>
              </a-row>
            </div>
            <div v-if="logform.selectedLogWay == 'api' && logform.ifLogSource == 1">
              <a-row gutter="16">
                <a-col :span="12">
                  <div class="right-switch">
                    <a-switch v-model:checked="logSourceSwitch" class="switch" @change="enableLogApi" />
                    <span class="switch-label">{{ tPrefix('iflogapi') }}</span>
                  </div>
                </a-col>
              </a-row>
              <a-row gutter="16">
                <a-col :span="8" style="flex: 1">
                  <a-form-item name="apiId" :label="tPrefix('api')" v-if="logSourceSwitch">
                    <a-select
                      v-model:value="logform.logapi.apiId"
                      class="input-field"
                      :options="apiList"
                      :fieldNames="{ label: 'apiName', value: 'apiId' }"
                      @change="changeApi"
                    >
                    </a-select>
                  </a-form-item>
                </a-col>
              </a-row>
              <a-row gutter="16">
                <a-col :span="8">
                  <a-form-item name="logEncodeFormat" :label="tPrefix('hostip')" v-if="logSourceSwitch">
                    <a-select
                      v-model:value="logform.logapi.logSourceIp"
                      class="input-field"
                      :options="ipList"
                      :fieldNames="{ label: 'ipv4', value: 'ipv4' }"
                    />
                  </a-form-item>
                </a-col>
                <a-col :span="8">
                  <a-form-item name="port" :label="tPrefix('logport')" v-if="logSourceSwitch">
                    <a-input v-model:value="logform.logapi.port" class="input-field" />
                  </a-form-item>
                </a-col>
              </a-row>
              <a-row gutter="16">
                <a-col :span="8" v-for="(field, index) in logform.logapi?.apiparamList" :key="index" style="display: flex; flex-wrap: wrap">
                  <a-form-item
                    :label="Object.keys(field)[0]"
                    :name="['logapi', 'apiparamList', index, Object.keys(field)[0]]"
                    :required="field.required === 'yes'"
                    v-if="logSourceSwitch"
                  >
                    <!-- 使用 v-model 来动态绑定 form.logapi.apiparamList[index][key] 的值  -->
                    <a-input
                      v-model:value="field[Object.keys(field)[0]]"
                      :type="field.type === 'int' || field.type === 'number' ? 'number' : 'text'"
                      :min="field.type === 'int' || field.type === 'number' ? 0 : undefined"
                    />
                  </a-form-item>
                </a-col>
              </a-row>
              <a-row gutter="16">
                <a-col :span="16">
                  <a-form-item name="timeThreshold" :label="tPrefix('timeThreshold')" v-if="logSourceSwitch">
                    <div style="display: inline-flex; width: 100%">
                      <a-col :span="12" style="flex: 1; margin-right: 8px">
                        <a-input-number
                          id="inputNumber"
                          class="input-field"
                          v-model:value="logform.logapi.timeThreshold"
                          :min="timeMin"
                          :max="timeMax"
                        />
                      </a-col>
                      <a-col :span="4" style="flex: 1">
                        <a-select v-model:value="logform.logapi.timeType" class="input-field" :allowClear="true">
                          <a-select-option value="Min">{{ t('routes.asset.min') }}</a-select-option>
                          <a-select-option value="Hour">{{ t('routes.asset.hour') }}</a-select-option>
                          <a-select-option value="Day">{{ t('routes.asset.day') }}</a-select-option>
                        </a-select>
                      </a-col>
                    </div>
                  </a-form-item>
                </a-col>
              </a-row>
            </div>
          </a-col>
        </a-row>
      </a-card>

      <!-- Proxy Configuration -->
<!--      <a-card class="proxy-configuration-card" v-show="!selectSourceFlag" v-if="logform.selectedLogWay == 'syslog' && logform.ifLogSource == 1">-->
<!--        <template #title>-->
<!--          <div>-->
<!--            <span class="card-label">{{ t('routes.asset.proxyconfiguration') }}</span>-->
<!--          </div>-->
<!--        </template>-->
<!--        <a-form-item :rules="[{ required: true, trigger: 'change' }]" :name="['logsource', 'proxyServerId']">-->
<!--          <a-select-->
<!--            :placeholder="t('routes.asset.selectproxy')"-->
<!--            v-model:value="logform.logsource.proxyServerId"-->
<!--            class="input-field"-->
<!--            :options="proxyList"-->
<!--            allowClear-->
<!--          >-->
<!--          </a-select>-->
<!--        </a-form-item>-->
<!--      </a-card>-->
    </a-form>

    <!-- Proxy Configuration -->
    <a-card
      :title="t('routes.asset.parsingruleconfiguration')"
      class="proxy-configuration-card"
      v-show="!selectSourceFlag"
      v-if="logform.selectedLogWay == 'syslog' && logform.ifLogSource == 1 && (socTenantId || !isTenantMode)"
    >
      <BasicTable @register="registerTable">
        <template #vendorProduct="{ record }">
          <div style="display: flex; gap: 8px; align-items: center">
            <img v-if="record.vendorIcon" :src="handleSrc(record)" style="width: 20px; height: 20px" />
            <span v-else :class="handleSrc(record)" style="font-size: 20px"></span>
            <span>{{ record.vendorName ?? t('common.none') }}</span>
          </div>
        </template>
        <template #action="{ record }">
          <div style="display: flex; gap: 16px; align-items: center">
            <a-switch v-model:checked="record.ifLogParse" @change="changeEnable($event, record)"></a-switch>
            <TableAction :actions="getTableAction(record)" />
          </div>
        </template>
      </BasicTable>
    </a-card>
  </div>

  <LogSourceModal ref="LogSourceModalRef" @ok="setLogSource" />
  <ParseRuleViewModal @register="parseRuleViewModalRegister" :reloadFun="reload" :tenantType="2" />
</template>

<script setup lang="ts">
  import { computed, nextTick, onMounted, ref, unref, watch } from 'vue';
  import { BasicTable, TableAction } from '/@/components/Table';
  import { useListPage } from '/@/hooks/system/useListPage';
  import { useColumns, searchFormSchema } from './logsource';
  import { useI18n } from 'vue-i18n';
  import { ajaxGetDictItems } from '/@/utils/dict';
  // import { queryPageListByIntegration } from '/@/views/parseRule/ParseRuleManage.api';
  // import { queryById } from '/@/views/integrationManagement/IntegrationManagement.api';
  import { queryList } from '/@/views/integrationManagement/modules/modules/logAPI/InitApi.api';
  import { cloneDeep } from 'lodash-es';
  import { message } from 'ant-design-vue';
  import LogSourceModal from '/@/views/asset/assetAddDrawers/logSourceModal.vue';
  import { listByAsset } from '/@/views/parseRule/ParseRuleManage.api';
  import { formLayout } from '/@/settings/designSetting';
  import MyTooltip from '/@/views/soar/modular/MyTooltip.vue';
  import { useDrawer } from '/@/components/Drawer';
  import ParseRuleViewModal from '/@/views/parseRule/ParseRuleViewModal.vue';
  import { render } from '/@/utils/common/renderUtils';
  import { getTenantMode } from '/@/utils/auth';

  const isTenantMode = getTenantMode();
  //声明
  const { t } = useI18n();
  function tp(name) {
    return t('routes.LogSourceManager.' + name);
  }
  const tPrefix = (name) => {
    return t('routes.assetBase.' + name);
  };

  const emits = defineEmits([
    'update:form',
    'update:keywordsList',
    'update:ifLogSource',
    'update:additionalNetwork',
    'update:network',
    'clearIpData',
  ]);
  const props = defineProps({
    form: Object as any,
    id: String as any,
    network: Object as any,
    logIpFileVsRule: Array as any,
    isUpdate: Boolean as any,
    integrationId: String as any,
    socTenantId: String as any,
  });

  const logWayList = ref([
    {
      label: tPrefix('receive'),
      value: 'syslog',
    },
    // {
    //   label: tPrefix('logapi'),
    //   value: 'api',
    // },
  ]);

  const logSourceSwitch = ref(false);
  const ipList = ref<any>([]);
  const apiList = ref();
  let formConfigModel: any = {};

  const logSourceFlag = ref(false);
  const logform = ref(cloneDeep(props.form));
  console.log('logform', unref(logform));
  if (!logform.value.logsource) {
    logform.value.logsource = {
      timeType: 'Min',
    };
  }
  if (logform.value.logsource.id) {
    logSourceFlag.value = true;
  }

  console.log('props.logIpFileVsRule', props.logIpFileVsRule);
  logform.value.logsource.logVsRuleArr = props.logIpFileVsRule;
  if (!logform.value.logsource?.logVsRuleArr) {
    logform.value.logsource.logVsRuleArr = [];
  }

  // Function to handle log way selection
  const selectLogWay = (way: string) => {
    logform.value.selectedLogWay = way;
    if (!logform.value?.logsource) {
      logform.value.logsource = {
        timeType: 'Min',
      };
    }
    if (!logform.value?.logapi) {
      logform.value.logapi = {
        timeType: 'Min',
      };
      loadIpList();
    }
    if (!logform.value.logsource.keywordsList || !logform.value.keywords) {
      logform.value.logsource.keywordsList = [{ name: undefined }];
    }
  };

  const timeMin = computed(() => {
    const type = logform.value.selectedLogWay === 'syslog' ? logform.value.logsource.timeType : logform.value.logapi.timeType;
    // 当 timeType 为 Min 或 Hour 时最小值为 0，Day 时最小值为 1
    return type === 'Day' ? 1 : 0;
  });

  const timeMax = computed(() => {
    const type = logform.value.selectedLogWay === 'syslog' ? logform.value.logsource.timeType : logform.value.logapi.timeType;
    if (type === 'Min') {
      return 59;
    } else if (type === 'Hour') {
      return 23;
    } else if (type === 'Day') {
      return 31;
    }
    return 0; // 默认0，可根据需要调整
  });

  //注册table数据
  const { tableContext } = useListPage({
    tableProps: {
      api: listByAsset,
      columns: useColumns(t),
      canResize: false,
      showActionColumn: true,
      showTableSetting: false,
      bordered: false,
      formConfig: {
        model: formConfigModel,
        labelWidth: 120,
        schemas: searchFormSchema,
        submitOnReset: false,
        autoSubmitOnEnter: true,
        showAdvancedButton: true,
        layout: formLayout,
      },
      beforeFetch: (params) => {
        params.socTenantId = props.socTenantId;
        if (props.isUpdate) {
          params.assetId = props.id;
        }
        return params;
      },
      afterFetch: (data) => {
        if (props.logIpFileVsRule.length > 0) {
          data.forEach((element) => {
            props.logIpFileVsRule.forEach((item) => {
              if (element.id == item) {
                element.ifLogParse = true;
              }
            });
          });
        } else {
          data.forEach((element) => {
            element.ifLogParse = false;
          });
        }
      },
    },
  });

  const [registerTable, { reload, getForm, getPaginationRef }] = tableContext;

  // 初始化数据
  //用于监听ip
  watch(
    () => props.network,
    () => {
      console.log(props.network);
      if (props.network.mainIp) {
        loadIpList();
      }
    },
    { deep: true }
  );
  watch(
    () => props.form.ifLogSource,
    () => {
      console.log('watch props', unref(props));
      if (props.form.ifLogSource == 1) {
        logform.value = cloneDeep(props.form);
      }
      console.log('watch ifLogSource', unref(logform));
      if (!logform.value?.logsource) {
        logform.value.logsource = {};
      }
    },
    { deep: true }
  );

  watch(
    () => props.form.logapi?.apiId,
    () => {
      if (props.form.logapi?.apiId) {
        logSourceSwitch.value = true;
        logWayList.value = [
          {
            label: tPrefix('receive'),
            value: 'syslog',
          },
          {
            label: tPrefix('logapi'),
            value: 'api',
          },
        ];
        enableLogApi();
        if (logform.value?.logapi?.params) {
          logform.value.logapi.apiparamList = JSON.parse(logform.value.logapi.params);
        }
      }
    },
    { deep: true }
  );

  watch(
    () => props.integrationId,
    () => {
      console.log('lprops.integrationIdogform', props.integrationId);
      if (props.integrationId) {
        logWayList.value = [
          {
            label: tPrefix('receive'),
            value: 'syslog',
          },
          {
            label: tPrefix('logapi'),
            value: 'api',
          },
        ];
        queryList({ pluginId: props.integrationId }).then((res) => {
          apiList.value = res;
          console.log(apiList.value);
        });
      } else {
        logWayList.value = [
          {
            label: tPrefix('receive'),
            value: 'syslog',
          },
        ];
      }
      if (logform.value.selectedLogWay == 'api') {
        logform.value.logapi = {};
        logform.value.ifLogSource = 2;
        logSourceSwitch.value = false;
      }
      logform.value.selectedLogWay = 'syslog';
    }
  );

  function isEmptyObject(obj) {
    return Object.keys(obj).length === 0;
  }
  if (!logform.value.logapi?.apiId && !logform.value.logsource?.logSourceIp && logform.value.ifLogSource == 1) {
    logform.value.selectedLogWay = 'api';
    logSourceSwitch.value = false;
    logform.value.logsource = {};
    logform.value.logapi = {};
  }
  if (!logform.value.logsource) {
    logform.value.logsource = {};
  }
  if (!logform.value.logapi) {
    logform.value.logapi = {};
  }

  if (!logform.value.logsource.keywordsList || !props.form.logsource.keywords || props.form.logsource.keywords === '') {
    logform.value.logsource.keywordsList = [{ name: undefined }];
  }
  if (logform.value.logapi?.apiId) {
    logSourceSwitch.value = true;
    logWayList.value = [
      {
        label: tPrefix('receive'),
        value: 'syslog',
      },
      {
        label: tPrefix('logapi'),
        value: 'api',
      },
    ];
    enableLogApi();
    if (logform.value?.logapi?.params) {
      logform.value.logapi.apiparamList = JSON.parse(logform.value.logapi.params);
    }
  }
  onMounted(() => {
    console.log('props.integrationIdlog', props.integrationId);
    if (props.integrationId) {
      logWayList.value = [
        {
          label: tPrefix('receive'),
          value: 'syslog',
        },
        {
          label: tPrefix('logapi'),
          value: 'api',
        },
      ];
    }
    loadIpList();
  });

  //查询代理
  const proxyList = ref<any>([]);
  const getProxyList = async () => {
    await ajaxGetDictItems(`proxyDict`, null).then((res) => {
      proxyList.value = res;
    });
  };
  getProxyList();

  //查询编码方式
  const encodeList = ref<any>([]);
  const getEncodeList = async () => {
    await ajaxGetDictItems(`log_charset`, null).then((res) => {
      encodeList.value = res;
    });
  };
  getEncodeList();

  //应用解析规则
  function changeEnable(flag, record) {
    record.ifLogParse = flag;
    if (!logform.value.logsource?.logVsRuleArr) {
      logform.value.logsource.logVsRuleArr = [];
    }
    if (flag && logform.value.logsource.logVsRuleArr.indexOf(record.id) == -1) {
      logform.value.logsource.logVsRuleArr.push(record.id);
    } else if (!flag) {
      const index = logform.value.logsource.logVsRuleArr.indexOf(record.id);
      if (index > -1) {
        logform.value.logsource.logVsRuleArr.splice(index, 1);
      }
    }
  }

  const [parseRuleViewModalRegister, { openDrawer: viewOpen }] = useDrawer();

  function viewRule(record) {
    console.log(record);
    viewOpen(true, {
      id: record.id,
      pid: record.pid,
    });
  }

  //添加dropList
  function addDrop(index) {
    logform.value.logsource.keywordsList.push({ name: undefined });
  }

  /**
   * 删除dropList
   */
  function delDrop(index) {
    logform.value.logsource.keywordsList.splice(index, 1);
    changeKeywordsList();
  }

  //使用logapi
  async function enableLogApi() {
    if (logSourceSwitch.value) {
      await queryList({ pluginId: props.integrationId }).then((res) => {
        apiList.value = res;
        console.log(apiList.value);
      });
      loadIpList();
    }
  }

  //改变api
  function changeApi() {
    let ele = apiList.value.find((item) => item.apiId == logform.value.logapi.apiId);
    let arr = JSON.parse(ele.apiParams);
    logform.value.logapi.apiparamList = [];
    arr.forEach((item) => {
      logform.value.logapi.apiparamList.push({
        [item.variable]: '',
        required: item.required,
        type: item.type,
      });
    });
  }

  //更新数据
  function updateForm() {
    if (!logSourceSwitch.value) {
      logform.value.logapi = {};
    }
    return logform.value;
  }

  function changeKeywordsList() {
    if (
      logform.value.logsource.keywordsList.length > 0 &&
      (logform.value.logsource.keywordsList[0].name != undefined || logform.value.logsource.keywordsList[0].name != '')
    ) {
      console.log('changeKeywordsList123', logform.value);
      console.log('update:keywordsList', logform.value.logsource.keywordsList);
      if (props.isUpdate) {
        emits('update:keywordsList', logform.value.logsource.keywordsList);
      }
    }
  }

  function changeIfLogSource() {
    if (logform.value.ifLogSource == 1) {
      loadIpList();
      logform.value.logsource.timeType = 'Min';
    }
  }

  function loadIpList() {
    // 保存之前的 ipList
    const previousIpList = [...ipList.value];
    ipList.value = [];
    console.log('props.networ12k', props.network);
    const rawState = cloneDeep(props.network);
    if (rawState.others && Array.isArray(rawState.others) && rawState.others.length > 0) {
      ipList.value = rawState.others;
    }
    if (rawState.mainIp && !isEmptyObject(rawState.mainIp) && rawState.mainIp.ipv4) {
      ipList.value.push(rawState.mainIp);
    }
    // 如果 ipList 为空，则使用之前的 ipList
    if (ipList.value.length === 0 && previousIpList.length > 0) {
      ipList.value = previousIpList;
    }
    if (
      ipList.value.length > 0 &&
      logform.value.logsource.logSourceIp &&
      !ipList.value.some((item) => item.ipv4 === logform.value.logsource.logSourceIp)
    ) {
      logform.value.logsource.logSourceIp = '';
    }
    if (ipList.value.length > 0 && logform.value.logapi.logSourceIp && !ipList.value.some((item) => item.ipv4 === logform.value.logapi.logSourceIp)) {
      logform.value.logapi.logSourceIp = '';
    }
  }

  const logFormRef = ref();

  async function validate() {
    await unref(logFormRef).validate();
  }

  function clearDataByChangeTenant() {
    if (selectSourceFlag.value) {
      selectSourceFlag.value = false;
      emits('clearIpData');
      logform.value = cloneDeep(props.form);
    }
  }

  defineExpose({ updateForm, logform, validate, clearDataByChangeTenant, reloadRule, loadIpList });

  const LogSourceModalRef = ref();

  const chooseSourceFlag = ref(false);
  /**
   * 迁移日志发生源
   */
  function selectLogSource() {
    LogSourceModalRef.value.open(props.socTenantId);
  }

  /**
   * true,标识是关联的发生源，目前关联的时候不能修改
   */
  const selectSourceFlag = ref(false);

  /**
   * 赋值发生源信息，同时把资产主IP赋值
   * @param data
   */
  function setLogSource(data) {
    console.log(data);

    logform.value.logsource.logSourceIp = data.logSourceIp;
    logform.value.logsource.logCharset = data.logCharset;
    logform.value.logsource.timeThreshold = data.timeThreshold;
    logform.value.logsource.timeType = data.timeType;
    logform.value.logsource.proxyServerId = data.proxyServerId;
    logform.value.logsource.id = data.id;
    logform.value.logsource.socTenantId = data.socTenantId;
    if (data.keywords) {
      const array = data.keywords.split(',');
      const list: any = [];
      for (let i = 0; i < array.length; i++) {
        list.push({
          name: array[i],
        });
      }
      logform.value.logsource.keywordsList = list;
    }

    selectSourceFlag.value = true;
    console.log('props.network', props.network);
    if (!props.network?.mainIp || !props.network?.mainIp?.ipv4) {
      const newNetwork = {
        mainIp: {
          ipv4: null,
          ipv6: null,
          mac: null,
          isMain: 1,
        },
        others: [],
      };
      newNetwork.mainIp.ipv4 = data.logSourceIp;
      emits('update:additionalNetwork', newNetwork);
      console.log('props.network1', props.network);
    } else if (props.network.mainIp.ipv4 != data.logSourceIp) {
      //把发生源IP设为主IP，原来的主IP改为其它IP
      const d = {
        ipv4: props.network.mainIp.ipv4,
      };
      if (props.network?.others) {
        props.network.others.push(d);
      } else {
        props.network.others = [d];
      }
      props.network.mainIp.ipv4 = data.logSourceIp;

      message.warning(tp('tip1'));
    }
    chooseSourceFlag.value = true;
  }

  function handleSrc(record) {
    if (!record.vendorIcon) {
      if (record.logType === 1) {
        return 'soc ax-setting-Security';
      } else if (record.logType === 2) {
        return 'soc ax-setting-Host';
      } else if (record.logType === 3) {
        return 'soc ax-setting-Network';
      } else if (record.logType === 4) {
        return 'soc ax-setting-Audit';
      }
    }
    return render.renderUploadImageSrc(record.vendorIcon);
  }

  function reloadRule() {
    if (!selectSourceFlag.value) {
      reload();
    }
  }

  /**
   * 操作栏
   */
  function getTableAction(record) {
    return [
      {
        label: t('common.viewText'),
        onClick: viewRule.bind(null, record),
      },
    ];
  }
</script>

<style scoped>
  .log-config-container {
    padding: 24px;
    /* color: #fff; */
  }

  .log-source-switch {
    display: flex;
    align-items: center;
    gap: 16px;
  }

  .switch-label {
    font-size: 16px;
    font-weight: 600;
    line-height: 24px;
    letter-spacing: 0px;
    color: #ffffff;
  }

  .switch-description {
    font-size: 12px;
    font-weight: normal;
    line-height: 16px;
    letter-spacing: 0px;
    color: rgba(255, 255, 255, 0.4);
  }

  .access-log-way-card,
  .proxy-configuration-card {
    /* border-radius: 8px; */
    margin-top: 16px;
    color: #fff;
  }

  .log-way-option {
    padding: 12px;
    color: #fff;
    /* border-radius: 4px; */
    margin-bottom: 16px;
    position: relative;
    cursor: pointer;
    transition: border 0.3s;
    border: 2px solid transparent; /* Default border */
  }

  /* Description text */
  .log-description {
    margin-top: 8px;
    font-size: 12px;
    color: #999;
  }

  .input-field {
    width: 100%;
    margin-bottom: 8px;
    color: #fff;
  }

  .text-description {
    margin-bottom: 8px;
    color: #555;
  }

  .right-switch {
    display: flex;
    align-items: center;
    margin-bottom: 8px;
  }

  .card-label {
    font-size: 16px;
    font-weight: 500;

    &::after {
      display: inline-block;
      margin-left: 4px;
      color: #ad242d;
      font-size: 14px;
      font-family: SimSun, sans-serif;
      line-height: 1;
      content: '*';
    }
  }

  :deep(.ant-card-head) {
    background-color: #030306 !important;
    /* border-radius: 8px !important; */
  }
  :deep(.ant-card-body) {
    background-color: #030306 !important;
    padding: 0 24px !important;
    /* border-radius: 8px !important; */
  }
</style>
