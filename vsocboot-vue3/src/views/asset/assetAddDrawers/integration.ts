import { BasicColumn, FormSchema } from '/@/components/Table';
import { createVNode } from 'vue';
import { useI18n } from '/@/hooks/web/useI18n';

const { t } = useI18n();

export const pluginColumns: BasicColumn[] = [
  // {
  //   title: '',
  //   dataIndex: 'ifIntegration',
  //   width: 150,
  //   slots: { customRender: 'switchaction' },
  // },
  {
    title: '',
    dataIndex: 'integrationName',
    // slots: { customRender: 'integrationName' },
  },
  // {
  //   title: '',
  //   dataIndex: 'manufacturer',
  // },
  {
    title: '',
    dataIndex: 'versions',
  },
];
