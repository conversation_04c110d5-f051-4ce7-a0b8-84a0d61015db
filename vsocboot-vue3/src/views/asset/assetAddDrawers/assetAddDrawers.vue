<template>
  <BasicDrawer v-bind="$attrs" @register="registerModal" :title="title" width="1400px" :destroyOnClose="true" @close="handleClose">
    <div class="integration_table">
      <!--引用表格-->
      <BasicTable @register="registerTable" :isSearch="isSearch">
        <template #context="{ record }">
          <div class="type_div">
            <a-space>
              <img v-for="(item, index) in record.types" :key="index" class="type_image" :src="getTypeSrc(item)" alt="" />
            </a-space>
          </div>
          <div style="display: flex; margin-top: 6px">
            <div class="img_div">
              <img style="max-width: 80px; max-height: 80px" :src="render.renderUploadImageSrc(record.logo)" alt="" />
            </div>
            <div style="flex: 0 0 calc(100% - 80px); display: flex; max-width: calc(100% - 80px)">
              <div style="padding: 0 16px; width: 100%">
                <div
                  class="ft16-bold f-color-1"
                  style="
                    display: -webkit-box;
                    -webkit-line-clamp: 1;
                    -webkit-box-orient: vertical;
                    overflow: hidden;
                    white-space: normal;
                    text-overflow: ellipsis;
                  "
                  :title="record?.integrationName"
                >
                  {{ record?.integrationName }}
                </div>
                <div
                  class="ft12 f-color-06"
                  style="
                    margin-top: 8px;
                    display: -webkit-box;
                    -webkit-line-clamp: 2;
                    -webkit-box-orient: vertical;
                    overflow: hidden;
                    white-space: normal;
                    text-overflow: ellipsis;
                  "
                  :title="record?.integrationDesc"
                >
                  {{ record?.integrationDesc }}
                </div>
              </div>
            </div>
          </div>
        </template>
        <template #context2="{ record }">
          <div style="margin-top: 6px">
            <div style="display: flex; gap: 8px">
              <div class="text_div ft12 f-color-06 text_ellipsis" :class="{ text_div2: !isTenantMode }" :title="record?.serialNumber">
                {{ tp('serialNumber') }} : {{ record?.serialNumber }}
              </div>
              <div v-if="isAdministrator()" class="text_div ft12 f-color-06 text_ellipsis" :title="record?.socTenantId_dictText">
                {{ tp('tenant') }} : {{ record?.socTenantId_dictText }}
              </div>
              <div class="text_div ft12 f-color-06 text_ellipsis" :class="{ text_div2: !isTenantMode }">
                {{ tp('publishUser') }} :
                <span v-if="record?.tenantType == 1 && isTenant()">MSSP</span>
                <span v-else :title="record?.createBy">{{ record?.createBy }}</span>
              </div>
              <div
                class="text_div ft12 f-color-06 text_ellipsis"
                :style="{ flex: isTenantMode ? '0 0 calc(20% - 8px)' : '0 0 calc(25% - 8px)' }"
                :title="record?.versions"
              >
                {{ tp('versions') }} : {{ record?.versions }}
              </div>
              <div class="text_div ft12 f-color-06 text_ellipsis" :style="{ flex: isTenantMode ? '0 0 25%' : '0 0 30%' }">
                {{ tp('plugin') }} :
                <span v-if="record.pluginId" :title="record?.pluginName + '(' + record?.pluginVersion + ')'">
                  {{ record?.pluginName }}({{ record?.pluginVersion }})
                </span>
                <span v-else>
                  {{ tp('none') }}
                </span>
              </div>
            </div>
            <div style="display: flex; margin-top: 12px; gap: 8px">
              <div class="number_div">{{ record?.ruleNum ?? 0 }} {{ tp('parsingRules') }} </div>
              <div class="number_div">{{ record?.apiNum ?? 0 }} {{ tp('logAPIs') }}</div>
              <div class="number_div">{{ record?.actionNum ?? 0 }} {{ tp('actions') }}</div>
              <div class="number_div">{{ record?.assetNum ?? 0 }} {{ tp('assets') }}</div>
            </div>
          </div>
        </template>
        <!--操作栏-->
        <template #action="{ record }">
          <a-space>
            <a-button type="primary" @click="handleAdd(record)">
              {{ t('routes.asset.addAsset') }}
            </a-button>
          </a-space>
        </template>
      </BasicTable>
    </div>
    <div class="fixed-bottom">
      <div class="text-container">
        <div class="title">{{ tp('addAssetFirst') }}</div>
        <div class="description">{{ tp('addAssetSecond') }}</div>
      </div>
      <div class="button-container" v-for="item in typeList" :key="item.value" :label="item.label" @click="addAsset(item.value)">
        <img
          src="../../../assets/images/asset/HostServer.svg"
          style="width: 20px; height: 20px; cursor: pointer; margin-bottom: 10px; filter: brightness(0) saturate(100%) invert(1)"
          v-if="item.value == 'Host'"
          class="button"
        />
        <img
          src="../../../assets/images/asset/CloudServer.svg"
          style="width: 20px; height: 20px; cursor: pointer; margin-bottom: 10px; filter: brightness(0) saturate(100%) invert(1)"
          v-if="item.value == 'Cloud'"
          class="button"
        />
        <img
          src="../../../assets/images/asset/Virtual machine.svg"
          style="width: 20px; height: 20px; cursor: pointer; margin-bottom: 10px; filter: brightness(0) saturate(100%) invert(1)"
          v-if="item.value == 'Virtual'"
          class="button"
        />
        <img
          src="../../../assets/images/asset/Network Device.svg"
          style="width: 20px; height: 20px; cursor: pointer; margin-bottom: 10px; filter: brightness(0) saturate(100%) invert(1)"
          v-if="item.value == 'Network'"
          class="button"
        />
        <img
          src="../../../assets/images/asset/Security device.svg"
          style="width: 20px; height: 20px; cursor: pointer; margin-bottom: 10px; filter: brightness(0) saturate(100%) invert(1)"
          v-if="item.value == 'Cybersecurity'"
          class="button"
        />
        <img
          src="../../../assets/images/asset/IOT device.svg"
          style="width: 20px; height: 20px; cursor: pointer; margin-bottom: 10px; filter: brightness(0) saturate(100%) invert(1)"
          v-if="item.value == 'IOT'"
          class="button"
        />
        <img
          src="../../../assets/images/asset/Application.svg"
          style="width: 20px; height: 20px; cursor: pointer; margin-bottom: 10px; filter: brightness(0) saturate(100%) invert(1)"
          v-if="item.value == 'Application'"
          class="button"
        />
        <div class="button-text">{{ item.label }}</div>
      </div>
    </div>
  </BasicDrawer>
  <addDrawer @register="register" @success="handleSuccess" @closeDrawer="closeDrawer" v-model:ipv4="ipv4" :times="times" />
</template>

<script lang="ts" name="integrationManagement-integrationManagement" setup>
  import { ref, unref, computed, provide } from 'vue';
  import { SettingOutlined } from '@ant-design/icons-vue';
  import { BasicTable } from '/@/components/Table';
  import { useListPage } from '/@/hooks/system/useListPage';
  import { BasicDrawer, useDrawerInner } from '/@/components/Drawer';
  import { columns, searchFormSchema, typeImg } from '/@/views/integrationManagement/IntegrationManagement.data';
  import { list } from '/@/views/integrationManagement/IntegrationManagement.api';
  import { useI18n } from '/@/hooks/web/useI18n';
  import { formLayout } from '/@/settings/designSetting';
  import { useDrawer } from '/@/components/Drawer';
  import { render } from '/@/utils/common/renderUtils';
  import { getTenantMode, isAdministrator, isTenant } from '/@/utils/auth';
  import addDrawer from './addDrawer.vue';
  import { typeOptions } from '/@/views/integrationManagement/IntegrationManagement.data';
  import { useAppStore } from '/@/store/modules/app';

  const isTenantMode = getTenantMode();
  const { t } = useI18n();
  function tp(name) {
    return t('integration.IntegrationManagement.' + name);
  }
  const isUpdate = ref(false);
  const title = computed(() => (!unref(isUpdate) ? tp('addAsset') : tp('editAsset')));
  const isSearch = ref<boolean>(true);
  const integrationType = ref();
  const socTenantId = ref<any>();
  const typeList = ref();
  const ipv4 = ref();
  const assetSource = ref();
  const appStore = useAppStore();
  provide('ipv4', ipv4.value);
  //注册table数据
  const { tableContext } = useListPage({
    tableProps: {
      title: 'Integration Management',
      api: list,
      columns,
      showHeader: false,
      canResize: false,
      showTableSetting: false,
      formConfig: {
        labelWidth: 120,
        schemas: searchFormSchema,
        autoSubmitOnEnter: true,
        showAdvancedButton: true,
        layout: formLayout,
      },
      actionColumn: {
        width: 90,
      },
      beforeFetch: (params) => {
        let arr = [] as any;
        if (integrationType.value) {
          arr.push(integrationType.value);
        }
        params.relatedIntegrationTypeList = arr;
        if (socTenantId.value) {
          params.socTenantId = socTenantId.value;
        }
        return params;
      },

      afterFetch: (data) => {
        for (let i in data) {
          const str = data[i].integrationTypeStr;
          data[i].types = str.split(',');
        }
        console.log(data);
        return data;
      },
    },
  });
  const times = ref('1');
  const [registerTable, { reload }, {}] = tableContext;
  //表单赋值
  const [registerModal, { setDrawerProps, closeDrawer }] = useDrawerInner(async (data) => {
    console.log(data);
    setDrawerProps({
      confirmLoading: false,
      showFooter: data?.showFooter,
      showCancelBtn: data?.showFooter,
      showOkBtn: data?.showFooter,
    });
    if (data.relatedIntegrationType) {
      integrationType.value = data.relatedIntegrationType;
      socTenantId.value = appStore.getRelatedTenantId;
      typeList.value = typeOptions.filter((item) => item.value === data.relatedIntegrationType);
    } else {
      typeList.value = typeOptions;
    }
    if (data.ipv4) {
      console.log('data.ipv4', data.ipv4);
      times.value = String(new Date().getTime());
      ipv4.value = data.ipv4;
      console.log('ipv4.value', ipv4.value);
    }
    if (data.assetSource) {
      assetSource.value = data.assetSource;
    }

    isUpdate.value = !!data?.isUpdate;
  });
  const [register, { openDrawer }] = useDrawer();
  provide('closeDrawer', closeDrawer);
  /**
   * 新增事件
   */
  const integrationId = ref();
  const ifRelatedAsset = ref(false);
  function handleAdd(record) {
    integrationId.value = record.id;
    if (integrationType.value) {
      ifRelatedAsset.value = true;
    } else {
      ifRelatedAsset.value = false;
    }
    openDrawer(true, {
      isUpdate: false,
      showFooter: true,
      integrationTypeStr: ifRelatedAsset.value ? 'Application' : record.integrationTypeStr,
      record: record,
      nointegration: false,
      assetSource: assetSource.value,
    });
    handleCancel();
  }

  /**
   * 成功回调
   */
  function handleSuccess() {
    reload();
  }

  //打开抽屉，在没有集成的情况下
  function addAsset(integrationType) {
    openDrawer(true, {
      isUpdate: false,
      showFooter: true,
      integrationTypeStr: integrationType,
      nointegration: true,
      assetSource: assetSource.value,
    });
    handleCancel();
  }

  function getTypeSrc(item) {
    return typeImg[item];
  }

  function handleClose() {
    handleCancel();
    appStore.setRelatedTenantId(undefined);
    appStore.setIfRelated(false);
    appStore.setRelatedParentId(undefined);
  }

  function handleCancel() {
    integrationType.value = null;
    socTenantId.value = null;
    closeDrawer();
  }
</script>
<style scoped lang="less">
  .integration_table {
    .img_div {
      flex: 0 0 80px;
      display: flex;
      justify-content: center;
      align-items: center;
    }

    .number_div {
      padding: 4px 8px;
      background: rgba(48, 140, 255, 0.2);
      border-radius: 4px;
    }

    .text_div {
      flex: 0 0 calc(18% - 8px);
    }

    .text_div2 {
      flex: 0 0 calc(22% - 8px);
    }

    .type_div {
      position: absolute;
      top: 0;
      left: 0;
      border-radius: 0 0 16px 0;
      line-height: 16px;
      padding: 4px;

      /* Font/白0.1 */
      background: rgba(255, 255, 255, 0.1);

      .type_image {
        width: 16px;
        height: 16px;
      }
    }

    :deep(.ant-table) {
      border-radius: 0;
    }

    :deep(.ant-table-tbody > tr > td) {
      border-left: 1px solid @border-color;
    }

    :deep(.searchForm) {
      background-color: transparent !important;
    }
  }

  .fixed-bottom {
    display: flex;
    bottom: 0;
    /* width: 100%; */
    /* 可以根据需要设置其他样式，比如高度、背景色、文本颜色等 */
    background-color: #1a1b1f;
    padding: 10px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    left: 0;
    right: 0;
    height: 100px;
  }
  .button-container {
    display: flex;
    flex-direction: column; /* 让图标和文字垂直排列 */
    align-items: center; /* 水平居中 */
    justify-content: center; /* 垂直居中 */
    float: right;
    padding: 10px;
    text-align: center;
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 8px;
    margin: 0 10px;
    width: 120px;
    height: 80px;
  }

  .text-container {
    float: left;
    width: 335px;
  }

  .title {

    font-size: 16px;
    font-weight: 600;
    line-height: 24px;
    letter-spacing: 0px;
    margin-bottom: 5px; /* 下外边距 */
    color: #ffffff;
  }

  .description {
    font-size: 12px; /* 描述字号 */

    font-size: 12px;
    font-weight: normal;
    line-height: 16px;
    letter-spacing: 0px;
    color: rgba(255, 255, 255, 0.6);
  }
  .button {
    border-radius: 4px; /* 圆角 */
    background-color: transparent; /* 背景透明 */
    color: white; /* 文字颜色 */
    font-size: 40px; /* 字体大小 */
    cursor: pointer; /* 鼠标悬停时显示手形光标 */
    transition: transform 0.2s ease-in-out; /* 过渡效果 */
  }
  .button-text {
    border-radius: 4px; /* 圆角 */
    background-color: transparent; /* 背景透明 */
    color: white; /* 文字颜色 */
    font-size: 14px; /* 字体大小 */
    text-align: center;
    cursor: pointer; /* 鼠标悬停时显示手形光标 */
    transition: transform 0.2s ease-in-out; /* 过渡效果 */
  }
  :deep(.searchForm) {
    background-color: transparent !important;
  }
</style>
