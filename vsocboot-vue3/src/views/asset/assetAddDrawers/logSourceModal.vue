<template>
  <a-modal v-model:visible="visible" :title="tp('logSource')" @ok="handleOk" :destroy-on-close="true" :mask-closable="false" width="1000px">
    <div class="p-[16px]">
      <BasicTable @register="registerTable" :isSearch="true" :row-selection="rowSelection" />
    </div>
  </a-modal>
</template>

<script setup lang="ts">
  import { nextTick, ref, unref } from 'vue';
  import { BasicTable } from '/@/components/Table';
  import { useListPage } from '/@/hooks/system/useListPage';
  import { TABLE_CACHE_KEY } from '/@/utils/valueEnum';
  import { useI18n } from '/@/hooks/web/useI18n';
  import { loadNoAssetList } from '/@/views/configure/logsourcemanager/LogSourceManagerList.api';
  import { getColumns, searchFormSchema } from '/@/views/configure/logsourcemanager/LogSourceManagerList.data';

  const { t } = useI18n();

  function tp(name) {
    return t('routes.LogSourceManager.' + name);
  }

  const emits = defineEmits(['ok']);
  const visible = ref(false);

  const { tableContext } = useListPage({
    tableProps: {
      api: loadNoAssetList,
      columns: getColumns(),
      canResize: false,
      formConfig: {
        baseColProps: {
          lg: 5, // ≥992px
          xl: 4, // ≥1200px
          xxl: 4, // ≥1600px
        },
        schemas: searchFormSchema,
        // autoSubmitOnEnter: true,
        // showAdvancedButton: true,
      },
      beforeFetch: (params) => {
        params.socTenantId = socTenantId;
      },
      showActionColumn: false,
      tableSetting: {
        cacheKey: TABLE_CACHE_KEY.logSourceManager,
      },
    },
  });

  const [registerTable, {}, { rowSelection, selectedRows, selectedRowKeys }] = tableContext;
  rowSelection.type = 'radio';

  const handleOk = () => {
    let source: any = unref(selectedRows);
    console.log(source);
    if (!source || source.length == 0) {
      return;
    }
    source = source[0];
    emits('ok', source);
    visible.value = false;
  };
  let socTenantId = '';
  const open = (data) => {
    socTenantId = data;
    visible.value = true;
    nextTick(() => {
      selectedRows.value = [];
      selectedRowKeys.value = [];
    });
  };

  defineExpose({
    open,
  });
</script>

<style scoped lang="less"></style>
