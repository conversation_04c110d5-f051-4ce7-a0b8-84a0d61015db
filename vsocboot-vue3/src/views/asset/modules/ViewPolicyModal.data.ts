import {BasicColumn, FormSchema} from '/@/components/Table';
import {useI18n} from "/@/hooks/web/useI18n";

const {t} = useI18n();
export const columns = (): BasicColumn[] => {
  const col: BasicColumn[] = [
    {
      title: t('routes.asset.port'),
      dataIndex: 'portNum',
    },
    {
      title: t('routes.asset.policytype'),
      dataIndex: 'tagType',
    },
    {
      title: t('routes.asset.desc'),
      dataIndex: 'tagDesc',
    },
  ]
  return col;
};

export const searchFormSchema: FormSchema[] = [
  {
    label: '',
    field: 'search',
    component: 'InputSearch',
    componentProps: {
      placeholder: t('routes.assetBase.searchtext')
    },
    colProps: {
      lg: 12, // ≥992px
      xl: 6, // ≥1200px
      xxl: 4, // ≥1600px
    }
  },
];


