<template>
  <BasicModal v-bind="$attrs" @register="registerModal" :title="t('routes.asset.taskexecutionreport')" width="80%">
    <AssetScanReportList v-model:value="taskId"></AssetScanReportList>
  </BasicModal>
</template>

<script lang="ts" setup>
  import { computed, nextTick, ref, unref } from 'vue';
  import { BasicModal, useModalInner } from '/@/components/Modal';
  import AssetScanReportList from '/@/views/asset/components/AssetScanReportList.vue';
  import { useI18n } from '/@/hooks/web/useI18n';
  const { t } = useI18n();
  const taskId = ref('');

  //表单赋值
  const [registerModal, { setModalProps, closeModal }] = useModalInner(async (data) => {
    //重置表单

    setModalProps({ confirmLoading: false, showCancelBtn: false, showOkBtn: false });
    nextTick(() => {
      taskId.value = data.record.id;
      console.log('port data', taskId.value);
    });
  });
</script>

<style lang="less" scoped></style>
