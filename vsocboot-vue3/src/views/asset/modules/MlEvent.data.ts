import {BasicColumn} from '/@/components/Table';
import {FormSchema} from '/@/components/Table';
import {useI18n} from "/@/hooks/web/useI18n";

const {t} = useI18n();


export const columns: BasicColumn[] = [
  {
    title: t('routes.MlEvent.ruleName'),
    dataIndex: 'ruleName'
  },
  {
    title: 'Role',
    dataIndex: 'Role',
    slots: { customRender: 'Role' },
  },
  {
    title: t('routes.MlEvent.urgency'),
    dataIndex: 'urgency',
    slots: { customRender: 'severity' },
  },
  {
    title: t('routes.MlEvent.riskStatus'),
    dataIndex: 'riskStatus',
    customRender: ({text}) => {
      const arr = ["", t('routes.MlEvent.new'),t('routes.MlEvent.investigating') , t('routes.MlEvent.close') ]
      return arr[text]
    }
  },
  {
    title: t('routes.MlEvent.alarmTime'),
    dataIndex: 'alarmTime'
  },
];

export const searchFormSchema: FormSchema[] = [
  {
    label: t('routes.MlEvent.ruleName'),
    field: 'ruleName',
    component: 'JInput'
  },
  {
    label: t('routes.MlEvent.urgency'),
    field: 'urgency',
    component: 'Select',
    componentProps: {
      options: [
        {label: t('common.Critical'), value: 1},
        {label:  t('common.High'), value: 2},
        {label:  t('common.Middle'), value: 3},
        {label: t('common.Low') , value: 4},
        {label:  t('common.Information'), value: 5},
      ]
    }
  },
  {
    label: t('routes.MlEvent.riskStatus'),
    field: 'riskStatus',
    component: 'Select',
    componentProps: {
      options: [

        {label:  t('routes.MlEvent.new'), value: 1},
        {label: t('routes.MlEvent.investigating') , value: 2},
        {label:t('routes.MlEvent.close'), value: 3},
      ]
    }
  },
  {
    label: t('routes.MlEvent.alarmTime'),
    field: 'startDate',
    component: 'RangeDate',
    componentProps: {
      datetime: true
    },
    colProps: {
      lg: 12, // ≥992px
      xl: 6, // ≥1200px
      xxl: 5, // ≥1600px
    },
  },
];

export const formSchema: FormSchema[] = [
  // TODO 主键隐藏字段，目前写死为ID
  {label: '', field: 'id', component: 'Input', show: false},
  {
    label: t('routes.MlEvent.ruleName'),
    field: 'ruleName',
    component: 'Input',
  },
  {
    label: t('routes.MlEvent.urgency'),
    field: 'urgency',
    component: 'Input',
  },
  {
    label: t('routes.MlEvent.riskStatus'),
    field: 'riskStatus',
    component: 'Input',
  },
  {
    label: t('routes.MlEvent.ruleJson'),
    field: 'ruleJson',
    component: 'Input',
  },
  {
    label: t('routes.MlEvent.alarmTime'),
    field: 'alarmTime',
    component: 'DatePicker',
    componentProps: {
      showTime: true,
      valueFormat: 'YYYY-MM-DD hh:mm:ss',
    },
  },
];
