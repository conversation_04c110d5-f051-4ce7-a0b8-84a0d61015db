<template>

  <div class="search_transparent">
    <!--引用表格-->
    <BasicTable @register="registerTable"   :rowSelection="rowSelection as any">
      <template #action="{ record }">
        <a-space size="5" class="action-border">
          <span @click="handleView(record)">{{ t("common.viewText") }}</span>
          <a-divider type="vertical"/>
          <span @click="toThreatHunting(record)">Hunting</span>
          <a-divider type="vertical"/>
          <a-dropdown :trigger="['click']">
            <span class="ant-dropdown-link" @click.prevent="loadInvestigation">
              Investigation
            </span>
            <template #overlay>
              <a-menu>
                <a-menu-item key="0" @click="showAddInvestigation(record?.eventId)">
                  <Icon icon="ant-design:plus-outlined"/>
                  {{ t('routes.riskLogs.add') }}
                </a-menu-item>
                <a-menu-divider/>
                <a-menu-item v-for="item in investigationData" :key="item.id"
                             @click="addInvestigation(item,record)">
                  <span>{{ item?.investigation }}</span>
                </a-menu-item>
                <a-menu-divider/>
                <a-menu-item key="1" @click="showMoreInvestigation(record)">
                  <span>{{ t('routes.riskLogs.addMore') }}</span>
                </a-menu-item>
              </a-menu>
            </template>
          </a-dropdown>
        </a-space>

      </template>
      <template #severity="{ text }">
        <span class="severity1" v-if="text== t('common.Critical')">{{ text }}</span>
        <span class="severity2" v-if="text==t('common.High') ">{{ text }}</span>
        <span class="severity3" v-if="text==t('common.Middle') ">{{ text }}</span>
        <span class="severity4" v-if="text==t('common.Low') ">{{ text }}</span>
        <span class="severity4" v-if="text==t('common.Information') ">{{ text }}</span>
      </template>
    </BasicTable>
    <RiskLogsModal ref="registerRiskLogsModal" :eventId="LogId" :type="LogType"/>
    <a-modal v-model:visible="inveVisible" :title="t('common.confirm')" @ok="saveToInve"
             :maskClosable=false>
      <a-row style="padding: 0 24px;">
        <a-col :span="24">
          {{t('routes.riskLogs.addInvestigationPrompt')}}
        </a-col>
        <a-col :span="24">
          <a-form class="antd-modal-form"
                  autocomplete="off" :layout="formLayout">
            <a-form-item label="Conclusion">
              <a-textarea v-model:value="conclusion"/>
            </a-form-item>
          </a-form>
        </a-col>
      </a-row>
    </a-modal>
  </div>
  <WhitelistVOModal @register="registerModal" type="1"/>
</template>

<script lang="ts" name="AssetRiskEventList" setup>
import {useRoute, useRouter} from "vue-router";
import {ref} from 'vue';
import {BasicTable} from '/@/components/Table';
import {useListPage} from '/@/hooks/system/useListPage'
import {columns, searchFormSchema} from './RiskEventView.data';
import {tableList} from '/@/views/aggregationRiskEventView/RiskEventView.api';
import {list} from "/@/views/investigation/InvestigationVO.api";
import {useI18n} from "/@/hooks/web/useI18n";
import {formLayout} from '/@/settings/designSetting';
import RiskLogsModal from "/@/views/riskLogs/modules/RiskLogsModal.vue";
import {useMessage} from "/@/hooks/web/useMessage";
import {useUserStore} from "/@/store/modules/user";
import {saveOrUpdate2} from "/@/views/risk/InvestigationRiskEventlogs.api";
import dayjs from "dayjs";
import WhitelistVOModal from "/@/views/whitelist/modules/WhitelistVOModal.vue";
import {useModal} from "/@/components/Modal";

const {t} = useI18n();
const isSearch = ref<boolean>(true);
const router = useRouter();
const {createMessage} = useMessage();
const route = useRoute();
const mainIp = ref(router.currentRoute.value.query.ip);

let formRule: any = [];
for (let i = 0; i < searchFormSchema.length; i++) {
  let base: any = {};
  for (let j in searchFormSchema[i]) {
    base[j] = searchFormSchema[i][j];
  }
  if (searchFormSchema[i].field == 'updateTimeStr') {
    base['defaultValue'] = dayjs().subtract(7, 'day').format('YYYY-MM-DD HH:mm:ss') + ',' + dayjs().format('YYYY-MM-DD HH:mm:ss');
  }
  formRule.push(base);
}
for (let i in route.query) {
  for (let j = 0; j < formRule.length; j++) {
    console.log(i, formRule[j].field);
    if (i == formRule[j].field) {
      formRule[j].defaultValue = route.query[i];
      break;
    }
    if (i == 'eventType' && formRule[j].field == 'eventName') {
      formRule[j].defaultValue = route.query[i];
      break;
    }
  }
}
let RiskPostureQuery: any = sessionStorage.getItem("RiskPostureQuery")
if (RiskPostureQuery) {
  RiskPostureQuery = JSON.parse(RiskPostureQuery)
  console.log(RiskPostureQuery)
  for (let j = 0; j < formRule.length; j++) {
    console.log(formRule[j].field);
    if (RiskPostureQuery[formRule[j].field]) {
      formRule[j].defaultValue = RiskPostureQuery[formRule[j].field];
    }
  }
  sessionStorage.setItem("RiskPostureQuery", "")
}

//注册table数据
const {tableContext} = useListPage({
  tableProps: {
    api: tableList,
    rowKey: "eventId",
    columns,
    canResize: false,
    formConfig: {
      labelWidth: 120,
      baseColProps: {
        lg: 6, // ≥992px
        xl: 4, // ≥1200px
        xxl: 4, // ≥1600px
      },
      labelCol: {
        xs: 24,
        sm: 8,
        md: 24,
        lg: 24,
        xl: 24,
        xxl: 24,
      },
      schemas: formRule,
      autoSubmitOnEnter: true,
      showAdvancedButton: true,
      layout: formLayout,
      baseColProps: {
        lg: 6, // ≥992px
        xl: 3, // ≥1200px
        xxl: 3, // ≥1600px
      },
    },
    defSort: {
      column: 'updateTime',
      order: 'desc',
    },
    actionColumn: {
      width: 250,
    },
    searchInfo: {
      fromMainIp :mainIp.value
    },
  },
})

const [registerTable, {}, {rowSelection, selectedRowKeys}] = tableContext


/**
 * 编辑事件
 */
function handleView(record: Recordable) {
  router.push({
    path: "/aggregationRiskEventView/modules/RiskEventViewModal",
    query: {type: record.type, eventId: record.eventId,tab:'3-1'}
  });
}

function toThreatHunting(record) {
  router.push({
    path: "/threatHunting/ThreatHuntingIndex",
    query: {val: record.type, eventName: record.eventName, eventId: record.eventId}
  });
}

const investigationData = ref<any>([]);
const LogId = ref<string>("");//记录当前点击的日志id
const LogType = ref<string>("");//记录当前点击的日志type
const loadInvestigation = () => {
  list({"pageSize": 5}).then((result) => {
    console.log(result)
    let list = result.records;
    investigationData.value = list
  });
}

function addInvestigation(data, record): void {
  console.log(data, record)
  let id = ""
  if (!record) {
    if (selectedRowKeys.value.length == 0) {
      createMessage.error(t('common.chooseDataText'));
      return;
    }
    id = selectedRowKeys.value.join(",");
    let types: any = []
    for (let i in rowSelection.value) {
      types.push(rowSelection.value[i].type)
    }
    LogType.value = types.join(",")
  } else {
    id = record.eventId
    if (record.type == '1') {
      LogType.value = "1"
    } else if (record.type == '2') {
      LogType.value = "2"
    }
  }
  inveId = data.id
  LogId.value = id
  inveVisible.value = true
}

const inveVisible = ref(false)
let inveId = ""
const conclusion = ref("")
const userStore = useUserStore();
const saveToInve = () => {
  if (addInveFlag) {
    addInveFlag = false
    let param = {
      eventId: LogId.value,
      conclusion: conclusion.value,
      conclusionBy: userStore.userInfo?.username,
      type: "1"
    }
    sessionStorage.setItem("addInvestigationParam", JSON.stringify(param));
    router.push({
      path: "/investigation/modules/InvestigationNewModal"
    });
    return
  }

  saveOrUpdate2({
    investigationId: inveId,
    eventId: LogId.value,
    conclusion: conclusion.value,
    conclusionBy: userStore.userInfo?.username,
    typeStr: LogType.value
  }, false).then(() => {
    inveVisible.value = false
    conclusion.value = ""
  })
}
const registerRiskLogsModal = ref();

function showMoreInvestigation(record) {
  let id = ""
  if (!record) {
    if (selectedRowKeys.value.length == 0) {
      createMessage.error(t('common.chooseDataText'));
      return;
    }
    id = selectedRowKeys.value.join(",");
    let types: any = []
    for (let i in rowSelection.value) {
      types.push(rowSelection.value[i].type)
    }
    LogType.value = types.join(",")
  } else {
    id = record.eventId
    if (record.type == '1') {
      LogType.value = "1"
    } else if (record.type == '2') {
      LogType.value = "2"
    }
  }
  LogId.value = id;
  registerRiskLogsModal.value.visible = true;
}

let addInveFlag = false
const showAddInvestigation = (id) => {
  console.log(id)
  if ('rows' == id) {
    if (selectedRowKeys.value.length > 0) {
      id = selectedRowKeys.value.join(',')
    } else {
      createMessage.error(t('common.chooseDataText'));
      return
    }
  }
  LogId.value = id;
  addInveFlag = true
  inveVisible.value = true
}

function goBack() {
  router.push({
    path: "/badactors/BadActorsViewModal"
  });
}

const [registerModal, {openModal}] = useModal();

function showAddWhite() {
  openModal(true, {
    isUpdate: false,
    showFooter: true,
  });
}
</script>
<style lang="less" scoped>
.search_transparent{
  /deep/.soc-basic-table-form-container{
    padding: 0px!important;
  }
  /deep/.searchForm{
    padding: 0px!important;
  }
}
/deep/.ant-picker {
  width:100%!important;
}
</style>
