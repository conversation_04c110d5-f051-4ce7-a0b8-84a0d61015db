<template>
  <div class="search_transparent">
    <!--引用表格-->
    <BasicTable @register="registerTable" >

      <!--操作栏-->
      <template #action="{ record }">
        <a-space size="5" class="action-border">
          <span @click="handleDetail(record)">{{ t('common.viewText') }}</span>
          <a-divider type="vertical"/>
          <a-dropdown :trigger="['click']">
            <span class="ant-dropdown-link" @click.prevent="loadInvestigation">
              {{ t('common.investigation') }}
            </span>
            <template #overlay>
              <a-menu>
                <a-menu-item key="0" @click="showAddInvestigation(record?.id)">
                  <Icon icon="ant-design:plus-outlined"/>
                  {{ t('routes.riskLogs.add') }}
                </a-menu-item>
                <a-menu-divider/>
                <a-menu-item v-for="item in investigationData" :key="item.id"
                             @click="addInvestigation(item,record?.id)">
                  <span>{{ item?.investigation }}</span>
                </a-menu-item>
                <a-menu-divider/>
                <a-menu-item key="1" @click="showMoreInvestigation(record?.id)">
                  <span>{{ t('routes.riskLogs.addMore') }}</span>
                </a-menu-item>
              </a-menu>
            </template>
          </a-dropdown>
          <a-divider type="vertical" v-if="record?.riskStatus != 3"/>
          <span @click="CloseMl(record)"
                v-if="record?.riskStatus != 3">{{ t('common.closeText') }}</span>
        </a-space>
      </template>

      <template #severity="{ text }">
        <span class="severity1" v-if="text == 1">Critical</span>
        <span class="severity2" v-if="text == 2">High</span>
        <span class="severity3" v-if="text == 3">Middle</span>
        <span class="severity4" v-if="text == 4">Low</span>
        <span class="severity4" v-if="text == 5">Information</span>
      </template>

      <template #Role="{ text,record }">
          <div style="display: flex;gap:8px;flex-direction: row">
            <div class="font12 srcIp" v-if="getRole(record.id,'src')"> Source IP</div>
            <div class="font12 dstIp" v-if="getRole(record.id,'dst')"> Destination IP</div>
          </div>
      </template>
    </BasicTable>
    <WhitelistVOModal @register="registerModal" type="2"/>
  </div>
  <RiskLogsModal ref="registerRiskLogsModal" :eventId="LogId" type="3" @success="reload"/>
  <a-modal v-model:visible="inveVisible" :title="t('common.confirm')" @ok="saveToInve"
           :maskClosable=false>
    <a-row style="padding: 0 24px;">
      <a-col :span="24">
        {{t('routes.riskLogs.addInvestigationPrompt')}}
      </a-col>
      <a-col :span="24">
        <a-form class="antd-modal-form"
                autocomplete="off" :layout="formLayout">
          <a-form-item label="Conclusion">
            <a-textarea v-model:value="conclusion"/>
          </a-form-item>
        </a-form>
      </a-col>
    </a-row>
  </a-modal>
</template>

<script lang="ts" name="AssetMlEventList" setup>
import {onMounted, ref, watch} from 'vue';
import {BasicTable} from '/@/components/Table';
import {useListPage} from '/@/hooks/system/useListPage'
import {columns, searchFormSchema} from './MlEvent.data';
import {invMlSave, list, saveOrUpdate} from './MlEvent.api';
import {useI18n} from "/@/hooks/web/useI18n";
import {formLayout} from '/@/settings/designSetting';
import {useRouter} from "vue-router";
import {useModal} from "/@/components/Modal";
import WhitelistVOModal from "/@/views/whitelist/modules/WhitelistVOModal.vue";
import {investigationList} from "/@/views/investigation/InvestigationVO.api";
import RiskLogsModal from "/@/views/riskLogs/modules/RiskLogsModal.vue";
import {useUserStore} from "/@/store/modules/user";
import {Modal} from "ant-design-vue";
import {getMlByAssetIp} from "/@/views/asset/AssetBase.api";


const router = useRouter();
const {t} = useI18n();
const mainIp = ref(router.currentRoute.value.query.ip);
const dstIPMap = ref({});
const srcIPMap = ref({});
onMounted(() => {
  getMlEventIdInfo();
})
/**
 * ML关联信息
 */
async function getMlEventIdInfo(){
 await getMlByAssetIp({ip : mainIp.value}).then((result)=>{
    result.forEach(item=>{
      dstIPMap[item.eventId] = item.dstIp;
      srcIPMap[item.eventId] = item.srcIp;
    })
    console.log('dstIPMap',dstIPMap)
    console.log('srcIPMap',srcIPMap)
  })
}



//注册table数据
const {tableContext} = useListPage({
  tableProps: {
    title: '',
    api: list,
    columns,
    canResize: false,
    formConfig: {
      baseColProps: {
        lg: 6, // ≥992px
        xl: 4, // ≥1200px
        xxl: 4, // ≥1600px
      },
      labelCol: {
        xs: 24,
        sm: 8,
        md: 24,
        lg: 24,
        xl: 24,
        xxl: 24,
      },
      labelWidth: 120,
      schemas: searchFormSchema,
      autoSubmitOnEnter: true,
      showAdvancedButton: true,
      layout: formLayout,
    },
    defSort: {
      column: 'alarmTime',
      order: 'desc',
    },
    beforeFetch: (params) => {
      let startDate = params.startDate
      if (startDate) {
        let array = startDate.split(',')
        params.alarmTime_begin = array[0]
        params.alarmTime_end = array[1]
      }
    },
    searchInfo: {
      mainIp :mainIp.value
    },
    actionColumn: {
      width: 230,
    },
  },
});

const [registerTable, {reload}, {}] = tableContext

function getRole(id,type){
  if(dstIPMap[id] && type == 'dst'){
    return true;
  }else if(srcIPMap[id] && type == 'src'){
    return true;
  }

  return false;
}
/**
 * 详情
 */
function handleDetail(record: Recordable) {
  sessionStorage.setItem("MlEventModalId", record.id)
  router.push("/mlView/modules/MlEventModal")
}


const [registerModal, {openModal}] = useModal();

function showAddWhite() {
  openModal(true, {
    isUpdate: false,
    showFooter: true,
  });
}


const investigationData = ref<any[]>([]);
const LogId = ref<string>("");//记录当前点击的日志id
const loadInvestigation = () => {
  investigationList({"pageSize": 5}).then((result) => {
    investigationData.value = result.records;
  });
}

function addInvestigation(data, id): void {
  inveId = data.id
  LogId.value = id
  inveVisible.value = true
}


const inveVisible = ref(false)
let inveId = ""
const conclusion = ref("")
const userStore = useUserStore();
const saveToInve = () => {

  if (addInveFlag) {
    addInveFlag = false
    let param = {
      eventId: LogId.value,
      conclusion: conclusion.value,
      conclusionBy: userStore.userInfo?.username,
      type: "3"
    }
    sessionStorage.setItem("addInvestigationParam", JSON.stringify(param));
    router.push({
      path: "/investigation/modules/InvestigationNewModal",
      query : {fromTab : 'tab'}
    });
    return
  }

  invMlSave({
    investigationId: inveId,
    eventId: LogId.value,
    conclusion: conclusion.value,
    conclusionBy: userStore.userInfo?.username,
    type: "3"
  }).then(() => {
    inveVisible.value = false
    conclusion.value = ""
    reload();
  })
}
const registerRiskLogsModal = ref();

function showMoreInvestigation(id) {
  LogId.value = id;
  registerRiskLogsModal.value.visible = true;
}

let addInveFlag = false
const showAddInvestigation = (id) => {
  LogId.value = id;
  addInveFlag = true
  inveVisible.value = true
}

function CloseMl(data) {
  Modal.confirm({
    title: 'Close',
    content: 'Are you sure to close?',
    okText: t('common.okText'),
    cancelText: t('common.cancelText'),
    onOk: () => {
      let params = {
        id: data.id,
        riskStatus: 3
      }
      saveOrUpdate(params, true).then(() => {
        reload()
      })
    }
  });
}

</script>
<style lang="less" scoped>

.srcIp{
  padding: 4px 8px;
  background: rgba(48, 140, 255, 0.2);
  border-radius: 4px;
  color: #308CFF;
}
.dstIp{
  padding: 4px 8px;
  background: rgba(246, 200, 77, 0.2);
  border-radius: 4px;
  color: #F6C84D;
}
.search_transparent{
  /deep/.soc-basic-table-form-container{
    padding: 0px!important;
  }
  /deep/.searchForm{
    padding: 0px!important;
  }
}
</style>
