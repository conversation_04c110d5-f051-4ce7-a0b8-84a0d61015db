<template>
  <BasicModal v-bind="$attrs" @register="registerModal" :title="title" @ok="handleSubmit" width="800px">
    <div class="padding16">
      <a-form ref="formRef" autocomplete="off" :model="form" :layout="formLayout">
        <div class="flex flex-row gap-16px">
          <div class="flex-1 flex flex-col">
            <a-form-item :label="tp('GroupName')" name="groupName" required>
              <a-input v-model:value="form.groupName" :placeholder="tp('GroupNamePlaceholder')" maxlength="255" />
            </a-form-item>
            <a-form-item :label="tp('GroupNumber')" name="groupNo">
              <a-input v-model:value="form.groupNo" :placeholder="tp('GroupNumberPlaceholder')" maxlength="128" />
            </a-form-item>
            <a-form-item :label="tp('ShortGroup')" name="groupShort">
              <a-input v-model:value="form.groupShort" :placeholder="tp('ShortGroupPlaceholder')" maxlength="255" />
            </a-form-item>
            <a-form-item :label="tp('Location')" name="groupLocation">
              <a-input v-model:value="form.groupLocation" :placeholder="tp('LocationPlaceholder')" maxlength="255" />
            </a-form-item>
            <a-form-item :label="tp('Description')" name="groupDesc" style="width: 100%;margin-bottom: 0!important;">
              <a-textarea v-model:value="form.groupDesc" style="height: 72px" maxlength="1023" />
            </a-form-item>
          </div>
          <div class="flex-1">
            <div class="font-600 fcolor1 pb-8px">{{ tp('IPSegment') }}</div>
            <div class="ax-bg-dark1 p-8px gap-8px flex flex-col h-361px overflow-y-auto">
              <div class="flex flex-row  gap-4px ipList" v-for="(item, index) in form.ipList" :key="index">
                <div class="ax-icon-button is_del" @click="delIP(index)">
                  <span class="soc ax-com-Decrease ax-icon"></span>
                </div>
                <div class="w-154px flex-shrink-0">
                  <a-form-item
                  label=""
                  :name="'startIp-' + index"
                  :rules="[
                    { required: false, validator: isValidIp, trigger: ['change', 'blur'] },
                    { validator: isValidIpRange, trigger: 'blur' },
                  ]"
                >
                  <a-input v-model:value="item.startIp" class="w-154px"/>
                </a-form-item>
                </div>
          
                
                
                <div class="pt-8px ">
                  <Icon icon="ant-design:swap-right-outlined" :size="16"> </Icon>
                </div>
                <div class="w-154px flex-shrink-0">
                  <a-form-item
                  label=""
                  :name="'endIp-' + index"
                  :rules="[
                    { required: false, validator: isValidIp, trigger: ['change', 'blur'] },
                    { validator: isValidIpRange, trigger: 'blur' },
                  ]"
                >
                  <a-input v-model:value="item.endIp"  />
                </a-form-item>
                </div>
               
              </div>
              <div>
                <a-button type="primary" size="small" ghost @click="addIPGroup">
                  <span class="soc ax-com-Add ax-icon"></span>
                  <span>{{ t('common.new') }}</span>
                </a-button>
              </div>
            </div>
          </div>
        </div>
      </a-form>
    </div>
  </BasicModal>
</template>

<script lang="ts" setup>
  import { computed, ref, unref } from 'vue';
  import { BasicModal, useModalInner } from '/@/components/Modal';
  import { AssetGroupEntity } from '../AssetGroup.data';
  import { formLayout } from '/@/settings/designSetting';
  import { useI18n } from '/@/hooks/web/useI18n';
  import { queryById, saveOrUpdate } from '/@/views/asset/AssetGroup.api';
  import { IPtoNum, validateInRange } from '/@/views/asset/IPUtils';

  const { t } = useI18n();
  function tp(name) {
    return t('routes.assetGroup.' + name);
  }
  // Emits声明
  const emit = defineEmits(['register', 'success']);
  const isUpdate = ref(true);
  const form = ref({
    ipList: [{ startIp: '', endIp: '' }]
  });
  const formRef = ref();
  //表单赋值
  const [registerModal, { setModalProps, closeModal }] = useModalInner(async (data) => {
    //重置表单

    setModalProps({
      confirmLoading: false,
      showCancelBtn: data?.showFooter,
      showOkBtn: data?.showFooter,
    });
    isUpdate.value = !!data?.isUpdate;
    form.value = { ipList: [{ startIp: '', endIp: '' }], socTenantId: data.socTenantId };
    if (data.parentId) {
      form.value.parentId = data.parentId;
    }
    if (unref(isUpdate)) {
      loadInfo(data.id);
    }
  });

  function loadInfo(id) {
    queryById({ id: id }).then((res) => {
    
      form.value = res;
    
      if(form.value.ipJson){
          form.value.ipList = JSON.parse(form.value.ipJson);
      }else{
        form.value.ipList = [{ startIp: '', endIp: '' }];
      }
      console.log(form.value)
    });
  }
  //设置标题
  const title = computed(() => (!unref(isUpdate) ? tp('Create') : t('common.editText')));

  //表单提交事件
  async function handleSubmit(v) {
    try {
      await formRef.value.validate();
      setModalProps({ confirmLoading: true });
      let values = { ...form.value };
      let ip = getIPList();
      values.ipJson = JSON.stringify(ip);
      values.personJson = JSON.stringify([]);
      //提交表单
      await saveOrUpdate(values, isUpdate.value);
      //关闭弹窗
      closeModal();
      //刷新列表
      emit('success', { isUpdate: isUpdate.value, values });
    } finally {
      setModalProps({ confirmLoading: false });
    }
  }
  function getIPList() {
    let list: any = [];
    if (form.value.ipList && form.value.ipList.length > 0) {
      form.value.ipList.forEach((item) => {
        if (item.startIp || item.endIp) {
          list.push(item);
        }
      });
    }

    return list;
  }
  function addIPGroup() {
    if (form.value.ipList) {
      form.value.ipList.push({ startIp: '', endIp: '' });
    }
  }

  function delIP(index) {
    form.value.ipList.splice(index, 1);
  }

  function isValidIp(rule, value, callback) {
    let field = rule.field.split('-');
    value = form.value.ipList[field[1]][field[0]];
    var reg =
      /^(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])$/;
    if (value && !reg.test(value)) {
      return Promise.reject('Incorrect IPV4 format');
      // return callback(new Error('Incorrect IPV4 format'))
    }
    return Promise.resolve();
  }
  async function isValidIpRange(rule, value, callback) {
    let field = rule.field.split('-');
    let data = form.value.ipList[field[1]];
    if (data.startIp && data.endIp && IPtoNum(data.startIp) > IPtoNum(data.endIp)) {
      return Promise.reject('The starting IP is greater than the ending IP');
    }
    if (!data.startIp && !data.endIp) {
      return Promise.resolve();
    }
    value = form.value.ipList[field[1]][field[0]];

    if (form.value.parentId && form.value.parentId != 0) {
      let ranges = await getParentIp();
      if (ranges.length > 0) {
        let result = validateInRange(ranges, value);
        if (!result) {
          return Promise.reject('out of upper range');
        }
        return Promise.resolve();
      }
    }
    return Promise.resolve();
  }

  async function getParentIp() {
    let ranges = [];
    if(!form.value.parentId){
      return ranges;
    }
    let res = await queryById({ id: form.value.parentId });

    if (res.ipJson) {
      let ipList = JSON.parse(res.ipJson);
      for (let ip of ipList) {
        if (!ip.startIp && ip.endIp) {
          ip.startIp = ip.endIp;
        } else if (!ip.endIp && ip.startIp) {
          ip.endIp = ip.startIp;
        }
        ranges.push([ip.startIp, ip.endIp]);
      }
    }
    return ranges;
  }
</script>

<style lang="less" scoped>
  .ipList {
    /deep/.ant-form-item {
      margin-bottom: 0 !important;
    }
  }
</style>
