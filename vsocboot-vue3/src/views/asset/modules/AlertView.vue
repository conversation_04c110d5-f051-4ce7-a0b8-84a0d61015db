<template>
  <div class="tab_view">
    <div class="font14 top_tab">
      <div @click="goRouter(index)" :class="['top_tab_div',{'active' : index == activeTab}]"
           v-for="(tab,index) in tabList">
       {{ tab }}
      </div>
    </div>
    <div class="contentH">
      <router-view v-slot="{ Component }">
        <keep-alive :include="cacheArr">
          <component :is="Component" />
        </keep-alive>

      </router-view>
    </div>
  </div>

</template>

<script lang="ts" name="AlertView" setup>
import {onActivated, onMounted, ref, watch} from "vue";
import {getMlByAssetIp} from "/@/views/asset/AssetBase.api";
import {useRouter} from "vue-router";
import {useI18n} from "/@/hooks/web/useI18n";

const {t} = useI18n();
const router = useRouter();
const activeTab = ref(0);
const mainIp = ref('');
const assetId = ref('');
let state = {};
const tabList = [
  t('routes.asset.riskEvent'),
  t('routes.asset.mLEvent'),
  t('routes.asset.suspiciousProcess'),
]
const cacheArr = ref(['Overview','AlertView','AssetRiskEventList','suspiciousProcesses-suspiciousProcesses']);
const mlEventArr = ref([]);

onActivated(()=>{
  console.log('onActivated alertView!!!!!')
})
onMounted(() => {
  const param = router.currentRoute.value.query;
  mainIp.value = param.ip;
  assetId.value = param.id;

  if (history.state.data) {
    state = JSON.parse(history.state.data);
  }
  if(param.subtab){
    activeTab.value = param.subtab;
  }else{
    activeTab.value = 0;
  }
})




function goRouter(n){
  console.log('Alert view goRouter',n)
  let path = '';
  if(n == 0){
    path = '/asset/modules/RiskEventList';
  }else if(n == 1){
    path = '/asset/modules/MlEventList';
  }else if(n == 2){
    path = '/asset/suspiciousProcesses/SuspiciousProcessesList';
  }
  console.log('goRouter view goRouter',path)
  activeTab.value = n;
  router.push({
    path: path,
    query: {ip: mainIp.value,id : assetId.value,subtab : n},
    state:{data : JSON.stringify(state)}
  });
}

</script>

<style lang="less" scoped>
.tab_view{
  flex: 1;

}
.contentH {
  height: calc(100% - 64px);
  padding: 16px;
}
.top_tab {
  display: flex;
  flex-direction: row;
  align-items: center;
  color: @font-color-default;
  font-weight: normal!important;
  .top_tab_div {
    margin: 0px 16px;
    padding:12px 0px;
    cursor: pointer;
    &.active {
      color: @font-color-white;
      border-bottom: 2px solid @primary-color;
      background: transparent;
    }
  }

}
</style>
