<template>
  <div class="padding16">
  <a-radio-group v-model:value="alarmType" >
    <a-radio-button value="1">Source</a-radio-button>
    <a-radio-button value="2">Destination</a-radio-button>
  </a-radio-group>
  <div v-show="alarmType == 1">
    <div style="height: 400px;width: 100%;" ref="chart1"></div>
  </div>
  <div v-show="alarmType == 2">
    <div style="height: 400px;width: 100%;" ref="chart2"></div>
  </div>
  </div>
</template>

<script lang="ts" setup name="asset-Correlation">
import {onMounted, Ref, ref} from "vue";
import {useECharts} from "/@/hooks/web/useECharts";
import {EChartsOption} from "echarts";
import {useRouter} from "vue-router";
const chart1 = ref<HTMLDivElement | null>(null);
const {setOptions: setOptions1} = useECharts(chart1 as Ref<HTMLDivElement>);
const chart2 = ref<HTMLDivElement | null>(null);
const {setOptions: setOptions2} = useECharts(chart2 as Ref<HTMLDivElement>);
const alarmType = ref('1');
const router = useRouter();
let ipAddress = "";
onMounted(()=>{
  const param = router.currentRoute.value.query;
  ipAddress = param.ip;
  loadChart1();
  loadChart2();
})
function loadChart1() {
  const data = {
    name: ipAddress,
    children: [
      {
        name: 'ftp',
        children: [
          {
            name: '80',
            children: [
              { name: '************', value: 721 },
              { name: '*************', value: 4294 }
            ]
          },
          {
            name: '8080',
            value: 3322,
            children: [
              { name: '**********', value: 721 },
              { name: '**********', value: 4294 }
            ]
          }
        ]
      },
      {
        name: 'ssh',
        children: [
          { name: '80', value: 8833 ,
            children: [
              { name: '*********', value: 721 },
              { name: '*********', value: 4294 }
            ]},

        ]
      },
      {
        name: 'rpcbind',
        children: [{ name: '8081', value: 4116,
          children: [
            { name: '********', value: 721 },
            { name: '********', value: 4294 }
          ] }]
      },


    ]
  };
  let option:EChartsOption = {
    tooltip: {
      trigger: 'item',
      triggerOn: 'mousemove'
    },
    series: [
      {
        type: 'tree',
        id: 0,
        name: 'tree1',
        data: [data],
        top: '10%',
        left: '8%',
        bottom: '0%',
        right: '20%',
        symbolSize: 7,
        edgeShape: 'polyline',
        edgeForkPosition: '63%',
        initialTreeDepth: 3,
        lineStyle: {
          width:1
        },
        label: {
          position: 'right',
          verticalAlign: 'middle',
          align: 'right',
          formatter: (params)=>{
            if(params.name == '*********'){
              return `{x|${params.name}}`
            }
            return  `{a|${params.name}}`

          },

          rich: {
            a: {
              backgroundColor: '#28282C',
              borderColor:'rgba(255,255,255,0.2)',
              borderWidth:1,
              padding:[6,30],
              borderRadius : 20,
            },

            x: {
              borderColor:'rgba(217, 0, 27, 1)',
              borderWidth:1,
              padding:[6,30],
              borderRadius : 20,
              backgroundColor :"#593035"
            },

          }

        },
        // labelLayout(params) {
        //   return {
        //     x: params.rect.x + 10,
        //     y: params.rect.y + 20,
        //     verticalAlign: 'middle',
        //     align: 'left'
        //   }
        // },
        leaves: {
          label: {
            position: 'right',
            verticalAlign: 'middle',
            align: 'left'
          }
        },
        emphasis: {
          focus: 'descendant'
        },
        expandAndCollapse: true,
        animationDuration: 550,
        animationDurationUpdate: 750
      }
    ]
  };

  setOptions1(option);
}
function loadChart2() {
  const data = {
    name: ipAddress,
    children: [
      {
        name: 'ftp',
        children: [
          {
            name: '80',
            children: [
              { name: '************', value: 721 },
              { name: '*************', value: 4294 }
            ]
          },
          {
            name: '8080',
            value: 3322,
            children: [
              { name: '**********', value: 721 },
              { name: '**********', value: 4294 }
            ]
          }
        ]
      },
      {
        name: 'ssh',
        children: [
          { name: '80', value: 8833 ,
            children: [
              { name: '*********', value: 721 },
              { name: '*********', value: 4294 }
            ]},

        ]
      },
      {
        name: 'rpcbind',
        children: [{ name: '8081', value: 4116,
          children: [
            { name: '********', value: 721 },
            { name: '********', value: 4294 }
          ] }]
      },


    ]
  };
  let option:EChartsOption = {
    tooltip: {
      trigger: 'item',
      triggerOn: 'mousemove'
    },
    series: [
      {
        type: 'tree',
        id: 0,
        name: 'tree1',
        data: [data],
        top: '10%',
        left: '8%',
        bottom: '2%',
        right: '20%',
        symbolSize: 7,
        edgeShape: 'polyline',
        edgeForkPosition: '63%',
        initialTreeDepth: 3,
        orient : 'RL',
        lineStyle: {
          width:1
        },
        label: {
          position: 'right',
          verticalAlign: 'middle',
          align: 'right',
          formatter: (params)=>{
            if(params.name == '*********'){
              return `{x|${params.name}}`
            }
            return  `{a|${params.name}}`

          },

          rich: {
            a: {
              backgroundColor: '#28282C',
              borderColor:'rgba(255,255,255,0.2)',
              borderWidth:1,
              padding:[6,30],
              borderRadius : 20,
            },

            x: {
              borderColor:'rgba(217, 0, 27, 1)',
              borderWidth:1,
              padding:[6,30],
              borderRadius : 20,
              backgroundColor :"#593035"
            },

          }

        },
        // labelLayout(params) {
        //   return {
        //     x: params.rect.x + 10,
        //     y: params.rect.y + 20,
        //     verticalAlign: 'middle',
        //     align: 'left'
        //   }
        // },
        leaves: {
          label: {
            position: 'left',
            verticalAlign: 'middle',
            align: 'left'
          }
        },
        emphasis: {
          focus: 'descendant'
        },
        expandAndCollapse: true,
        animationDuration: 550,
        animationDurationUpdate: 750
      }
    ]
  };

  setOptions2(option);
}
</script>

<style scoped>

</style>
