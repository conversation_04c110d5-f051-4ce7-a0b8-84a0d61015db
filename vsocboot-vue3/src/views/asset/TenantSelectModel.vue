<template>
  <a-modal v-model:visible="visible" :title="t('routes.ProposalManagement.selectTenant')" width="400px" height="600px">
    <template #footer>
      <a-button
        key="back"
        @click="handleCancel"
        style="font-size: 14px; font-variant: tabular-nums; line-height: 1.5715; list-style: none; margin-right: 8px"
        >{{ t('common.cancelText') }}</a-button
      >
      <j-upload-button key="submit" type="primary" @click="handleOk">{{ t('common.confirm') }}</j-upload-button>
    </template>

    <div class="title-item1">
      <span class="title-content1">{{ t('routes.ProposalManagement.Applytenant') }}</span>
    </div>

    <div class="tenant-list">
      <a-radio-group v-model:value="selectedTenantId">
        <div v-for="(tenant, index) in tenants" :key="index" class="tenant-item">
          <a-radio :value="tenant.id">
            <span class="tenant-text">{{ tenant.name }}</span>
          </a-radio>
        </div>
      </a-radio-group>
    </div>
  </a-modal>
</template>

<script setup lang="ts">
  import { ref } from 'vue';
  import { useI18n } from 'vue-i18n';
  import { queryTenantList, importExcel } from '/@/views/asset/AssetBase.api';
  import { useMessage } from '/@/hooks/web/useMessage';

  const { t } = useI18n();
  const emits = defineEmits(['handleSuccess']);
  const visible = ref(false);
  const selectedTenantId = ref('');
  const tenants = ref<any>([]);
  const tenantList = ref<any>([]);
  const { createMessage } = useMessage();
  const uploading = ref(false);

  const showModal = (data) => {
    queryTenantList({}).then((res) => {
      tenantList.value = res;
      tenants.value = res.map((ele) => ({
        name: ele.name,
        id: ele.id,
      }));
      if (tenants.value.length > 0) {
        selectedTenantId.value = tenants.value[0].id;
      }
      visible.value = true;
    });
  };

  const handleOk = (file) => {
    console.log('file', file);
    if (!selectedTenantId.value) {
      createMessage.warning(t('routes.ProposalManagement.selectTenantTip'));
      return;
    }
    uploading.value = true;
    importExcel(file.file, { tenant: selectedTenantId.value }, cac);
  };
  function cac(res) {
    console.log('res', res);
    if (res.success == true && res.code == 200) {
      createMessage.success(res.message);
      // 无论如何都调用刷新
      setTimeout(() => {
        emits('handleSuccess');
      }, 100);
    } else {
      createMessage.error(res.message);
    }
    visible.value = false;
  }
  const handleCancel = () => {
    visible.value = false;
  };

  defineExpose({
    showModal,
  });
</script>

<style scoped>
  .title-item {
    padding: 14px 0px 14px 0px;
    background: #18191d;
    margin-left: 16px;
    box-sizing: border-box;
    /* Font/白0.1 */
    border-width: 1px 0px 1px 0px;
    border-style: solid;
    border-color: rgba(255, 255, 255, 0.1);
  }

  .title-content {
    font-size: 13px;
    font-weight: normal;
    line-height: 20px;
    letter-spacing: 0em;
    margin-left: 8px;

    /* Font/白0.8 */
    color: rgba(255, 255, 255, 0.8);
  }

  .title-item1 {
    padding: 14px 0px 14px 0px;
    margin: 4px 0 4px 56px;
  }

  .title-content1 {
    font-size: 14px;
    font-weight: 600;
    line-height: 24px;
    letter-spacing: 0px;
  }

  .tenant-item {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
    margin-left: 56px;
  }
  .tenant-text {
    font-size: 13px;
    font-weight: normal;
    line-height: 20px;
    letter-spacing: 0em;
  }

  .tenant-list {
    margin-top: 10px;
  }
</style>
