<template>
  <a-dropdown :trigger="['click']">
    <div class="tags-wrapper" @click.prevent>
      <div v-for="(item, index) in checkedTagList" class="checked-tag">
        <div class="ellipsis">{{ item.name }}</div>
        <Icon icon="ant-design:close-outlined" :size="11" class="fcolor3 cursor" @click.stop="removeCheckedTag(index)"></Icon>
      </div>
    </div>
    <template #overlay>
      <a-menu class="overflow-scroll h-300px">
        <a-menu-item v-if="hasPermission('asset:tag')">
          <div v-if="isAdd" @click.stop="isAdd = !isAdd">
            <Icon icon="ant-design:plus-outlined" :size="12"></Icon>
            {{ t('routes.asset.newtag') }}
          </div>
          <div class="edit-tag" v-else @click.stop>
            <a-input v-model:value="tagName" :placeholder="t('common.inputText')" @keydown.stop @mousedown.stop />
            <Icon icon="ant-design:check-outlined" class="cursor" @click="doAddTag"></Icon>
            <Icon icon="ant-design:rollback-outlined" class="cursor" @click="cancelAdd"></Icon>
          </div>
        </a-menu-item>
        <a-menu-divider />
        <a-menu-item v-for="(item, index) in tagList">
          <div v-if="!item.edit" @click.stop class="flex-row tag-item">
            <div class="tagName">
              <a-checkbox v-model:checked="item.checked">
                <div class="checkLabel" :title="item.name">{{ item.name }}</div>
              </a-checkbox>
            </div>
            <div style="width: 100px" v-if="isTenantMode">
              <template v-if="item.socTenantId">{{ item?.tenantName }}-built </template>
              <template v-else>MSSP-built</template>
            </div>
            <div class="flex-row tag-btns" @click.stop v-if="hasPermission('asset:tag')">
              <Icon icon="ant-design:delete-outlined" :size="12" class="fcolor3 cursor" @click.stop="delTag(item.id)"></Icon>
              <Icon icon="ant-design:form-outlined" :size="12" class="fcolor3 cursor" @click.stop="showEditTag(item)"></Icon>
            </div>
          </div>
          <div class="edit-tag" v-else @click.stop>
            <a-input v-model:value="tagName" :placeholder="t('common.inputText')" @keydown.stop @click.stop />
            <Icon icon="ant-design:check-outlined" class="cursor" @click="doEditTag(item)"></Icon>
            <Icon icon="ant-design:rollback-outlined" class="cursor" @click="cancelEdit(item)"></Icon>
          </div>
        </a-menu-item>
      </a-menu>
    </template>
  </a-dropdown>
</template>

<script lang="ts" name="asset-AssetTag" setup>
  import { defineEmits, defineExpose, defineProps, ref, watch } from 'vue';
  import { addTag, deleteOne, editTag, queryTagList } from '/@/views/asset/Tag.api';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { usePermission } from '/@/hooks/web/usePermission';
  import { getTenantMode } from '/@/utils/auth';
  import { useI18n } from '/@/hooks/web/useI18n';

  const { t } = useI18n();
  const { createMessage } = useMessage();
  const emit = defineEmits(['update:value', 'ok']);
  const tagName = ref('');
  const socTenantId = ref('');
  const isAdd = ref(true);
  const tagList = ref([]);
  const checkedTagList = ref([]);
  const delBtnShow = ref(true);
  const isTenantMode = getTenantMode();

  const { hasPermission } = usePermission();
  const props = defineProps({
    socTenantId: String,
  });
  watch(
    () => props.socTenantId,
    (n) => {
      socTenantId.value = n;
      loadTagList();
    },
    { immediate: true }
  );

  watch(
    () => tagList.value,
    (n) => {
      checkedTagList.value = n.filter((item) => item.checked === true);
      let tagIds = checkedTagList.value.map((item) => item.id);
      emit('update:value', tagIds.toString());
    },
    { deep: true }
  );

  /**
   * bind tag
   * @param values
   */
  function bindTag(values) {
    tagList.value.forEach((item) => {
      if (-1 != values.indexOf(item.id)) {
        item.checked = true;
      }
    });
  }
  /**
   * 取消选中
   * @param index
   */
  function removeCheckedTag(index) {
    let tag = checkedTagList.value[index];
    tag.checked = false;
    for (let item in tagList.value) {
      if (tag.id == item.id) {
        item.checked = false;
        break;
      }
    }
  }

  /**
   * 获取tag列表
   */
  function loadTagList() {
    queryTagList({ tagType: 1, socTenantId: socTenantId.value }).then((result) => {
      console.log('result', result);
      let map = {};
      //防止刷新后，已选的不显示
      if (checkedTagList.value.length > 0) {
        checkedTagList.value.forEach((item) => {
          map[item.id] = true;
        });
        result.forEach((item) => {
          item.checked = map[item.id] || false;
        });
      }
      delBtnShow.value = true;
      tagList.value = result;
    });
  }

  /**
   * 删除 tag
   * @param id
   */
  function delTag(id) {
    if (delBtnShow.value) {
      delBtnShow.value = false;
      deleteOne({ id }, loadTagList);
    }
  }

  /**
   * 回显Tag
   * @param item
   */
  function showEditTag(item) {
    item.edit = true;
    tagName.value = item.name;
  }

  /**
   * 取消编辑
   * @param item
   */
  function cancelEdit(item) {
    item.edit = false;
    tagName.value = '';
  }

  /**
   * 修改标签
   */
  function doEditTag(item) {
    if (!tagName.value) {
      createMessage.warning('Please enter tag name!');
      return;
    }
    editTag({ name: tagName.value, id: item.id }).then(() => {
      tagName.value = '';
      loadTagList();
    });
  }

  /**
   * 添加标签
   */
  function doAddTag() {
    if (!tagName.value) {
      createMessage.warning('Please enter tag name!');
      return;
    }
    addTag({ name: tagName.value, tagType: 1 }).then(() => {
      tagName.value = '';
      loadTagList();
    });
  }

  /**
   * 取消
   */
  function cancelAdd() {
    tagName.value = '';
    isAdd.value = true;
  }

  defineExpose({
    bindTag,
  });
</script>

<style scoped lang="less">
  .tags-wrapper {
    // border: 1px solid @border-color;
    padding: 2px 8px;
    height: auto;
    min-height: 32px;
    border-radius: 6px;
    display: flex;
    align-items: center;
    gap: 4px;
    flex-wrap: wrap;
    background: rgba(255, 255, 255, 0.1) !important;
    .checked-tag {
      background: @dark-bg3;
      display: flex;
      flex-direction: row;
      gap: 8px;
      padding: 0px 8px;
      border-radius: 4px;
      align-items: center;
      align-content: space-between;
      justify-content: space-between;
      width: 165px;
    }
  }

  .edit-tag {
    display: flex;
    flex-direction: row;
    gap: 8px;
    align-items: center;
  }

  .flex-row {
    display: flex;
    flex-direction: row;
    align-items: center;
  }

  .tag-item {
    justify-content: space-between;
    gap: 8px;

    .tagName {
      width: 100px;
    }
    .checkLabel {
      width: 70px;
      overflow: hidden;
      text-overflow: ellipsis;
      word-break: break-all;
      white-space: nowrap;
    }

    .tag-btns {
      justify-content: flex-end;
      gap: 8px;
    }
  }
  .ellipsis {
    max-width: 165px;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    word-break: keep-all;
  }
  :deep(.ant-dropdown-content) {
    height: 300px;
    overflow: scroll;
  }
</style>
