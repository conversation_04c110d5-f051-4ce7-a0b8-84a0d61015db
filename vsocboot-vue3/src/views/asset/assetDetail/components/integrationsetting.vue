<template>
  <BasicDrawer v-bind="$attrs" @register="registerModal" :title="title" width="1200px" :destroyOnClose="true" @close="close">
    <div style="display: flex; padding-bottom: 24px">
      <div class="img_div" v-if="integrationId">
        <img style="width: 72px; height: 72px" :src="render.renderUploadImageSrc(dataInfo.logo)" alt="" />
      </div>
      <div class="img_div" v-if="!integrationId">
        <img style="width: 72px; height: 72px" src="../../../../assets/images/asset/noIntegration.png" alt="" />
      </div>
      <div style="width: 900px">
        <div class="ft12 f-color-06 text_ellipsis" style="width: 520px">
          {{ dataInfo.integrationDesc }}
        </div>
        <div style="display: flex; margin-top: 8px; gap: 24px">
          <div class="text_div ft12 f-color-06 text_ellipsis">
            {{ tp('serialNumber') }} :
            {{ dataInfo?.serialNumber }}
          </div>
          <div v-if="isAdministrator()" class="text_div ft12 f-color-06 text_ellipsis">
            {{ tp('tenant') }} : {{ dataInfo?.socTenantId_dictText }}
          </div>
          <div class="text_div ft12 f-color-06 text_ellipsis">
            {{ tp('publishUser') }} :
            <span v-if="dataInfo?.tenantType == 1 && isTenant()">MSSP</span>
            <span v-else>{{ dataInfo?.createBy }}</span>
          </div>
          <div class="text_div ft12 f-color-06 text_ellipsis">
            {{ tp('versions') }} :
            {{ dataInfo?.versions }}
          </div>
          <!-- <div class="text_div ft12 f-color-06 text_ellipsis" style="max-width: 30%">
            {{ tp('plugin') }} :
            <template v-if="dataInfo?.id"> {{ dataInfo?.pluginName }}({{ dataInfo?.pluginVersion }}) </template>
            <template v-else>
              {{ tp('none') }}
            </template>
          </div> -->
        </div>
      </div>

      <a-divider type="vertical" style="height: 72px" />
      <div style="margin-top: 8px">
        <a-tooltip v-model:visible="visible" placement="left" trigger="click">
          <template #title>
            <div style="display: flex; flex-direction: column; gap: 8px; cursor: pointer">
              <span type="text" @click="changeIntegration" style="cursor: pointer" v-if="integrationId != null">{{
                t('routes.assetBase.changeintegration')
              }}</span>
              <span type="text" @click="delIntegration" style="cursor: pointer" v-if="integrationId != null">{{
                t('routes.assetBase.deleteintegration')
              }}</span>
              <span type="text" @click="addIntegration" style="cursor: pointer" v-if="integrationId == null">{{
                t('routes.assetBase.addintegration')
              }}</span>
            </div>
          </template>
          <div style="padding: 16px; margin-left: 10px">
            <a-button type="primary">{{ t('common.operation') }}</a-button>
          </div>
        </a-tooltip>
      </div>
    </div>

    <div>
      <BasicTable @register="registerTable" :isSearch="false" />
    </div>
  </BasicDrawer>
  <changeintegration
    v-if="loadFlag"
    @register="register"
    :id="id"
    :socTenantId="socTenantId"
    :integrationId="integrationId"
    @reload="reload"
    @load="load"
  />
</template>

<script lang="ts" name="integrationManagement-integrationManagement" setup>
  import { ref } from 'vue';
  import { BasicDrawer, useDrawer, useDrawerInner } from '/@/components/Drawer';
  import { useI18n } from '/@/hooks/web/useI18n';
  import { render } from '/@/utils/common/renderUtils';
  import { isAdministrator, isTenant } from '/@/utils/auth';
  import { queryById } from '/@/views/integrationManagement/modules/IntegrationInit.api';
  import { BasicTable } from '/@/components/Table';
  import { useListPage } from '/@/hooks/system/useListPage';
  import { columns } from '../record/record.data';
  import { list } from '../record/record.api';
  import changeintegration from './changeintegration.vue';
  import { deleteIntegration } from '/@/views/asset/assetDetail/record/record.api';

  const props = defineProps({
    id: String as any,
    socTenantId: String as any,
  });
  const { t } = useI18n();

  function tp(name) {
    return t('integration.IntegrationManagement.' + name);
  }
  const visible = ref<boolean>(false);
  const emits = defineEmits(['closeDrawer', 'loadData']);
  const title = t('routes.assetBase.integrationsetting');

  //数据
  const dataInfo = ref<any>({});
  const integrationId = ref();
  const loadFlag = ref(false);
  //表单赋值
  const [registerModal, { setDrawerProps, closeDrawer }] = useDrawerInner(async (data) => {
    console.log(data);
    dataInfo.value = {};
    integrationId.value = data.integrationId;
    setDrawerProps({
      confirmLoading: false,
      showFooter: false,
    });
    loadFlag.value = true;
    if (data.integrationId) {
      loadInfo();
    }
  });

  function loadInfo() {
    queryById({ id: integrationId.value }).then((data) => {
      console.log(data);
      dataInfo.value = data;
    });
  }

  function close() {
    dataInfo.value = {};
    integrationId.value = '';
    loadFlag.value = false;
    closeDrawer();
  }

  function load(e) {
    if (e) {
      integrationId.value = e;
      loadInfo();
    } else {
      integrationId.value = null;
      dataInfo.value = {};
    }
    emits('loadData', props.id);
    reload();
  }

  const [register, { openDrawer }] = useDrawer();
  //注册table数据
  const { tableContext } = useListPage({
    tableProps: {
      api: list,
      columns,
      rowKey: 'id',
      canResize: false,
      showTableSetting: false,
      useSearchForm: false,
      showActionColumn: false,
      beforeFetch: (params) => {
        params.assetId = props.id;
      },
    },
  });

  const [registerTable, { reload }] = tableContext;

  function changeIntegration() {
    visible.value = false;
    openDrawer(true, {
      isUpdate: true,
      showFooter: false,
      nointegration: false,
    });
  }

  async function delIntegration() {
    visible.value = false;
    await deleteIntegration({ id: props.id }, load);
  }

  function addIntegration() {
    visible.value = false;
    openDrawer(true, {
      isUpdate: true,
      showFooter: false,
      nointegration: true,
    });
  }
</script>
<style scoped lang="less">
  .content_wraper {
    display: flex;
    flex-direction: column;
    align-items: center;

    .boxcon {
      border: none;
      width: 1200px;
      height: auto;
      padding: 12px;

      .left-half {
        width: 55%;
        float: left;
        display: flex;
        flex-wrap: wrap;
        flex-direction: row;
        column-gap: 20px;
      }

      .cloud-leftwidth {
        width: 70%;
        float: left;
        display: flex;
        flex-wrap: wrap;
        flex-direction: row;
        column-gap: 20px;
      }

      .right-half {
        width: 45%;
        float: right;
      }

      .cloud-rightwidth {
        width: 30%;
        float: right;
      }
    }
  }

  :deep(.ant-tabs-content) {
    background-color: @dark-bg1 !important;
  }

  .img_div {
    flex: 0 0 72px;
    // display: flex;
    margin-left: 20px;
    margin-right: 16px;
    justify-content: center;
    align-items: center;
  }

  .name_div {
    margin-right: 20px;
    color: #fff;
    font-weight: bold;
    flex: 0 0 20%;
    font-size: 18px;
  }

  .text_div {
    max-width: 18%;
  }

  .number_div {
    padding: 4px 8px;
    border-radius: 4px;
  }

  .border_bottom {
    border-bottom: 1px solid @border-color;
  }

  :deep(.ant-tabs-content) {
    background-color: @dark-bg1 !important;
  }
</style>
