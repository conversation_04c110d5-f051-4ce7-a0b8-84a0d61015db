import { BasicColumn } from '/@/components/Table';
import {FormSchema} from '/@/components/Table';
import {isAdministrator} from "/@/utils/auth";
import {useI18n} from "/@/hooks/web/useI18n";
import { typeOptions } from '/@/views/integrationManagement/IntegrationManagement.data';
import {ASSET_STATE, ASSET_TYPE} from "/@/views/asset/AssetBase.option";
const {t} = useI18n();
export const sourceOptions = [
  {
    label: 'platform',
    value: 0,
  },
  {
    label: 'customization',
    value: 1,
  },
];
export const useColumns = (): BasicColumn[] => {
  const colums: BasicColumn[] = [
    {
      title: t('routes.assetBase.asssetName'),
      dataIndex: 'assetName',
      width: 150,
    },
    {
      title: t('routes.assetBase.os'),
      dataIndex: 'os',
      width: 150,
    },
    {
      title: t('routes.assetBase.VMIP'),
      dataIndex: 'ipv4',
      width: 150,
    },
    {
      title: t('routes.assetBase.integration'),
      dataIndex: 'integrationName',
      width: 150,
    },
    
    {
      title: t('routes.assetBase.AssetLevel'),
      dataIndex: 'assetLevel',
      width: 150,
      slots: {customRender: 'level'},
    },
    {
      title: t('routes.assetBase.logSource'),
      dataIndex: 'ifLogSource',
      width: 150,
      customRender: ({text}) => {
        if (text == 1) {
          return 'Yes';
        } else if (text == 2) {
          return 'No';
        }
      },
    },
    {
      title: t('routes.assetBase.onlinestatus'),
      dataIndex: 'assetState',
      width: 150,
      slots: {customRender: 'onlineState'},
    },
    {
      title: t('routes.assetBase.safestatus'),
      dataIndex: 'assetSafe',
      width: 150,
      slots: {customRender: 'safeState'},
    },
  ];
  return colums;
};

export const searchFormSchema: FormSchema[] = [
  {
    label: t('common.tenantName'),
    field: 'socTenantId',
    component: 'JSearchSelect',
    componentProps: {
      dict: "tenantDict"
    },
    ifShow: isAdministrator()
  },
];


export const assetConfig = {
  column: useColumns(),
  searchForm: searchFormSchema
}
