<template>
  <div>
    <!--引用表格-->
    <BasicTable @register="registerTable" :rowSelection="rowSelection" :isSearch="isSearch" class="auto-min-height">
      <!--插槽:table标题-->
      <template #form-formFooter>
        <!-- <a-button  :class="{'btn-checked': isSearch == true}" type="text" @click="isSearch=!isSearch" preIcon="ant-design:filter-outlined"> {{ t('common.filter') }}</a-button> -->
        <!--
            <a-button type="primary" @click="handleAdd" preIcon="ant-design:plus-outlined"> Expand View</a-button>
-->
      </template>
      <template #services="{ text }">
        <div class="flex-wrap" v-if="text">
          <div :key="item" :class="[`service-${getStr(item)[1]}`, 'service']" v-for="item in text.split(',')">{{ getStr(item)[0] }}</div>
        </div>
      </template>
      <template #ports="{ text }">
        <div class="flex-wrap">
          <div class="safe">Safe : {{ getPorts(1, text) }}</div>
          <div class="risk">Risk : {{ getPorts(2, text) }}</div>
          <div class="danger">Very high risk : {{ getPorts(3, text) }}</div>
        </div>
      </template>
      <!--操作栏-->
      <template #action="{ record }">
        <TableAction :actions="getTableAction(record)" />
      </template>
    </BasicTable>

    <!-- 表单区域 -->

    <UnaddedAssetDetail @register="registerModal"></UnaddedAssetDetail>
    <addDrawer @register="registerDrawer" @success="handleSuccess" />
  </div>
</template>

<script lang="ts" name="asset-assetDiscovery" setup>
  import { ref, computed, unref, provide } from 'vue';
  import { BasicTable, useTable, TableAction } from '/@/components/Table';
  import { useModal } from '/@/components/Modal';
  import { useListPage } from '/@/hooks/system/useListPage';
  import UnaddedAssetDetail from '../modules/UnaddedAssetDetail.vue';
  import { columns, searchFormSchema } from '../AssetDiscovery.data';
  import { list, deleteOne, batchDelete, getImportUrl, getExportUrl } from '../AssetDiscovery.api';
  import { useI18n } from '/@/hooks/web/useI18n';
  import { formLayout } from '/@/settings/designSetting';
  import { Modal } from 'ant-design-vue';
  import { defHttp } from '/@/utils/http/axios';
  import { useRouter } from 'vue-router';
  import { TABLE_CACHE_KEY } from '/@/utils/valueEnum';
  import { useDrawer } from '/@/components/Drawer';
  import addDrawer from '../assetAddDrawers/addDrawer.vue';
  const router = useRouter();
  const { t } = useI18n();
  const isSearch = ref<boolean>(true);
  //注册model
  const [registerModal, { openModal }] = useModal();
  //注册table数据
  const { prefixCls, tableContext } = useListPage({
    tableProps: {
      title: 'dicovery assets',
      api: list,
      columns,
      canResize: false,
      formConfig: {
        labelWidth: 120,
        schemas: searchFormSchema,
        autoSubmitOnEnter: true,
        showAdvancedButton: true,
        layout: formLayout,
      },
      actionColumn: {
        width: 180,
      },
      tableSetting: {
        cacheKey: TABLE_CACHE_KEY.unaddedAsset,
      },
    },
  });

  const [registerTable, { reload }, { rowSelection, selectedRowKeys }] = tableContext;

  const [registerDrawer, { openDrawer }] = useDrawer();
  provide('handleAddDiscovery', handleAddDiscovery);
  /**
   * 新增事件
   */
  function handleAdd(record: Recordable) {
    openDrawer(true, {
      isUpdate: false,
      showFooter: false,
      ipv4: record.ipAddress,
      assetSource: 3,
    });
  }

  function handleAddDiscovery(type) {
    openDrawer(true, {
      isUpdate: false,
      showFooter: false,
      relatedIntegrationType: type,
      assetSource: 3,
    });
  }

  /**
   * 详情
   */
  function handleDetail(record: Recordable) {
    openModal(true, {
      record,
    });
  }
  /**
   * 删除事件
   */
  async function handleIgnore(record) {
    Modal.confirm({
      title: t('routes.asset.ignore'),
      content: t('routes.asset.ps'),
      okText: t('common.okText'),
      cancelText: t('common.cancelText'),
      wrapClassName: 'delete_confirm',
      onOk: () => {
        reload();
      },
    });
  }

  /**
   * 成功回调
   */
  function handleSuccess({ isUpdate, values }) {
    reload();
  }
  /**
   * 操作栏
   */
  function getTableAction(record) {
    return [
      {
        label: t('common.view'),
        onClick: handleDetail.bind(null, record),
      },
      {
        label: t('common.add'),
        onClick: handleAdd.bind(null, record),
      },
      {
        label: t('common.ignore'),
        onClick: handleIgnore.bind(null, record),
      },
    ];
  }
  function getPorts(type, value) {
    if (value) {
      let arr = value.split(',');
      let data = arr.filter((item) => {
        if (item.split('-')[0] == type) {
          return item;
        }
      });
      if (data.length > 0) {
        return data[0].split('-')[1];
      }
    }
    return 0;
  }
  // 截取字符串
  function getStr(str) {
    const lastIndex = str.lastIndexOf('-');
    let result: any = [];
    if (lastIndex !== -1) {
      const part1 = str.slice(0, lastIndex); // "microsoft-ds"
      const part2 = str.slice(lastIndex + 1); // "1"
      result = result.concat(part1, part2);
    } else {
      result = result.concat('', '');
    }
    return result;
  }
</script>
<style scoped>
  .flex-wrap {
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    gap: 8px;
  }
  .service {
    color: #fff;
    padding: 4px 8px;
    border-radius: 4px;
  }
  /*普通*/
  .service-1 {
    background-color: rgb(#63a103, 0.1);
    border: 1px solid #63a103;
  }
  /*敏感*/
  .service-2 {
    background-color: rgb(#f59a23, 0.1);
    border: 1px solid #f59a23;
  }
  /*高危*/
  .service-3 {
    background-color: rgb(#d9001b, 0.1);
    border: 1px solid #d9001b;
  }
  .safe {
    color: #63a103;
  }
  .risk {
    color: #f59a23;
  }
  .danger {
    color: #d9001b;
  }
</style>
