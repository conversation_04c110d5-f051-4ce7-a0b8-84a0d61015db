import {defHttp} from '/@/utils/http/axios';
import {Modal} from 'ant-design-vue';
import {useI18n} from "/@/hooks/web/useI18n";
const {t} = useI18n();
enum Api {
  list = '/asset/assetPolicyManagement/list',
  save='/asset/assetPolicyManagement/add',
  edit='/asset/assetPolicyManagement/edit',
  queryById = '/asset/assetPolicyManagement/queryById',
  deleteOne = '/asset/assetPolicyManagement/delete',
  deleteBatch = '/asset/assetPolicyManagement/deleteBatch',
  queryDetailList = '/asset/assetPolicyManagement/queryDetailList',
  getList = '/asset/assetPolicyManagement/getList',
  queryList = '/asset/assetScan/queryList',
}
/**
 * 列表接口
 * @param params
 */
export const list = (params) =>
  defHttp.get({url: Api.list, params});
/**
 * 查询所有接口
 * @param params
 */
export const getList = (params) =>
  defHttp.get({url: Api.getList, params});
/**
 * 查询所有接口
 * @param params
 */
export const queryList = (params) =>
  defHttp.get({url: Api.queryList, params});
/**
 * 详情列表接口
 * @param params
 */
export const queryDetailList = (params) =>
  defHttp.get({url: Api.queryDetailList, params});
/**
 * 列表接口
 * @param params
 */
export const queryById = (params) =>
  defHttp.get({url: Api.queryById, params});

/**
 * 删除单个
 * @param params
 * @param handleSuccess
 */
export const deleteOne = (params,handleSuccess) => {
  Modal.confirm({
    title: t('common.delText'),
    content: t('common.delConfirmText'),
    okText: t('common.delText'),
    cancelText: t('common.cancelText'),
    wrapClassName: 'delete_confirm',
    onOk: () => {
      return defHttp.delete({url: Api.deleteOne, params}, {joinParamsToUrl: true}).then(() => {
        handleSuccess();
      });
    }
  });
}
/**
 * 批量删除
 * @param params
 * @param handleSuccess
 */
export const batchDelete = (params, handleSuccess) => {
  Modal.confirm({
    title: t('common.delConfirmText'),
    content: t('common.delContent'),
    okText: t('common.delText'),
    cancelText: t('common.cancelText'),
    wrapClassName: 'delete_confirm',
    onOk: () => {
      return defHttp.delete({url: Api.deleteBatch, data: params}, {joinParamsToUrl: true}).then(() => {
        handleSuccess();
      });
    }
  });
}
/**
 * 保存或者更新
 * @param params
 * @param isUpdate 是否是更新数据
 */
export const saveOrUpdate = (params, isUpdate) => {
  let url = isUpdate ? Api.edit : Api.save;
  return defHttp.post({url: url, params});
}
