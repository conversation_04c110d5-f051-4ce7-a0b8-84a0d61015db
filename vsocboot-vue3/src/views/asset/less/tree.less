

.tree {
  /deep/ .ant-tree-treenode {
    width: 100%;
    display: flex;
    padding: 4px 0 4px 16px;
    &:hover {
      background: rgba(255, 255, 255, 0.1);
      border-radius: 4px;
    }
  }

  /deep/ .ant-tree-treenode-selected {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 4px;
  }

  /deep/ .ant-tree-node-content-wrapper {
    flex: 1;
    align-items: center;
    color: @font-color-default;

    font-size: 12px;
    font-weight: normal;
    &:hover {
      background: transparent !important;
    }

    &.ant-tree-node-selected {
      background-color: transparent;
    }

    &.ant-tree-node-selected:after {
      position: absolute;
      background: red;
      left: 0px;
      height: 32px;
      width: 100%
    }
  }

  /deep/ .ant-tree-switcher {
    width: 16px;
  }
}
