<!--
 * @Author: Delavin.TnT
 * @LastEditors: Delevin.TnT
 * @Date: 2021-07-06 14:18:14
 * @LastEditTime: 2023-08-11 14:18:42
-->
<template>
  <div class="container">
    <template v-for="(item, key) in filterTitle" :key="key">
      <div class="cardFilter" :class="{ active: actived == key }" @click="toggleFilter(key)">
        <div class="content_text">
          <div class="value">{{ item }} {{ filterValue[`${key}_count`] }}</div>
        </div>
      </div>
    </template>
  </div>
</template>

<script lang="ts" setup>
import {reactive} from 'vue';
import {agentStatistic} from "/@/views/asset/agentHost/AgentHost.api";

const props = defineProps({
  actived: {
    type: String,
    required: true,
  },
  id: String,
  osver: Number,
  filterTitle: {
    type: Object,
    required: true,
  }
});
const emit = defineEmits(['update:actived']);


//节点值仓
const filterValue = reactive({
  process_count: 0,
  port_count: 0,
  account_count: 0,
  service_count: 0,
  kernel_count: 0,
  environment_count: 0,
});
//切换节点
const toggleFilter = (key: string): void => {
  emit('update:actived', key);
};
//获取值
const getStatic = async () => {
  if (!props.id || !props.osver) {
    return
  }
  const {body} = await agentStatistic({agentId: props.id, osver: props.osver});
  console.log(body)
  for (let k in filterValue) {
    filterValue[k] = body[k] || 0;
  }
};
getStatic();

defineExpose({
  getStatic
})

</script>

<style lang="less" scoped>
.container {
  margin: 0px 32px;
  display: flex;
  justify-content: flex-start;
  overflow: auto;
}

.cardFilter {
  padding: 10px;
  background: @dark-bg1;
  border-radius: 8px 8px 0px 0px;
  display: flex;
  justify-content: flex-start;
  margin-right: 10px;
  cursor: pointer;
  color: @font-color-1;
}

.line {
  width: 6px;
  height: 20px;
  border-radius: 8px;
  margin-right: 8px;
  background: #f8f8f8;
}

.active {
  background: @dark-bg2;
  color: @font-color-white;

  .value {
    font-weight: 600;
  }
}

.content_text {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  margin-top: -3px;

  .value {

    font-size: 14px;
    line-height: 24px;
    text-align: center;
    letter-spacing: 0px;
  }


}
</style>
