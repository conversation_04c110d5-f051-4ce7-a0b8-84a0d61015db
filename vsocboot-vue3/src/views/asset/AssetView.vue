<template>
  <div class="top-wrapper ">
    <div class="top-wrapper_left">
      <div class="top-wrapper_left_content">
        <Icon icon="ant-design:left-outlined" :size="20" class="cursor" @click="closeRetun">
        </Icon>
        <div :class="fontClass">  </div>
        <div>
          <div class="font14 fcolor">{{ form?.mainIp }}</div>
          <div class="font12 fcolor3">{{ form?.assetName }}</div>
        </div>
      </div>

      <div class="font16 top_tab">
        <div :class="['top_tab_div',{'active' : 0 == activeTab}]"  @click="goRouter(0)"> {{t('routes.asset.overview')}}</div>
        <div :class="['top_tab_div',{'active' : 1 == activeTab}]"  @click="goRouter(1)" v-if="form?.agentId">{{t('routes.asset.assetHost')}} </div>
<!--        <div :class="['top_tab_div',{'active' : 2 == activeTab}]"  @click="goRouter(2)"> Vulnerability</div>-->
        <div :class="['top_tab_div',{'active' : 3 == activeTab}]"  @click="goRouter(3)" v-if="form?.mainIp"> {{t('routes.asset.alert')}}</div>
      </div>
    </div>
    <div class="top-wrapper_right" v-if="!!form?.agentId">
      <template v-if="form?.online == 2">
        <div class="flex-column">
          <div class="font12 fcolor3 online">Offline Time</div>
          <div class="font13 fcolor1">2024.01.11 23:13:44</div>
        </div>
        <div class="split"></div>
      </template>
      <template v-if="form?.online == 1">
        <div class="flex-column">
          <div class="font12 fcolor3 online online_green"></div>
        </div>
        <div class="split"></div>
      </template>

      <div class="flex-row">
        <div class="fresh">
          <img :src="fresh" class="cursor" :class="{disabled:form?.online != 1 || refLoading}"
               :title="refLoading?'updating':''"
               @click="renewData"/>
        </div>
        <div class="flex-column" v-if="form?.infoTime">
          <div class="font12 fcolor3">Update Time</div>
          <div class="font13 fcolor1">{{form?.infoTime}}</div>
        </div>
      </div>

    </div>
  </div>
  <div class="contentH">

    <div v-if="activeTab==1" style="width: 100%">
      <AgentHost :agentId="form?.agentId as string" ref="agentHostRef"
                 :osver="form?.assetType==='Host-Windows' ? 1 : 2"/>
    </div>
    <div v-else style="width: 100%">
      <keep-alive :include="cacheArr"  exclude="redirect">
        <router-view v-if="fromPage == 'integration'" ></router-view>
        <router-view v-else></router-view>
      </keep-alive>
    </div>

  </div>


</template>

<script lang="ts" name="asset-assetBaseEdit" setup>
import {onMounted, onUnmounted, ref,defineProps} from 'vue';
import {useRouter} from "vue-router";

import {AssetBaseEntity, getFontClass} from "/@/views/asset/AssetBase.option";
import {
  agentAssetUpdate,
  loadAgentSimple,
  loadInfoTimeData,
  queryById
} from "/@/views/asset/AssetBase.api";
import AgentHost from "/@/views/asset/agentHost/AgentHost.vue";
import fresh from '/@/assets/images/asset/fresh.png';
import {useI18n} from "/@/hooks/web/useI18n";

const {t} = useI18n();
const router = useRouter();
const form = ref<AssetBaseEntity>();
const activeTab = ref(0);
const subChildClick = ref(false);
const fontClass = ref('');
let state = {};
let agentId = ""

const props = defineProps({
  assetId: {
    type: String,
    required: true
  },
  fromPage: String,
})
const cacheArr = ref(['Overview','AlertView','AssetRiskEventList','suspiciousProcesses-suspiciousProcesses']);
onMounted(() => {

  if (history.state.data) {
    state = JSON.parse(history.state.data);
    console.log('state-------------',state)
  }
  const param = router.currentRoute.value.query;

  if(param.subtab){
    subChildClick.value = true;
    activeTab.value = 3;
  }
  //集成查看
  if (props.fromPage == 'integration') {
    queryAsset(props.assetId, 0);
  } else {
    queryAsset(param.id, param.tab);
  }


})


/**
 * 路由跳转
 * @param n
 */
function goRouter(n){
  activeTab.value = n;
  let path = '';
  let query = {ip: form.value?.mainIp,id : form.value.id};
  if(n == 0){
    path = '/asset/modules/Overview';
    query.tab = 0;
  }else if(n == 3){
    path = '/asset/modules/Alert';
    query.subtab = 0;
  }else if(n == 2){
    query.tab = 2;
    path = '/asset/modules/Vulnerability';
  }else if(n == 1){
    query.tab = 1;
    path = '/asset/agentHost/AgentHost';
    return

  }

  router.push({
    path: path,
    query: query,
    state:{data : JSON.stringify(state)}
  });
}
function queryAsset(id,tab) {
  queryById({id}).then((data) => {
    console.log('queryAsset result-->', data);
    agentId = data.agentId
    form.value = data;
    loadInfoTime()
    if (data.assetType) {
      form.value.assetTypeArr = data.assetType.split("-");
    }
    fontClass.value = 'fontImg ' + getFontClass(form.value.assetType);
    if (data.mainIp) {
      let obj = JSON.parse(data.mainIp)
      if (obj?.ipv4) {
        form.value.mainIp = obj?.ipv4;
      } else if (obj?.ipv6) {
        form.value.mainIp = obj?.ipv6;
      } else if (obj?.mac) {
        form.value.mainIp = obj?.mac;
      }
    }




  })
}

function loadInfoTime() {
  let osver = 2
  if(form.value && agentId){
    if (form.value?.assetType === "Host-Windows") {
      osver = 1
    } else {
      loadInfoTimeData({agentId: agentId, osver: 2}).then(({body}) => {
        console.log('loadInfoTime result-->', body);
        form.value.infoTime = body.info_time;
      })
    }
    loadAgentSimple({agentId: agentId, osver: osver}).then(({body}) => {
      console.log('loadInfoTime result-->', body);
      form.value.online = body.online;
      form.value.lastOfflineAt = body.last_offline_at;
    })
  }

}

const agentHostRef = ref()
const refLoading = ref(false)
function renewData() {
  console.log("刷新页面")
  if (form.value?.online == '1' && !refLoading.value) {
    refLoading.value = true
    if (form.value?.assetType === "Host-Windows") {
      //刷新页面
      loadInfoTime()
      console.log(agentHostRef.value)
      if (agentHostRef.value) {
        agentHostRef.value.refData()
      }
      refLoading.value = false
    } else {
      refresh()
    }
  }
}

let timer: any = null;
const refresh = async () => {
  //判断是否可以进入轮询
  const {code} = await agentAssetUpdate({type: 1, mac: form.value?.agentId});
  //可以轮询
  if (!code) {
    timer = setInterval(async () => {
      const {is_success, code} = await agentAssetUpdate({type: 2, mac: form.value?.agentId});
      if (code) {
        clearInterval(timer);
      }
      if (is_success === 1) {
        refLoading.value = false
        loadInfoTime()
        clearInterval(timer);
      }
    }, 5000);
  }
};

onUnmounted(() => {
  clearInterval(timer);
});


/**
 * 返回
 */
function closeRetun() {

  const param = router.currentRoute.value.query;
  if(param?.page == '1'){
    router.push({
      path: '/integrationManagement/IntegrationManagementList',
    });
    return
  }
  router.push({
    path: '/asset/AssetBaseList',
    state:{data : JSON.stringify(state)}
  });

}
</script>
<style lang="less" scoped>
.top-wrapper {
  display: flex;
  padding: 0px 16px;
  flex-direction: row;
  align-items: center;
  height: 64px;
  justify-content: space-between;
  border-bottom: 1px solid @border-color;

  .top-wrapper_left {
    display: flex;
    flex-direction: row;
    align-items: center;
    gap: 0px;

    .top-wrapper_left_content {
      display: flex;
      flex-direction: row;
      width: 252px;
      align-items: center;
      gap: 8px;
    }

    img {
      height: 40px;
      width: 40px;

    }
  }

  .top-wrapper_right {
    display: flex;
    flex-direction: row;
    align-items: center;
    gap: 24px;

    .split {
      border-right: 1px solid @border-color;
      width: 1px;
      height: 40px;

    }
  }
}

.mb-8 {
  margin-bottom: 8px;
}
.fontImg{
  font-size: 40px;
  color: @primary-color;
}
.top_tab {
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 48px;
  height: 64px;
  color: @font-color-default;

  .top_tab_div {
    height: 64px;
    line-height: 64px;
    cursor: pointer;

    &.active {
      color: @font-color-white;
      border-bottom: 2px solid @primary-color;
      background: transparent;
    }
  }

}

.flex-row {
  display: flex;
  flex-direction: row;
  gap: 16px;
  align-items: center;
}

.flex-column {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.fresh {
  img {
    width: 20px;
    height: 20px;
  }
}

.contentH {
  height: calc(100% - 64px);
  display: flex;
}
.online{
  position: relative;
  left: 16px;
  &:before{
    content: '';
    position: absolute;
    width:8px;
    height: 8px;
    border-radius: 50%;
    background: @font-color-1;
    z-index: 1;
    top:50%;
    margin-top: -4px;
    left: -16px;
  }
}

.online_green{
  &:before{
    background: rgb(24, 186, 121)
  }
}

.disabled {
  cursor: no-drop;
}
</style>
