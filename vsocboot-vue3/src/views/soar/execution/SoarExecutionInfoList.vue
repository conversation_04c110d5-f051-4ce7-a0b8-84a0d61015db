<template>
  <div class='mt-[-16px]'>
    <!--引用表格-->
    <BasicTable @register="registerTable" :rowSelection="rowSelection" :isSearch="isSearch">
      <!--插槽:table标题-->
      <template #form-formFooter>
        <a-dropdown v-if="selectedRowKeys.length > 0">
          <template #overlay>
            <a-menu>
              <a-menu-item key="1" @click="batchHandleDelete">
                <Icon icon="ant-design:delete-outlined"/>
                {{ t('common.delText') }}
              </a-menu-item>
            </a-menu>
          </template>
          <a-button>{{ t('common.batch') }}
            <Icon icon="mdi:chevron-down"/>
          </a-button>
        </a-dropdown>
        <!-- <a-button :class="{'btn-checked': isSearch == true}" type="text" @click="isSearch=!isSearch"
                  preIcon="ant-design:filter-outlined"> {{ t('common.filter') }}
        </a-button> -->
      </template>
      <!--操作栏-->
      <template #action="{ record }">
        <TableAction :actions="getTableAction(record)"/>
      </template>
    </BasicTable>
  </div>
</template>

<script lang="ts" name="soarExecutionInfo-soarExecutionInfo" setup>
import {ref} from 'vue';
import {BasicTable, TableAction} from '/@/components/Table';
import {useListPage} from '/@/hooks/system/useListPage'
import {columns, searchFormSchema} from './SoarExecutionInfo.data';
import {executionList, deleteOne, batchDelete} from './SoarExecutionInfo.api';
import {useI18n} from "/@/hooks/web/useI18n";
import {formLayout} from '/@/settings/designSetting';
import {useRouter} from "vue-router";
import {TABLE_CACHE_KEY} from "/@/utils/valueEnum";

const router = useRouter()

const {t} = useI18n();
const isSearch = ref<boolean>(true);
//注册table数据
const {tableContext} = useListPage({
  tableProps: {
    api: executionList,
    rowKey: "executionId",
    columns,
    canResize: false,
    defSort: {
      column: "startedat",
      order: 'desc'
    },
    tableSetting: {
      cacheKey: TABLE_CACHE_KEY.workflowExecution
    },
    formConfig: {
      labelWidth: 120,
      schemas: searchFormSchema,
      autoSubmitOnEnter: true,
      showAdvancedButton: true,
      layout: formLayout,
    },
    actionColumn: {
      width: 150,
    },
  },
})

const [registerTable, {reload}, {rowSelection, selectedRowKeys}] = tableContext

/**
 * 详情
 */
function handleDetail(record: Recordable) {
  sessionStorage.setItem("soar.workflowId", record.workflowId);
  sessionStorage.setItem("soar.workflowTab", "execution");
  sessionStorage.setItem("soar.executionId", record.executionId);
  router.push("/soar/workflow/WorkflowIndex")
}

/**
 * 删除事件
 */
async function handleDelete(record) {
  await deleteOne({id: record.executionId}, reload);
}

/**
 * 批量删除事件
 */
async function batchHandleDelete() {
  await batchDelete({ids: selectedRowKeys.value}, reload);
}

/**
 * 操作栏
 */
function getTableAction(record) {
  return [
    {
      label: t('common.viewText'),
      onClick: handleDetail.bind(null, record),
    },
    {
      label: t('common.delText'),
      popConfirm: {
        title: t('common.delConfirmText'),
        confirm: handleDelete.bind(null, record),
        placement: 'left'
      }
    }
  ]
}


</script>
<style scoped>

</style>
