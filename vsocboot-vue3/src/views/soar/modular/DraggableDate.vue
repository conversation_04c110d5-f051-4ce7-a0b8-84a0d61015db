<template>
  <div style="position: absolute; right: 0; top: -28px">
    <a-tabs v-model:activeKey="isDraggable" @change="tabChange">
      <a-tab-pane :key="false" :tab="t('common.fixed')" :disabled="!isEdit" />
      <a-tab-pane :key="true" :tab="t('common.expression')" :disabled="!isEdit" />
    </a-tabs>
  </div>
  <div v-if="!isDraggable" @dragover.prevent="handleDragOver" @drop="handleDrop">
    <a-date-picker
      v-model:value="inputValue"
      style="width: 100%"
      :class="{ paramsDragstartClass: isParamsDragstart }"
      :placeholder="placeholder"
      @change="inputChange"
      ref="inputRef"
      valueFormat="YYYY-MM-DD HH:mm:ss"
      :disabled="!isEdit"
      :showTime="true"
    />
  </div>
  <template v-else>
    <div @dragover.prevent="handleDragOver" @drop="handleDrop">
      <a-input
        v-model:value="inputValue"
        :class="{ paramsDragstartClass: isParamsDragstart }"
        :placeholder="t('workflow.workflow.dragInputPlaceholder')"
        @input="inputChange"
        :disabled="!isEdit"
        ref="inputRef"
      />
    </div>
    <div>{{ showExpressionData }}</div>
  </template>
</template>

<script setup lang="ts">
  import { inject, ref, unref } from 'vue';
  import { isParamsDragstart, useNodeDataStore } from '/@/views/soar/utils/workflowCache';
  import { handleDragOver } from '/@/views/soar/utils/util';
  import {getBetweenBraces, handleExpression} from '/@/utils/util';
  import { useI18n } from '/@/hooks/web/useI18n';
  const { t } = useI18n();
  const props = defineProps({
    placeholder: String,
    text: String,
    textType: String,
  });
  const isDraggable = ref(false);

  if (getBetweenBraces(props?.text ?? '').length > 0) {
    isDraggable.value = true;
  }
  const isEdit: any = inject('isEdit');
  const inputValue = ref(props.text);
  const inputRef = ref();
  const emits = defineEmits(['update:text', 'update:textType', 'change']);

  function tabChange(activeKey) {
    console.log('activeKey', activeKey);
    inputValue.value = '';
    inputChange();
  }

  const showExpressionData = ref<any>();

  function handleDrop(event) {
    console.log('handleDrop', event);
    if (!isDraggable.value) {
      isDraggable.value = true;
    }
    if (!inputValue.value) {
      inputValue.value = '';
    }
    const data = event.dataTransfer.getData('path');
    inputValue.value = inputValue.value + '{{' + data + '}}';

    inputChange();
    event.preventDefault();
  }

  function inputChange() {
    emits('update:text', unref(inputValue));
    emits('update:textType', isDraggable.value === true ? 'expression' : 'fixed');
    if (isDraggable.value) {
      let expressionData: any = inputValue.value;
      showExpressionData.value = handleExpression(expressionData);
    }
  }
</script>

<style scoped lang="less">
  :deep(.ant-tabs-nav) {
    margin: 0;
  }

  :deep(.ant-tabs-tab) {
    padding: 0;
  }

  :deep(.ant-tabs-tab + .ant-tabs-tab) {
    margin: 0 0 0 5px;
  }

  :deep(.ant-tabs-top > .ant-tabs-nav::before) {
    border: 0;
  }
</style>
