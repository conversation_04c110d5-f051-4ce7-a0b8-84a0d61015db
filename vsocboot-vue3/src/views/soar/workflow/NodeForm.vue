<template>
  <a-modal
    v-model:visible="visible"
    :title="tPrefix('nodeFormTitle')"
    @ok="handleOk"
    :maskClosable="false"
    :destroyOnClose="true"
    width="100%"
    wrap-class-name="full-modal"
    :footer="null"
    @cancel="close"
    @dragover.prevent="handleDragOver"
  >
    <div class="padding16">
      <TimerForm ref="formModule" v-if="NodeData.type == NODE_TYPE.timer" :id="NodeData.id" />
      <MysqlForm ref="formModule" v-else-if="NodeData.type == NODE_TYPE.mysql" :prevNodeId="prevNodeId" :id="NodeData.id" />
      <HttpRequestForm ref="formModule" v-else-if="NodeData.type == NODE_TYPE.httpRequest" :id="NodeData.id" :prevNodeId="prevNodeId" />
      <WebhookForm ref="formModule" v-else-if="NodeData.type == NODE_TYPE.webhook" :id="NodeData.id" />
      <SendEmailForm ref="formModule" v-else-if="NodeData.type == NODE_TYPE.sendEmail" :id="NodeData.id" :prevNodeId="prevNodeId" />
      <CodeForm ref="formModule" v-else-if="NodeData.type == NODE_TYPE.code" :id="NodeData.id" />
      <SetForm ref="formModule" v-else-if="NodeData.type == NODE_TYPE.set" :id="NodeData.id" :prevNodeId="prevNodeId" />
      <RenameKeysForm ref="formModule" v-else-if="NodeData.type == NODE_TYPE.renamekeys" :id="NodeData.id" :prevNodeId="prevNodeId" />
      <IfForm ref="formModule" v-else-if="NodeData.type == NODE_TYPE.if" :id="NodeData.id" :prevNodeId="prevNodeId" />
      <SwitchForm ref="formModule" v-else-if="NodeData.type == NODE_TYPE.switch" :id="NodeData.id" :prevNodeId="prevNodeId" />
      <FilterForm ref="formModule" v-else-if="NodeData.type == NODE_TYPE.filter" :id="NodeData.id" :prevNodeId="prevNodeId" />
      <ItemListForm ref="formModule" v-else-if="NodeData.type == NODE_TYPE.itemList" :id="NodeData.id" :prevNodeId="prevNodeId" />
      <EmailTriggerForm ref="formModule" v-else-if="NodeData.type == NODE_TYPE.receiveMail" :id="NodeData.id" />
      <PlaybookForm ref="formModule" v-else-if="NodeData.type == NODE_TYPE.playbook" :id="NodeData.id" />
      <SshForm ref="formModule" v-else-if="NodeData.type == NODE_TYPE.ssh" :prevNodeId="prevNodeId" :id="NodeData.id" />
      <Clickhouse ref="formModule" v-else-if="NodeData.type == NODE_TYPE.clickhouse" :prevNodeId="prevNodeId" :id="NodeData.id" />
      <VirustotalForm ref="formModule" v-else-if="NodeData.type == NODE_TYPE.virustotal" :id="NodeData.id" :prevNodeId="prevNodeId" />
      <ThreatbooktipForm ref="formModule" v-else-if="NodeData.type == NODE_TYPE.threatbooktip" :id="NodeData.id" :prevNodeId="prevNodeId" />
      <Huawei
        ref="formModule"
        v-else-if="
          NodeData.type == NODE_TYPE.huaweiusg6000v ||
          NODE_TYPE.huaweiusg9000v ||
          NODE_TYPE.huaweiusg6000E ||
          NODE_TYPE.huaweiusg6000 ||
          NODE_TYPE.huaweiusg9500
        "
        :id="NodeData.id"
        :prevNodeId="prevNodeId"
        :type="NodeData.type"
      />
    </div>
  </a-modal>
</template>

<script setup lang="ts">
  import { inject, ref, provide } from 'vue';
  import TimerForm from '/@/views/soar/nodeForm/TimerForm.vue';
  import MysqlForm from '/@/views/soar/nodeForm/MysqlForm.vue';
  import HttpRequestForm from '/@/views/soar/nodeForm/HttpRequestForm.vue';
  import { NODE_TYPE } from '/@/views/soar/utils/nodeViewUtils';
  import { handleDragOver } from '/@/views/soar/utils/util';
  import WebhookForm from '/@/views/soar/nodeForm/WebhookForm.vue';
  import SendEmailForm from '/@/views/soar/nodeForm/SendEmailForm.vue';
  import CodeForm from '/@/views/soar/nodeForm/CodeForm.vue';
  import SetForm from '/@/views/soar/nodeForm/SetForm.vue';
  import RenameKeysForm from '/@/views/soar/nodeForm/RenameKeysForm.vue';
  import IfForm from '/@/views/soar/nodeForm/IfForm.vue';
  import SwitchForm from '/@/views/soar/nodeForm/SwitchForm.vue';
  import FilterForm from '/@/views/soar/nodeForm/FilterForm.vue';
  import ItemListForm from '/@/views/soar/nodeForm/ItemListForm.vue';
  import EmailTriggerForm from '/@/views/soar/nodeForm/EmailTriggerForm.vue';
  import PlaybookForm from '/@/views/soar/nodeForm/PlaybookForm.vue';
  import SshForm from '/@/views/soar/nodeForm/SshForm.vue';
  import Clickhouse from '/@/views/soar/nodeForm/ClickhouseForm.vue';
  import VirustotalForm from '/@/views/soar/nodeForm/VirustotalForm.vue';
  import ThreatbooktipForm from '/@/views/soar/nodeForm/ThreatbooktipForm.vue';
  import Huawei from '/@/views/soar/nodeForm/Huawei.vue';
  import { useI18n } from '/@/hooks/web/useI18n';
  import { getPrevOneNodes } from '../utils/Workflow';

  const { t } = useI18n();

  const tPrefix = (name) => {
    return t('workflow.workflow.' + name);
  };
  const visible = ref(false);
  const isMainPage: any = inject('isMainPage');
  const prevNodeId = ref('');
  const NodeData = ref<any>({});
  const showModal = (data: any) => {
    prevNodeId.value = getPrevOneNodes(data.id);
    NodeData.value = data;
    visible.value = true;
    isMainPage.value = false;
  };
  const formModule = ref();
  const close = async () => {
    await formModule.value.handleValidate();
    visible.value = false;
    isMainPage.value = true;
  };

  const handleOk = () => {
    close();
  };

  defineExpose({
    showModal,
  });
</script>

<style scoped lang="less">
  :deep(.ant-input-number) {
    width: 100%;
  }

  :deep(.prompt_span) {
    color: @font-color-1;
    font-size: @fz12;
  }

  :deep(.nodeFormDiv) {
    border: 1px solid @border-color;
    padding: 10px;
    padding-right: 0px;
  }

  :deep(.paramsDragstartClass) {
    border: 1px solid #0a9fe5;
  }

  :deep(.input_div) {
    height: calc(100vh - 100px);
  }

  :deep(.output_div) {
    height: calc(100vh - 100px);
  }

  :deep(.scrollDiv) {
    height: calc(100vh - 140px);
    overflow-x: hidden;
    overflow-y: auto;
    padding-right: 5px;
  }
</style>
