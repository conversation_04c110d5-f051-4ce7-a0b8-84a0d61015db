<template>
  <div style="width: 100%;height: 100%;display: flex;">
    <div class="list_div">
      <!--引用表格-->
      <BasicTable @register="registerTable"/>
    </div>
    <div class="canvas_div" :class="{'canvas_div_cursor':keydownFlag}"
         @mousedown="mousedown" @mouseup="mouseup" @mousemove="mousemove"
         @click.stop="canvasClick">
      <div id="soar_view_canvas" style="position: relative;">
        <template v-if="initCompleted">
          <template v-if="nodeList.length > 0">
            <Node v-for="(node,index) in nodeList" :data="node" :nodeIndex="index"
                  :key="'node_'+node.id" :isEdit="false"
                  ref="nodeRef"/>
          </template>
        </template>
      </div>
    </div>
  </div>
  <div style="text-align: center;position: fixed;bottom: 50px;width: 100%;">
    <div style="position: absolute;left: 350px;" class="flex flex-row gap-4px">
      <div class="ax-icon-button" @click="zoomAuto">
        <icon icon="ant-design:expand-outlined" size="20"/>
      </div>
      <div class="ax-icon-button" @click="zoomIn">
        <icon icon="ant-design:zoom-in-outlined" size="20"/>
      </div>
      <div class="ax-icon-button" @click="zoomOut">
        <icon icon="ant-design:zoom-out-outlined" size="20"/>
      </div>
      <div class="ax-icon-button" @click="zoomBack" v-if="zoomNumber != 1">
        <icon icon="ant-design:undo-outlined" size="20"/>
      </div>
    </div>
  </div>

</template>

<script setup lang="ts">
import {defineProps, nextTick, onMounted, onUnmounted, ref} from "vue";
import {connection, jsPlumbInstanceView, workflowInit} from "/@/views/soar/utils/WorkflowView";
import Node from "/@/views/soar/workflow/Node.vue";
import {useI18n} from "/@/hooks/web/useI18n";
import {BasicColumn, BasicTable} from "/@/components/Table";
import {useListPage} from "/@/hooks/system/useListPage";
import {nodeStoreViewData, useNodeViewInit,} from "/@/views/soar/utils/workflowCache";
import {executionList, loadExecutionData} from "/@/views/soar/execution/SoarExecutionInfo.api";
import {closestNumberDivisibleBy} from "/@/views/soar/utils/util";
import {GRID_SIZE} from "/@/views/soar/utils/nodeViewUtils";
import Icon from "/@/components/Icon";

const {t} = useI18n();
const spinning = ref(false);
const tPrefix = (name) => {
  return t("workflow.workflow." + name);
}

const props = defineProps({
  defExecutionId: String
})

const initCompleted = ref(false)
const nodeList = ref<any[]>([])
let workflowId: any = sessionStorage.getItem("soar.workflowId");
const columns: BasicColumn[] = [
  {
    title: tPrefix('startedat'),
    dataIndex: 'startedat',
    width: 180
  },
  {
    title: tPrefix('mode'),
    dataIndex: 'mode',
    width: 100,
    customRender(opt) {
      if (opt.value == 1) {
        return t('workflow.SoarExecutionInfo.test')
      } else {
        return t('workflow.SoarExecutionInfo.run')
      }
    },
  },
];
/**
 * 选择的记录id
 */
let executionId = props.defExecutionId ?? "";
//注册table数据
const {tableContext} = useListPage({
  tableProps: {
    api: executionList,
    columns,
    rowKey: 'executionId',
    canResize: false,
    useSearchForm: false,
    showActionColumn: false,
    tableSetting: {
      setting: false
    },
    scroll: {y: 'calc(100vh - 200px)' as any},
    defSort: {
      column: 'startedat',
      order: 'desc',
    },
    beforeFetch: (param) => {
      //只查看成功的
      param.status = 1;
      param.workflowId = workflowId ? workflowId : '-';
      return param;
    },
    customRow: (record, index) => {
      let isSelect = "";
      //第一次进入，默认显示第一个
      if (!executionId && index == 0) {
        executionId = record.executionId;
        setTimeout(() => {
          const list = document.getElementsByName("clickTr");
          list[0].className = list[0].className + " select";
          loadInfo(record);
        }, 100)
      } else if (executionId === record.executionId) {
        isSelect = "select";
      }
      return {
        name: 'clickTr',
        class: isSelect,
        onclick: (e) => {
          const list = document.getElementsByName("clickTr");
          for (let i = 0; i < list.length; i++) {
            list[i].className = list[i].className.replace("select", "").trim();
          }
          const $tr = e.target.parentElement;
          $tr.className = $tr.className + " select";
          loadInfo(record);
        }
      };
    },
  }
})

const [registerTable, {}, {}] = tableContext

onMounted(() => {
  const container = document.getElementById("soar_view_canvas");
  if (!container) {
    return
  }
  workflowInit(container, {
    setSelectNode: setSelectNode,
  });

  if (!jsPlumbInstanceView.value) {
    return;
  }
  initCompleted.value = true;
  useNodeViewInit();

  if (props.defExecutionId) {
    loadInfo({executionId: props.defExecutionId})
  }

  window.addEventListener('keydown', keydownHandler);
  window.addEventListener('keyup', keyupHandler);
})


//移动代码开始
let mouseDownFlag = false;
let keydownFlag = ref(false);
const mouseMoveData = {
  x: 0,
  y: 0,
  left: 0,
  top: 0
}

function mousedown(e) {
  if (!keydownFlag.value) {
    return
  }
  mouseDownFlag = true;
  const soarCanvas: any = document.getElementById("soar_view_canvas");
  let left = soarCanvas.offsetLeft;
  let top = soarCanvas.offsetTop;
  mouseMoveData.left = left;
  mouseMoveData.top = top;
  mouseMoveData.x = e.clientX;
  mouseMoveData.y = e.clientY;
}

function mouseup() {
  mouseDownFlag = false;
}

function mousemove(e) {
  if (mouseDownFlag && keydownFlag.value) {
    const soarCanvas: any = document.getElementById("soar_view_canvas");
    let left = mouseMoveData.left;
    let top = mouseMoveData.top;
    left = left + (e.clientX - mouseMoveData.x);
    top = top + (e.clientY - mouseMoveData.y);
    soarCanvas.style.left = left + 'px';
    soarCanvas.style.top = top + 'px';
  }
}

//移动代码结束


onUnmounted(() => {
  window.removeEventListener('keydown', keydownHandler);
  window.removeEventListener('keyup', keyupHandler);
});

/**
 * 监听按键
 * @param event
 */
const keydownHandler = (event) => {
  if (event.key === 'Control') {
    keydownFlag.value = true;
  }
};

const keyupHandler = (event) => {
  if (event.key === 'Control') {
    keydownFlag.value = false;
  }
}

/**
 * 点击节点，选中样式
 * @param element
 */
function setSelectNode(element: Element) {
  const nodeIndex: any = element.getAttribute("nodeIndex");
  for (let i = 0; i < nodeList.value.length; i++) {
    if (i == nodeIndex) {
      nodeList.value[i].selected = true;
    } else {
      nodeList.value[i].selected = false;
    }
  }

}

/**
 * 加载数据
 */
function loadInfo(record) {
  executionId = record.executionId;
  jsPlumbInstanceView?.value?.destroy()
  nodeList.value = [];
  spinning.value = true;
  loadExecutionData({executionId: record.executionId}).then(async data => {
    spinning.value = false;
    const viewId = "view_";
    let list = JSON.parse(data.nodes);
    for (let i = 0; i < list.length; i++) {
      list[i].id = viewId + list[i].id;
      if (list[i].parameters) {
        list[i].nodeParams = JSON.parse(list[i].parameters)
      }
    }
    nodeList.value = list;

    await nextTick()
    console.log(data.connections)
    const lines = JSON.parse(data.connections);
    for (let i = 0; i < lines.length; i++) {
      lines[i].from = viewId + lines[i].from;
      const tos = lines[i].to;
      for (let k = 0; k < tos.length; k++) {
        tos[k] = viewId + tos[k];
      }
    }
    console.log('lines', lines)
    connection(lines)

    const map = JSON.parse(data.data);
    const newMap = {}
    for (const key in map) {
      newMap[viewId + key] = map[key];
    }
    console.log('newMap', newMap)
    nodeStoreViewData.value = newMap;
    zoomAuto()
  })
}


/**
 * 点击画布,取消所有选中状态
 */
function canvasClick(e) {
  if (e.target.className === 'canvas_div') {
    const list = nodeList.value;
    for (let i = 0; i < list.length; i++) {
      list[i].selected = false;
    }
  }
}


const zoomNumber = ref(1);

/**
 * 自适应
 */
function zoomAuto() {

  let minLeft = 0;
  let maxLeft = 0;
  let minTop = 0;
  let maxTop = 0;
  //获取所有节点最上、最下、最左、最有的坐标，计算所需宽度和高度
  for (const i in nodeList.value) {
    const id = nodeList.value[i].id;
    const left = document.getElementById(id)?.offsetLeft;
    const top = document.getElementById(id)?.offsetTop;
    if (left && top) {
      if (i == '0') {
        minLeft = maxLeft = left;
        minTop = maxTop = top;
      }
      if (left < minLeft) {
        minLeft = left;
      }
      if (left > maxLeft) {
        maxLeft = left;
      }
      if (top < minTop) {
        minTop = top;
      }
      if (top > maxTop) {
        maxTop = top;
      }
    }
  }
  const padding = 400;
  const totalWidth = maxLeft - minLeft + 300 + padding;
  const totalHeight = maxTop - minTop + 100 + padding;

  const canvas = document.getElementById("soar_view_canvas") as HTMLElement;
  const width = canvas.clientWidth;
  const height = canvas.clientHeight;

  const zoomLevel = Math.min(width / totalWidth, height / totalHeight, 1);
  let xOffset = minLeft * -1 * zoomLevel; // find top right corner
  xOffset += (width - (maxLeft - minLeft + 300)) / 2 * zoomLevel; // add padding to center workflow

  let yOffset = minTop * -1 * zoomLevel; // find top right corner
  yOffset += (height - (maxTop - minTop + 100)) / 2 * zoomLevel; // add padding to center workflow
  canvas.style.left = closestNumberDivisibleBy(xOffset, GRID_SIZE) + "px";
  canvas.style.top = closestNumberDivisibleBy(yOffset, GRID_SIZE) + "px";
  if (jsPlumbInstanceView.value) {
    canvas.style.transform = "scale(" + zoomLevel + ")";
    jsPlumbInstanceView.value.setZoom(zoomLevel);
    zoomNumber.value = zoomLevel;
  }
}

/**
 * 放大
 */
function zoomIn() {
  if (jsPlumbInstanceView.value) {
    let zoom = jsPlumbInstanceView.value.currentZoom;
    const div = document.getElementById("soar_view_canvas");
    if (div) {
      let left = div.offsetLeft / zoom;
      let top = div.offsetTop / zoom;
      zoom *= 1.25;
      left *= zoom;
      top *= zoom;
      div.style.left = closestNumberDivisibleBy(left, GRID_SIZE) + "px";
      div.style.top = closestNumberDivisibleBy(top, GRID_SIZE) + "px";
      div.style.transform = "scale(" + zoom + ")";
      jsPlumbInstanceView.value.setZoom(zoom);
      zoomNumber.value = zoom;
    }
  }
}

/**
 * 缩小
 */
function zoomOut() {
  if (jsPlumbInstanceView.value) {
    let zoom = jsPlumbInstanceView.value.currentZoom;
    const div = document.getElementById("soar_view_canvas");
    if (div) {
      let left = div.offsetLeft / zoom;
      let top = div.offsetTop / zoom;
      zoom /= 1.25;
      left *= zoom;
      top *= zoom;
      div.style.left = closestNumberDivisibleBy(left, GRID_SIZE) + "px";
      div.style.top = closestNumberDivisibleBy(top, GRID_SIZE) + "px";
      div.style.transform = "scale(" + zoom + ")";
      jsPlumbInstanceView.value.setZoom(zoom);
      zoomNumber.value = zoom;
    }
  }
}

/**
 * 还原
 */
function zoomBack() {
  if (jsPlumbInstanceView.value) {
    let zoom = jsPlumbInstanceView.value.currentZoom;
    const div = document.getElementById("soar_view_canvas");
    if (div) {
      let left = div.offsetLeft / zoom;
      let top = div.offsetTop / zoom;
      zoom = 1;
      left *= zoom;
      top *= zoom;
      div.style.left = closestNumberDivisibleBy(left, GRID_SIZE) + "px";
      div.style.top = closestNumberDivisibleBy(top, GRID_SIZE) + "px";
      div.style.transform = "scale(" + zoom + ")";
      jsPlumbInstanceView.value.setZoom(zoom);
      zoomNumber.value = zoom;
    }
  }
}

</script>


<style scoped lang="less">

.list_div {
  width: 300px;
  margin-top: 49px;
  z-index: 9;
}

.canvas_div {
  position: fixed;
  left: 360px;
  right: 0;
  bottom: 0;
  top: 52px;
}

.canvas_div_cursor {
  cursor: grab;
}

#soar_view_canvas {
  height: 100%;
  width: 100%;
}

:deep(.select) {
  background-color: #308CFF;
}
</style>
