<template>
  <div class="page_title">{{ t('workflow.workflow.umpConnectionManagement') }}</div>
  <div class="padding16">

    <div class="ump_div" style="">
      <a-form :layout="formLayout">
        <a-form-item :label="t('workflow.workflow.umpIpAddress')">
          <a-input/>
        </a-form-item>
        <a-form-item :label="t('workflow.workflow.key')">
          <div style="display: flex;">
            <a-input v-model:value="key" :disabled="true"/>
            <div style="margin-left: 5px;line-height: 32px;">
              <Icon icon="ant-design:redo-outlined" style="cursor: pointer;" @click="refKey"/>
            </div>

          </div>
        </a-form-item>
        <div style="text-align: right;">
          <a-button type="primary">{{ t('workflow.workflow.networkConnectivityTest') }}</a-button>
        </div>

      </a-form>
    </div>

  </div>
</template>


<script setup lang="ts">
import {ref} from "vue";
import Icon from "/@/components/Icon";
import {formLayout} from "/@/settings/designSetting";
import {buildUUID} from "/@/utils/uuid";
import {useI18n} from "/@/hooks/web/useI18n";

const {t} = useI18n();
const key = ref("")
key.value = buildUUID()

function refKey(){
  key.value = buildUUID()
}

</script>

<style scoped lang="less">
.ump_div {
  width: 800px;
  margin: auto;
  padding: 16px;
  background-color: @dark-bg2;
}
</style>
