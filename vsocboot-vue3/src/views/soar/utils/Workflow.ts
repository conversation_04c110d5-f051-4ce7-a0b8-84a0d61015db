import {
  BrowserJsPlumbInstance,
  Connection,
  EVENT_CONNECTION,
  EVENT_CONNECTION_DETACHED,
  EVENT_CONNECTION_MOUSEOUT,
  EVENT_CONNECTION_MOUSEOVER,
  EVENT_CONNECTION_MOVED,
  EVENT_ELEMENT_CLICK,
  newInstance,
} from "@jsplumb/browser-ui";
import {
  CONNECTOR_ARROW_OVERLAYS,
  CONNECTOR_FLOWCHART_TYPE,
  CONNECTOR_PAINT_STYLE_DEFAULT,
  CONNECTOR_PAINT_STYLE_PRIMARY,
  GRID_SIZE,
  NODE_TYPE
} from "/@/views/soar/utils/nodeViewUtils";
import {ref} from "vue";
import {isString} from "/@/utils/is";
import {PointXY} from "@jsplumb/browser-ui/types/util/util";
import {useNodeDataStore} from "/@/views/soar/utils/workflowCache";

export const jsPlumbInstanceRef = ref<BrowserJsPlumbInstance>();
export const isDragging = ref(false)
/**
 * 点击线上的添加标识，给主页监听使用，调用主页弹出节点选择页
 */
export const lineAddNodeFlag = ref(false);
/**
 * 添加节点参数
 */
export const lineAddNodeParam = ref<any>();

export const lineHoverTimeId = ref<any>({});

export function workflowInit(container: HTMLElement, options) {
  jsPlumbInstanceRef.value = newInstance({
    container: container,
    connector: CONNECTOR_FLOWCHART_TYPE,
    resizeObserver: false,
    endpoint: {
      type: 'Dot',
      options: {
        radius: 9,
        cssClass: 'endpointClass',
      },
    },
    paintStyle: CONNECTOR_PAINT_STYLE_DEFAULT,
    hoverPaintStyle: CONNECTOR_PAINT_STYLE_PRIMARY,
    connectionOverlays: CONNECTOR_ARROW_OVERLAYS,
    elementsDraggable: true,
    dragOptions: {
      cursor: 'pointer',
      grid: {"w": GRID_SIZE, "h": GRID_SIZE},
      start: () => {
        isDragging.value = true;
        return true;
      },
      stop: () => {
        isDragging.value = false;
      },
    },
  });

  //节点点击选中
  jsPlumbInstanceRef.value.bind(EVENT_ELEMENT_CLICK, (element: Element) => {
    if (options && options.setSelectNode) {
      options.setSelectNode(element);
    }
  });

  jsPlumbInstanceRef.value.bind(EVENT_CONNECTION_MOVED, () => {
  });
  /**
   * 链接建立触发
   */
  jsPlumbInstanceRef.value.bind(EVENT_CONNECTION, () => {
    if (options && options.refNodeAddFlag) {
      options.refNodeAddFlag();
    }
  });
  /**
   * 链接断开触发
   */
  jsPlumbInstanceRef.value.bind(EVENT_CONNECTION_DETACHED, () => {
    if (options && options.refNodeAddFlag) {
      options.refNodeAddFlag();
    }
  });
  /**
   * 鼠标悬停在链接线上触发
   */
  jsPlumbInstanceRef.value.bind(EVENT_CONNECTION_MOUSEOVER, (connection: Connection) => {
    const id = connection.id + "_line";
    const $lineDiv = document.getElementById(id);
    if ($lineDiv) {
      $lineDiv.style.display = "block";
      window.clearTimeout(lineHoverTimeId.value[id]);
    }
  });
  /**
   * 鼠标离开在链接线上触发
   */
  jsPlumbInstanceRef.value.bind(EVENT_CONNECTION_MOUSEOUT, (connection: Connection) => {
    const id = connection.id + "_line";
    const $lineDiv = document.getElementById(id);
    if ($lineDiv) {
      window.clearTimeout(lineHoverTimeId.value);
      lineHoverTimeId.value[id] = window.setTimeout(() => {
        $lineDiv.style.display = "none";
      }, 200)
    }
  });


}

/**
 * 初始化连线
 * @param connections 连线数据
 */
export function connection(connections) {
  if (!connections) {
    return
  }
  const line = JSON.parse(connections)
  for (let i = 0; i < line.length; i++) {
    const endpoints: any = jsPlumbInstanceRef.value?.selectEndpoints({source: line[i].from})
    const to = line[i].to;
    for (let j = 0; j < endpoints.entries.length; j++) {
      if (to[j]) {
        const points: any = jsPlumbInstanceRef.value?.selectEndpoints({target: to[j]});
        if (points && points.entries) {
          jsPlumbInstanceRef.value?.connect({
            source: endpoints.entries[j],
            target: points.entries[0],
            anchors: ["Right", "Left"],
          })
        }
      }
    }
  }
}

export function deleteNodeByElement(element: Element) {
  const endpoints = jsPlumbInstanceRef.value?.getEndpoints(element);
  jsPlumbInstanceRef.value?._removeElement(element);
  jsPlumbInstanceRef.value?.deleteConnectionsForElement(element)
  for (const i in endpoints) {
    jsPlumbInstanceRef.value?.deleteEndpoint(endpoints[i])
  }
}

/**
 * 根据节点id获取该节点连线前面的所有节点id，不包括本身
 * @param nodeId
 */
export function getAllPrevNodes(nodeId) {
  return getPrevNodes(nodeId)
}

/**
 * 迭代，向前找连线的所有节点id
 * @param targetId
 */
function getPrevNodes(targetId) {
  const nodeIds: any = [];
  const lines = jsPlumbInstanceRef.value?.getConnections({target: targetId});
  //目前只有一个入，所以只会查到一条线
  if (lines && lines.length > 0) {
    for (let i = 0; i < lines.length; i++) {
      const sourceId = lines[i].sourceId;
      //防止循环节点导致找上一个节点死循环
      if (nodeIds.indexOf(sourceId) > -1) {
        continue;
      }
      nodeIds.push(sourceId);
      const nodeIds2 = getPrevNodes(sourceId)
      nodeIds.push(...nodeIds2);
      //如果有多条线，只取第一条线
      break;
    }
  }
  return nodeIds;
}

/**
 * 获取指定节点后面的所有节点
 * @param sourceId 节点id
 * @param flag 获取层级，不写获取所有，true表示只获取下一级节点
 */
export function getNextNodes(sourceId, flag?: boolean) {
  const nodeIds: any = [];
  const lines = jsPlumbInstanceRef.value?.getConnections({source: sourceId});
  if (lines && lines.length > 0) {
    for (let i = 0; i < lines.length; i++) {
      const targetId = lines[i].targetId;
      //防止循环节点导致找上一个节点死循环
      if (nodeIds.indexOf(targetId) > -1) {
        continue;
      }
      nodeIds.push(targetId);
      if (!flag) {
        const nodeIds2 = getNextNodes(targetId)
        nodeIds.push(...nodeIds2);
      }
    }
  }
  return nodeIds;
}

/**
 * 判断目标节点是否在分支节点返回的线路上
 * @param lineIndex 分支节点返回的线下标
 * @param nodeId 分支节点id
 * @param targetId 目标节点id
 */
export function isBranchLine(lineIndex, nodeId, targetId) {
  const lines = jsPlumbInstanceRef.value?.getConnections({source: nodeId});
  let result = false;
  if (lines) {
    let nextId = "";
    for (let i = 0; i < lines.length; i++) {
      if (lines[i].endpoints[0].portId == lineIndex) {
        nextId = lines[i].targetId;
        break;
      }
    }
    if (nextId) {
      const ids: [string] = getNextNodes(nextId);
      result = ids.includes(targetId);
    }
  }
  return result;
}

/**
 * 根据节点数据返回的线标识获取线上的下一节点id
 * @param lineIndex
 * @param nodeId
 */
export function getLineNextNodeId(lineIndex, nodeId) {
  const lines = jsPlumbInstanceRef.value?.getConnections({source: nodeId});
  let id = "";
  if (lines) {
    for (let i = 0; i < lines.length; i++) {
      if (lines[i].endpoints[0].portId == lineIndex) {
        id = lines[i].targetId;
        break;
      }
    }
  }
  return id;
}

/**
 * 获取线的源接节点id
 * @param targetId
 */
export function getPrevOneNodes(targetId) {
  let nodeId = ""
  const lines = jsPlumbInstanceRef.value?.getConnections({target: targetId});
  if (lines && lines.length > 0) {
    // 目前只有一条线
    for (let i = 0; i < lines.length; i++) {
      nodeId = lines[i].sourceId;
      //如果有多条线，只取第一条线
      break;
    }
  }
  return nodeId;

}

export function getContent(value: any): string {
  return isString(value) ? `"${value}"` : value;
}

/**
 * 删除目标节点的线
 * @param targetId 目标节点id
 * @param lineId 删除的线的id
 */
export function deleteConnection(targetId, lineId) {
  const lines = jsPlumbInstanceRef.value?.getConnections({target: targetId});
  if (lines) {
    for (let i = 0; i < lines.length; i++) {
      if (lines[i].id === lineId) {
        jsPlumbInstanceRef.value?.deleteConnection(lines[i]);
      }
    }
  }
}

/**
 * 判断添加节点位置是否够用，位置不够则其后边的节点向右移动
 */
export function nodeMoveRight(nodeId, lineId) {

  const element = document.getElementById(nodeId);
  if (element) {
    const point = jsPlumbInstanceRef.value?.getPosition(element) as PointXY;
    moveNextNode(point, nodeId, false, lineId)
  }
}

/**
 * 向右移动节点
 * @param point 初始节点坐标
 * @param nodeId 节点id
 * @param isMove 上级节点是否移动了，如果移动为true
 * @param lineId 线的id
 */
function moveNextNode(point, nodeId, isMove?, lineId?) {
  const lines = jsPlumbInstanceRef.value?.getConnections({source: nodeId});
  if (lines) {
    //找到线的结束节点，判断结束节点和开始节点的x轴间距是否够放下新节点，400px
    for (let i = 0; i < lines.length; i++) {
      if (lineId && lines[i].id === lineId) {
        const point2 = jsPlumbInstanceRef.value?.getPosition(lines[i].target) as PointXY;
        if (point2.x - point.x > 0 && point2.x - point.x < 400) {
          setNodePointXY(lines[i].target, point2.x + 300, point2.y)
          isMove = true;
        }
        moveNextNode(point, lines[i].targetId, isMove)
      } else if (!lineId) {
        if (isMove) {
          const point2 = jsPlumbInstanceRef.value?.getPosition(lines[i].target) as PointXY;
          setNodePointXY(lines[i].target, point2.x + 300, point2.y)
          moveNextNode(point, lines[i].targetId, isMove)
        } else {
          const point2 = jsPlumbInstanceRef.value?.getPosition(lines[i].target) as PointXY;
          if (point2.x - point.x > 0 && point2.x - point.x < 400) {
            setNodePointXY(lines[i].target, point2.x + 300, point2.y)
            isMove = true;
          }
          moveNextNode(point, lines[i].targetId, isMove)
        }
      }
    }
  }
}

function setNodePointXY(element: HTMLElement, x, y) {
  jsPlumbInstanceRef.value?.setElementPosition(element, x, y);
  element.style.left = x + "px";
  element.style.top = y + "px";
}

export function getLineSourceEndpoints(nodeId) {
  const lines = jsPlumbInstanceRef.value?.getConnections({target: nodeId});
  if (lines) {
    return lines[0].endpoints[0];
  } else {
    return 0;
  }
}


/**
 * 判断输入数据，if等分支节点
 * @param data
 */
export function getInputData(data, id) {
  let inputData;
  if (data?.nodeId) {
    //if节点数据，根据返回线下标判断是否显示数据
    if (data.nodeId.startsWith(NODE_TYPE.if + "_") || data.nodeId.startsWith(NODE_TYPE.switch + "_")) {
      const nextIds: [string] = getNextNodes(data.nodeId, true);
      //是分支节点的下一级节点
      if (nextIds.includes(id)) {
        const endpoint = getLineSourceEndpoints(id);
        if (data?.lineIndex == endpoint.portId) {
          inputData = data?.data;
        } else {
          inputData = "";
        }
      } else {
        //不是分支节点的下一级，需判断是否再分支节点返回的线上
        const flag = isBranchLine(data?.lineIndex, data.nodeId, id);
        if (flag) {
          inputData = data?.data;
        } else {
          inputData = "";
        }
      }
    } else {
      inputData = data?.data;
    }
  }
  return inputData;
}

export function getOutputData(data, lineIndex, id) {
  lineIndex.value = data?.lineIndex ?? 0;
  let outputData;
  let outputData2;
  const prevId = getPrevOneNodes(id);
  //前一个节点是if判断等节点
  if (prevId.startsWith(NODE_TYPE.if + "_")) {
    const endpoint = getLineSourceEndpoints(id);
    const prevData = useNodeDataStore.getNodeData(prevId);
    if (prevData?.lineIndex == endpoint.portId) {
      outputData = data?.data;
      outputData2 = data?.data2;
    } else {
      outputData = "";
      outputData2 = "";
    }
  } else {
    outputData = data?.data;
    outputData2 = data?.viewData;
  }
  return {data: outputData, viewData: outputData2};
}
