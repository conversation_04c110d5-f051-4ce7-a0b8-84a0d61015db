import {ConnectorSpec, FlowchartConnector, OverlaySpec, PaintStyle} from "@jsplumb/browser-ui";
import {EndpointSpec} from "@jsplumb/browser-ui/types/common/endpoint";
import {
  deleteConnection,
  lineAddNodeFlag,
  lineAddNodeParam,
  lineHoverTimeId
} from "/@/views/soar/utils/Workflow";
import {useI18n} from "/@/hooks/web/useI18n";

const {t} = useI18n();

export const OVERLAY_ENDPOINT_ARROW_ID = 'endpoint-arrow';
export const GRID_SIZE = 20;
/**
 * reattachConnections: Whether or not to set reattach:true on connections that have this endpoint as their source. Defaults to false.
 * 设为true后该源节点的连接不能重源节点处拖拽断开
 *
 * connectionsDetachable:Whether or not connections that have this endpoint as their source are configured to be detachable with the mouse. Defaults to true.
 * 是否可以使用鼠标拆卸连线，false不能
 */
export const defaultEndpointOptions = {
  reattachConnections: false,
  connectionsDetachable: false
};


/**
 * 节点类型
 */
export const NODE_TYPE = {
  timer: "timer",
  webhook: "webhook",
  mysql: "mysql",
  httpRequest: "httprequest",
  if: "if",
  code: "code",
  sendEmail: "sendmail",
  set: "set",
  renamekeys: "renamekeys",
  switch: "switch",
  filter: 'filter',
  itemList: 'itemList',
  receiveMail: "receivemail",
  playbook: "playbook",  // anxinSoar
  subworkflow: 'subworkflow',
  ssh: 'ssh',
  clickhouse:'clickhouse',
  virustotal:'virustotal',
  threatbooktip:'threatbooktip',
  huaweiusg6000v: 'huaweiusg6000v',
  huaweiusg9000v: 'huaweiusg9000v',
  huaweiusg6000: 'huaweiusg6000',
  huaweiusg6000E: 'huaweiusg6000E',
  huaweiusg9500: 'huaweiusg9500',
};


export const START_NODE_TYPE = [NODE_TYPE.timer, NODE_TYPE.webhook, NODE_TYPE.receiveMail];

/**
 * 节点数据
 */
export const NODE_DATA_CONFIG = [
  {
    name: "Timer",
    type: NODE_TYPE.timer,
    bean: NODE_TYPE.timer,
    category: 1
  },
  {
    name: "Webhook",
    type: NODE_TYPE.webhook,
    bean: NODE_TYPE.webhook,
    category: 1
  },
  {
    name: "Email Trigger",
    type: NODE_TYPE.receiveMail,
    bean: NODE_TYPE.receiveMail,
    category: 1
  },
  {
    name: "Sub Workflow",
    type: NODE_TYPE.subworkflow,
    bean: NODE_TYPE.subworkflow,
    category: 1
  },
  {
    name: "IF",
    type: NODE_TYPE.if,
    bean: NODE_TYPE.if,
    category: 2
  },
  {
    name: "Switch",
    type: NODE_TYPE.switch,
    bean: NODE_TYPE.switch,
    category: 2
  },
  {
    name: "HTTP Request",
    type: NODE_TYPE.httpRequest,
    bean: NODE_TYPE.httpRequest,
    category: 3
  },
  {
    name: "CODE",
    type: NODE_TYPE.code,
    bean: NODE_TYPE.code,
    category: 3
  },
  {
    name: "Send Email",
    type: NODE_TYPE.sendEmail,
    bean: NODE_TYPE.sendEmail,
    category: 3
  },
  {
    name: "Set",
    type: NODE_TYPE.set,
    bean: NODE_TYPE.set,
    category: 3
  },
  {
    name: "Rename Keys",
    type: NODE_TYPE.renamekeys,
    bean: NODE_TYPE.renamekeys,
    category: 3
  },
  {
    name: "Filter",
    type: NODE_TYPE.filter,
    bean: NODE_TYPE.filter,
    category: 3
  },
  {
    name: "Item List",
    type: NODE_TYPE.itemList,
    bean: NODE_TYPE.itemList,
    category: 3
  },
  {
    name: "Playbook",
    type: NODE_TYPE.playbook,
    bean: NODE_TYPE.playbook,
    category: 3
  },
  {
    name: "Mysql",
    type: NODE_TYPE.mysql,
    bean: NODE_TYPE.mysql,
    category: 3
  },
  {
    name: "SSH",
    type: NODE_TYPE.ssh,
    bean: NODE_TYPE.ssh,
    category: 3
  },
  {
    name: "ClickHouse",
    type: NODE_TYPE.clickhouse,
    bean: NODE_TYPE.clickhouse,
    category: 3
  },
  {
    name: "Virustotal",
    type: NODE_TYPE.virustotal,
    bean: NODE_TYPE.virustotal,
    category: 3
  },
  {
    name: "Threatbook Tip",
    type: NODE_TYPE.threatbooktip,
    bean: NODE_TYPE.threatbooktip,
    category: 3
  },
  {
    name: "HUAWEI USG6000V",
    type: NODE_TYPE.huaweiusg6000v,
    bean: 'huawei-firewall',
    category: 3
  },
  {
    name: "HUAWEI USG9000V",
    type: NODE_TYPE.huaweiusg9000v,
    bean: 'huawei-firewall',
    category: 3
  },
  {
    name: "HUAWEI USG6000",
    type: NODE_TYPE.huaweiusg6000,
    bean: 'huawei-firewall',
    category: 3
  },
  {
    name: "HUAWEI USG6000E",
    type: NODE_TYPE.huaweiusg6000E,
    bean: 'huawei-firewall',
    category: 3
  },
  {
    name: "HUAWEI USG9500",
    type: NODE_TYPE.huaweiusg9500,
    bean: 'huawei-firewall',
    category: 3
  },
]


export const CONNECTOR_FLOWCHART_TYPE: ConnectorSpec = {
  type: FlowchartConnector.type,
  options: {
    cornerRadius: 5,
    stub: 30,
    gap: 5
  },
};

export const ENDPOINT_TARGET_TYPE: EndpointSpec = {
  type: "Rectangle",
  options: {
    with: 10,
    height: 20,
    cssClass: 'endpointClass',
    hoverClass: 'endpointHoverClass'
  }
};

export const CONNECTOR_ARROW_OVERLAYS: OverlaySpec[] = [
  {
    type: 'Arrow',
    options: {
      id: OVERLAY_ENDPOINT_ARROW_ID,
      location: 1,
      width: 12,
      foldback: 1,
      length: 10,
      visible: true,
      cssClass: 'arrowClass'
    },
  },
  {
    type: "Custom",
    options: {
      create: (component) => {
        const $template: any = document.getElementById("line_hover_div_template")
        const d = document.createElement("div")
        const lineId = component.id
        d.id = lineId + "_line"
        d.className = "lineBackground";
        d.innerHTML = $template.innerHTML
        const childNodes = d.childNodes;
        childNodes[0].addEventListener("click", () => {
          const elementId = component.endpoints[0].elementId;
          const targetElementId = component.endpoints[1].elementId;
          const element: any = document.getElementById(elementId);
          lineAddNodeParam.value = {
            sourceData: {
              item: {
                x: component.endpoints[0].endpoint.x,
                y: component.endpoints[0].endpoint.y,
                id: elementId,
                endpoint: component.endpoints[0],
                position: [element.offsetLeft + 250, element.offsetTop]
              },
              index: 0
            },
            targetData: {
              item: {
                id: targetElementId,
                endpoint: component.endpoints[1],
              }
            },
            lineId: lineId
          }
          lineAddNodeFlag.value = true;
        })
        childNodes[1].addEventListener("click", () => {
          deleteConnection(component.targetId, lineId);
        });
        d.addEventListener("mouseenter", (e: any) => {
          const id: any = e?.target?.id;
          window.clearTimeout(lineHoverTimeId.value[id]);
        });
        d.addEventListener("mouseleave", (e: any) => {
          const id: any = e?.target?.id;
          lineHoverTimeId.value[id] = window.setTimeout(() => {
            d.style.display = "none";
          }, 200)
        });
        return d
      },
      location: 0.5,
      id: "customOverlay"
    }
  }
];

export const VIEW_CONNECTOR_ARROW_OVERLAYS: OverlaySpec[] = [
  {
    type: 'Arrow',
    options: {
      id: OVERLAY_ENDPOINT_ARROW_ID,
      location: 1,
      width: 12,
      foldback: 1,
      length: 10,
      visible: true,
      cssClass: 'arrowClass'
    },
  }
];

export const CONNECTOR_PAINT_STYLE_DEFAULT: PaintStyle = {
  stroke: '#C6C8D0',
  strokeWidth: 2,
  outlineWidth: 12,
  outlineStroke: 'transparent',
};

export const CONNECTOR_PAINT_STYLE_PRIMARY = {
  ...CONNECTOR_PAINT_STYLE_DEFAULT,
  stroke: '#308CFF',
};
/**
 * 字符串比较符号
 */
export const stringOperation = [
  {
    label: t('common.compare.contains'),
    value: 'contains',
  },
  {
    label: t('common.compare.notContains'),
    value: 'notContains',
  },
  {
    label: t('common.compare.endsWith'),
    value: 'endsWith',
  },
  {
    label: t('common.compare.notEndsWith'),
    value: 'notEndsWith',
  },
  {
    label: t('common.compare.equal'),
    value: 'equal',
  },
  {
    label: t('common.compare.notEqual'),
    value: 'notEqual',
  },
  {
    label: t('common.compare.startsWith'),
    value: 'startsWith',
  },
  {
    label: t('common.compare.notStartsWith'),
    value: 'notStartsWith',
  },
  {
    label: t('common.compare.isEmpty'),
    value: 'isEmpty',
  },
  {
    label: t('common.compare.isNotEmpty'),
    value: 'isNotEmpty',
  },
];
/**
 * 数字比较符号
 */
export const numberOperation = [
  {
    label: t('common.compare.smaller'),
    value: 'smaller',
  },
  {
    label: t('common.compare.smallerEqual'),
    value: 'smallerEqual',
  },
  {
    label: t('common.compare.equal'),
    value: 'equal',
  },
  {
    label: t('common.compare.notEqual'),
    value: 'notEqual',
  },
  {
    label: t('common.compare.larger'),
    value: 'larger',
  },
  {
    label: t('common.compare.largerEqual'),
    value: 'largerEqual',
  },
]
/**
 * 时间比较符号
 */
export const dateTimeOperation = [
  {
    label: t('common.compare.after'),
    value: 'after',
  },
  {
    label: t('common.compare.before'),
    value: 'before',
  },
]
