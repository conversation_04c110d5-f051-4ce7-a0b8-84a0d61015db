import {BrowserJsPlumbInstance, EVENT_ELEMENT_CLICK, newInstance,} from "@jsplumb/browser-ui";
import {
  CONNECTOR_FLOWCHART_TYPE,
  CONNECTOR_PAINT_STYLE_DEFAULT,
  CONNECTOR_PAINT_STYLE_PRIMARY,
  GRID_SIZE,
  NODE_TYPE,
  VIEW_CONNECTOR_ARROW_OVERLAYS
} from "/@/views/soar/utils/nodeViewUtils";
import {ref} from "vue";
import {useNodeDataStore} from "/@/views/soar/utils/workflowCache";

export const jsPlumbInstanceView = ref<BrowserJsPlumbInstance>();

export function workflowInit(container: HTMLElement, options) {
  jsPlumbInstanceView.value = newInstance({
    container: container,
    connector: CONNECTOR_FLOWCHART_TYPE,
    resizeObserver: false,
    endpoint: {
      type: 'Dot',
      options: {
        radius: 9,
        cssClass: 'endpointClass',
      },
    },
    paintStyle: CONNECTOR_PAINT_STYLE_DEFAULT,
    hoverPaintStyle: CONNECTOR_PAINT_STYLE_PRIMARY,
    connectionOverlays: VIEW_CONNECTOR_ARROW_OVERLAYS,
    elementsDraggable: false,
    dragOptions: {
      cursor: 'pointer',
      grid: {"w": GRID_SIZE, "h": GRID_SIZE},
    },
  });
  //节点点击选中
  jsPlumbInstanceView.value.bind(EVENT_ELEMENT_CLICK, (element: Element) => {
    if (options && options.setSelectNode) {
      options.setSelectNode(element);
    }
  });
}

/**
 * 初始化连线
 * @param connections 连线数据
 */
export function connection(line) {
  for (let i = 0; i < line.length; i++) {
    const endpoints: any = jsPlumbInstanceView.value?.selectEndpoints({source: line[i].from})
    const to = line[i].to;
    for (let j = 0; j < endpoints.entries.length; j++) {
      if (to[j]) {
        const points: any = jsPlumbInstanceView.value?.selectEndpoints({target: to[j]});
        if (points && points.entries) {
          jsPlumbInstanceView.value?.connect({
            source: endpoints.entries[j],
            target: points.entries[0],
            anchors: ["Right", "Left"],
            detachable: false
          })
        }
      }
    }
  }
}


/**
 * 根据节点id获取该节点连线前面的所有节点id，不包括本身
 * @param nodeId
 */
export function getViewAllPrevNodes(nodeId) {
  return getPrevNodes(nodeId)
}

/**
 * 迭代，向前找连线的所有节点id
 * @param targetId
 */
function getPrevNodes(targetId) {
  const nodeIds: any = [];
  const lines = jsPlumbInstanceView.value?.getConnections({target: targetId});
  //目前只有一个入，所以只会查到一条线
  if (lines && lines.length > 0) {
    for (let i = 0; i < lines.length; i++) {
      const sourceId = lines[i].sourceId;
      //防止循环节点导致找上一个节点死循环
      if (nodeIds.indexOf(sourceId) > -1) {
        continue;
      }
      nodeIds.push(sourceId);
      const nodeIds2 = getPrevNodes(sourceId)
      nodeIds.push(...nodeIds2);
      //如果有多条线，只取第一条线
      break;
    }
  }
  return nodeIds;
}

function getLineSourceEndpoints(nodeId) {
  const lines = jsPlumbInstanceView.value?.getConnections({target: nodeId});
  if (lines) {
    return lines[0].endpoints[0];
  } else {
    return 0;
  }
}

/**
 * 获取指定节点后面的所有节点
 * @param sourceId 节点id
 * @param flag 获取层级，不写获取所有，true表示只获取下一级节点
 */
function getNextNodes(sourceId, flag?: boolean) {
  if (!sourceId.includes("view_")) {
    sourceId = "view_" + sourceId;
  }
  const nodeIds: any = [];
  const lines = jsPlumbInstanceView.value?.getConnections({source: sourceId});
  if (lines && lines.length > 0) {
    for (let i = 0; i < lines.length; i++) {
      const targetId = lines[i].targetId;
      //防止循环节点导致找上一个节点死循环
      if (nodeIds.indexOf(targetId) > -1) {
        continue;
      }
      nodeIds.push(targetId);
      if (!flag) {
        const nodeIds2 = getNextNodes(targetId)
        nodeIds.push(...nodeIds2);
      }
    }
  }
  return nodeIds;
}

/**
 * 判断目标节点是否在分支节点返回的线路上
 * @param lineIndex 分支节点返回的线下标
 * @param nodeId 分支节点id
 * @param targetId 目标节点id
 */
function isBranchLine(lineIndex, nodeId, targetId) {
  if (!nodeId.includes("view_")) {
    nodeId = "view_" + nodeId;
  }
  const lines = jsPlumbInstanceView.value?.getConnections({source: nodeId});
  let result = false;
  if (lines) {
    let nextId = "";
    for (let i = 0; i < lines.length; i++) {
      if (lines[i].endpoints[0].portId == lineIndex) {
        nextId = lines[i].targetId;
        break;
      }
    }
    if (nextId) {
      const ids: [string] = getNextNodes(nextId);
      result = ids.includes(targetId);
    }
  }
  return result;
}

/**
 * 判断输入数据，if等分支节点
 * @param data
 */
export function getViewInputData(data, id) {
  let inputData;
  if (data?.nodeId) {
    //if节点数据，根据返回线下标判断是否显示数据
    if (data.nodeId.startsWith(NODE_TYPE.if + "_") || data.nodeId.startsWith(NODE_TYPE.switch + "_")) {
      const nextIds: [string] = getNextNodes(data.nodeId, true);
      //是分支节点的下一级节点
      if (nextIds.includes(id)) {
        const endpoint = getLineSourceEndpoints(id);
        if (data?.lineIndex == endpoint.portId) {
          inputData = data?.data;
        } else {
          inputData = "";
        }
      } else {
        //不是分支节点的下一级，需判断是否再分支节点返回的线上
        const flag = isBranchLine(data?.lineIndex, data.nodeId, id);
        if (flag) {
          inputData = data?.data;
        } else {
          inputData = "";
        }
      }
    } else {
      inputData = data?.data;
    }
  }
  return inputData;
}

/**
 * 获取线的源接节点id
 * @param targetId
 */
function getPrevOneNodes(targetId) {
  let nodeId = ""
  const lines = jsPlumbInstanceView.value?.getConnections({target: targetId});
  if (lines && lines.length > 0) {
    // 目前只有一条线
    for (let i = 0; i < lines.length; i++) {
      nodeId = lines[i].sourceId;
      //如果有多条线，只取第一条线
      break;
    }
  }
  return nodeId;

}

export function getViewOutputData(data, lineIndex, id) {
  lineIndex.value = data?.lineIndex ?? 0;
  let outputData;
  let outputData2;
  const prevId = getPrevOneNodes(id);
  //前一个节点是if判断等节点
  if (prevId.startsWith(NODE_TYPE.if + "_")) {
    const endpoint = getLineSourceEndpoints(id);
    const prevData = useNodeDataStore.getNodeData(prevId);
    if (prevData?.lineIndex == endpoint.portId) {
      outputData = data?.data;
      outputData2 = data?.data2;
    } else {
      outputData = "";
      outputData2 = "";
    }
  } else {
    outputData = data?.data;
    outputData2 = data?.viewData;
  }
  return {data: outputData, viewData: outputData2};
}
