<template>
  <a-row>
    <a-col :span="9" class="input_div">
      <InputPanel :id="props.id" />
    </a-col>
    <a-col :span="6">
      <div class="nodeFormDiv">
        <a-form ref="formRef" :model="nodeParams" autocomplete="off" :layout="formLayout">
          <NodeTitle :id="props.id">
            <template #execute v-if="isEdit">
              <a-button type="primary" @click="runNode" :disabled="isRunNode">
                {{ t('workflow.workflow.executeNodes') }}
              </a-button>
            </template>
          </NodeTitle>
          <div class="scrollDiv">
            <a-form-item :label="tPrefix('credential')" name="credentialId" required>
              <div style="display: flex">
                <a-select
                  style="flex-grow: 1"
                  v-model:value="nodeParams.credentialId"
                  @change="credentialChange"
                  :options="credentialOptions"
                  :disabled="!isEdit"
                >
                  <a-select-option value="new">{{ t('workflow.CreateCredential') }}</a-select-option>
                </a-select>
                <div v-if="isEdit && nodeParams.credentialId" style="min-width: 20px; margin-left: 10px; padding-top: 5px">
                  <Icon icon="ant-design:edit-outlined" style="cursor: pointer" @click="showCredentialOptionsDialog" />
                </div>
              </div>
            </a-form-item>
            <a-form-item :label="tPrefix('huaweiname')" :name="['name']" required>
              <a-input v-model:value="nodeParams.name" :placeholder="t('workflow.workflow.pleaseEnterTheName')" />
            </a-form-item>
            <a-form-item :label="tPrefix('action')" :name="['action']" required>
              <div style="display: flex">
                <a-switch v-model:checked="nodeParams.action" :unCheckedValue="false" :checkedValue="true" />
              </div>
            </a-form-item>
            <a-form-item :label="tPrefix('service')" :name="['service']" required>
              <div style="display: flex">
                <a-select v-model:value="nodeParams.service" allowClear>
                  <a-select-option value="tcp">{{ t('workflow.workflow.tcp') }}</a-select-option>
                  <a-select-option value="udp">{{ t('workflow.workflow.udp') }}</a-select-option>
                </a-select>
              </div>
            </a-form-item>
            <a-form-item :label="tPrefix('sourcezone')" :name="['source_zone']" required>
              <div style="display: flex">
                <a-select v-model:value="nodeParams.source_zone" allowClear>
                  <a-select-option value="tcp">{{ t('workflow.workflow.trusted') }}</a-select-option>
                  <a-select-option value="udp">{{ t('workflow.workflow.untrust') }}</a-select-option>
                </a-select>
              </div>
            </a-form-item>
            <a-form-item :label="tPrefix('destinationzone')" :name="['destination_zone']" required>
              <div style="display: flex">
                <a-select v-model:value="nodeParams.destination_zone" allowClear>
                  <a-select-option value="tcp">{{ t('workflow.workflow.trusted') }}</a-select-option>
                  <a-select-option value="udp">{{ t('workflow.workflow.untrust') }}</a-select-option>
                </a-select>
              </div>
            </a-form-item>
            <a-form-item :label="tPrefix('vsys')" :name="['vsys']" required>
              <div style="display: flex">
                <a-input v-model:value="nodeParams.vsys" />
              </div>
            </a-form-item>
          </div>
        </a-form>
      </div>
    </a-col>
    <a-col :span="9" class="output_div" style="padding-left: 16px">
      <OutputPanel :id="props.id" />
    </a-col>
  </a-row>
  <CredentialModal ref="credentialModalRef" @setCredentialId="setCredentialId" />
</template>

<script setup lang="ts">
  import NodeTitle from '/@/views/soar/modular/NodeTitle.vue';
  import { formLayout } from '/@/settings/designSetting';
  import { useI18n } from '/@/hooks/web/useI18n';
  import { inject, reactive, ref } from 'vue';
  import InputPanel from '/@/views/soar/modular/InputPanel.vue';
  import { executeNodes } from '/@/views/soar/utils/util';
  import OutputPanel from '/@/views/soar/modular/OutputPanel.vue';
  import { isRunNode } from '/@/views/soar/utils/workflowCache';
  import { queryDictList } from '/@/views/soar/credential/CredentialConfig.api';
  import CredentialModal from '/@/views/soar/credential/modules/CredentialModal.vue';

  const { t } = useI18n();

  const tPrefix = (name) => {
    return t('workflow.workflow.' + name);
  };

  const props = defineProps({
    id: String,
    prevNodeId: String,
    type: String,
  });

  const formRef = ref();
  const nodeParams: any = inject('nodeParams');
  const isNodeError: any = inject('isNodeError');
  const isEdit: any = inject('isEdit');
  const credentialOptions = ref<any[]>([]);
  const credentialModalRef = ref();

  if (!nodeParams.value.query) {
    nodeParams.value = reactive<any>({});
  }

  //校验
  async function handleValidate() {
    await formRef.value
      .validate()
      .then((value) => {
        console.log(value);
        isNodeError.value = false;
      })
      .catch((error) => {
        console.log(error);
        isNodeError.value = true;
      });
  }

  function credentialChange(value) {
    console.log(value);
    if (value == 'new') {
      credentialModalRef.value.showModal(props.type);
      nodeParams.value.credentialId = '';
    }
  }

  const showCredentialOptionsDialog = () => {
    credentialModalRef.value.showModal(props.type, nodeParams.value.credentialId);
  };

  loadCredentialOptions();

  function loadCredentialOptions() {
    credentialOptions.value = [];
    queryDictList({ credentialType: props.type }).then((data) => {
      console.log(data);
      let list: any = [];
      for (let i in data) {
        list.push({
          label: data[i].credentialName,
          value: data[i].credentialId,
        });
      }
      list.push({
        label: t('workflow.workflow.CreateCredential'),
        value: 'new',
      });
      credentialOptions.value = list;
    });
  }

  function setCredentialId(credentialId) {
    console.log(credentialId);
    loadCredentialOptions();
    nodeParams.value.credentialId = credentialId;
    if (credentialId) {
      formRef.value.clearValidate('credentialId');
    }
  }

  async function runNode() {
    await handleValidate();
    console.log('isNodeError', isNodeError.value);
    if (!isNodeError.value) {
      executeNodes(props.id);
    }
  }

  defineExpose({
    handleValidate,
  });
</script>

<style scoped lang="less">
  textarea {
    height: 300px; /* 设置足够的高度 */
    width: 100%;
  }
  .parameterDiv {
    position: relative;
    padding-left: 20px;
  }
  .parameterDel {
    position: absolute;
    cursor: pointer;
    left: 0;
    top: 2px;
    color: red;
    display: none !important;
  }

  .parameterDiv:hover .parameterDel {
    display: block !important;
  }
</style>
