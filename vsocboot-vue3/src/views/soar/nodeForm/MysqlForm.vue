<template>
  <a-row>
    <a-col :span="9" class="input_div">
      <InputPanel :id="props.id" />
    </a-col>
    <a-col :span="6">
      <div class="nodeFormDiv">
        <a-form ref="formRef" :model="nodeParams" autocomplete="off" :layout="formLayout">
          <NodeTitle :id="props.id">
            <template #execute v-if="isEdit">
              <a-button type="primary" @click="runNode" :disabled="isRunNode">
                {{ t('workflow.workflow.executeNodes') }}
              </a-button>
            </template>
          </NodeTitle>
          <div class="scrollDiv">
            <a-form-item :label="tPrefix('credential')" name="credentialId" required>
              <div style="display: flex">
                <a-select
                  style="flex-grow: 1"
                  v-model:value="nodeParams.credentialId"
                  @change="credentialChange"
                  :options="credentialOptions"
                  :disabled="!isEdit"
                >
                  <a-select-option value="new">{{ t('workflow.CreateCredential') }}</a-select-option>
                </a-select>
                <div v-if="isEdit && nodeParams.credentialId" style="min-width: 20px; margin-left: 10px; padding-top: 5px">
                  <Icon icon="ant-design:edit-outlined" style="cursor: pointer" @click="showCredentialOptionsDialog" />
                </div>
              </div>
            </a-form-item>
            <a-form-item name="operation" :label="tPrefix('operation')">
              <a-select
                v-model:value="nodeParams.operation"
                :disabled="!isEdit || !nodeParams.credentialId"
                @change="changeCredential"
                :options="operationList"
                :fieldNames="{ label: 'label', value: 'value' }"
              >
              </a-select>
            </a-form-item>

            <!-- operation == 'sql' -->
            <a-form-item
              :label="tPrefix('query')"
              v-if="nodeParams.operation == 'sql'"
              :name="['sql']"
              :rules="[{ required: true, message: tPrefix('requiresql') }]"
            >
              <div ref="editorContainer" style="height: 300px; width: 100%" v-if="nodeParams.operation == 'sql'"></div>
              <textarea v-model="nodeParams.sql" style="display: none"></textarea>
            </a-form-item>
            <!-- operation == 'sql' -->

            <!-- operation == 'insert' -->
            <a-form-item
              :label="tPrefix('table')"
              v-if="nodeParams.operation == 'insert'"
              :name="['insert', 'tableName']"
              :rules="[{ required: true, message: tPrefix('requireTableName') }]"
            >
              <div style="display: flex">
                <a-select
                  v-model:value="nodeParams.insert.tableName"
                  :options="tableList"
                  :fieldNames="{ label: 'label', value: 'value' }"
                  @focus="queryList"
                  show-search
                  option-filter-prop="children"
                  :filter-option="filterOption"
                ></a-select
              ></div>
            </a-form-item>

            <a-form-item :label="tPrefix('datamode')" v-if="nodeParams.operation == 'insert'" :name="['insert', 'type']" required>
              <a-select
                v-model:value="nodeParams.insert.type"
                :options="datamodeList"
                :fieldNames="{ label: 'label', value: 'value' }"
                allowClear
              ></a-select>
              <div disabled>
                <a-textarea v-model:value="textValue" :autosize="true" v-if="nodeParams.insert.type == 'auto'" style="cursor: pointer" disabled />
              </div>
            </a-form-item>

            <a-form-item
              name="manually"
              :label="tPrefix('valuestosend')"
              v-if="nodeParams.insert.type == 'manually' && nodeParams.operation == 'insert'"
            >
              <template v-for="(item, index) in nodeParams.insert.manually" :key="index">
                <a-divider dashed v-if="index > 0" />
                <div class="parameterDiv">
                  <Icon
                    icon="ant-design:delete-outlined"
                    class="parameterDel"
                    v-if="isEdit"
                    :title="tPrefix('deleteItem')"
                    @click="delManually(nodeParams.insert.manually, index)"
                  />
                  <a-form-item
                    :label="tPrefix('column')"
                    :name="['insert', 'manually', index, 'col']"
                    :rules="[{ validator: validateAdd, trigger: 'change' }]"
                  >
                    <a-select
                      v-model:value="item.col"
                      :options="colList"
                      :fieldNames="{ label: 'col', value: 'text' }"
                      @change="changeCol()"
                      @focus="queryColumns()"
                      allowClear
                    ></a-select>
                  </a-form-item>
                  <a-form-item
                    :label="tPrefix('value')"
                    :name="['insert', 'manually', index, 'val', 'text']"
                    :rules="[{ validator: validateAdd, trigger: 'change' }]"
                  >
                    <DraggableFixedAndExpression v-model:text="item.val.text" v-model:textType="item.val.textType" :prevNodeId="prevNodeId" />
                  </a-form-item>
                </div>
              </template>
              <div style="margin-bottom: 10px">
                <a-button style="width: 100%" @click="addManually" :disabled="!isEdit">
                  {{ tPrefix('addValue') }}
                </a-button>
              </div>
            </a-form-item>
            <!-- operation == 'insert' -->

            <!-- operation == 'select' -->
            <a-form-item
              :label="tPrefix('table')"
              v-if="nodeParams.operation == 'select'"
              :name="['select', 'tableName']"
              :rules="[{ required: true, message: tPrefix('requireTableName') }]"
            >
              <div style="display: flex"
                ><a-select
                  v-model:value="nodeParams.select.tableName"
                  :options="tableList"
                  :fieldNames="{ label: 'label', value: 'value' }"
                  @focus="queryList"
                  show-search
                  option-filter-prop="children"
                  :filter-option="filterOption"
                ></a-select
              ></div>
            </a-form-item>
            <a-form-item name="allCols" :label="tPrefix('allCols')" v-if="nodeParams.operation == 'select'">
              <a-switch v-model:checked="nodeParams.select.allCols" />
            </a-form-item>
            <a-form-item
              :label="tPrefix('cols')"
              v-if="!nodeParams.select.allCols && nodeParams.operation == 'select'"
              :name="['select', 'colsArr']"
              :rules="[{ validator: validateValue, trigger: 'blur' }]"
            >
              <a-select
                v-model:value="nodeParams.select.colsArr"
                mode="multiple"
                style="width: 100%"
                :placeholder="tPrefix('pleaseSelectColumns')"
                :options="selectAllList"
                :fieldNames="{ label: 'col', value: 'text' }"
                @change="changeCols"
                @focus="querySelectColumns"
              ></a-select>
            </a-form-item>

            <a-form-item name="distinct" :label="tPrefix('distinct')" v-if="nodeParams.operation == 'select'">
              <a-switch v-model:checked="nodeParams.select.distinct" />
            </a-form-item>
            <a-form-item
              :label="tPrefix('limit')"
              v-if="nodeParams.operation == 'select'"
              :name="['select', 'limit']"
              :rules="[{ required: true, message: tPrefix('requireLimit') }]"
            >
              <DraggableFixedAndExpression v-model:text="nodeParams.select.limit" :prevNodeId="prevNodeId" />
            </a-form-item>

            <a-form-item name="where" :label="tPrefix('where')" v-if="nodeParams.operation == 'select'">
              <template v-for="(item, index) in nodeParams.select.where" :key="index">
                <a-divider dashed v-if="index > 0" />
                <div class="parameterDiv">
                  <Icon
                    icon="ant-design:delete-outlined"
                    class="parameterDel"
                    v-if="isEdit"
                    :title="tPrefix('deleteItem')"
                    @click="delSelectCondition(nodeParams.select.where, index)"
                  />
                  <a-form-item
                    :label="tPrefix('column')"
                    :name="['select', 'where', index, 'col']"
                    :rules="[{ validator: validateWhere, trigger: 'change' }]"
                  >
                    <a-select
                      v-model:value="item.col"
                      :options="whereList"
                      :fieldNames="{ label: 'col', value: 'text' }"
                      allowClear
                      @change="changeWhereCol()"
                      @focus="querySelectColumns()"
                    >
                      <template #option="{ type, col }">
                        <span>{{ col }}<br />{{ tPrefix('type')+":" + type }}</span>
                      </template>
                    </a-select>
                  </a-form-item>
                  <a-form-item
                    :label="tPrefix('operator')"
                    :name="['select', 'where', index, 'operation']"
                    :rules="[{ validator: validateWhere, trigger: 'change' }]"
                  >
                    <a-select
                      v-model:value="item.operation"
                      :options="operatorList"
                      :fieldNames="{ label: 'label', value: 'value' }"
                      allowClear
                    ></a-select>
                  </a-form-item>
                  <a-form-item
                    :label="tPrefix('value')"
                    :name="['select', 'where', index, 'val', 'text']"
                    :rules="[{ validator: validateWhere, trigger: 'blur' }]"
                    v-if="item.operation != 'Is Null'"
                  >
                    <DraggableText v-model:text="item.val.text" v-model:textType="item.val.textType" />
                  </a-form-item>
                </div>
              </template>
              <div style="margin-bottom: 10px">
                <a-button style="width: 100%" @click="addSelectCondition" :disabled="!isEdit">
                  {{ tPrefix('addcondition') }}
                </a-button>
              </div>
            </a-form-item>

            <a-form-item
              :label="tPrefix('whereType')"
              v-if="nodeParams.operation == 'select'"
              :name="['select', 'whereType']"
              :rules="[{ validator: validateCombined, trigger: 'blur' }]"
            >
              <a-select
                v-model:value="nodeParams.select.whereType"
                :options="whereTypeList"
                :fieldNames="{ label: 'label', value: 'value' }"
                allowClear
              ></a-select>
            </a-form-item>

            <a-form-item name="order" :label="tPrefix('order')" v-if="nodeParams.operation == 'select'">
              <template v-for="(item, index) in nodeParams.select.order" :key="index">
                <a-divider dashed v-if="index > 0" />
                <div class="parameterDiv">
                  <Icon
                    icon="ant-design:delete-outlined"
                    class="parameterDel"
                    v-if="isEdit"
                    :title="tPrefix('deleteItem')"
                    @click="delOrder(nodeParams.select.order, index)"
                  />
                  <a-form-item
                    :label="tPrefix('column')"
                    :name="['select', 'order', index, 'col']"
                    :rules="[{ validator: validateOrder, trigger: 'change' }]"
                  >
                    <a-select
                      v-model:value="item.col"
                      :options="orderList"
                      :fieldNames="{ label: 'col', value: 'text' }"
                      allowClear
                      @change="changeOrderCol()"
                    >
                    </a-select>
                  </a-form-item>
                  <a-form-item
                    :label="tPrefix('orderType')"
                    :name="['select', 'order', index, 'orderType']"
                    :rules="[{ validator: validateOrder, trigger: 'change' }]"
                  >
                    <a-select
                      v-model:value="item.orderType"
                      :options="orderTypeList"
                      :fieldNames="{ label: 'label', value: 'value' }"
                      allowClear
                    ></a-select>
                  </a-form-item>
                </div>
              </template>
              <div style="margin-bottom: 10px">
                <a-button style="width: 100%" @click="addOrder" :disabled="!isEdit">
                  {{ tPrefix('addsort') }}
                </a-button>
              </div>
            </a-form-item>
            <!-- operation == 'select' -->

            <!-- operation == 'update' -->
            <a-form-item
              :label="tPrefix('table')"
              v-if="nodeParams.operation == 'update'"
              :name="['update', 'tableName']"
              :rules="[{ required: true, message: tPrefix('requireTableName') }]"
            >
              <div style="display: flex"
                ><a-select
                  v-model:value="nodeParams.update.tableName"
                  :options="tableList"
                  :fieldNames="{ label: 'label', value: 'value' }"
                  @focus="queryList"
                  show-search
                  option-filter-prop="children"
                  :filter-option="filterOption"
                ></a-select
              ></div>
            </a-form-item>
            <a-form-item :label="tPrefix('datamode')" v-if="nodeParams.operation == 'update'" :name="['update', 'type']" required>
              <a-select
                v-model:value="nodeParams.update.type"
                :options="datamodeList"
                :fieldNames="{ label: 'label', value: 'value' }"
                allowClear
              ></a-select>
              <a-textarea v-model:value="textValue" :placeholder="tPrefix('basicUsage')" :autosize="true" v-if="nodeParams.update.type == 'auto'" disabled />
            </a-form-item>
            <a-form-item :label="tPrefix('matchCol')" v-if="nodeParams.operation == 'update'" :name="['update', 'matchCol']" required>
              <a-select
                v-model:value="nodeParams.update.matchCol"
                style="width: 100%"
                :placeholder="tPrefix('pleaseSelectColumns')"
                :options="updateAllList"
                :fieldNames="{ label: 'col', value: 'text' }"
                @focus="queryUpdateColumns()"
              ></a-select>
            </a-form-item>
            <a-form-item :label="tPrefix('matchVal')" v-if="nodeParams.operation == 'update'" :name="['update', 'matchVal', 'text']" required>
              <DraggableFixedAndExpression
                v-model:text="nodeParams.update.matchVal.text"
                v-model:textType="nodeParams.update.matchVal.textType"
                :prevNodeId="prevNodeId"
              />
            </a-form-item>
            <a-form-item
              name="manually"
              :label="tPrefix('valuestosend')"
              v-if="nodeParams.update.type == 'manually' && nodeParams.operation == 'update'"
            >
              <template v-for="(item, index) in nodeParams.update.manually" :key="index">
                <a-divider dashed v-if="index > 0" />
                <div class="parameterDiv">
                  <Icon
                    icon="ant-design:delete-outlined"
                    class="parameterDel"
                    v-if="isEdit"
                    :title="tPrefix('deleteItem')"
                    @click="delManually(nodeParams.update.manually, index)"
                  />
                  <a-form-item
                    :label="tPrefix('column')"
                    :name="['update', 'manually', index, 'col']"
                    :rules="[{ validator: validateUpdate, trigger: 'change' }]"
                  >
                    <a-select
                      v-model:value="item.col"
                      :options="updateColList"
                      :fieldNames="{ label: 'col', value: 'text' }"
                      @change="changeCol()"
                      @focus="queryUpdateColumns()"
                      allowClear
                    ></a-select>
                  </a-form-item>
                  <a-form-item
                    :label="tPrefix('value')"
                    :name="['update', 'manually', index, 'val', 'text']"
                    :rules="[{ validator: validateUpdate, trigger: 'change' }]"
                  >
                    <DraggableFixedAndExpression v-model:text="item.val.text" v-model:textType="item.val.textType" :prevNodeId="prevNodeId" />
                  </a-form-item>
                </div>
              </template>
              <div style="margin-bottom: 10px">
                <a-button style="width: 100%" @click="addManually" :disabled="!isEdit">
                  {{ tPrefix('addValue') }}
                </a-button>
              </div>
            </a-form-item>
            <!-- operation == 'update' -->
          </div>
        </a-form>
      </div>
    </a-col>
    <a-col :span="9" class="output_div" style="padding-left: 16px">
      <OutputPanel :id="props.id" />
    </a-col>
  </a-row>

  <CredentialModal ref="credentialModalRef" @setCredentialId="setCredentialId" />
</template>

<script setup lang="ts">
import NodeTitle from '/@/views/soar/modular/NodeTitle.vue';
import {formLayout} from '/@/settings/designSetting';
import {useI18n} from '/@/hooks/web/useI18n';
import {inject, nextTick, onMounted, reactive, ref, watch} from 'vue';
import Icon from '/@/components/Icon';
// import MysqlItemLabel from '/@/views/soar/modular/MysqlItemLabel.vue';
import CredentialModal from '/@/views/soar/credential/modules/CredentialModal.vue';
import {queryDictList} from '/@/views/soar/credential/CredentialConfig.api';
import InputPanel from '/@/views/soar/modular/InputPanel.vue';
import {executeNodes} from '/@/views/soar/utils/util';
import OutputPanel from '/@/views/soar/modular/OutputPanel.vue';
import DraggableFixedAndExpression from '/@/views/soar/modular/DraggableFixedAndExpression.vue';
import DraggableText from '/@/views/soar/modular/DraggableText.vue';
import {isRunNode} from '/@/views/soar/utils/workflowCache';
import {NODE_TYPE} from '/@/views/soar/utils/nodeViewUtils';
import {queryColumn, queryTables} from '../apinode/mysqlNode.api';
import ace from 'ace-builds/src-noconflict/ace';
import 'ace-builds/src-noconflict/mode-sql'; // SQL 语法模式
import 'ace-builds/src-noconflict/theme-dracula'; // 主题
import 'ace-builds/src-noconflict/ext-language_tools'; // 自动补全扩展
import {INSERT, SELECT, UPDATE} from './mysqlForm.type';
import {message} from 'ant-design-vue';

const { t } = useI18n();

  const tPrefix = (name) => {
    return t('workflow.workflow.' + name);
  };
  const textValue = tPrefix('auto');

  const props = defineProps({
    id: String,
    prevNodeId: String,
  });

  const formRef = ref();
  const nodeParams: any = inject('nodeParams');
  const isNodeError: any = inject('isNodeError');
  const isEdit: any = inject('isEdit');

  const listSelectList = ref([
    {
      value: '1',
      label: 'Form list',
    },
    {
      value: '2',
      label: 'Name',
    },
  ]);

  const datamodeList = ref([
    {
      value: 'auto',
      label: 'Auto-Map Input Data to Columns',
    },
    {
      value: 'manually',
      label: 'Map Each Column Manually',
    },
  ]);

  const operationList = ref([
    {
      value: 'insert',
      label: 'Insert',
    },
    {
      value: 'select',
      label: 'Select',
    },
    {
      value: 'update',
      label: 'Update',
    },
    {
      value: 'sql',
      label: 'Sql',
    },
  ]);

  const operatorList = ref([
    {
      value: 'Equal',
      label: 'Equal',
    },
    {
      value: 'Not Equal',
      label: 'Not Equal',
    },
    {
      value: 'Like',
      label: 'Like',
    },
    {
      value: 'Greater Than',
      label: 'Greater Than',
    },
    {
      value: 'Less Than',
      label: 'Less Than',
    },
    {
      value: 'Greater Than Or Equal',
      label: 'Greater Than Or Equal',
    },
    {
      value: 'Less Than Or Equal',
      label: 'Less Than Or Equal',
    },
    {
      value: 'Is Null',
      label: 'Is Null',
    },
  ]);

  const whereTypeList = ref([
    {
      value: 'and',
      label: 'AND',
    },
    {
      value: 'or',
      label: 'OR',
    },
  ]);

  const orderTypeList = ref([
    {
      value: 'asc',
      label: 'ASC',
    },
    {
      value: 'desc',
      label: 'DESC',
    },
  ]);

  //初始化数据
  if (!nodeParams.value.operation) {
    nodeParams.value.operation = 'sql';
  }
  if (!nodeParams.value.insert) {
    nodeParams.value.insert = reactive<INSERT>({
      tableName: undefined,
      type: undefined,
      manually: [],
    });
  }
  if (!nodeParams.value.select) {
    nodeParams.value.select = reactive<SELECT>({
      tableName: undefined,
      allCols: false,
      colsArr: [],
      cols: undefined,
      distinct: false,
      limit: 10,
      where: [],
      whereType: undefined,
      order: [],
    });
  }
  if (!nodeParams.value.update) {
    nodeParams.value.update = reactive<UPDATE>({
      tableName: undefined,
      type: undefined,
      matchCol: undefined,
      matchVal: {
        text: undefined,
        textType: undefined,
      },
      manually: [],
    });
  }

  function changeCols() {
    nodeParams.value.select.cols = nodeParams.value.select.colsArr.join(',');
  }

  watch(nodeParams.value.operation, (newValue) => {
    if (nodeParams.value.operation.getValue() == 'sql') {
      initEdit();
    }
  });

  const editorContainer = ref(null);

  onMounted(() => {
    nodeParams.value.table = '1';

    initEdit();
  });

  function initEdit() {
    // 初始化 Ace Editor
    if (editorContainer.value) {
      let editor = ace.edit(editorContainer.value);
      // 设置编辑器主题和模式
      editor.setTheme('ace/theme/dracula');
      editor.session.setMode('ace/mode/sql');

      // 启用自动补全
      editor.setOptions({
        enableBasicAutocompletion: true,
        enableLiveAutocompletion: true,
      });

      // 设置初始值
      editor.setValue(nodeParams.value.sql, -1);

      // 自定义 SQL 自动补全（可以自定义提示内容）
      const sqlCompleter = {
        getCompletions: function (editor, session, pos, prefix, callback) {
          // 自定义补全内容
          const completions = [
            { value: 'SELECT', meta: 'Keyword' },
            { value: 'FROM', meta: 'Keyword' },
            { value: 'WHERE', meta: 'Keyword' },
            // 可添加更多 SQL 关键字和语法
          ];
          callback(null, completions);
        },
      };

      // 注册自定义补全插件
      ace.require('ace/ext/language_tools').addCompleter(sqlCompleter);

      // 设置光标移动到正确位置的事件
      editor.session.on('changeAnnotation', function () {
        editor.execCommand('startAutocomplete');
      });

      // 监听编辑器内容变化并更新响应式数据属性
      editor.session.on('change', () => {
        nodeParams.value.sql = editor.getValue();
      });

      // 监听响应式数据属性变化并更新编辑器内容
      watch(nodeParams.value.sql, (newValue) => {
        if (editor.getValue() !== newValue) {
          editor.setValue(newValue, -1); // 使用 -1 以确保光标位置不变
        }
      });
    }
  }

  async function handleValidate() {
    await formRef.value
      .validate()
      .then((value) => {
        console.log(value);
        isNodeError.value = false;
      })
      .catch((error) => {
        console.log(error);
        isNodeError.value = true;
      });
  }

  const credentialOptions = ref<any[]>([]);

  loadCredentialOptions();

  function loadCredentialOptions() {
    credentialOptions.value = [];
    queryDictList({ credentialType: NODE_TYPE.mysql }).then((data) => {
      console.log(data);
      let list: any = [];
      for (let i in data) {
        list.push({
          label: data[i].credentialName,
          value: data[i].credentialId,
        });
      }
      list.push({
        label: t('workflow.workflow.CreateCredential'),
        value: 'new',
      });
      credentialOptions.value = list;
    });
  }

  const credentialModalRef = ref();

  function credentialChange(value) {
    console.log(value);
    if (value == 'new') {
      credentialModalRef.value.showModal(NODE_TYPE.mysql);
      nodeParams.value.credentialId = '';
    }
  }

  const showCredentialOptionsDialog = () => {
    credentialModalRef.value.showModal(NODE_TYPE.mysql, nodeParams.value.credentialId);
  };

  function setCredentialId(credentialId) {
    console.log(credentialId);
    loadCredentialOptions();
    nodeParams.value.credentialId = credentialId;
    if (credentialId) {
      formRef.value.clearValidate('credentialId');
    }
  }

  async function runNode() {
    await handleValidate();
    console.log('isNodeError', isNodeError.value);
    if (!isNodeError.value) {
      executeNodes(props.id);
    }
  }

  // 切换编辑器显示的函数
  const changeCredential = () => {
    if (nodeParams.value.operation == 'sql') {
      // 当重新显示编辑器时，等待下一次 DOM 更新后初始化
      nextTick(() => {
        initEdit();
      });
    } else {
      nodeParams.value.table = '2';
    }
  };
  const tableList = ref();
  async function queryList() {
    await queryTables({ id: nodeParams.value.credentialId }).then((data) => {
      let list: any = [];
      for (let i in data) {
        list.push({
          label: data[i],
          value: data[i],
        });
      }
      tableList.value = list;
    });
  }

  const colList = ref();
  const selectAllList = ref<any>([]);
  const whereList = ref<any>([]);
  const orderList = ref<any>([]);
  const updateColList = ref<any>([]);
  const updateAllList = ref<any>([]);
  //用于insert
  async function queryColumns() {
    await queryColumn({ id: nodeParams.value.credentialId, name: nodeParams.value.insert.tableName }).then((data) => {
      let list: any = [];
      for (let i in data) {
        list.push({
          col: data[i].name,
          text: data[i].name,
          disabled: false,
        });
      }
      colList.value = list;
    });
  }
  //用于select
  async function querySelectColumns() {
    await queryColumn({ id: nodeParams.value.credentialId, name: nodeParams.value.select.tableName }).then((data) => {
      let list: any = [];
      whereList.value = [];
      orderList.value = [];
      selectAllList.value = [];
      for (let i in data) {
        list.push({
          col: data[i].name,
          text: data[i].name,
          disabled: false,
        });
        whereList.value.push({
          col: data[i].name,
          text: data[i].name,
          type: data[i].type,
          disabled: false,
        });
        orderList.value.push({
          col: data[i].name,
          text: data[i].name,
          disabled: false,
        });
        selectAllList.value.push({
          col: data[i].name,
          text: data[i].name,
          disabled: false,
        });
      }
    });
  }
  //用于update
  async function queryUpdateColumns() {
    await queryColumn({ id: nodeParams.value.credentialId, name: nodeParams.value.update.tableName }).then((data) => {
      let list: any = [];
      updateColList.value = [];
      updateAllList.value = [];
      for (let i in data) {
        list.push({
          col: data[i].name,
          text: data[i].name,
          disabled: false,
        });
        updateColList.value.push({
          col: data[i].name,
          text: data[i].name,
          disabled: false,
        });
        updateAllList.value.push({
          col: data[i].name,
          text: data[i].name,
          disabled: false,
        });
      }
    });
  }

  //insert的添加和删除
  function addManually() {
    if (nodeParams.value.operation == 'insert') {
      if (!nodeParams.value.insert.tableName) {
        message.warning('Please choose Table!');
        return;
      }
      nodeParams.value.insert.manually.push({
        col: '',
        val: {
          text: '',
          textType: 'fixed',
        },
      });
    }
    if (nodeParams.value.operation == 'update') {
      if (!nodeParams.value.update.tableName) {
        message.warning('Please choose Table!');
        return;
      }
      nodeParams.value.update.manually.push({
        col: '',
        val: {
          text: '',
          textType: 'fixed',
        },
      });
    }
  }

  function delManually(list, index) {
    list.splice(index, 1);
    changeCol();
  }

  //select的添加和删除
  function addSelectCondition() {
    nodeParams.value.select.where.push({
      col: '',
      operation: '',
      val: {
        text: '',
        textType: 'fixed',
      },
    });
  }
  function delSelectCondition(list, index) {
    list.splice(index, 1);
    changeWhereCol();
  }
  function addOrder() {
    nodeParams.value.select.order.push({ col: '', orderType: '' });
  }
  function delOrder(list, index) {
    list.splice(index, 1);
    changeOrderCol();
  }

  function changeCol() {
    if (nodeParams.value.operation == 'insert') {
      colList.value.forEach((e) => {
        if (nodeParams.value.insert.manually.find((item) => e.col === item.col)) {
          e.disabled = true;
        } else {
          e.disabled = false;
        }
      });
    }
    if (nodeParams.value.operation == 'update') {
      updateColList.value.forEach((e) => {
        if (nodeParams.value.update.manually.find((item) => e.col === item.col)) {
          e.disabled = true;
        } else {
          e.disabled = false;
        }
      });
    }
  }

  function changeWhereCol() {
    if (nodeParams.value.operation == 'select') {
      whereList.value.forEach((element) => {
        if (nodeParams.value.select.where.find((p) => element.col === p.col)) {
          element.disabled = true;
        } else {
          element.disabled = false;
        }
      });
      console.log('orderList', orderList.value);
    }
  }

  function changeOrderCol() {
    console.log('orderList111', orderList.value);
    if (nodeParams.value.operation == 'select') {
      orderList.value.forEach((ele) => {
        if (nodeParams.value.select.order.find((o) => ele.col === o.col)) {
          ele.disabled = true;
        } else {
          ele.disabled = false;
        }
      });
    }
  }

  // 自定义模糊过滤函数
  const filterOption = (input, option) => {
    return option.label.toLowerCase().includes(input.toLowerCase());
  };

  //校验
  function validateValue(_rule: any, value: string) {
    if (!nodeParams.value.select.allCols && nodeParams.value.select.colsArr.length == 0) {
      return Promise.reject('Please choose Columns');
    }
    return Promise.resolve();
  }
  function validateCombined(_rule: any, value: string) {
    if (nodeParams.value.select.where.length > 1 && !value) {
      return Promise.reject('Please choose Combine Conditions');
    }
    return Promise.resolve();
  }
  function validateWhere(_rule: any, value: any) {
    if (nodeParams.value.select.where.length > 0 && !value) {
      return Promise.reject('Please enter the value');
    }
    return Promise.resolve();
  }
  function validateOrder(_rule: any, value: any) {
    if (nodeParams.value.select.order.length > 0 && !value) {
      return Promise.reject('Please enter the value');
    }
    return Promise.resolve();
  }
  function validateAdd(_rule: any, value: any) {
    if (nodeParams.value.insert.manually.length > 0 && !value) {
      return Promise.reject('Please enter the value');
    }
    return Promise.resolve();
  }
  function validateUpdate(_rule: any, value: any) {
    if (nodeParams.value.update.manually.length > 0 && !value) {
      return Promise.reject('Please enter the value');
    }
    return Promise.resolve();
  }

  defineExpose({
    handleValidate,
  });
</script>

<style scoped lang="less">
  textarea {
    height: 300px; /* 设置足够的高度 */
    width: 100%;
  }
  .parameterDiv {
    position: relative;
    padding-left: 20px;
  }
  .parameterDel {
    position: absolute;
    cursor: pointer;
    left: 0;
    top: 2px;
    color: red;
    display: none !important;
  }

  .parameterDiv:hover .parameterDel {
    display: block !important;
  }
</style>
