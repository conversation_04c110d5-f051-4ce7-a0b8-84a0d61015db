<template>
  <a-row>
    <a-col :span="9" class="input_div">
      <InputPanel :id="props.id"/>
    </a-col>
    <a-col :span="6">
      <div class="nodeFormDiv">
        <a-form ref="formRef" :model="nodeParams" autocomplete="off" :layout="formLayout">
          <NodeTitle :id="props.id">
            <template #execute v-if="isEdit">
              <a-button type="primary" @click="runNode" :disabled="isRunNode">
                {{ t('workflow.workflow.executeNodes') }}
              </a-button>
            </template>
          </NodeTitle>
          <div class="scrollDiv">
            <a-form-item
              name="mode" required>
              <template #label>
                <MyFormItemLabel
                  :tooltip-title="tPrefix('modeTooltip')"
                  :label="tPrefix('mode')"/>
              </template>
              <a-select v-model:value="nodeParams.mode" :disabled="!isEdit" @change="modeChange">
                <a-select-option value="rule">{{ tPrefix('Rules') }}</a-select-option>
                <a-select-option value="expression">{{ tPrefix('Expression') }}</a-select-option>
              </a-select>
            </a-form-item>

            <template v-if="nodeParams.mode === 'rule'">
              <a-form-item
                name="dataType" required>
                <template #label>
                  <MyFormItemLabel
                    :tooltip-title="tPrefix('dataTypeTooltip')"
                    :label="tPrefix('dataType')"/>
                </template>
                <a-select v-model:value="nodeParams.dataType" @change="dataTypeChange"
                          :disabled="!isEdit">
                  <a-select-option value="str">{{ t('common.String') }}</a-select-option>
                  <a-select-option value="num">{{ t('common.Number') }}</a-select-option>
                  <a-select-option value="dateTime">{{ t('common.DateTime') }}</a-select-option>
                </a-select>
              </a-form-item>
              <template v-if="nodeParams.dataType != 'dateTime'">
                <a-form-item
                  :name="['value1','text']" required>
                  <template #label>
                    <MyFormItemLabel
                      :tooltip-title="tPrefix('value1Tooltip')"
                      :label="tPrefix('value1')"/>
                  </template>
                  <DraggableText
                    v-model:text="nodeParams.value1.text"
                    v-model:textType="nodeParams.value1.textType"/>
                </a-form-item>
              </template>
              <template v-else>
                <a-form-item :name="['value1','text']" required>
                  <template #label>
                    <MyFormItemLabel
                      :tooltip-title="tPrefix('value1Tooltip')"
                      :label="tPrefix('value1')"/>
                  </template>
                  <DraggableDate v-model:text="nodeParams.value1.text"
                                 v-model:textType="nodeParams.value1.textType"/>
                </a-form-item>
                <a-form-item
                  :name="['value1','formatter']"
                  :rules="[{validator: validateFormat,trigger:'blur'}]">
                  <template #label>
                    <MyFormItemLabel
                      :tooltip-title="tPrefix('formatterTooltip')"
                      :label="tPrefix('formatter')"/>
                  </template>
                  <a-input v-model:value="nodeParams.value1.formatter"
                           :placeholder="t('workflow.workflow.example')+':yyyy-MM-dd HH:mm:ss or MM/dd/yyyy HH:mm:ss'"/>
                </a-form-item>
              </template>

              <a-form-item v-if="nodeParams.rules.length>0" :label="tPrefix('RoutingRules')">
                <template v-for="(item,index) in nodeParams.rules" :key="index">
                  <a-divider dashed v-if="index > 0"/>
                  <div class="parameterDiv">
                    <Icon icon="ant-design:delete-outlined" class="parameterDel" v-if="isEdit"
                          :title="t('workflow.workflow.deleteItem')" @click="delValue(nodeParams.rules,index)"/>
                    <a-form-item>
                      <template #label>
                        <MyFormItemLabel
                          :tooltip-title="tPrefix('operationTooltip')"
                          :label="tPrefix('operation')"/>
                      </template>
                      <a-select v-model:value="item.operation" :options="operationOptions"
                                :disabled="!isEdit"/>
                    </a-form-item>
                    <template v-if="nodeParams.dataType != 'dateTime'">
                      <a-form-item :name="['rules',index,'value2','text']" required>
                        <template #label>
                          <MyFormItemLabel
                            :tooltip-title="tPrefix('value2Tooltip')"
                            :label="tPrefix('value2')"/>
                        </template>
                        <DraggableText
                          v-model:text="item.value2.text"
                          v-model:textType="item.value2.textType"/>
                      </a-form-item>
                    </template>
                    <template v-else>
                      <a-form-item :name="['rules',index,'value2','text']" required>
                        <template #label>
                          <MyFormItemLabel
                            :tooltip-title="tPrefix('value2Tooltip')"
                            :label="tPrefix('value2')"/>
                        </template>
                        <DraggableDate v-model:text="item.value2.text"
                                       v-model:textType="item.value2.textType"/>
                      </a-form-item>
                      <a-form-item
                        :name="['rules', index,'value2', 'formatter']"
                        :rules="[{validator: validateFormat,trigger:'blur'}]">
                        <template #label>
                          <MyFormItemLabel
                            :tooltip-title="tPrefix('formatterTooltip')"
                            :label="tPrefix('formatter')"/>
                        </template>
                        <a-input v-model:value="item.value2.formatter"
                                 :placeholder="t('workflow.workflow.example')+':yyyy-MM-dd HH:mm:ss or MM/dd/yyyy HH:mm:ss'"/>
                      </a-form-item>
                    </template>

                    <a-form-item :name="['rules',index,'output']" required>
                      <template #label>
                        <MyFormItemLabel
                          :tooltip-title="tPrefix('outputTooltip')"
                          :label="tPrefix('output')"/>
                      </template>
                      <a-select v-model:value="item.output" :disabled="!isEdit">
                        <a-select-option value="0">0</a-select-option>
                        <a-select-option value="1">1</a-select-option>
                        <a-select-option value="2">2</a-select-option>
                        <a-select-option value="3">3</a-select-option>
                      </a-select>
                    </a-form-item>
                  </div>
                </template>
              </a-form-item>

              <div style="margin-bottom: 10px;width: 100%;">
                <a-button style="width: 100%;" @click="addValue" :disabled="!isEdit">
                  {{ t('workflow.workflow.Add') }}
                </a-button>
              </div>
              <a-form-item name="defaultOutput" required>
                <template #label>
                  <MyFormItemLabel
                    :tooltip-title="tPrefix('defaultOutputTooltip')"
                    :label="tPrefix('defaultOutput')"/>
                </template>
                <a-select v-model:value="nodeParams.defaultOutput.text" :disabled="!isEdit">
                  <a-select-option value="0">0</a-select-option>
                  <a-select-option value="1">1</a-select-option>
                  <a-select-option value="2">2</a-select-option>
                  <a-select-option value="3">3</a-select-option>
                </a-select>
              </a-form-item>
            </template>
            <template v-else>
              <a-form-item :name="['value1','text']" required>
                <template #label>
                  <MyFormItemLabel
                    :tooltip-title="tPrefix('outputTooltip2')"
                    :label="tPrefix('output')"/>
                </template>
                <DraggableText
                  v-model:text="nodeParams.value1.text"
                  v-model:textType="nodeParams.value1.textType"/>
              </a-form-item>
              <a-form-item name="defaultOutput" required>
                <template #label>
                  <MyFormItemLabel
                    :tooltip-title="tPrefix('defaultOutputTooltip')"
                    :label="tPrefix('defaultOutput')"/>
                </template>
                <a-select v-model:value="nodeParams.defaultOutput.text" :disabled="!isEdit">
                  <a-select-option value="0">0</a-select-option>
                  <a-select-option value="1">1</a-select-option>
                  <a-select-option value="2">2</a-select-option>
                  <a-select-option value="3">3</a-select-option>
                </a-select>
              </a-form-item>
            </template>
          </div>
        </a-form>
      </div>

    </a-col>
    <a-col :span="9" class="output_div" style="padding-left: 16px;">
      <OutputPanel :id="props.id" nodeType="switch"/>
    </a-col>
  </a-row>


</template>

<script setup lang="ts">

import NodeTitle from "/@/views/soar/modular/NodeTitle.vue";
import {formLayout} from "/@/settings/designSetting";
import {useI18n} from "/@/hooks/web/useI18n";
import {inject, ref, unref} from "vue";
import Icon from "/@/components/Icon";
import MyFormItemLabel from "/@/views/soar/modular/MyFormItemLabel.vue"
import InputPanel from "/@/views/soar/modular/InputPanel.vue";
import {executeNodes} from "/@/views/soar/utils/util";
import OutputPanel from "/@/views/soar/modular/OutputPanel.vue";
import type {MenuProps} from 'ant-design-vue'
import {checkDateFormat} from "/@/views/soar/workflow/Workflow.api";
import DraggableDate from "/@/views/soar/modular/DraggableDate.vue";
import {handleExpression} from "/@/utils/util";
import {isRunNode} from "/@/views/soar/utils/workflowCache";
import {
  dateTimeOperation,
  numberOperation,
  stringOperation
} from "/@/views/soar/utils/nodeViewUtils";
import DraggableText from "/@/views/soar/modular/DraggableText.vue";


const {t} = useI18n();

const tPrefix = (name) => {
  return t("workflow.switchForm." + name);
}

const props = defineProps({
  id: String
})

const formRef = ref();
const nodeParams: any = inject("nodeParams");
const isNodeError: any = inject("isNodeError");
const isEdit: any = inject("isEdit");

if (!nodeParams.value?.value1) {
  nodeParams.value.value1 = {text: '', textType: 'fixed'};
}
if (!nodeParams.value?.mode) {
  nodeParams.value.mode = 'rule';
}
if (!nodeParams.value?.dataType) {
  nodeParams.value.dataType = 'str';
}
if (!nodeParams.value?.defaultOutput) {
  nodeParams.value.defaultOutput = {text: 0, textType: 'fixed'};
}
if (!nodeParams.value?.rules) {
  nodeParams.value.rules = [];
}


const operationOptions = ref<any>([])
getOption()

function getOption() {
  if (nodeParams.value.dataType === 'num') {
    operationOptions.value = numberOperation;
  } else if (nodeParams.value.dataType === 'str') {
    operationOptions.value = stringOperation;
  } else if (nodeParams.value.dataType === 'dateTime') {
    operationOptions.value = dateTimeOperation;
  } else {
    operationOptions.value = [];
  }
}

function dataTypeChange() {
  nodeParams.value.rules = []
  getOption()
}

const addValue: MenuProps['onClick'] = () => {
  if (!nodeParams.value?.rules) {
    nodeParams.value.rules = [];
  }
  if ('num' == nodeParams.value.dataType) {
    nodeParams.value.rules.push({
      output: '0',
      operation: 'smaller',
      value2: {text: '0', textType: 'fixed'},
    });
  } else if ('str' == nodeParams.value.dataType) {
    nodeParams.value.rules.push({
      output: '0',
      operation: 'equal',
      value2: {text: '0', textType: 'fixed'},
    });
  } else if ('dateTime' == nodeParams.value.dataType) {
    nodeParams.value.rules.push({
      output: '0',
      operation: 'after',
      value2: {text: '', textType: 'fixed', formatter: ''},
    });
  }
};

function delValue(list, index) {
  list.splice(index, 1)
}


async function handleValidate() {
  try {
    await formRef.value.validate();
    isNodeError.value = false;
  } catch (e) {
    console.log(e)
    isNodeError.value = true;
  }
}

async function runNode() {
  await handleValidate()
  console.log('isNodeError', isNodeError.value)
  if (!isNodeError.value) {
    executeNodes(props.id)
  }
}

async function validateFormat(_rule: any, value: string) {
  if (!value) {
    return Promise.reject('Please enter Format');
  }
  const data = unref(nodeParams);
  // field : "dateTime.0.formatter1"
  const field = _rule.field;
  if (field == 'value1.formatter') {
    const flag = await checkDateFormat({date: handleExpression(data.value1.text), format: value})
    if (!flag) {
      return Promise.reject('Time format error');
    }
    return Promise.resolve();
  }
  const arr = field.split(".");
  const o = data[arr[0]][arr[1]];
  let date = o.value2.text;
  const flag = await checkDateFormat({date: handleExpression(date), format: value})
  if (!flag) {
    return Promise.reject('Time format error');
  }
  return Promise.resolve();
}

function modeChange(value) {
  if (value === 'rule') {
    nodeParams.value.defaultOutput = {text: '0', textType: 'fixed'};
  }
}

defineExpose({
  handleValidate
})
</script>


<style scoped lang="less">
.parameterDiv {
  position: relative;
  padding-left: 20px;
}

.parameterDel {
  position: absolute;
  cursor: pointer;
  left: 0;
  top: 2px;
  color: red;
  display: none !important;
}

.parameterDiv:hover .parameterDel {
  display: block !important;
}

.scrollDiv {
  max-height: calc(100vh - 140px);
  overflow-x: hidden;
  overflow-y: auto;
  padding-right: 5px;
}


</style>
