<template>
  <a-row>
    <a-col :span="9" class="input_div">
      <InputPanel :id="props.id" />
    </a-col>
    <a-col :span="6">
      <div class="nodeFormDiv">
        <a-form ref="formRef" :model="nodeParams" autocomplete="off" :layout="formLayout">
          <NodeTitle :id="props.id">
            <template #execute v-if="isEdit">
              <a-button type="primary" @click="runNode" :disabled="isRunNode">
                {{ t('workflow.workflow.executeNodes') }}
              </a-button>
            </template>
          </NodeTitle>
          <div class="scrollDiv">
            <a-form-item name="key" :label="tPrefix('key')" required>
              <a-input v-model:value="nodeParams.key" />
            </a-form-item>
            <a-form-item :label="tPrefix('type')" :name="['type']" required>
              <a-select v-model:value="nodeParams.type" style="width: 100%" :placeholder="t('workflow.workflow.pleaseSelectType')" :options="typeList"></a-select>
            </a-form-item>
            <a-form-item name="query" :label="tPrefix('query')" required>
              <DraggableFixedAndExpression v-model:text="nodeParams.query.text" v-model:textType="nodeParams.query.textType" />
            </a-form-item>
          </div>
        </a-form>
      </div>
    </a-col>
    <a-col :span="9" class="output_div" style="padding-left: 16px">
      <OutputPanel :id="props.id" />
    </a-col>
  </a-row>
</template>

<script setup lang="ts">
import NodeTitle from '/@/views/soar/modular/NodeTitle.vue';
import {formLayout} from '/@/settings/designSetting';
import {useI18n} from '/@/hooks/web/useI18n';
import {inject, reactive, ref} from 'vue';
import InputPanel from '/@/views/soar/modular/InputPanel.vue';
import {executeNodes} from '/@/views/soar/utils/util';
import OutputPanel from '/@/views/soar/modular/OutputPanel.vue';
import DraggableFixedAndExpression from '/@/views/soar/modular/DraggableFixedAndExpression.vue';
import {isRunNode} from '/@/views/soar/utils/workflowCache';
import {Virustotal} from './mysqlForm.type';

const { t } = useI18n();

  const tPrefix = (name) => {
    return t('workflow.workflow.' + name);
  };
  const textValue = tPrefix('auto');

  const props = defineProps({
    id: String,
    prevNodeId: String,
  });

  const formRef = ref();
  const nodeParams: any = inject('nodeParams');
  const isNodeError: any = inject('isNodeError');
  const isEdit: any = inject('isEdit');

  if (!nodeParams.value.query) {
    nodeParams.value = reactive<Virustotal>({
      key: undefined,
      type: undefined,
      query: {
        text: undefined,
        textType: undefined,
      },
    });
  }

  //typeList下拉选
  const typeList = ref([
    {
      value: 'url',
      label: 'URL',
    },
    {
      value: 'domain',
      label: 'DOMAIN',
    },
    {
      value: 'ip',
      label: 'IP',
    },
  ]);

  //校验
  async function handleValidate() {
    await formRef.value
      .validate()
      .then((value) => {
        console.log(value);
        isNodeError.value = false;
      })
      .catch((error) => {
        console.log(error);
        isNodeError.value = true;
      });
  }

  async function runNode() {
    await handleValidate();
    console.log('isNodeError', isNodeError.value);
    if (!isNodeError.value) {
      executeNodes(props.id);
    }
  }

  defineExpose({
    handleValidate,
  });
</script>

<style scoped lang="less">
  textarea {
    height: 300px; /* 设置足够的高度 */
    width: 100%;
  }
  .parameterDiv {
    position: relative;
    padding-left: 20px;
  }
  .parameterDel {
    position: absolute;
    cursor: pointer;
    left: 0;
    top: 2px;
    color: red;
    display: none !important;
  }

  .parameterDiv:hover .parameterDel {
    display: block !important;
  }
</style>
