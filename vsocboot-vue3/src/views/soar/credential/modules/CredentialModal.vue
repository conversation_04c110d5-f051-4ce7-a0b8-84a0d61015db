<template>
  <a-modal v-model:visible="visible" :title="t('workflow.credential.Credential')" @ok="handleOk" :maskClosable="false" @cancel="close">
    <div class="padding16">
      <a-form :model="formModule" ref="formModuleRef" :layout="formLayout" autocomplete="off">
        <a-form-item :label="tPrefix('name')" required name="credentialName">
          <a-input v-model:value="formModule.credentialName" />
        </a-form-item>
        <a-form-item v-if="source === 'credential'" :label="tPrefix('type')" required name="credentialType">
          <JSearchSelect v-model:value="formModule.credentialType" :dict-options="credentialTypeOptions" />
        </a-form-item>
        <template v-if="formModule.credentialType == 'basicAuth'">
          <a-form-item :label="tPrefix('user')" required :name="['data', 'user']">
            <a-input v-model:value="formModule.data.user" />
          </a-form-item>
          <a-form-item :label="tPrefix('pwd')" required :name="['data', 'pwd']">
            <a-input-password v-model:value="formModule.data.pwd" />
          </a-form-item>
        </template>

        <template v-if="formModule.credentialType == NODE_TYPE.mysql">
          <a-form-item :label="tPrefix('host')" required :name="['data', 'host']">
            <a-input v-model:value="formModule.data.host" />
          </a-form-item>
          <a-form-item :label="tPrefix('database')" required :name="['data', 'dbName']">
            <a-input v-model:value="formModule.data.dbName" />
          </a-form-item>
          <a-form-item :label="tPrefix('user')" required :name="['data', 'user']">
            <a-input v-model:value="formModule.data.user" />
          </a-form-item>
          <a-form-item :label="tPrefix('passwd')" required :name="['data', 'passwd']">
            <a-input-password v-model:value="formModule.data.passwd" :visibilityToggle="false" />
          </a-form-item>
          <a-form-item
            :label="tPrefix('port')"
            required
            :rules="[{ required: true, validator: validatePort, trigger: 'change' }]"
            :name="['data', 'port']"
          >
            <a-input v-model:value="formModule.data.port" />
          </a-form-item>
          <a-form-item :label="tPrefix('timeout')" :name="['data', 'connectTimeout']">
            <a-input v-model:value="formModule.data.connectTimeout" />
          </a-form-item>
        </template>

        <template v-if="formModule.credentialType == NODE_TYPE.clickhouse">
          <a-form-item :label="tPrefix('host')" required :name="['data', 'host']">
            <a-input v-model:value="formModule.data.host" />
          </a-form-item>
          <a-form-item :label="tPrefix('database')" required :name="['data', 'dbName']">
            <a-input v-model:value="formModule.data.dbName" />
          </a-form-item>
          <a-form-item :label="tPrefix('user')" required :name="['data', 'user']">
            <a-input v-model:value="formModule.data.user" />
          </a-form-item>
          <a-form-item :label="tPrefix('passwd')" required :name="['data', 'passwd']">
            <a-input-password v-model:value="formModule.data.passwd" :visibilityToggle="false" />
          </a-form-item>
          <a-form-item
            :label="tPrefix('port')"
            required
            :rules="[{ required: true, validator: validatePort, trigger: 'change' }]"
            :name="['data', 'port']"
          >
            <a-input v-model:value="formModule.data.port" />
          </a-form-item>
          <a-form-item :label="tPrefix('timeout')" :name="['data', 'connectTimeout']">
            <a-input v-model:value="formModule.data.connectTimeout" />
          </a-form-item>
        </template>

        <template v-if="formModule.credentialType == NODE_TYPE.ssh">
          <!-- <a-form-item :label="tPrefix('connectusing')" required :name="['data', 'type']">
            <a-radio-group v-model:value="formModule.data.type">
              <a-radio value="passwd">{{ tPrefix('passwd') }}</a-radio>
              <a-radio value="key">{{ tPrefix('privatekey') }}</a-radio>
            </a-radio-group>
          </a-form-item> -->
          <a-form-item :label="tPrefix('host')" required :name="['data', 'host']">
            <a-input v-model:value="formModule.data.host" />
          </a-form-item>
          <a-form-item
            :label="tPrefix('port')"
            required
            :rules="[{ required: true, validator: validatePort, trigger: 'change' }]"
            :name="['data', 'port']"
          >
            <a-input v-model:value="formModule.data.port" />
          </a-form-item>
          <a-form-item :label="tPrefix('user')" required :name="['data', 'user']">
            <a-input v-model:value="formModule.data.user" />
          </a-form-item>
          <a-form-item :label="tPrefix('passwd')" required :name="['data', 'passwd']">
            <a-input-password v-model:value="formModule.data.passwd" :visibilityToggle="false" />
          </a-form-item>
          <!-- <a-form-item :label="tPrefix('privatekey')" required :name="['data', 'key']" v-if="formModule.data.type == 'key'">
            <a-input-password v-model:value="formModule.data.key" :visibilityToggle="false" />
          </a-form-item>
          <a-form-item :label="tPrefix('passphrase')" :name="['data', 'passphrase']" v-if="formModule.data.type == 'key'">
            <a-input-password v-model:value="formModule.data.passphrase" :visibilityToggle="false" />
          </a-form-item> -->
        </template>

        <template v-if="formModule.credentialType == NODE_TYPE.sendEmail">
          <a-form-item :label="tPrefix('user')" required :name="['data', 'user']">
            <a-input v-model:value="formModule.data.user" />
          </a-form-item>
          <a-form-item :label="tPrefix('passwd')" required :name="['data', 'passwd']">
            <a-input-password v-model:value="formModule.data.passwd" />
          </a-form-item>
          <a-form-item :label="tPrefix('host')" required :name="['data', 'host']">
            <a-input v-model:value="formModule.data.host" />
          </a-form-item>
          <a-form-item
            :label="tPrefix('port')"
            required
            :rules="[{ required: true, validator: validatePort, trigger: 'change' }]"
            :name="['data', 'port']"
          >
            <a-input v-model:value="formModule.data.port" />
          </a-form-item>
          <a-form-item :label="tPrefix('encrypt')" :name="['data', 'encrypt']">
            <a-switch v-model:checked="formModule.data.encrypt" checkedValue="1" unCheckedValue="2" />
          </a-form-item>
        </template>

        <template v-if="formModule.credentialType == NODE_TYPE.receiveMail">
          <a-form-item :label="tPrefix('user')" required :name="['data', 'user']">
            <a-input v-model:value="formModule.data.user" />
          </a-form-item>
          <a-form-item :label="tPrefix('passwd')" required :name="['data', 'passwd']">
            <a-input-password v-model:value="formModule.data.passwd" />
          </a-form-item>
          <a-form-item :label="tPrefix('host')" required :name="['data', 'host']">
            <a-input v-model:value="formModule.data.host" />
          </a-form-item>
          <a-form-item
            :label="tPrefix('port')"
            required
            :rules="[{ required: true, validator: validatePort, trigger: 'change' }]"
            :name="['data', 'port']"
          >
            <a-input v-model:value="formModule.data.port" />
          </a-form-item>
          <a-form-item :label="tPrefix('encrypt')" :name="['data', 'encrypt']">
            <a-switch v-model:checked="formModule.data.encrypt" />
          </a-form-item>
          <a-form-item :label="tPrefix('trust')" :name="['data', 'trust']">
            <a-switch v-model:checked="formModule.data.trust" />
          </a-form-item>
        </template>

        <template
          v-if="
            formModule.credentialType == NODE_TYPE.huaweiusg6000v ||
            formModule.credentialType == NODE_TYPE.huaweiusg9000v ||
            formModule.credentialType == NODE_TYPE.huaweiusg6000E ||
            formModule.credentialType == NODE_TYPE.huaweiusg6000 ||
            formModule.credentialType == NODE_TYPE.huaweiusg9500
          "
        >
          <a-form-item :label="tPrefix('url')" required :name="['data', 'url']">
            <a-input v-model:value="formModule.data.url" />
          </a-form-item>
          <a-form-item :label="tPrefix('authorization')" required :name="['data', 'authorization']">
            <a-input v-model:value="formModule.data.authorization" />
          </a-form-item>
        </template>
      </a-form>
    </div>
  </a-modal>
</template>

<script setup lang="ts">
  import { ref, unref, defineEmits } from 'vue';
  import { useI18n } from '/@/hooks/web/useI18n';
  import { formLayout } from '/@/settings/designSetting';
  import { queryById, saveOrUpdate } from '/@/views/soar/credential/CredentialConfig.api';
  import { NODE_TYPE } from '/@/views/soar/utils/nodeViewUtils';
  import { Rule } from 'ant-design-vue/es/form';
  import JSearchSelect from '/@/components/Form/src/jeecg/components/JSearchSelect.vue';
  import { credentialTypeOptions } from '/@/views/soar/credential/CredentialConfig.data';

  const emits = defineEmits(['setCredentialId', 'refTable']);
  const { t } = useI18n();

  const tPrefix = (name) => {
    return t('workflow.credential.' + name);
  };
  defineProps({
    source: String,
  });

  const isUpdate = ref(false);
  const visible = ref(false);
  const formModule = ref<any>({
    credentialId: '',
    credentialName: '',
    data: {},
    credentialType: '',
  });

  let validatePort = async (_rule: Rule, value: number) => {
    if (!value) {
      return Promise.reject('Please input the   port number');
    }
    if (!Number.isInteger(value * 1)) {
      return Promise.reject('Please input the  correct port number');
    }
  };

  const showModal = (type: any, credentialId: any) => {
    formModule.value.credentialType = type;

    visible.value = true;
    console.log('credentialId', credentialId);
    console.log('credentialType', type);
    if (credentialId != null) {
      isUpdate.value = true;
      loadInfo(credentialId);
    } else {
      isUpdate.value = false;
      formModule.value.credentialId = '';
      formModule.value.credentialName = '';
      formModule.value.data = {};
      if (type === NODE_TYPE.sendEmail) {
        formModule.value.data.encrypt = '1';
      } else if (type === NODE_TYPE.receiveMail) {
        formModule.value.data.encrypt = false;
        formModule.value.data.trust = false;
      }
      if (!formModule.value.data.port) {
        formModule.value.data.port = '22';
      }
    }
  };
  const formModuleRef = ref();

  const close = () => {
    visible.value = false;
  };
  const loadInfo = (credentialId) => {
    queryById({ id: credentialId }).then((result) => {
      console.log(result);
      formModule.value = result;
      formModule.value.data = JSON.parse(result.data);
    });
  };

  const handleOk = () => {
    formModuleRef.value
      .validate()
      .then(() => {
        let formData = unref(formModule);

        let data = {
          credentialId: formData.credentialId,
          credentialName: formData.credentialName,
          data: JSON.stringify(formData.data),
          credentialType: formData.credentialType,
        };
        saveOrUpdate(data, isUpdate.value).then((data2) => {
          console.log(data2);
          emits('setCredentialId', data2.credentialId);
          emits('refTable');
          close();
        });
      })
      .catch((error) => {
        console.log(error);
      });
  };

  defineExpose({
    showModal,
  });
</script>

<style scoped lang="less"></style>
