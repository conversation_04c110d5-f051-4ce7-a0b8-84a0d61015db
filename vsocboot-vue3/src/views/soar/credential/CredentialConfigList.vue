<template>
  <div class="page_title">
    <PageTitle />
  </div>
  <div class="padding16">
    <!--引用表格-->
    <!-- <BasicTable @register="registerTable" :rowSelection="rowSelection" :isSearch="isSearch"> -->
    <!--插槽:table标题-->
    <!-- <template #form-formFooter>
        <a-dropdown v-if="selectedRowKeys.length > 0">
          <template #overlay>
            <a-menu>
              <a-menu-item key="1" @click="batchHandleDelete">
                <Icon icon="ant-design:delete-outlined"/>
                {{ t('common.delText') }}
              </a-menu-item>
            </a-menu>
          </template>
          <a-button>{{ t('common.batch') }}
            <Icon icon="mdi:chevron-down"/>
          </a-button>
        </a-dropdown>
        <a-button :class="{'btn-checked': isSearch == true}" type="text" @click="isSearch=!isSearch"
                  preIcon="ant-design:filter-outlined"> {{ t('common.filter') }}
        </a-button>
        <a-button type="primary" @click="handleAdd" preIcon="ant-design:plus-outlined">
          {{ t('common.new') }}
        </a-button>
      </template> -->
    <!--操作栏-->
    <!-- <template #action="{ record }">
        <TableAction :actions="getTableAction(record)"/>
      </template>
    </BasicTable> -->
    <div class="header">
      <BasicForm style="flex: 1" @register="registerForm" @submit="handleSubmit" :isSearch="true" :layout="formLayout" />
      <a-button class="" type="primary" @click="handleAdd" >  <span class="soc ax-com-Add ax-icon"></span> {{ t('common.new') }}</a-button>
    </div>
    <a-spin :spinning="spinning">
      <div style="display: flex; flex-wrap: wrap">
        <div style="flex: 0 0 20%; padding: 8px" v-for="(item, index) in dataList" :key="item.credentialId">
          <div class="workflow_block">
            <div class="cred-body">
              <div class="cred-img" style="margin-right: 12px"><img src="./credit.png" alt="" style="width: 34px; height: 34px" /></div>
              <div class="cred-content">
                <div style="margin-bottom: 8px; font-size: 13px; font-weight: 600">{{ item.credentialName }}</div>
                <div style="font-size: 12px; font-weight: normal" class="cred-word">
                  <span>{{ item.credentialType }}</span>
                  <a-divider style="border-left-color: #ffffff" type="vertical" />
                  <span style="margin-right: 8px">{{ t('workflow.credential.lastUpdate') }}</span>
                  <span>{{ item.updatedAt }}</span>
                  <a-divider style="border-left-color: #ffffff" type="vertical" />
                  <span style="margin-right: 8px">{{ t('workflow.credential.created') }}</span>
                  <span>{{ item.createdAt }}</span>
                </div>
              </div>
            </div>
            <div style="text-align: right; margin-top: 24px">
              <a-space>
                <a-button @click="handleEdit(item)">
                  {{ t('workflow.WorkflowConfig.open') }}
                </a-button>
                <a-button @click="handleDelete(item)">{{ t('common.delText') }}</a-button>
              </a-space>
            </div>
          </div>
        </div>
        <div v-if="dataList.length === 0" style="text-align: center; width: 100%">
          {{ t('common.noData') }}
        </div>
      </div>
    </a-spin>
    <CredentialModal ref="credentialModalRef" source="credential" @refTable="loadList" />
  </div>
</template>

<script lang="ts" name="soarCredentialConfig-soarCredentialConfigEntity" setup>
  import { ref, onMounted } from 'vue';
  import PageTitle from '/@/components/Menu/src/components/PageTitle.vue';
  import { searchFormSchema } from './CredentialConfig.data';
  import { list, deleteOne } from './CredentialConfig.api';
  import { useI18n } from '/@/hooks/web/useI18n';
  import BasicForm from '/@/components/Form/src/BasicForm.vue';
  import { formLayout } from '/@/settings/designSetting';
  import { useForm } from '/@/components/Form';
  import CredentialModal from '/@/views/soar/credential/modules/CredentialModal.vue';
  import { Modal } from 'ant-design-vue';
  import { throttle } from 'lodash-es';

  const { t } = useI18n();
  // const isSearch = ref<boolean>(true);
  const dataList = ref<any>([]);
  const spinning = ref<boolean>(false);
  const [registerForm, { getFieldsValue }] = useForm({
    schemas: searchFormSchema,
    //显示操作按钮
    showActionButtonGroup: false,
    rowProps: { gutter: 8 },
    baseColProps: {
      xs: 24, // <576px
      sm: 12, // ≥576px
      md: 8, // ≥768px
      lg: 6, // ≥992px
      xl: 4, // ≥1200px
      xxl: 3, // ≥1600px
    },
  });
  const handleSubmit = throttle(() => {
    loadList();
  }, 1000);
  function loadList() {
    spinning.value = true;
    const params = getFieldsValue();
    console.log(getFieldsValue(), ' getFieldsValue');
    params.pageSize = 999;
    list(params).then((data) => {
      console.log(data);
      dataList.value = data.records;
      spinning.value = false;
    });
  }
  onMounted(() => {
    loadList();
  });
  const credentialModalRef = ref();

  /**
   * 新增事件
   */
  function handleAdd() {
    credentialModalRef.value.showModal();
  }

  /**
   * 编辑事件
   */
  function handleEdit(record: Recordable) {
    credentialModalRef.value.showModal('', record.credentialId);
  }

  /**
   * 删除事件
   */
  async function handleDelete(record) {
    Modal.confirm({
      title: t('common.delText'),
      content: t('common.delConfirmText'),
      okText: t('common.okText'),
      cancelText: t('common.cancelText'),
      wrapClassName: 'delete_confirm',
      onOk: () => {
        deleteOne({ id: record.credentialId }, loadList);
      },
    });
    // await deleteOne({ id: record.credentialId }, loadList);
  }
</script>
<style scoped lang="less">
  .workflow_block {
    padding: 16px;
    border: 1px solid @border-color;
    background-color: @dark-bg2;
    width: 350px;
    .mb16 {
      margin-bottom: 16px;
    }
  }
  .header {
    display: flex;
    justify-content: space-between;
  }
  .cred-body {
    display: flex;
  }
  .cred-word {
    span {
      white-space: nowrap;
      // margin-right: 5px;
    }
  }
</style>
