import { defHttp } from '/@/utils/http/axios';

enum Api {
  loadSystemInfo = '/systemInfo/loadSystemInfo',
  loadAppTaskSystemInfo = '/systemInfo/loadAppTaskSystemInfo',
  loadProxySystemInfo = '/systemInfo/loadProxySystemInfo',
  getServiceList = '/nacos/monitor/serviceList',
  getServiceInstances = '/nacos/monitor/serviceInstances',
  getVersionInfo = '/nacos/monitor/version',
  getMetricsInfo = '/nacos/monitor/metrics',
}

//web系统
export const loadSystemInfo = () =>
  defHttp.get({
    url: Api.loadSystemInfo,
  });
/**
 * app和task
 * @param params
 */
export const loadAppTaskSystemInfo = (params) =>
  defHttp.get({
    url: Api.loadAppTaskSystemInfo,
    params,
  });
/**
 * proxy
 * @param params
 */
export const loadProxySystemInfo = (params) =>
  defHttp.get({
    url: Api.loadProxySystemInfo,
    params,
  });
/**
 * 获取服务列表
 * @param params
 */
export const getServiceList = (params) =>
  defHttp.get({
    url: Api.getServiceList,
    params,
  });

/**
 * 获取服务实例列表
 * @param params
 */
export const getServiceInstances = (params) =>
  defHttp.get({
    url: Api.getServiceInstances,
    params,
  });
/**
 * 获取版本信息
 * @param params
 */
export const getVersionInfo = (params) =>
  defHttp.get({
    url: Api.getVersionInfo,
    params,
  });

/**
 * 获取指标信息
 * @param params
 */
export const getMetricsInfo = (params) =>
  defHttp.get({
    url: Api.getMetricsInfo,
    params,
  });
