<template>
  <div ref="chartRef" :style="{ height, width }"></div>
</template>
<script lang="ts">
import {defineComponent, PropType, ref, Ref, reactive, watchEffect} from 'vue';
import {useECharts} from '/@/hooks/web/useECharts';
import {GaugeChart} from 'echarts/charts';
import {cloneDeep} from 'lodash-es';

export default defineComponent({
  name: 'ChartGauge',
  props: {
    chartData: {
      type: Object as PropType<Object>,
      default: () => [] as PropType<Object>,
    },
    option: {
      type: Object,
      default: () => ({}),
    },
    width: {
      type: String as PropType<string>,
      default: '100%',
    },
    height: {
      type: String as PropType<string>,
      default: 'calc(100vh - 78px)',
    },
  },
  setup(props) {
    const chartRef = ref<HTMLDivElement | null>(null);
    const {setOptions, echarts} = useECharts(chartRef as Ref<HTMLDivElement>);
    const option: any = reactive({
      title: {
        text: '',
        textStyle: {
          color: '#fff'
        }
      },
      series: [
        {
          type: 'gauge',
          progress: {
            show: true,
            width: 10
          },
          axisLine: {
            lineStyle: {
              width: 10
            }
          },
          axisTick: {
            show: false
          },
          splitLine: {
            length: 5,
            lineStyle: {
              width: 2,
              color: '#999'
            }
          },
          axisLabel: {
            distance: 15,
            color: '#999',
            fontSize: 12
          },
          anchor: {
            show: true,
            showAbove: true,
            size: 12,
            itemStyle: {
              borderWidth: 2
            }
          },
          title: {
            show: false
          },
          detail: {
            valueAnimation: true,
            fontSize: 18,
            offsetCenter: [0, '70%']
          },
          data: [
            {
              value: 70
            }
          ]
        }
      ]
    });

    watchEffect(() => {
      props.chartData && initCharts();
    });

    function initCharts() {
      echarts.use(GaugeChart);
      if (props.option) {
        Object.assign(option, cloneDeep(props.option));
      }
      option.series[0].data[0].value = props.chartData['value'];
      option.title.text =  props.chartData['title']
      setOptions(option);
    }

    return {chartRef};
  },
});
</script>
