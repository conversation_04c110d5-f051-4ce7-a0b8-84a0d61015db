<template>
  <div class="page_title">
    <page-title/>
  </div>
  <div class="pl-16px">
    <a-row style="padding-top: 10px">
      <a-col :span="6" class="top_div">
        <a-row style="height: 30px">
          {{ tp('ProportionSeverity') }}
        </a-row>
        <div ref="chart1Ref" style="width: 100%;height: 150px"></div>
      </a-col>
      <a-col :span="6" class="top_div">
        <a-row style="height: 30px">
          {{ tp('QuantityTrend') }}
        </a-row>
        <div ref="chart2Ref" style="width: 100%;height: 150px"></div>
      </a-col>
      <a-col :span="6" class="top_div">
        <a-row style="height: 30px">
          {{ tp('MeanTime') }}
        </a-row>
        <a-row style="width: 100%;height: 150px">
          <a-col style="flex: 0 0 150px">
            <div class="status_div">
              <div class="border_div">
                <div class="image"></div>
              </div>
            </div>
          </a-col>
          <a-col style="flex: 0 0 calc(100% - 150px)">
            <div style="height: 50%;font-size: 24px;font-weight: 600;">
              <div style="translate: 0% 35px;">
                {{ avgTime }}
                <span v-if="avgFlag == '1'" style="color: #F75555">
                  <Icon icon="ant-design:arrow-up-outlined"/>
                  <span style="font-size: 14px">
                    {{ avgTime2 }}
                  </span>
                </span>
                <span v-if="avgFlag == '2'" style="color: #00ff00">
                  <Icon icon="ant-design:arrow-down-outlined"/>
                  <span style="font-size: 14px">
                    {{ avgTime2 }}
                  </span>
                </span>

              </div>
            </div>
            <div style="height: 50%">
              {{ tp('MeanTimeTip') }}
            </div>
          </a-col>
        </a-row>
      </a-col>
      <a-col :span="6" class="top_div">
        <a-row style="height: 30px">
          {{ tp('KeyIndicators') }}
        </a-row>
        <a-row style="width: 100%;height: 150px">
          <a-col class="top_right_div">
            <div class="CriticalClass">{{ t('common.Critical') }}</div>
            <div style="font-size: 16px;font-weight: 600;">{{ SeverityData?.Critical ?? 0 }}</div>
          </a-col>
          <a-col class="top_right_div">
            <div class="HighClass">{{ t('common.High') }}</div>
            <div style="font-size: 16px;font-weight: 600;">{{ SeverityData?.High ?? 0 }}</div>
          </a-col>
          <a-col class="top_right_div">
            <div class="MediumClass">{{ t('common.Medium') }}</div>
            <div style="font-size: 16px;font-weight: 600;">{{ SeverityData?.Medium ?? 0 }}</div>
          </a-col>
          <a-col class="top_right_div">
            <div class="LowClass">{{ t('common.Low') }}</div>
            <div style="font-size: 16px;font-weight: 600;">{{ SeverityData?.Low ?? 0 }}</div>
          </a-col>
          <a-col class="top_right_div">
            <div class="InformationClass">{{ t('common.Information') }}</div>
            <div style="font-size: 16px;font-weight: 600;">{{
                SeverityData?.Information ?? 0
              }}
            </div>
          </a-col>
        </a-row>
      </a-col>
    </a-row>
    <!--引用表格-->
    <BasicTable @register="registerTable" :rowSelection="rowSelection as any" :isSearch="isSearch">
      <!--插槽:table标题-->
      <template #form-formFooter>
        <a-dropdown v-if="selectedRowKeys.length > 0 && hasPermission('investigation:del')">
          <template #overlay>
            <a-menu>
              <a-menu-item key="1" @click="batchHandleDelete">
                <Icon icon="ant-design:delete-outlined"/>

                {{ t('common.delText') }}
              </a-menu-item>
            </a-menu>
          </template>
          <a-button>
            {{ t('common.batch') }}
            <Icon icon="mdi:chevron-down"/>
          </a-button>
        </a-dropdown>
<!--        <a-button :class="{'btn-checked': isSearch == true}" type="text" @click="isSearch=!isSearch"-->
<!--                  preIcon="ant-design:filter-outlined"> {{ t('common.filter') }}-->
<!--        </a-button>-->
        <a-button type="primary" @click="handleAdd"
                  v-if="hasPermission('investigation:add')">
          <span class="soc ax-com-Add ax-icon"></span>{{ t('common.add') }}
        </a-button>
      </template>
      <template #userInfo="{ record }">
        <UserName :record="record"/>
      </template>
      <template #severity="{ text }">
        <Severity :value="text"/>
      </template>
      <template #status="{ text }">
        <span
          :class="{'CriticalClass':text=='Pending','textColorClass':text=='Processing',
          'closedClass':text=='Closed'}">
          {{ t('common.' + text) }}
        </span>
      </template>
      <!--操作栏-->
      <template #action="{ record }">
        <a-space size="5" class="action-border">
          <span @click="handleOpen(record)">{{ t('routes.investigation.open') }}</span>

          <template v-if="hasPermission('investigation:del')">
            <a-divider type="vertical"/>
            <span @click="handleDelete(record)">
              {{ t('common.delText') }}
            </span>
          </template>

          <template v-if="hasPermission('investigation:close')">
            <a-divider type="vertical"/>
            <span @click="handleClose(record)" v-if="record?.status != 'Closed'">
              {{ t('routes.investigation.close.title') }}
            </span>
            <span @click="handleClose(record)" v-if="record?.status == 'Closed'">
              {{ t('routes.investigation.close.title2') }}
            </span>
          </template>
          <template v-if="hasPermission('ticket:useinternel-2') ||
                          hasPermission('ticket:useSS-2') ||
                          hasPermission('ticket:useinternel-1') ||
                          hasPermission('ticket:useIssued-1')">
            <a-divider type="vertical" v-if="workflowList.length > 0"/>
            <a-dropdown v-if="workflowList.length > 0">
              <span>{{ t('routes.investigation.ticketsBtn') }}</span>
              <template #overlay>
                <a-menu>
                  <ApplyMenu v-model:workflowList="workflowList" :type="1" :record="record"/>
                </a-menu>
              </template>
            </a-dropdown>
          </template>


        </a-space>

      </template>
    </BasicTable>

    <closeModules @register="registerCloseModule" @saveClose="reload"/>
  </div>
</template>

<script lang="ts" name="investigation-investigationVO" setup>
import {useRoute, useRouter} from 'vue-router';
import {formLayout} from '/@/settings/designSetting';
import {onMounted, Ref, ref} from 'vue';
import {BasicTable} from '/@/components/Table';
import {useListPage} from '/@/hooks/system/useListPage'
import {getColumns, searchFormSchema} from './InvestigationVO.data';
import {
  batchDelete,
  deleteOne,
  list,
  loadInveStatis,
  loadInvestigationSeverity,
  loadInvestigationTimeDiff,
} from './InvestigationVO.api';
import {useI18n} from "/@/hooks/web/useI18n";
import {Modal} from 'ant-design-vue';
import UserName from "/@/components/vsoc/UserName.vue";
import {useModal} from "/@/components/Modal";
import {useECharts} from "/@/hooks/web/useECharts";
import {EChartsOption} from "echarts";
import dayjs from "dayjs";
import {queryEntryTicket} from "/@/views/workflow/view/ts/TicketUtils";
import ApplyMenu from "/@/views/workflow/view/ApplyMenu.vue";
import closeModules from '/@/views/situationPerception/modules/closeModules.vue'
import {usePermission} from "/@/hooks/web/usePermission";
import {TABLE_CACHE_KEY} from "/@/utils/valueEnum";
import {getHistoryParamByRoute, setHistoryParamByRoute} from "/@/utils/ckTable";
import PageTitle from "/@/components/Menu/src/components/PageTitle.vue";
import Severity from "/@/components/Severity/Severity.vue";

const {hasPermission} = usePermission();
const router = useRouter();
const route = useRoute();

const {t} = useI18n();

function tp(name) {
  return t('routes.investigation.' + name);
}

const isSearch = ref(true);
if (!route.query.statusStr) {
  route.query.statusStr = "";
}
for (let i in route.query) {
  for (let j = 0; j < searchFormSchema.length; j++) {
    if (i == searchFormSchema[j].field) {
      searchFormSchema[j].defaultValue = route.query[i];
      break;
    }
  }
}
// begin 获取跳转到详情页时的查询条件 2024-08-02
const historyRoutePath = "/situationPerception/situationNewPerception";
const historyParam = getHistoryParamByRoute(historyRoutePath);
let formConfigModel: any = {}
if (historyParam) {
  //给table的查询表单赋值
  formConfigModel = historyParam;
}
//end 获取跳转到详情页时的查询条件 2024-08-02
//注册table数据
const {tableContext} = useListPage({
  tableProps: {
    title: '调查',
    api: list,
    columns: getColumns(),
    canResize: false,
    formConfig: {
      model: formConfigModel,
      baseColProps: {
        lg: 6, // ≥992px
        xl: 4, // ≥1200px
        xxl: 3, // ≥1600px
      },
      labelCol: {
        xs: 24,
        sm: 8,
        md: 24,
        lg: 24,
        xl: 24,
        xxl: 24,
      },
      schemas: searchFormSchema,
      autoSubmitOnEnter: true,
      showAdvancedButton: true,
      layout: formLayout,
    },
    actionColumn: {
      width: 270,
    },
    tableSetting: {
      cacheKey: TABLE_CACHE_KEY.investigation
    },
    pagination: {
      current: historyParam?.pageNo,
      defaultCurrent: historyParam?.pageNo,
      defaultPageSize: historyParam?.pageSize
    },
    afterFetch: (data) => {
      return data
    }
  },
})

const [registerTable, {reload, getForm, getPaginationRef}, {
  rowSelection,
  selectedRowKeys
}] = tableContext
//工单列表
const workflowList: any = ref([]);
onMounted(() => {
  getTickets();
})

/**
 * 获取entry ticket
 */
async function getTickets() {
  workflowList.value = await queryEntryTicket(1);
  console.log('workflowList.value---------->', workflowList.value)
}


/**
 * 新增事件
 */
function handleAdd() {
  router.push("/investigation/modules/InvestigationNewModal");
}


/**
 * 详情
 */
function handleOpen(record: Recordable) {
  let query = {id: record.id, name: record.investigation}
  sessionStorage.setItem("investigationInfoJson", JSON.stringify(query))
  const historyData = getForm().getFieldsValue();
  const pagination: any = getPaginationRef();
  if (!(pagination instanceof Boolean)) {
    historyData.pageNo = pagination?.current;
    historyData.pageSize = pagination?.pageSize;
  }
  console.log("historyData", historyData)
  setHistoryParamByRoute(historyData);
  router.push({
    path: "/situationPerception/situationNewPerception",
    query: query
  });
}

/**
 * 删除事件
 */
async function handleDelete(record) {
  // await deleteOne({id: record.id}, reload);
  Modal.confirm({
    title: t('common.delText'),
    content: t('common.delConfirmText') + "?",
    okText: t('common.delText'),
    cancelText: t('common.cancelText'),
    wrapClassName: 'delete_confirm',
    onOk: () => {
      deleteOne({id: record.id}, reload);
    }
  });
}

/**
 * 批量删除事件
 */
async function batchHandleDelete() {
  batchDelete({ids: selectedRowKeys.value}, reload);
}

const [registerCloseModule, {openModal}] = useModal();

function handleClose(data) {

  let param: any = {
    id: data.id,
    status: data.status
  }
  openModal(true, param);
}

loadInvestigationSeverityData()

const chart1Ref = ref<HTMLDivElement | null>(null);
const {setOptions} = useECharts(chart1Ref as Ref<HTMLDivElement>);

function loadInvestigationSeverityData() {
  loadInvestigationSeverity().then((data) => {
    let seriesData: any = [];
    let colors = {
      'critical': '#F75555',
      'high': '#FB7E52',
      'medium': '#F8A556',
      'low': '#F6C84D',
      'information': '#439EF6',
    }
    let total = 0
    let map = {}
    for (let i in data) {
      total += data[i].num
      map[t('common.' + data[i].severity)] = data[i].num
    }
    let legendData: any = [];
    for (let i in data) {
      seriesData.push({
        value: data[i].num,
        name: t('common.' + data[i].severity),
        itemStyle: {
          color: colors[data[i].severity.toLocaleLowerCase()]
        }
      })

      legendData.push({
        name: t('common.' + data[i].severity),
        textStyle: {
          color: colors[data[i].severity.toLocaleLowerCase()]
        }
      })
    }
    let option: EChartsOption = {
      tooltip: {
        trigger: 'item',
        formatter: '{b} : {c} ({d}%)',
        confine: true
      },
      legend: {
        data: legendData,
        orient: 'vertical',
        right: 10,
        bottom: 10,
        formatter: (name) => {
          return name + " " + (map[name] * 100 / total).toFixed(2) + "%"
        },
      },
      series: [
        {
          name: '',
          type: 'pie',
          center: ['40%', '50%'],
          data: seriesData,
          label: {
            show: false,
            color: '#fff'
          },
        }
      ]
    };
    setOptions(option);

  })
}

loadInvestigationTimeDiffData()
const avgTime = ref("")
const avgTime2 = ref("")
const avgFlag = ref("")

function loadInvestigationTimeDiffData() {
  loadInvestigationTimeDiff().then(data => {
    let size = data.length
    if (size > 1) {
      let endSize = Math.ceil(size / 2)
      let total1 = 0
      let total2 = 0
      for (let i = 0; i < endSize; i++) {
        total1 += data[i].num
      }
      for (let i = endSize; i < size; i++) {
        total2 += data[i].num
      }
      let avg1 = total1 / endSize
      let avg2 = total2 / (size - endSize)
      let text = calcTime(avg1, 1, "")
      let units = [t('common.sec'), t('common.min'), t('common.hour'), t('common.day')]
      if (text) {
        let array = text.split(",")
        text = ""
        let length = array.length
        if (length < 2) {
          length = 2
        }
        for (let i = length - 2; i < array.length; i++) {
          if (array[i] != '0') {
            text = array[i] + units[i] + text
          }
        }
      }
      avgTime.value = text
      let text2 = ""
      if (avg1 > avg2) {
        avgFlag.value = "1"
        text2 = calcTime(avg1 - avg2, 1, "")
      } else {
        avgFlag.value = "2"
        text2 = calcTime(avg2 - avg1, 1, "")
      }

      if (text2) {
        let array = text2.split(",")
        text2 = ""
        let length = array.length
        if (length < 2) {
          length = 2
        }
        for (let i = length - 2; i < array.length; i++) {
          if (array[i] != '0') {
            text2 = array[i] + units[i] + text2
          }
        }
      }
      avgTime2.value = text2
    }
  })
}

function calcTime(val, unit, text) {
  //unit 1秒，2分，3小时，4天
  if (unit == 4) {
    return text + val
  }
  let units = {
    1: 60,
    2: 60,
    3: 24
  }
  let num = units[unit]
  if (val >= num) {
    text = text + val % num + ","
    val = Math.floor(val / num)
    text = calcTime(val, unit + 1, text)
    return text
  } else {
    return text + val
  }
}

loadInvestigationSeverityData2()
const SeverityData = ref<any>({})

function loadInvestigationSeverityData2() {
  loadInvestigationSeverity().then(data => {
    for (let i in data) {
      SeverityData.value[data[i].severity] = data[i].num
    }
  })
}

loadInveStatisData()

const chart2Ref = ref<HTMLDivElement | null>(null);
const {setOptions: setOptions2} = useECharts(chart2Ref as Ref<HTMLDivElement>);

function loadInveStatisData() {
  let start = dayjs().subtract(7, 'day').format('YYYY-MM-DD HH:mm:ss')
  loadInveStatis({
    statisticalDate_begin: start,
    type: 1,
    column: 'statisticalDate',
    order: "asc"
  }).then(data => {
    console.log(data)

    let xAxisData: any = []
    let seriesData1: any = []
    let seriesData2: any = []
    for (let i in data) {
      xAxisData.push(data[i].statisticalDate)
      seriesData1.push(data[i].pendingNum)
      seriesData2.push(data[i].processingNum)
    }
    let option: EChartsOption = {
      tooltip: {
        trigger: "axis",

      },
      grid: {
        top: 20,
        left: 20,
        right: 20,
        bottom: 0,
        containLabel: true
      },
      xAxis: {
        type: 'category',
        data: xAxisData
      },
      yAxis: {
        type: 'value'
      },
      series: [
        {
          name: "pending",
          data: seriesData1,
          type: 'bar',
          stack: '1',
          barMaxWidth: 20,
        },
        {
          name: "processing",
          data: seriesData2,
          type: 'bar',
          stack: '1',
          barMaxWidth: 20,
        }
      ]
    }
    setOptions2(option)

  })
}

</script>


<style lang="less" scoped>
:deep(.ant-picker-range) {
  width: 100%;
}

.user_wraper {
  display: flex;
  align-items: center;
  justify-content: left;

  img {
    width: 24px;
    height: 24px;
    border-radius: 50%;

    &:not(:first-child) {
      margin-left: 5px;
    }
  }

  .members-number {
    min-width: 24px;
    height: 24px;
    border-radius: 12px;
    background-color: #f1c40f;
    color: #fff;
    text-align: center;
    padding: 0 5px;

    &:not(:first-child) {
      margin-left: 5px;
    }
  }
}

.avatar_wrapper_img {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background: @primary-color;
  display: flex;
  align-items: center;
  justify-content: center;

  &:not(:first-child) {
    margin-left: 5px;
  }

  .img_title {
    font-size: 12px;

  }
}

:deep(.soc-basic-table-form-container .searchForm) {
  background-color: transparent;
}

.status_div {
  width: 150px;
  height: 150px;
  padding: 35px;

  .border_div {
    width: 80px;
    height: 80px;
    padding: 25px;
    border: 1px solid @border-color;
    border-radius: 40px;

    .image {
      background-image: url('../../assets/images/status.png');
      background-size: 100%;
      width: 30px;
      height: 30px
    }
  }
}

.top_div {
  padding: 10px;
  border-right: 1px solid @border-color;
  border-bottom: 1px solid @border-color;
}

.top_right_div {
  flex: 0 0 20%;
  text-align: center;
  padding: 50px 0;
}

.hide {
  display: none;
}

.menu-item {
  cursor: pointer;
  padding: 8px 16px;

  &:hover {
    background: rgba(255, 255, 255, 0.08);
  }
}
</style>
