<template>
  <div>
    <BasicForm @register="registerForm" @submit="handleSubmit" @reset="handleSubmit"/>
  </div>
  <a-spin :spinning="spinning">
    <div style="max-height: calc(100vh - 270px);overflow: auto;">
      <template v-for="(item,index) in eventData" :key="'event'+index">
        <div style="position:relative;">
        <div v-show="item.show == undefined || item.show == true" class="card_div"
             @click="showInfo(item)" style="padding-right: 30px">
          <a-row>
            <a-col :span="4">
              <div>{{ item?.eventName }}</div>
              <div>
                <a-avatar v-if="item?.avatar" :title="item?.conclusionBy"
                          :src="render.renderUploadImageSrc(item?.avatar)"
                          :size="30"/>
                {{ item?.conclusion }}
              </div>

            </a-col>
            <a-col :span="20">
              <div v-if="item.type == '1'" style="display: flex;">
                <div style="width: calc(100% / 7);" class="border-right">
                  <div class="th">{{t('routes.RiskEventLogView.deviceName')}}</div>
                  <div class="td">{{ item?.deviceName }}</div>
                </div>
                <div style="width: calc(100% / 7);" class="border-right">
                  <div class="th">{{t('routes.RiskEventLogView.fromIp')}}</div>
                  <div class="td">{{ item?.fromIp }}</div>
                </div>
                <div style="width: calc(100% / 7);" class="border-right">
                  <div class="th">{{t('routes.RiskEventLogView.updateTime')}}</div>
                  <div class="td">{{ item?.updateTime }}</div>
                </div>
                <div style="width: calc(100% / 7);" class="border-right">
                  <div class="th">{{t('routes.RiskEventLogView.logCount')}}</div>
                  <div class="td">{{ item?.logCount }}</div>
                </div>
                <div style="width: calc(100% / 7);" class="border-right">
                  <div class="th">{{t('routes.RiskEventLogView.severity')}}</div>
                  <div class="td">{{ item?.severity }}</div>
                </div>
                <div style="width: calc(100% / 7);" class="border-right">
                  <div class="th">{{t('routes.RiskEventLogView.srcIp')}}</div>
                  <div class="td">{{ item?.srcIp }}</div>
                </div>
                <div style="width: calc(100% / 7);">
                  <div class="th">{{t('routes.RiskEventLogView.dstIp')}}</div>
                  <div class="td">
                    {{ item?.dstIp }}
                  </div>
                </div>
              </div>
              <div v-else-if="item.type == '2'" style="display: flex;">
                <div style="width: 10%;" class="border-right">
                  <div class="th">{{t('routes.RiskEventLogView.deviceName')}}</div>
                  <div class="td">{{ item?.deviceName }}</div>
                </div>
                <div style="width: 10%;" class="border-right">
                  <div class="th">{{t('routes.RiskEventLogView.fromIp')}}</div>
                  <div class="td">{{ item?.fromIp }}</div>
                </div>
                <div style="width: 20%;" class="border-right">
                  <div class="th">{{t('routes.RiskEventLogView.updateTime')}}</div>
                  <div class="td">{{ item?.updateTime }}</div>
                </div>
                <div style="width: 10%;" class="border-right">
                  <div class="th">{{t('routes.RiskEventLogView.logCount')}}</div>
                  <div class="td">{{ item?.logCount }}</div>
                </div>
                <div style="width: 10%;" class="border-right">
                  <div class="th">{{t('routes.RiskEventLogView.severity')}}</div>
                  <div class="td">{{ item?.severity }}</div>
                </div>
                <div style="width: 40%;">
                  <div class="th">{{t('routes.RiskEventLogView.tag')}}</div>
                  <div class="td">
                    {{ item?.info?.detectionRuleTags }}
                  </div>
                </div>
              </div>
            </a-col>
          </a-row>
        </div>
        <div v-show="item.show == undefined || item.show == true" style="position: absolute;
        right: 10px;top:calc(50% - 10px);cursor: pointer;">
          <a-popconfirm
            :title="t('common.delConfirmText')"
            ok-text="Ok"
            cancel-text="Cancel"
            @confirm="deleteCard(index)">
            <Icon icon="ant-design:delete-outlined" style="cursor: pointer;"/>
          </a-popconfirm>
        </div>
        </div>
      </template>
    </div>
  </a-spin>
</template>

<script lang="ts" setup>
import {useRouter} from "vue-router"
import {defineEmits, defineProps, ref, watch} from 'vue'
import {render} from "/@/utils/common/renderUtils";
import {tableList} from "/@/views/aggregationRiskEventView/RiskEventView.api";
import {
  queryById as hostById
} from "/@/views/aggregationriskeventhost/AggregationRiskEventHost.api";
import {BasicForm, useForm} from '/@/components/Form';
import {useI18n} from "/@/hooks/web/useI18n";
import {RISK_TYPE_SELECT, SEVERITY_SELECT} from "/@/utils/valueEnum";

const router = useRouter()

const emits = defineEmits(['callDel'])
const spinning = ref(false)
const props = defineProps({
  eventData: {
    type: Array as any,
    default: () => []
  },
  eventId: {
    type: Object,
    default: () => null
  },
  queryData: {
    type: Object,
    default: () => null
  },
  addFlag: {
    type: Boolean,
    default: false
  }
})
console.log(props)
if (props.eventId) {
  loadList()
}

async function loadList() {
  console.log("----------------")
  let map = props.eventId
  let eventId: any = []
  for (let key in map) {
    eventId.push(key)
  }
  if (eventId.length == 0) {
    return
  }
  spinning.value = true;
  const params = {
    column: 'updateTime',
    order: 'desc',
    inIds: eventId?.join(","),
    pageSize: 999
  };
  tableList(params).then((data) => {
    for (let i in data.records) {
      loadEventInfo(data.records[i])
      data.records[i].conclusion = map[data.records[i].eventId].conclusion
      data.records[i].conclusionBy = map[data.records[i].eventId].conclusionBy
      data.records[i].avatar = map[data.records[i].eventId].avatar
      props.eventData.push(data.records[i])
    }
    spinning.value = false
  })
}

async function loadEventInfo(data) {
  if (data.type == "2") {
    await hostById({id: data.eventId}).then((data2) => {
      data.info = data2
    })
  }
}

function deleteCard(index) {
  let d = props.eventData.splice(index, 1);
  emits('callDel', 1, d)
}


const {t} = useI18n();
const [registerForm, {setFieldsValue, getFieldsValue}] = useForm({
  //注册表单列
  schemas: [
    {
      label: '',
      field: 'riskType',
      component: 'JSelectInput',
      componentProps: {
        options: RISK_TYPE_SELECT,
        placeholder: t('routes.RiskEventLogView.riskType')
      },
      colProps: {span: 2}
    },
    {
      label: '',
      field: 'eventName',
      component: 'Input',
      componentProps:{
        placeholder: t('routes.RiskEventLogView.eventName')
      },
      colProps: {span: 2}
    },
    {
      label: '',
      field: 'fromIp',
      component: 'Input',
      componentProps:{
        placeholder: t('routes.RiskEventLogView.fromIp')
      },
      colProps: {span: 2}
    },
    {
      label: '',
      field: 'updateTimeStr',
      component: 'RangeDate',
      componentProps: {
        datetime: true,
        placeholder: [t('routes.RiskEventLogView.updateTime'),t('routes.RiskEventLogView.updateTime')]
      },
      colProps: {span: 6}
    },
    {
      label: '',
      field: 'severityStr',
      component: 'JSelectMultiple',
      componentProps: {
        options: SEVERITY_SELECT,
        placeholder: t('routes.RiskEventLogView.severity')
      },
      colProps: {span: 3}
    },
    {
      label: '',
      field: 'srcIp',
      component: 'Input',
      componentProps: {
        placeholder: t('routes.RiskEventLogView.srcIp')
      },
      colProps: {span: 2},
      ifShow: ({values}) => {
        return values.riskType == '1';
      },
    },
    {
      label: '',
      field: 'dstIp',
      component: 'Input',
      componentProps: {
        placeholder: t('routes.RiskEventLogView.dstIp')
      },
      colProps: {span: 2},
      ifShow: ({values}) => {
        return values.riskType == '1';
      },
    }, {
      label: '',
      field: 'tag',
      component: 'Input',
      componentProps: {
        placeholder: t('routes.RiskEventLogView.tag')
      },
      colProps: {span: 2},
      ifShow: ({values}) => {
        return values.riskType == '2';
      },
    },],
  //回车提交
  autoSubmitOnEnter: true,
  showActionButtonGroup: true,
  //不显示重置按钮
  showResetButton: true,
  showSubmitButton: true,
});

/**
 * 点击提交按钮的value值
 * @param values
 */
function handleSubmit(values: any) {
  console.log('提交按钮数据::::', values);
  let data: any[] = props.eventData
  for (let i in data) {
    data[i].show = true
    for (let key in values) {
      if (values[key] == undefined || key == 'riskType') {
        continue
      }
      if (key == 'severityStr') {
        let arr = values[key].split(",")
        if (arr.indexOf(data[i]['severity'] + "") == -1) {
          data[i].show = false
        }
      } else if (key == 'updateTimeStr') {
        let times = values[key].split(",")
        if (data[i]['updateTime'] < times[0] || data[i]['updateTime'] > times[1]) {
          data[i].show = false
        }
      } else if ((data[i][key] + "").indexOf(values[key]) == -1) {
        data[i].show = false
      }
    }
  }
  props.eventData = data
  console.log(props.eventData)
}

watch(() => props.queryData, () => {
  console.log(props.queryData)
  updateFormValue()
})
console.log(props.queryData)
if (props.queryData) {
  setTimeout(() => {
    setFieldsValue(props.queryData);
    handleSubmit(props.queryData)
  }, 500)
}

async function updateFormValue() {
  setFieldsValue(props.queryData);
  handleSubmit(props.queryData)
}

function showInfo(data) {
  if (props.addFlag) {
    return
  }
  let queryJson = getFieldsValue()
  let json = {
    data: queryJson,
    type: "2"
  }
  sessionStorage.setItem("inRiskEvent_1", JSON.stringify(json));
  router.push({
    path: '/aggregationRiskEventView/modules/RiskEventViewModal',
    query: {type: data.type, eventId: data.eventId}
  })
}

</script>

<style lang="less" scoped>

.card_div {
  padding: 10px;
  border-radius: 5px;
  margin: 5px 0;
  background-color: @bg-color;
}


.th, .td {
  text-align: center;
  padding: 5px;
}

.td {
  //border-top: 1px solid @border-color;
}

:deep(.searchForm .ant-col) {
  padding: 0 2px;

  .mr-2 {
    margin-right: 5px;
  }

}

.border-right {
  border: 0;
}
</style>
