<template>
  <div>

    <BasicTable @register="registerTable" rowKey="log_id">
      <!--插槽:table标题-->
      <template #form-formFooter>
        <a-button @click="handleAdd">
          {{t('routes.investigation.continueAdd')}}
        </a-button>
      </template>
      <!--操作栏-->
      <template #action="{ record }">
        <a-button @click="deleteLogs(record)">{{t('common.delText')}}</a-button>
      </template>
    </BasicTable>

  </div>
</template>

<script lang="ts" setup>
  import {useRouter} from 'vue-router';
  import {
    ref,
    reactive,
    defineExpose,
    nextTick,
    defineProps,
    defineEmits,
    computed,
    onMounted
  } from 'vue';
  import {deleteOne, loadRiskLogsList, delRiskLogsList} from '../InvestigationVO.api';
  import {useI18n} from "/@/hooks/web/useI18n";
  import {BasicTable} from '/@/components/Table';
  import {useListPage} from "/@/hooks/system/useListPage";
  import {Modal} from 'ant-design-vue';

  const router = useRouter();

  const props = defineProps({
    modelValue: {
      type: String,
      default: "-"
    },
    logId:{
      type:String
    },
    tableSource: {
      type: String,
      default: "1"
    },
  })
  console.log(props)

  const emit = defineEmits(['saveLogInfo'])

  const {t} = useI18n();
  let columns = reactive([
    // {title: 'ck_enter_date', dataIndex: 'ck_enter_date', ellipsis: true},
    {title: 'enter_date', dataIndex: 'enter_date', ellipsis: true},
    {title: 'event_type', dataIndex: 'event_type', ellipsis: true},
    {title: 'event_name', dataIndex: 'event_name', ellipsis: true},
    {title: 'src_ip', dataIndex: 'src_ip', ellipsis: true},
    {title: 'dst_ip', dataIndex: 'dst_ip', ellipsis: true},
    {title: 'src_port', dataIndex: 'src_port', ellipsis: true},
    {title: 'dst_port', dataIndex: 'dst_port', ellipsis: true},
    {title: 'proxy_ip', dataIndex: 'proxy_ip', ellipsis: true},
    {title: 'event_level', dataIndex: 'event_level', ellipsis: true},
    {title: 'syslog', dataIndex: 'syslog', ellipsis: true},
  ]);
  let columnsHost = reactive([
    {title: 'enter_date', dataIndex: 'enter_date', ellipsis: true},
    {title: 'from_ip', dataIndex: 'from_ip', ellipsis: true},
    {title: 'host_hostname', dataIndex: 'host_hostname', ellipsis: true},
    {title: 'host_event_type', dataIndex: 'host_event_type', ellipsis: true},
    {title: 'host_severity', dataIndex: 'host_severity', ellipsis: true},
    {title: 'host_source_name', dataIndex: 'host_source_name', ellipsis: true},
    {title: 'host_domain', dataIndex: 'host_domain', ellipsis: true}
  ]);
  const {tableContext} = useListPage({
    tableProps: {
      title: '',
      api: loadRiskLogsList,
      columns : props.tableSource=='1'?columns:(props.tableSource=='2'?columnsHost:columns),
      searchInfo: {
        table: props.tableSource,
        investigationId: props.modelValue,
        logId: props.logId
      },
      canResize: false,
      useSearchForm: false,
      showActionColumn: props.modelValue != '-',
      actionColumn: {
        width: 120,
      },
    },
  })

  const [registerTable, {reload}, {rowSelection, selectedRowKeys}] = tableContext


  const deleteLogs = (record) => {
    console.log(record)
    Modal.confirm({
      title: t('common.delText'),
      content: t('common.delConfirmText') + "?",
      okText: t('common.delText'),
      cancelText: t('common.cancelText'),
      wrapClassName: 'delete_confirm',
      onOk: () => {
        delRiskLogsList({logId: record.log_id, investigationId: record.investigationId}, reload)
      }
    });
  }

  const handleAdd = () => {
    emit('saveLogInfo')
    // router.push({path: "/riskLogs/RiskLogsIndex"});
  }


</script>
<style scoped>
</style>
