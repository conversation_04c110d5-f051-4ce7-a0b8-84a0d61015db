<template>
  <div>
    <BasicForm @register="registerForm" @submit="handleSubmit" @reset="handleSubmit"/>
  </div>
  <a-spin :spinning="spinning">
    <div style="max-height: calc(100vh - 270px);overflow: auto;">
      <template v-for="(item,index) in badActorData" :key="'badActor'+index">
        <div style="position:relative;">
          <div v-show="item.show == undefined || item.show == true" class="card_div"
               @click="showInfo(item)" style="padding-right: 30px">
            <a-row>
              <a-col :span="4">
                <div>{{ item?.ip }}</div>
                <div>
                  <a-avatar v-if="item?.avatar" :title="item?.conclusionBy"
                            :src="render.renderUploadImageSrc(item?.avatar)"
                            :size="30"/>
                  {{ item?.conclusion }}
                </div>
              </a-col>
              <a-col :span="20">
                <div style="display: flex;">
                  <div style="width: 120px;" class="border-right">
                    <div class="th">{{ tp('severity') }}</div>
                    <div class="td">{{ item?.severity }}</div>
                  </div>
                  <div style="width: 120px;" class="border-right">
                    <div class="th">{{ tp('status') }}</div>
                    <div class="td">
                      <span v-if="item?.status === 0">{{ t('common.Unclosed') }}</span>
                      <span v-if="item?.status === 1">{{ t('common.Closed') }}</span>
                    </div>
                  </div>
                  <div style="width: 320px;" class="border-right">
                    <div class="th">{{ tp('Duration') }}</div>
                    <div class="td">{{ item?.attackFirstTime }} - {{ item?.attackLatestTime }}</div>
                  </div>
                  <!--              <div style="width: calc(100% / 5);" class="border-right">-->
                  <!--                <div class="th">Last alerm time</div>-->
                  <!--                <div class="td">{{ item?.attackLatestTime }}</div>-->
                  <!--              </div>-->
                  <div style="width: 120px;" class="border-right">
                    <div class="th">{{ tp('AttackTarget') }}</div>
                    <div class="td">{{ item?.targetNum }}</div>
                  </div>
                  <div style="width: 120px;" class="border-right">
                    <div class="th">{{ tp('AttackNumber') }}</div>
                    <div class="td">{{ item?.attackNum }}</div>
                  </div>
                  <div style="width: 180px;" class="border-right">
                    <div class="th">{{ tp('AttackMethodsNumber') }}</div>
                    <div class="td">{{ item?.attackWayNum }}</div>
                  </div>
                  <div style="width: 120px;" class="border-right">
                    <div class="th">{{ tp('AttackMethod') }}</div>
                    <div class="td">
                      <template v-if="item?.attackWay">
                        <template v-for="(ie,index) in item.attackWay.split(',')">
                          <a-tag v-if="index < 2" color="red"
                                 :key="index"
                                 style="margin-right:5px;" :title="item.attackWay"
                                 class="item-tag">
                            {{ ie }}
                          </a-tag>
                        </template>

                      </template>

                    </div>
                  </div>
                  <div style="width: 150px;">
                    <div class="th">{{ tp('Score') }}</div>
                    <div class="td">{{ item?.threatScore }}</div>
                  </div>
                </div>
              </a-col>
            </a-row>
          </div>
          <div style="position: absolute; right: 10px;top:calc(50% - 10px);cursor: pointer;">
            <a-popconfirm
              :title="t('common.delConfirmText')"
              ok-text="Ok"
              cancel-text="Cancel"
              @confirm="deleteCard(index)">
              <Icon icon="ant-design:delete-outlined" style="cursor: pointer;"/>
            </a-popconfirm>
          </div>
        </div>
      </template>
    </div>
  </a-spin>
</template>

<script lang="ts" setup>
import {defineEmits, defineProps, ref, watch} from 'vue'
import {render} from "/@/utils/common/renderUtils";
import {list} from "/@/views/badactors/BadActors.api";
import {BasicForm, useForm} from '/@/components/Form';
import {useRouter} from "vue-router";
import {useI18n} from "/@/hooks/web/useI18n";
import {CLOSE_STATUS_SELECT, SEVERITY_SELECT} from "/@/utils/valueEnum";

const {t} = useI18n();

function tp(name) {
  return t('routes.investigation.' + name);
}

const router = useRouter()
const emits = defineEmits(['callDel'])
const spinning = ref(false)
const props = defineProps({
  badActorData: {
    type: Array as any,
    default: () => []
  },
  badActorId: {
    type: Object,
    default: () => null
  },
  queryData: {
    type: Object,
    default: () => null
  },
  addFlag: {
    type: Boolean,
    default: false
  }
})
console.log(props)

if (props.badActorId) {
  loadList()
}

function showInfo(data) {
  if (props.addFlag) {
    return
  }
  let queryJson = getFieldsValue()
  let json = {
    data: queryJson,
    type: "4"
  }
  sessionStorage.setItem("inRiskEvent_1", JSON.stringify(json));
  sessionStorage.setItem("BadActorsData", JSON.stringify(data))
  router.push({
    path: "/badactors/BadActorsViewModal"
  });
}

function deleteCard(index) {
  let d = props.badActorData.splice(index, 1);
  emits('callDel', 4, d)
}

/**
 * 监听，变化了重新加载数据
 */
watch(props.badActorId, () => {
  console.log(props.badActorId)
  if (props.badActorId) {
    loadList()
  }
})

function loadList() {
  let map = props.badActorId
  let ids: any = []
  for (let key in map) {
    ids.push(key)
  }
  if (ids.length == 0) {
    return
  }
  spinning.value = true;
  const params = {
    column: 'updateTime',
    order: 'desc',
    inIds: ids?.join(","),
    pageSize: 999
  };
  list(params).then((data) => {
    for (let i in data.records) {
      data.records[i].conclusion = map[data.records[i].id].conclusion
      data.records[i].conclusionBy = map[data.records[i].id].conclusionBy
      data.records[i].avatar = map[data.records[i].id].avatar
      props.badActorData.push(data.records[i])
    }
    spinning.value = false
  })
}


const [registerForm, {setFieldsValue, getFieldsValue}] = useForm({
  //注册表单列
  schemas: [
    {
      label: '',
      field: 'ip',
      component: 'Input',
      componentProps: {
        placeholder: tp('badActor')
      },
      colProps: {span: 4},
    },
    {
      label: '',
      field: 'severity',
      component: 'Select',
      colProps: {span: 4},
      componentProps: {
        options: SEVERITY_SELECT,
        placeholder: tp('severity')
      },
    },
    {
      label: '',
      field: 'status',
      component: 'Select',
      colProps: {span: 4},
      componentProps: {
        options: CLOSE_STATUS_SELECT,
        placeholder: tp('status')
      },
    },
    {
      label: '',
      field: 'attackLatestTimeRange',
      component: 'RangeDate',
      componentProps: {
        datetime: true,
        placeholder: [tp('LastAlertTime'),tp('LastAlertTime')]
      },
      colProps: {
        lg: 12, // ≥992px
        xl: 5, // ≥1200px
        xxl: 5, // ≥1600px
      },
    }
  ],
  //回车提交
  autoSubmitOnEnter: true,
  showActionButtonGroup: true,
  //不显示重置按钮
  showResetButton: true,
  showSubmitButton: true,
});

/**
 * 点击提交按钮的value值
 * @param values
 */
function handleSubmit(values: any) {
  console.log('提交按钮数据::::', values);
  let badActorData: any[] = props.badActorData
  for (let i in badActorData) {
    badActorData[i].show = true
    if (values.ip) {
      if (badActorData[i].ip.indexOf(values.ip) == -1) {
        badActorData[i].show = false
      }
    }
    if (values.severity) {
      if (badActorData[i].severity != values.severity) {
        badActorData[i].show = false
      }
    }
    if (values.status != undefined) {
      if (badActorData[i].status !== values.status) {
        badActorData[i].show = false
      }
    }
    if (values.attackLatestTimeRange) {
      let times = values.attackLatestTimeRange.split(",")
      if (badActorData[i].attackLatestTime < times[0] || badActorData[i].attackLatestTime >
        times[1]) {
        badActorData[i].show = false
      }
    }
  }
  props.badActorData = badActorData
  console.log(props.badActorData)
}

watch(() => props.queryData, () => {
  console.log(props.queryData)
  updateFormValue()
})
console.log(props.queryData)
if (props.queryData) {
  setTimeout(() => {
    setFieldsValue(props.queryData);
    handleSubmit(props.queryData)
  }, 500)
}

async function updateFormValue() {
  setFieldsValue(props.queryData);
  handleSubmit(props.queryData)
}
</script>

<style lang="less" scoped>
.card_div {
  padding: 10px;
  border-radius: 5px;
  margin: 5px 0;
  background-color: @bg-color;
}

.th {
  white-space: nowrap;
  display: block;
  overflow: hidden;
  text-overflow: ellipsis;
}

.th, .td {
  text-align: center;
  padding: 5px;
}

.td {
  //border-top: 1px solid @border-color;
}

:deep(.searchForm .ant-col) {
  padding: 0 2px;

  .mr-2 {
    margin-right: 5px;
  }

}

.border-right {
  border: 0;
}
</style>
