<template>
  <div>
    <BasicForm @register="registerForm" @submit="handleSubmit" @reset="handleSubmit"/>
  </div>
  <a-spin :spinning="spinning">
    <div style="max-height: calc(100vh - 270px);overflow: auto;">
      <template v-for="(item,index) in processesData" :key="'process'+index">
        <div style="position:relative;">
          <div v-show="item.show == undefined || item.show == true" class="card_div"
               @click="showInfo(item)" style="padding-right: 30px">
            <a-row>
              <a-col :span="4">
                <div>{{ item?.processName }}</div>
                <div>
                  <a-avatar v-if="item?.avatar" :title="item?.conclusionBy"
                            :src="render.renderUploadImageSrc(item?.avatar)"
                            :size="30"/>
                  {{ item?.conclusion }}
                </div>
              </a-col>
              <a-col :span="20">
                <div style="display: flex;">
                  <!--                		Process Name	Last detected	First detected	Severity	Status-->
                  <div style="width: calc(100% / 5);" class="border-right">
                    <div class="th">{{ t('routes.SuspiciousProcessesCollect.deviceIp') }}</div>
                    <div class="td">{{ item?.deviceIp }}</div>
                  </div>
                  <div style="width: calc(100% / 5);" class="border-right">
                    <div class="th">{{ t('routes.SuspiciousProcesses.processName') }}</div>
                    <div class="td">
                      {{ item?.processName }}
                    </div>
                  </div>
                  <div style="width: calc(100% / 5);" class="border-right">
                    <div class="th">{{ t('routes.SuspiciousProcesses.lastDetected') }}</div>
                    <div class="td">{{ item?.lastDetected }}</div>
                  </div>
                  <div style="width: calc(100% / 5);" class="border-right">
                    <div class="th">{{ t('routes.SuspiciousProcesses.firstDetected') }}</div>
                    <div class="td">{{ item?.firstDetected }}</div>
                  </div>
                  <div style="width: calc(100% / 5);" class="border-right">
                    <div class="th">{{ t('routes.SuspiciousProcesses.severity') }}</div>
                    <div class="td">
                      <span v-if="item?.severity == 1">{{ t('common.Low') }}</span>
                      <span v-if="item?.severity == 2">{{ t('common.Middle') }}</span>
                      <span v-if="item?.severity == 3">{{ t('common.High') }}</span>
                      <span v-if="item?.severity == 4">{{ t('common.Critical') }}</span>
                    </div>
                  </div>
                  <div style="width: calc(100% / 5);">
                    <div class="th">{{ t('routes.SuspiciousProcesses.status') }}</div>
                    <div class="td">
                      <span v-if="item?.severity == 1">{{ t('common.Unclosed') }}</span>
                      <span v-if="item?.severity == 2">{{ t('common.Closed') }}</span>
                    </div>
                  </div>
                </div>
              </a-col>
            </a-row>
          </div>
          <div style="position: absolute; right: 10px;top:calc(50% - 10px);cursor: pointer;">
            <a-popconfirm
              :title="t('common.delConfirmText')"
              ok-text="Ok"
              cancel-text="Cancel"
              @confirm="deleteCard(index)">
              <Icon icon="ant-design:delete-outlined" style="cursor: pointer;"/>
            </a-popconfirm>
          </div>
        </div>
      </template>
    </div>
  </a-spin>
</template>

<script lang="ts" setup>
import {defineEmits, defineProps, ref} from 'vue'
import {render} from "/@/utils/common/renderUtils";
import {list} from "/@/views/suspiciousProcesses/SuspiciousProcesses.api";
import {BasicForm, useForm} from '/@/components/Form';
import {useI18n} from "/@/hooks/web/useI18n";

const {t} = useI18n();


const emits = defineEmits(['callDel'])
const spinning = ref(false)
const props = defineProps({
  processesData: {
    type: Array as any,
    default: () => []
  },
  processesId: {
    type: Object,
    default: () => null
  }
})
console.log(props)
if (props.processesId) {
  loadList()
}

function showInfo(data) {
  // let queryJson = getFieldsValue()
  // let json = {
  //   data: queryJson,
  //   type: "3"
  // }
  // sessionStorage.setItem("inRiskEvent_1", JSON.stringify(json));
  // router.push({path: "/riskml/modules/RiskMlOutModal", query: {id: data.id}});
}

function loadList() {
  let map = props.processesId
  let ids: any = []
  for (let key in map) {
    ids.push(key)
  }
  if (ids.length == 0) {
    return
  }
  spinning.value = true;
  const params = {
    column: 'lastDetected',
    order: 'desc',
    inIds: ids?.join(","),
    pageSize: 999
  };
  list(params).then((data) => {
    for (let i in data.records) {
      data.records[i].conclusion = map[data.records[i].id].conclusion
      data.records[i].conclusionBy = map[data.records[i].id].conclusionBy
      data.records[i].avatar = map[data.records[i].id].avatar
      props.processesData.push(data.records[i])
    }
    spinning.value = false
  })
}

function deleteCard(index) {
  let d = props.processesData.splice(index, 1);
  emits('callDel', 5, d)
}


const [registerForm, {getFieldsValue, setFieldsValue}] = useForm({
  //注册表单列
  schemas: [
    {
      label: '',
      field: 'processName',
      component: 'Input',
      componentProps: {
        placeholder: t('routes.SuspiciousProcesses.processName')
      },
      colProps: {span: 3}
    },
    {
      label: '',
      field: 'lastDetected',
      component: 'RangeDate',
      componentProps: {
        datetime: true,
        placeholder: [t('routes.SuspiciousProcesses.lastDetected'),t('routes.SuspiciousProcesses.lastDetected')]
      },
      colProps: {span: 6}
    },
    {
      label: '',
      field: 'severity',
      component: 'JSelectInput',
      colProps: {span: 3},
      componentProps: {
        // 1: Low, 2: Middle, 3: High, 4: Critical
        options: [{
          label: t('common.Low'),
          value: 1
        }, {
          label: t('common.Middle'),
          value: 2
        }, {
          label: t('common.High'),
          value: 3
        }, {
          label: t('common.Critical'),
          value: 4
        }],
        placeholder: t('routes.SuspiciousProcesses.severity')
      }
    },
    {
      label: '',
      field: 'status',
      component: 'JSelectInput',
      // 1:Open, 2:Closed,  3:Whitelisted
      componentProps: {
        options: [{
          label: t('common.Unclosed'),
          value: 1
        }, {
          label: t('common.Closed'),
          value: 2
        }],
        placeholder: t('routes.SuspiciousProcesses.status')
      },
      colProps: {span: 3}
    }],
  //回车提交
  autoSubmitOnEnter: true,
  showActionButtonGroup: true,
  //不显示重置按钮
  showResetButton: true,
  showSubmitButton: true,
});

/**
 * 点击提交按钮的value值
 * @param values
 */
function handleSubmit(values: any) {
  console.log('提交按钮数据::::', values);
  let data: any[] = props.processesData
  // urgency createTime mlStatus
  for (let i in data) {
    data[i].show = true
    for (let key in values) {
      if (values[key] == undefined) {
        continue
      }
      if (key == 'lastDetected') {
        let times = values[key].split(",")
        if (data[i][key] < times[0] || data[i][key] > times[1]) {
          data[i].show = false
        }
      } else if ((data[i][key] + "").indexOf(values[key]) == -1) {
        data[i].show = false
      }
    }
  }
  props.processesData = data
  console.log(props.processesData)
}

</script>

<style lang="less" scoped>
.card_div {
  padding: 10px;
  border-radius: 5px;
  margin: 5px 0;
  background-color: @bg-color;
}


.th, .td {
  text-align: center;
  padding: 5px;
}

.td {
  //border-top: 1px solid @border-color;
}

:deep(.searchForm .ant-col) {
  padding: 0 2px;

  .mr-2 {
    margin-right: 5px;
  }

}

.border-right {
  border: 0;
}
</style>
