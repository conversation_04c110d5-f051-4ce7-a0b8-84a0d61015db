<template>
  <a-modal v-model:visible="visible" :title="t('common.add')" @ok="handleOk" width="80%"
           :destroyOnClose="true" :maskClosable="false">
    <div style="padding: 16px;">
      <div style="margin-bottom: 10px;">
        <div>{{tp('Conclusion')}}</div>
        <a-textarea
          v-model:value="conclusion"
          auto-size
        />
      </div>
      <BasicTable v-if="visible" @register="registerTable" :isSearch="true"
                  :rowSelection="rowSelection as any">
        <template #severity="{ text }">
          <Severity :value="text"/>
        </template>
      </BasicTable>
    </div>

  </a-modal>

</template>

<script lang="ts" setup>
import {defineEmits, defineExpose, nextTick, ref, toRaw} from 'vue'
import {BasicTable} from "/@/components/Table";
import {useListPage} from "/@/hooks/system/useListPage";
import {tableList} from "/@/views/aggregationRiskEventView/RiskEventView.api";
import {columns, getSearchFormSchema} from "/@/views/aggregationRiskEventView/RiskEventView.data";
import {formLayout} from "/@/settings/designSetting";
import dayjs from "dayjs";
import Severity from "/@/components/Severity/Severity.vue";
import {useI18n} from "/@/hooks/web/useI18n";

const {t} = useI18n();
function tp(name) {
  return t('routes.investigation.' + name);
}
const visible = ref(false)
const conclusion = ref("")

const emits = defineEmits(["addRiskEvent"])

async function handleOk() {
  console.log(selectedRowKeys.value)
  console.log(rowSelection.selectedRows)
  emits('addRiskEvent', toRaw(rowSelection.selectedRows), conclusion.value)
  visible.value = false
}

let formRule: any = [];
const searchFormSchema = getSearchFormSchema()
for (let i = 0; i < searchFormSchema.length; i++) {
  //因为外边已经选了租户了，所以这个租户查询条件去掉
  //最后一个查询按钮不要
  if (i === 0 || i === searchFormSchema.length - 1) {
    searchFormSchema[i].ifShow = false
  }
  let base: any = {};
  for (let j in searchFormSchema[i]) {
    base[j] = searchFormSchema[i][j];
  }
  formRule.push(base);
}

const eventIds = ref("")
//注册table数据
const {tableContext} = useListPage({
  tableProps: {
    api: tableList,
    rowKey: "eventId",
    columns: columns(true),
    canResize: false,
    pagination: {
      defaultPageSize: 10,
      pageSize: 10
    },
    formConfig: {
      labelWidth: 120,
      schemas: formRule,
      autoSubmitOnEnter: true,
      showAdvancedButton: false,
      layout: formLayout,
      baseColProps: {
        lg: 6, // ≥992px
        xl: 3, // ≥1200px
        xxl: 3, // ≥1600px
      },
    },
    beforeFetch: beforeFetch,
    defSort: {
      column: 'updateTime',
      order: 'desc',
    },
    showActionColumn: false,
  },
})

const [registerTable, {updateSelectedRowKeys, setSelectedRowKeys}, {
  rowSelection,
  selectedRowKeys
}] = tableContext

let socTenantId = ""

function open(ids, tenantId) {
  socTenantId = tenantId;
  formRule.value = [];
  for (let i = 0; i < searchFormSchema.length; i++) {
    let base: any = {};
    for (let j in searchFormSchema[i]) {
      base[j] = searchFormSchema[i][j];
    }
    if (searchFormSchema[i].field == 'updateTimeStr') {
      base['defaultValue'] = dayjs().subtract(7, 'day').format('YYYY-MM-DD HH:mm:ss') + ',' + dayjs().format('YYYY-MM-DD HH:mm:ss');
    }
    formRule.push(base);
  }
  conclusion.value = ""
  eventIds.value = ids
  console.log("111")
  //rowSelection.selectedRows = []
  nextTick(() => {
    console.log("222")
    visible.value = true
    setTimeout(function () {
      console.log("333")
      updateSelectedRowKeys([]);
      setSelectedRowKeys([]);
    }, 50)
  });
}

function beforeFetch(params) {
  console.log("444")
  params.notIds = eventIds.value
  params.socTenantId = socTenantId
  return params
}

defineExpose({
  open
})

</script>

<style lang="less" scoped>

</style>
