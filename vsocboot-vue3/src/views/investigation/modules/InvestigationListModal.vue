<template>
  <a-modal :title="title" :width="width" :visible="visible" :footer="null" @ok="handleOk"
           :destroyOnClose="true" :maskClosable=false
           :zIndex="2000"
           @cancel="handleCancel">
    <BasicTable @register="registerRiskLogsTable" :rowSelection="rowSelection as any"
                :isSearch="true">
      <template #form-formFooter>
        <a-col :span="18">
          <a-button type="primary" style="float: right;" @click="saveLog">
            {{ t('routes.riskLogs.save') }}
          </a-button>
        </a-col>

      </template>
    </BasicTable>
  </a-modal>
  <a-modal v-model:visible="inveVisible" :title="t('common.confirm')" @ok="saveToInve"
           :destroyOnClose="true" :zIndex="2001"
           :maskClosable=false>
    <a-row style="padding: 0 24px;">
      <a-col :span="24">
        {{ t('routes.riskLogs.addInvestigationPrompt') }}
      </a-col>
      <a-col :span="24">
        <a-form class="antd-modal-form"
                autocomplete="off" :layout="formLayout">
          <a-form-item :label="t('routes.investigation.Conclusion')">
            <a-textarea v-model:value="conclusion"/>
          </a-form-item>
        </a-form>
      </a-col>
    </a-row>
  </a-modal>
</template>

<script lang="ts" setup>
import {defineExpose, nextTick, ref} from 'vue';
import {useListPage} from "/@/hooks/system/useListPage";
import {list} from "/@/views/investigation/InvestigationVO.api";
import {getColumns} from "/@/views/investigation/InvestigationVO.data";
import {BasicTable} from '/@/components/Table';
import {useI18n} from "/@/hooks/web/useI18n";
import {saveOrUpdate3} from '/@/views/risk/InvestigationRiskEventlogs.api'
import {formLayout} from "/@/settings/designSetting";
import {useUserStore} from "/@/store/modules/user";
import {message} from "ant-design-vue";
import {isAdministrator} from "/@/utils/auth";

const {t} = useI18n();

const title = ref<string>(t('routes.riskLogs.addMore'));
const width = ref<number>(1600);
const visible = ref<boolean>(false);
const emit = defineEmits(['success']);
const inveVisible = ref(false)

let paramsData:any = {};

//注册table数据
const {tableContext} = useListPage({
  tableProps: {
    title: '',
    api: list,
    columns: getColumns(),
    canResize: false,
    immediate:false,
    formConfig: {
      labelWidth: 120,
      schemas: [{
        label: '',
        field: 'investigation',
        component: 'Input'
      },],
      autoSubmitOnEnter: true,
    },
    showActionColumn: false,
    beforeFetch: (param) => {
      param.socTenantIds = paramsData?.socTenantId?.split(",")
      return param
    },
  },
})

const [registerRiskLogsTable, {reload}, {rowSelection, selectedRowKeys}] = tableContext


const conclusion = ref("")
const userStore = useUserStore();
const saveToInve = () => {
  let eventId = paramsData.eventId
  let ids = selectedRowKeys.value.join(',')
  //bad actor 运维管理员加调查特殊处理，因为有多个租户
  if (isAdministrator() && paramsData.type == '4') {
    console.log(rowSelection)
    let selectedRows = rowSelection.selectedRows
    console.log(selectedRows)
    console.log(paramsData.eventId)
    let map = JSON.parse(paramsData.eventId)
    console.log(map)
    //调查id
    let dcIds: any = []
    //badactor id
    let dcIdVsBadIds: any = []
    for (let i in selectedRows) {
      //调查的租户id
      let tenantId = selectedRows[i].socTenantId
      console.log('tenantId:', tenantId)
      let badId = map[tenantId]
      console.log('badId:', badId)
      //数组调查id和调查对应租户的bad id一一对应了
      dcIds.push(selectedRows[i].id)
      dcIdVsBadIds.push(badId)
    }
    ids = dcIds.join(",")
    eventId = dcIdVsBadIds.join(",")
  }


  let param: any = {
    investigationId: ids,
    eventId: eventId,
    conclusion: conclusion.value,
    conclusionBy: userStore.userInfo?.username,
    type: paramsData.type,
  }
  if (paramsData.type?.indexOf(",") > -1) {
    param.typeStr = paramsData.type
  }
  saveOrUpdate3(param).then(() => {
    inveVisible.value = false
    visible.value = false
    conclusion.value = ""
    emit("success");
  })
}

function saveLog() {
  console.log(rowSelection)
  let selectedRows = rowSelection.selectedRows
  console.log(selectedRows)
  let ids = selectedRowKeys.value.join()
  if (!ids) {
    message.warn(t('common.chooseDataText'))
    return
  }
  if (paramsData.huntingId == "huntingId") {
    //这个需要特殊处理
    emit("success", ids);
    return
  }
  inveVisible.value = true
}

/**
 * 确定按钮点击事件
 */
function handleOk() {
  handleCancel();
}

/**
 * 取消按钮回调事件
 */
function handleCancel() {
  visible.value = false;
}

/**
 * 显示
 * @param data
 */
function open(data){
  console.log(data)
  paramsData = data;
  visible.value = true;
  nextTick(()=>{
    reload();
  })
}
defineExpose({
  open
});
</script>

<style>
</style>
