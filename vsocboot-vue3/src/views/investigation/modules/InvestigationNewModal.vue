<template>
  <div class="modal-top padding16">
    <div class="font16 fcolor">
      <Icon icon="ant-design:left-outlined" @click="closeRetun" style="margin-right: 5px;cursor:
      pointer;"/>
    </div>
    <div>
      <a-button style="margin-right: 10px" @click="closeRetun">{{ t('common.cancelText') }}
      </a-button>
      <a-button type="primary" :loading="saveLoading" @click="onSubmit">{{
          t('common.saveText')
        }}
      </a-button>
    </div>

  </div>
  <div class="form-container">
    <div class="font16 fcolor" style="padding-bottom: 10px">{{ tp('BaseInformation') }}</div>
    <a-form
      ref="formRef"
      :model="formData"
      :rules="rules"
      :layout="formLayout"
      autocomplete="off"
    >

      <a-form-item ref="investigation"
                   class="inves-formItem"
                   :label="tp('investigation')"
                   name="investigation">
        <a-input v-model:value="formData.investigation" @focusout="InvestigationChange"/>
      </a-form-item>

      <a-form-item :label="t('routes.investigation.severity')" name="severity"
                   class="inves-formItem">
        <a-select v-model:value="formData.severity">
          <a-select-option value="Critical">{{ t('common.Critical') }}</a-select-option>
          <a-select-option value="High">{{ t('common.High') }}</a-select-option>
          <a-select-option value="Medium">{{ t('common.Medium') }}</a-select-option>
          <a-select-option value="Low">{{ t('common.Low') }}</a-select-option>
          <a-select-option value="Information">{{ t('common.Information') }}</a-select-option>
        </a-select>
      </a-form-item>
      <a-form-item :label="t('routes.investigation.tenant')" name="socTenantId"
                   class="inves-formItem" v-if="isAdministrator() && tenantList.length > 0">
        <JSearchSelect v-model:value="formData.socTenantId" :dictOptions="tenantList"
                       @change="tenantChange"/>
      </a-form-item>
    </a-form>

  </div>
  <div v-if="tabShow" class="tab-content-wrapper">
    <a-tabs type="card" v-model:activeKey="tabActiveKey">
      <a-tab-pane key="1" :tab="tp('riskEvents')">
        <div style="padding: 10px;min-height: 500px;">
          <InRiskEventCard :eventData="eventData"
                           :eventId="eventId" :addFlag="true"/>
          <a-button type="primary" @click="showRiskEvent"
                    v-if="!!formData.socTenantId || !isAdministrator()"
                    style="margin-top: 5px">
            {{ tp('AddRiskEvent') }}
          </a-button>
        </div>
      </a-tab-pane>
      <a-tab-pane key="2" :tab="tp('ml')">
        <div style="padding: 10px;min-height: 500px;">
          <InMlCard :mlData="mlData" :mlId="mlId" :addFlag="true"/>
          <a-button type="primary" @click="showMl"
                    v-if="!!formData.socTenantId || !isAdministrator()"
                    style="margin-top: 5px">
            {{ tp('AddML') }}
          </a-button>
        </div>
      </a-tab-pane>
      <a-tab-pane key="3" :tab="tp('badActor')">
        <div style="padding: 10px;min-height: 500px;">
          <InBadActorCard :badActorData="badActorData" :badActorId="badActorId" :addFlag="true"/>

          <a-button type="primary" @click="showBadActor"
                    v-if="!!formData.socTenantId || !isAdministrator()"
                    style="margin-top: 5px">
            {{ tp('AddBadActor') }}
          </a-button>
        </div>
      </a-tab-pane>
      <a-tab-pane key="4" :tab="tp('suspiciousProcesses')">
        <div style="padding: 10px;min-height: 500px;">
          <InProcessesCard :processesData="processesData" :processesId="processesId"
                           :addFlag="true"/>
          <a-button type="primary" @click="showProcesses"
                    v-if="!!formData.socTenantId || !isAdministrator()"
                    style="margin-top: 5px">
            {{ tp('AddSuspiciousProcess') }}
          </a-button>
        </div>
      </a-tab-pane>
      <a-tab-pane key="5" :tab="tp('huntingResult')">
        <div style="padding: 10px;min-height: 500px;">
          <InThreatHuntingCard :sourceData="huntingData" :huntingMap="huntingMap"
                               :addFlag="true"/>
        </div>
      </a-tab-pane>
    </a-tabs>
  </div>
  <InRiskEvent ref="riskEventRef" @addRiskEvent="addRiskEvent"/>
  <InMl ref="mlRef" @addMl="addMl"/>
  <InBadActor ref="badActorRef" @addBadActor="addBadActor"/>
  <InProcesses ref="processesRef" @addProcesses="addProcesses"/>
</template>

<script lang="ts" setup>
import {useRoute, useRouter} from 'vue-router';
import {formLayout} from '/@/settings/designSetting';
import {isExist, list, saveOrUpdate} from '../InvestigationVO.api';
import {nextTick, reactive, ref, toRaw} from 'vue';
import type {Rule} from 'ant-design-vue/es/form';
import {useI18n} from "/@/hooks/web/useI18n";
import InRiskEvent from './InRiskEvent.vue'
import InRiskEventCard from './InRiskEventCard.vue'
import InMl from './InMl.vue'
import InMlCard from './InMlCard.vue'
import InBadActor from './InBadActor.vue'
import InBadActorCard from './InBadActorCard.vue'
import InProcesses from './InProcesses.vue'
import InProcessesCard from './InProcessesCard.vue'
import {
  queryById as securityById
} from "/@/views/aggregationriskeventsecurity/AggregationRiskEventSecurity.api";
import {
  queryById as hostById
} from "/@/views/aggregationriskeventhost/AggregationRiskEventHost.api";
import {useUserStore} from "/@/store/modules/user";
import InThreatHuntingCard from "/@/views/investigation/modules/InThreatHuntingCard.vue";
import JSearchSelect from "/@/components/Form/src/jeecg/components/JSearchSelect.vue";
import {Modal} from "ant-design-vue";
import {isAdministrator} from "/@/utils/auth";
import {queryList} from "/@/views/system/tenant/tenant.api";

const {t} = useI18n();

function tp(name) {
  return t('routes.investigation.' + name);
}

const router = useRouter();
const route = useRoute();
const formRef = ref();
let oldSocTenantId = ""
const formData = reactive({
  id: null,
  investigation: 'Investigation',
  severity: 'Critical',
  socTenantId: ''
});
const tabActiveKey = ref("1")
const userStore = useUserStore();
const isUpdate = ref(false);
const investigationId = ref<string>();
const tabShow = ref(false)
console.log(route.query)

const mlId = ref<any>({})
const badActorId = ref<any>({})
const processesId = ref<any>({})
const eventId = ref<any>({})
const huntingMap = ref<any>({})
const huntingData = ref<any[]>([])
const tenantList = ref<any[]>([])

const fromTab = ref(router.currentRoute.value.query.fromTab);
if (route.query && route.query.id) {
  isUpdate.value = true;
  loadTenantListData("")
  list(route.query).then((data) => {
    console.log(data)
    if (data.records.length > 0) {
      let dataJson = data.records[0];
      investigationId.value = dataJson.id;
      formData.id = dataJson.id;
      formData.investigation = dataJson.investigation;
      formData.severity = dataJson.severity;
      formData.socTenantId = dataJson.socTenantId;
      oldSocTenantId = dataJson.socTenantId;
      tabShow.value = true;
    }
  });
} else {
  let addInvestigationParam = sessionStorage.getItem("addInvestigationParam");
  sessionStorage.removeItem("addInvestigationParam")
  if (addInvestigationParam) {
    let json = JSON.parse(addInvestigationParam);
    console.log(json)
    formData.socTenantId = json.socTenantId;
    oldSocTenantId = json.socTenantId;
    loadTenantListData(json.socTenantId)

    if (json?.type == '4') {//bad actor
      tabActiveKey.value = "3"
      console.log(json?.eventId)
      badActorId.value[json?.eventId] = {
        conclusion: json?.conclusion,
        conclusionBy: userStore.userInfo?.username,
        avatar: userStore.userInfo?.avatar
      }

    } else if (json?.type == '3') {//ml
      tabActiveKey.value = "2"
      mlId.value[json?.eventId] = {
        conclusion: json?.conclusion,
        conclusionBy: userStore.userInfo?.username,
        avatar: userStore.userInfo?.avatar
      }
    } else if (json?.type == '5') {
      tabActiveKey.value = "4"
      let ids = json?.eventId.split(",")
      for (let i in ids) {
        processesId.value[ids[i]] = {
          conclusion: json?.conclusion,
          conclusionBy: userStore.userInfo?.username,
          avatar: userStore.userInfo?.avatar
        }
      }
    } else if (json?.type == '1') {//risk event
      tabActiveKey.value = "1"
      eventId.value[json?.eventId] = {
        conclusion: json?.conclusion,
        conclusionBy: userStore.userInfo?.username,
        avatar: userStore.userInfo?.avatar
      }
    } else if (json?.type == '6') {
      tabActiveKey.value = "5"
      huntingMap.value[json?.eventId] = {
        conclusion: json?.conclusion,
        conclusionBy: userStore.userInfo?.username,
        avatar: userStore.userInfo?.avatar,
        severity: json?.severity,
      }
    }
  } else {
    loadTenantListData("")
  }
  console.log(formData.investigation);
  tabShow.value = true;
  nextTick(() => {
    InvestigationChange();
  });
}


const rules: Record<string, Rule[]> = {
  investigation: [
    {required: true, message: '不能为空', trigger: 'change'},
  ],
  socTenantId: [
    {required: true, message: '不能为空', trigger: 'change'},
  ]
};
const saveLoading = ref(false)
const onSubmit = () => {
  saveLoading.value = true
  formRef.value
    .validate()
    .then(() => {

      let params: any = toRaw(formData)
      for (let key in params) {
        if (!params[key]) {
          params[key] = ""
        }
      }

      let riskEventId: any = []
      let riskEventType: any = []
      let riskEventConclusion: any = []
      let riskEventConclusionBy: any = []
      for (let i in eventData.value) {
        riskEventId.push(eventData.value[i].eventId)
        riskEventType.push(eventData.value[i].type)
        riskEventConclusion.push(eventData.value[i].conclusion ? eventData.value[i].conclusion : '-')
        riskEventConclusionBy.push(eventData.value[i].conclusionBy)
      }
      params.riskEventId = riskEventId.join(",")
      params.riskEventType = riskEventType.join(",")
      params.riskEventConclusion = riskEventConclusion.join(",")
      params.riskEventConclusionBy = riskEventConclusionBy.join(",")

      let mlId: any = []
      let mlConclusion: any = []
      let mlConclusionBy: any = []
      for (let i in mlData.value) {
        mlId.push(mlData.value[i].id)
        mlConclusion.push(mlData.value[i].conclusion ? mlData.value[i].conclusion : '-')
        mlConclusionBy.push(mlData.value[i].conclusionBy)
      }
      params.mlId = mlId.join(",")
      params.mlConclusion = mlConclusion.join(",")
      params.mlConclusionBy = mlConclusionBy.join(",")

      let badActorId: any = []
      let badActorConclusion: any = []
      let badActorConclusionBy: any = []
      for (let i in badActorData.value) {
        badActorId.push(badActorData.value[i].id)
        badActorConclusion.push(badActorData.value[i].conclusion ? badActorData.value[i].conclusion : '-')
        badActorConclusionBy.push(badActorData.value[i].conclusionBy)
      }
      params.badActorId = badActorId.join(",")
      params.badActorConclusion = badActorConclusion.join(",")
      params.badActorConclusionBy = badActorConclusionBy.join(",")

      let processesId: any = []
      let processesConclusion: any = []
      let processesConclusionBy: any = []
      for (let i in processesData.value) {
        processesId.push(processesData.value[i].id)
        processesConclusion.push(processesData.value[i].conclusion ? processesData.value[i].conclusion : '-')
        processesConclusionBy.push(processesData.value[i].conclusionBy)
      }
      params.processesId = processesId.join(",")
      params.processesConclusion = processesConclusion.join(",")
      params.processesConclusionBy = processesConclusionBy.join(",")


      let huntingId: any = []
      let huntingConclusion: any = []
      let huntingConclusionBy: any = []
      for (let i in huntingData.value) {
        huntingId.push(huntingData.value[i].id)
        huntingConclusion.push(huntingData.value[i].conclusion ? huntingData.value[i].conclusion : '-')
        huntingConclusionBy.push(huntingData.value[i].conclusionBy)
      }
      params.huntingId = huntingId.join(",")
      params.huntingConclusion = huntingConclusion.join(",")
      params.huntingConclusionBy = huntingConclusionBy.join(",")

      console.log('values', params);
      saveOrUpdate(params, isUpdate.value).then(() => {
        router.push("/investigation/InvestigationVOList");
      });

    })
    .catch(error => {
      console.log('error', error);
      saveLoading.value = false
    });
};


const closeRetun = () => {
  if (fromTab.value) {
    router.go(-1);
  } else {
    router.push("/investigation/InvestigationVOList");
  }

}


const InvestigationChange = () => {
  console.log(formData.investigation);
  isExist({"investigation": formData.investigation}).then((data) => {
    console.log(data)
    formData.investigation = data.data;
    if (data > 0) {
      //formData.investigation = formData.investigation + "#" + data;
    }
  });
}

const riskEventRef = ref()

function showRiskEvent() {
  let ids: any = [];
  for (let i in eventData.value) {
    ids.push(eventData.value[i].eventId)
  }
  riskEventRef.value.open(ids.join(","), formData.socTenantId)
}

const eventData = ref<any[]>([])

async function addRiskEvent(list, conclusion) {
  for (let i in list) {
    list[i].conclusion = conclusion
    list[i].conclusionBy = userStore.userInfo?.username
    list[i].avatar = userStore.userInfo?.avatar
    await loadEventInfo(list[i])
  }
  eventData.value = eventData.value.concat(list)
}


async function loadEventInfo(data) {
  if (data.type == "1") {
    await securityById({id: data.eventId}).then((data2) => {
      data.info = data2
    })
  } else if (data.type == "2") {
    await hostById({id: data.eventId}).then((data2) => {
      data.info = data2
    })
  }
}

const mlRef = ref()
const mlData = ref<any[]>([])

function showMl() {
  let ids: any = [];
  for (let i in mlData.value) {
    ids.push(mlData.value[i].id)
  }
  mlRef.value.open(ids.join(","), formData.socTenantId)
}

function addMl(list, conclusion) {
  for (let i in list) {
    list[i].conclusion = conclusion
    list[i].conclusionBy = userStore.userInfo?.username
    list[i].avatar = userStore.userInfo?.avatar
  }
  mlData.value = mlData.value.concat(list)
}


const badActorRef = ref()
const badActorData = ref<any[]>([])

function showBadActor() {
  let ids: any = [];
  for (let i in badActorData.value) {
    ids.push(badActorData.value[i].id)
  }
  badActorRef.value.open(ids.join(","), formData.socTenantId)
}

function addBadActor(list, conclusion) {
  for (let i in list) {
    list[i].conclusion = conclusion
    list[i].conclusionBy = userStore.userInfo?.username
    list[i].avatar = userStore.userInfo?.avatar
  }
  badActorData.value = badActorData.value.concat(list)
}


const processesRef = ref()
const processesData = ref<any[]>([])

function showProcesses() {
  let ids: any = [];
  for (let i in processesData.value) {
    ids.push(processesData.value[i].id)
  }
  processesRef.value.open(ids.join(","), formData.socTenantId)
}

function addProcesses(list, conclusion) {
  for (let i in list) {
    list[i].conclusion = conclusion
    list[i].conclusionBy = userStore.userInfo?.username
    list[i].avatar = userStore.userInfo?.avatar
  }
  processesData.value = processesData.value.concat(list)
}


function tenantChange(value) {
  console.log(value)
  //添加了数据，提示用户，如果更改需要清空已选数据
  if (eventData.value.length > 0 ||
    mlData.value.length > 0 ||
    badActorData.value.length > 0 ||
    processesData.value.length > 0 ||
    huntingData.value.length > 0) {
    Modal.confirm({
      title: t('routes.investigation.tenantConfirm.title'),
      content: t('routes.investigation.tenantConfirm.content'),
      okText: t('common.okText'),
      cancelText: t('common.cancelText'),
      onOk: () => {
        eventData.value = []
        mlData.value = []
        badActorData.value = []
        processesData.value = []
        huntingData.value = []
        oldSocTenantId = value
      },
      onCancel: () => {
        formData.socTenantId = oldSocTenantId
      }
    });
  } else {
    oldSocTenantId = value
  }
}

function loadTenantListData(ids) {
  queryList({ids: ids}).then(data => {
    console.log(data)
    let list: any = []
    for (let i in data) {
      list.push({
        "value": data[i].id,
        "text": data[i].name,
        "label": data[i].name,
        "title": data[i].name,
      })
    }
    tenantList.value = list
  })
}

</script>

<style lang="less" scoped>
.form-container {
  background: @bg-color;
  padding: 16px 16px 0;
}

.tab-content-wrapper {
  margin-top: 15px;

  /deep/ .ant-tabs-nav {
    margin: 0px !important;
  }
}

.inves-formItem {
  width: 10%;
  display: inline-block;
  padding: 0 5px;
}

.modal-top {

  display: flex;
  align-items: center;
  justify-content: space-between;
}

</style>
