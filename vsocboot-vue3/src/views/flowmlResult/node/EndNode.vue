<template>
  <div class="end_div">
    <div class="left_line_div" id="end_line"></div>
    <div class="line_div"></div>
    <div class="end_node">
      <img src="../../../assets/images/mlNode/end.png" style="width: 25px;"/>
      <div>
        {{tp("End")}}
      </div>
    </div>
  </div>

</template>

<script setup lang="ts">
import {useI18n} from "/@/hooks/web/useI18n";

const {t} = useI18n();

function tp(name) {
  return t('routes.FlowmlRule.' + name);
}
</script>

<style scoped lang="less">
.end_div {

  @node-border-color: rgba(255, 255, 255, 0.2);
  @node-line-color: rgba(255, 255, 255, 0.8);
  @border-size: 3px;
  @line-border-size: 2px;

  display: flex;

  .line_div {
    width: 20px;
    flex: 0 0 20px;
    border-top: @line-border-size solid @node-line-color;
    position: relative;
    top: 47px;
    height: 0px;
  }

  .left_line_div {
    width: 2px;
    height: calc(100% - 79px);
    border-left: @line-border-size solid @node-line-color;
    top: 47px;
    position: relative;
    min-height: 2px;
  }

  .end_node {
    border: @border-size solid @node-border-color;
    width: 88px;
    flex: 0 0 88px;
    height: 88px;
    border-radius: 12px;
    text-align: center;
    padding: 18px 0px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
  }
}

</style>
