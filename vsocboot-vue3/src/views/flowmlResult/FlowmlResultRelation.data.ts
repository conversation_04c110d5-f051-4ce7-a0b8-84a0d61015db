import {BasicColumn} from '/@/components/Table';
import {FormSchema} from '/@/components/Table';
import {useI18n} from "/@/hooks/web/useI18n";

const {t} = useI18n();

//该属性是国际化中英文配置，需要在/src/locales/lang/en/routes中创建FlowmlResultRelation.ts文件，把下方属性复制到文件中
/*
export default {
     'eventId': '事件id',
     'fromNode': 'from节点',
     'toNode': 'to节点',
     'versionKey': '版本',
};
*/

export const columns: BasicColumn[] = [
   {
    title: t('routes.FlowmlResultRelation.eventId'),
    dataIndex: 'eventId'
   },
   {
    title: t('routes.FlowmlResultRelation.fromNode'),
    dataIndex: 'fromNode'
   },
   {
    title: t('routes.FlowmlResultRelation.toNode'),
    dataIndex: 'toNode'
   },
   {
    title: t('routes.FlowmlResultRelation.versionKey'),
    dataIndex: 'versionKey'
   },
];

export const searchFormSchema: FormSchema[] = [
 {
    label: t('routes.FlowmlResultRelation.eventId'),
    field: 'eventId',
    component: 'Input'
  },
 {
    label: t('routes.FlowmlResultRelation.fromNode'),
    field: 'fromNode',
    component: 'Input'
  },
];

export const formSchema: FormSchema[] = [
  // TODO 主键隐藏字段，目前写死为ID
  {label: '', field: 'id', component: 'Input', show: false},
  {
    label: t('routes.FlowmlResultRelation.eventId'),
    field: 'eventId',
    component: 'Input',
  },
  {
    label: t('routes.FlowmlResultRelation.fromNode'),
    field: 'fromNode',
    component: 'Input',
  },
  {
    label: t('routes.FlowmlResultRelation.toNode'),
    field: 'toNode',
    component: 'Input',
  },
  {
    label: t('routes.FlowmlResultRelation.versionKey'),
    field: 'versionKey',
    component: 'Input',
  },
];
