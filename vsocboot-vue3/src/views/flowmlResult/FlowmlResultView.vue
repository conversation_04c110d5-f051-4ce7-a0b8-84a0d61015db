<template>
  <div style="padding: 10px">
    <div style="padding: 10px 0;position: relative;">
      <span class="goBack" @click="goBack">
        <Icon icon="ant-design:left-outlined" style="margin-right: 5px;cursor: pointer;"/>
        {{t('common.goBack')}}
      </span>
      <div style="position: absolute;right: 10px;top:0;">
        <a-dropdown :trigger="['click']" v-if="hasPermission('ticket:useinternel-2') ||
                          hasPermission('ticket:useSS-2') ||
                          hasPermission('ticket:useinternel-1') ||
                          hasPermission('ticket:useIssued-1')">
          <a-button style="margin-right: 10px;">
            {{ t('common.ticketBtn') }}
          </a-button>
          <template #overlay>
            <a-menu>
              <ApplyMenu v-model:workflowList="workflowList" eventType="2" type="2"
                         :record="eventInfo"/>
            </a-menu>
          </template>
        </a-dropdown>
        <a-button style="margin-right: 10px;" @click="showAddWhite">
          {{t('routes.WhitelistVO.addWhitelist')}}
        </a-button>
        <a-dropdown :trigger="['click']" v-if="hasPermission('investigation:add') ||
                            hasPermission('investigation:join')">
          <a-button style="margin-right: 10px;" @click.prevent="loadInvestigation">
            {{ t('common.investigation') }}
          </a-button>
          <template #overlay>
            <a-menu>
              <a-menu-item key="0" @click="showAddInvestigation(eventInfo.id)"
                           v-if="hasPermission('investigation:add')">
                <Icon icon="ant-design:plus-outlined"/>
                {{ t('routes.riskLogs.add') }}
              </a-menu-item>
              <a-menu-divider/>
              <a-menu-item v-for="item in investigationData" :key="item.id"
                           @click="addInvestigation(item,eventInfo.id)">
                <span>{{ item['investigation'] }}</span>
              </a-menu-item>
              <a-menu-divider/>
              <a-menu-item key="1" @click="showMoreInvestigation(eventInfo.id)">
                <span>{{ t('routes.riskLogs.addMore') }}</span>
              </a-menu-item>
            </a-menu>
          </template>
        </a-dropdown>
        <a-button @click="page = !page">{{t('routes.FlowmlRule.switch')}}</a-button>
      </div>
    </div>

    <div v-show="page">
      <div class="writing_div">
        <div class="nodes_div">
          <div class="right_node">
            <div class="right_node_content">
              <div style="display: flex;padding-right: 16px;">
                <StartNode :node-map="nodeMap" @selectNode="selectNode"
                           @showAllDialog="showAllDialog"
                           @addNodeShow="addNodeShow"/>
                <EndNode/>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div v-show="currentNode.code" class="border_top" style="padding-top: 10px;">
        <h3>{{ currentNode['name'] }}</h3>
        <div>
          {{ currentNode['condition'] }}
        </div>
        <div class="border_top" style="margin: 5px 0px;">
          <div v-if="logsShow">
            <LogList ref="logListRef" :params="logsParams"/>
          </div>
        </div>
      </div>
      <ViewAllNode ref="viewAllNodeRef" @addNode="addNodeShow2" @delNode="delNodeShow"/>
    </div>
    <div v-show="!page">
      <FlowmlResultView2 :id="id"/>
    </div>
    <WhitelistVOModal @register="registerModal" type="2"/>
    <InvestigationListModal ref="registerRiskLogsModal" :eventId="LogId" type="3"
                   :socTenantId="eventInfo.socTenantId"/>
    <a-modal v-model:visible="inveVisible" :title="t('common.confirm')" @ok="saveToInve" @cancel="conclusion = ''"
             :maskClosable=false :destroyOnClose="true">
      <a-row style="padding: 0 24px;">
        <a-col :span="24">
          {{t('routes.riskLogs.addInvestigationPrompt')}}
        </a-col>
        <a-col :span="24">
          <a-form class="antd-modal-form"
                  autocomplete="off" :layout="formLayout">
            <a-form-item :label="t('routes.FlowmlRule.conclusion')">
              <a-textarea v-model:value="conclusion"/>
            </a-form-item>
          </a-form>
        </a-col>
      </a-row>
    </a-modal>
  </div>
</template>

<script lang="ts" name="flowmlResult-flowmlResultRelation" setup>
import {nextTick, onMounted, provide, ref} from 'vue';
import {queryByEventId} from "/@/views/flowmlResult/FlowmlResultRelation.api";
import StartNode from "/@/views/flowmlResult/node/StartNode.vue";
import EndNode from "/@/views/flowmlResult/node/EndNode.vue";
import {invMlSave, queryById} from "/@/views/mlView/MlEvent.api";
import {useRouter} from "vue-router";
import LogList from './LogList.vue'
import ViewAllNode from "/@/views/flowmlResult/ViewAllNode.vue";
import FlowmlResultView2 from "/@/views/flowmlResult/FlowmlResultView2.vue";
import {useModal} from "/@/components/Modal";
import WhitelistVOModal from "/@/views/whitelist/modules/WhitelistVOModal.vue";
import ApplyMenu from "/@/views/workflow/view/ApplyMenu.vue";
import {usePermission} from "/@/hooks/web/usePermission";
import {queryEntryTicket} from "/@/views/workflow/view/ts/TicketUtils";
import {useI18n} from "/@/hooks/web/useI18n";
import {loadInvestigationTop5} from "/@/views/investigation/InvestigationVO.api";
import {useUserStore} from "/@/store/modules/user";
import {formLayout} from "/@/settings/designSetting";
import InvestigationListModal from "/@/views/investigation/modules/InvestigationListModal.vue";

const props = defineProps({
  isModal: {
    type: Boolean,
    default: false
  },
  closeDrawer: Function
});
const {t} = useI18n();
const {hasPermission} = usePermission();
const currentNode = ref<any>({})
provide("currentNode", currentNode)

const page = ref(true)

const router = useRouter()
const nodeMap = ref<any>({})
let id = sessionStorage.getItem("MlEventModalId")
const workflowList: any = ref([]);
onMounted(() => {
  loadInfo()
  getTickets();
})

async function getTickets() {
  workflowList.value = await queryEntryTicket(2);
}

let dataMap: any = {}

/**
 * 存节点json数据对象 key:parentCode,value:json
 */
const nodeJsonMap = ref<any>({})
provide("nodeJsonMap", nodeJsonMap)
const eventInfo = ref<any>({})

/**
 * 加载并初始化数据
 */
function loadInfo() {
  queryById({id: id}).then(eventData => {
    console.log(eventData)
    eventInfo.value = eventData
    let ruleJson = JSON.parse(eventData.ruleJson)
    console.log(ruleJson)
    let nodes = ruleJson.nodes;
    dataMap = {}
    for (let i = 0; i < nodes.length; i++) {
      nodes[i].nodeCode = nodes[i].code
      dataMap[nodes[i].code] = nodes[i]
    }

    queryByEventId({id: id}).then(data => {
      let resultMap: any = {}
      for (let i = 0; i < data.length; i++) {
        resultMap[data[i].id] = data[i]
      }
      for (let i = 0; i < data.length; i++) {
        let d = data[i]
        if (!d.parent_id) {
          d.parent_id = "start"
        }
        let node = nodeJsonMap.value[d.parent_id]
        if (!node) {
          node = copyData(dataMap[d.node_code])
        }
        let jsonArray = node.json
        if (!jsonArray) {
          jsonArray = []
        }
        jsonArray.push({
          left_data: JSON.parse(d.left_data),
          right_data: JSON.parse(d.right_data),
          id: d.id
        })
        node.id = d.parent_id
        node.json = jsonArray
        nodeJsonMap.value[d.parent_id] = node
      }
      console.log(nodeJsonMap.value)
      let result = copyData(nodeJsonMap.value)

      calcDataNum(result)
    })
  })
}


const maxNum = 2;

/**
 * 计算节点数量，默认只显示 maxNum 个数
 * @param result
 */
function calcDataNum(result) {
  for (let key in result) {
    let num = result[key].json.length
    if (num > maxNum) {
      result[key].json.splice(maxNum, num - maxNum)
      result[key].showBtn = true
    }
  }
  console.log(result)
  nodeMap.value = result
}

/**
 * 复制节点数据
 * @param data
 */
function copyData(data) {
  let list = JSON.parse(JSON.stringify(data))
  return list
}


const logListRef = ref()

const logsShow = ref(false);
const logsParams = ref<any>({});
/**
 * 选择节点，触发查询字段
 */
async function selectNode(data) {

  console.log(data)
  currentNode.value = data
  let leftData = data.json[0].left_data
  let field: any = []
  let fieldValue: any = []
  for (let key in leftData) {
    field.push(key)
    fieldValue.push(leftData[key])
  }
  let d2: any = {
    tableType: data.dataset,
    nodeCode: data.nodeCode,
    eventId: id,
    field: field.join(","),
    fieldValue: fieldValue.join(",")
  }
  logsParams.value = d2;
  logsShow.value = false;
  nextTick(()=>{
    logsShow.value = true;
  })
  // logListRef.value.loadTable(d2)
}


const viewAllNodeRef = ref()

function showAllDialog(data) {
  let allData = nodeJsonMap.value
  let viewAllData = allData[data.id].json;
  let selectData = nodeMap.value[data.id].json
  viewAllNodeRef.value.init(viewAllData, selectData, data)
}

/**
 * 添加两条数据
 * @param data
 */
function addNodeShow(data) {
  let allData = nodeJsonMap.value
  let parentAllList = allData[data.id].json;
  let parentList = nodeMap.value[data.id].json
  let diffList = findDifferencesId(parentAllList, parentList)
  for (let i = 0; i < diffList.length; i++) {
    if (i >= maxNum) {
      nodeMap.value[data.id].showBtn = true
      break
    }
    nodeMap.value[data.id].showBtn = false
    nodeMap.value[data.id].json.push(diffList[i])
  }
}

/**
 * 添加指定数据
 * @param addData 要添加的数据
 * @param data 添加的节点
 */
function addNodeShow2(addData, data) {
  let allData = nodeJsonMap.value
  data.json.push(addData)
  if (data.json.length == allData[data.code].length) {
    data.showBtn = false
  }
}

/**
 *
 * @param delData 删除的数据
 * @param data 删除数据的节点
 */
function delNodeShow(delData, data) {
  let index = -1
  for (let i = 0; i < data.json.length; i++) {
    if (data.json[i].id == delData.id) {
      index = i
      break
    }
  }
  if (index > -1) {
    data.json.splice(index, 1)
    data.showBtn = true
  }
}

function findDifferencesId(allList, list) {
  let all = copyData(allList)
  let diffList: any = []
  for (let i in all) {
    let d = list.find(item => item.id === all[i].id)
    if (!d) {
      diffList.push(all[i])
    }
  }
  return diffList;
}

const [registerModal, {openModal}] = useModal();

function showAddWhite() {
  openModal(true, {
    isUpdate: false,
    showFooter: true
  });
}


const investigationData = ref<any[]>([]);
const LogId = ref<string>("");//记录当前点击的日志id
const loadInvestigation = () => {
  loadInvestigationTop5({socTenantIds: [eventInfo.value.socTenantId]}).then((result) => {
    investigationData.value = result.records;
  });
}

function addInvestigation(data, id): void {
  inveId = data.id
  LogId.value = id
  inveVisible.value = true
}


const inveVisible = ref(false)
let inveId = ""
const conclusion = ref("")
const userStore = useUserStore();
const saveToInve = () => {

  if (addInveFlag) {
    addInveFlag = false
    let param = {
      eventId: LogId.value,
      conclusion: conclusion.value,
      conclusionBy: userStore.userInfo?.username,
      socTenantId: eventInfo.value.socTenantId,
      type: "3"
    }
    sessionStorage.setItem("addInvestigationParam", JSON.stringify(param));
    router.push({
      path: "/investigation/modules/InvestigationNewModal"
    });
    return
  }

  invMlSave({
    investigationId: inveId,
    eventId: LogId.value,
    conclusion: conclusion.value,
    conclusionBy: userStore.userInfo?.username,
    type: "3"
  }).then(() => {
    inveVisible.value = false
    conclusion.value = ""
  })
}
const registerRiskLogsModal = ref();

function showMoreInvestigation(id) {
  LogId.value = id;
  registerRiskLogsModal.value.visible = true;
}

let addInveFlag = false
const showAddInvestigation = (id) => {
  LogId.value = id;
  addInveFlag = true
  inveVisible.value = true
}


function goBack() {
  if(props.isModal){
    props.closeDrawer && props.closeDrawer();
    return;
  }
  let json = sessionStorage.getItem("inRiskEvent_1")
  if (json) {
    sessionStorage.removeItem("inRiskEvent_1")
    sessionStorage.setItem("inRiskEvent_2", json)
  }
  router.go(-1)
}

</script>
<style scoped lang="less">

.border_top {
  border-top: 1px solid @border-color;
}

.writing_div {

  .nodes_div {
    display: flex;

    .left_node {
      width: 150px;
      border-right: 1px solid @border-color;

      .n {
        margin: 5px 0px;
      }

    }

    .right_node {
      padding: 16px;
      //padding-top: 50px;
      width: calc(100%);
      max-height: 500px;
      overflow: auto;
      position: relative;

      .right_node_content {
        display: flex;
        justify-content: space-around;
      }

      .node_masking {
        z-index: 9;
        position: absolute;
        left: 0;
        top: 0;
        right: 0;
        bottom: 0;
      }
    }

  }

  .rule_div {

    .search_div {
      width: 100%;
    }
  }
}
</style>
