import {defHttp} from '/@/utils/http/axios';
import {Modal} from 'ant-design-vue';
import {useI18n} from "/@/hooks/web/useI18n";
const {t} = useI18n();

enum Api {
  list = '/flowmlResult/flowmlResultRelation/list',
  save='/flowmlResult/flowmlResultRelation/add',
  queryByEventId='/flowmlResult/flowmlResultRelation/queryByEventId',
  edit='/flowmlResult/flowmlResultRelation/edit',
  deleteOne = '/flowmlResult/flowmlResultRelation/delete',
  deleteBatch = '/flowmlResult/flowmlResultRelation/deleteBatch',
  importExcel = '/flowmlResult/flowmlResultRelation/importExcel',
  exportXls = '/flowmlResult/flowmlResultRelation/exportXls',
  loadLogList = '/flowmlResult/flowmlResultRelation/loadLogList'
}
/**
 * 导出api
 * @param params
 */
export const getExportUrl = Api.exportXls;
/**
 * 导入api
 */
export const getImportUrl = Api.importExcel;
/**
 * 列表接口
 * @param params
 */
export const list = (params) =>
  defHttp.get({url: Api.list, params});

export const queryByEventId = (params) =>
  defHttp.get({url: Api.queryByEventId, params});

export const loadLogList = (params) =>
  defHttp.get({url: Api.loadLogList, params});


/**
 * 删除单个
 * @param params
 * @param handleSuccess
 */
export const deleteOne = (params,handleSuccess) => {
  return defHttp.delete({url: Api.deleteOne, params}, {joinParamsToUrl: true}).then(() => {
    handleSuccess();
  });
}
/**
 * 批量删除
 * @param params
 * @param handleSuccess
 */
export const batchDelete = (params, handleSuccess) => {
  Modal.confirm({
    title: t('common.delConfirmText'),
    content: t('common.delContent'),
    okText: t('common.okText'),
    cancelText: t('common.cancelText'),
    onOk: () => {
      return defHttp.delete({url: Api.deleteBatch, data: params}, {joinParamsToUrl: true}).then(() => {
        handleSuccess();
      });
    }
  });
}
/**
 * 保存或者更新
 * @param params
 * @param isUpdate 是否是更新数据
 */
export const saveOrUpdate = (params, isUpdate) => {
  let url = isUpdate ? Api.edit : Api.save;
  return defHttp.post({url: url, params});
}
