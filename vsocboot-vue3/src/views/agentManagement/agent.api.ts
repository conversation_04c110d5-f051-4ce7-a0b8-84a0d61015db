import {defHttp} from '/@/utils/http/axios';

enum Api {
  loadAgentList = '/agent/loadAgentList',
  loadAgentStatusStatistic = '/agent/loadAgentStatusStatistic',
  loadAgentLatestVersion = '/agent/loadAgentLatestVersion',
  loadAgentByHostId = '/agent/loadAgentByHostId',
  restartAgent = '/agent/restartAgent',
  restartBatchAgent = '/agent/restartBatchAgent',
  clearException = '/agent/clearException',
  applylog = '/agent/applylog',
  upgrade = '/agent/upgrade',
  batchUpgrade = '/agent/batchUpgrade',
  conflictState = '/agent/conflictState',
  conflictStateBatch = '/agent/conflictStateBatch',
  uninstallAgent = '/agent/uninstallAgent',
  logStatus = '/agent/logStatus',
  pkgMerge = '/agent/pkgMerge',
  agentRecall = '/agent/agentRecall',
  uninstallAgentBatch = '/agent/uninstallAgentBatch',
  loadAgent = '/agent/loadAgent',
  makeInstallCmd = '/agent/makeInstallCmd'
}


export const loadAgentList = (params) => {
  return defHttp.get({url: Api.loadAgentList, params});
}

export const loadAgentStatusStatistic = (params?) => {
  return defHttp.get({url: Api.loadAgentStatusStatistic, params});
}

export const loadAgentLatestVersion = (params?) => {
  return defHttp.get({url: Api.loadAgentLatestVersion, params});
}

export const loadAgentByHostId = (params?) => {
  return defHttp.get({url: Api.loadAgentByHostId, params});
}

export const restartAgentRequest = (params?) => {
  return defHttp.get({url: Api.restartAgent, params});
}

export const restartBatchAgentRequest = (params?) => {
  return defHttp.get({url: Api.restartBatchAgent, params});
}


export const clearExceptionRequest = (params?) => {
  return defHttp.get({url: Api.clearException, params});
}
export const applylogRequest = (params?) => {
  return defHttp.get({url: Api.applylog, params});
}
export const logStatusRequest = (params?) => {
  return defHttp.get({url: Api.logStatus, params});
}

/**
 * 升级
 * @param params
 */
export const upgradeRequest = (params?) => {
  return defHttp.get({url: Api.upgrade, params});
}
/**
 * 批量升级
 * @param params
 */
export const batchUpgradeRequest = (params?) => {
  return defHttp.get({url: Api.batchUpgrade, params});
}

export const conflictStateRequest = (params?) => {
  return defHttp.get({url: Api.conflictState, params});
}
export const conflictStateBatchRequest = (params?) => {
  return defHttp.get({url: Api.conflictStateBatch, params});
}

export const uninstallAgentRequest = (params?) => {
  return defHttp.get({url: Api.uninstallAgent, params});
}

export const pkgMergeRequest = (params?) => {
  return defHttp.get({url: Api.pkgMerge, params});
}

export const agentRecallRequest = (params?) => {
  return defHttp.get({url: Api.agentRecall, params});
}
export const uninstallAgentBatchRequest = (params?) => {
  return defHttp.get({url: Api.uninstallAgentBatch, params});
}
export const loadAgent = (params?) => {
  return defHttp.get({url: Api.loadAgent, params});
}
//生成命令
export const makeInstallCmd = (params) => {
  return defHttp.get({url: Api.makeInstallCmd, params});
}
