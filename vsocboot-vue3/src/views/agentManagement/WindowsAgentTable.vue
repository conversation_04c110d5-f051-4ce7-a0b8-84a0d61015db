<template>

  <!--引用表格-->
  <BasicTable @register="registerTable" :rowSelection="rowSelection as any"
              :isSearch="true">
    <template #form-formFooter>
      <a-dropdown v-if="selectedRowKeys.length > 0">
        <template #overlay>
          <a-menu>
            <a-menu-item key="1" @click="upgraAgentBatch(selectedRowKeys.join())">
              <span>{{ t('routes.agent.upgraagt') }}</span>
            </a-menu-item>
            <!--          <a-menu-item key="2" @click="restartAgentBatch(selectedRowKeys.join())">-->
            <!--            {{ t('routes.agent.restartAgent') }}-->
            <!--          </a-menu-item>-->
            <a-menu-item key="3" @click="unisagentBatch(selectedRowKeys.join())">
              {{ t('routes.agent.unisagent') }}
            </a-menu-item>
            <a-menu-divider/>
            <a-menu-item key="4" @click="clearException(selectedRowKeys.join())">
              {{ t('routes.agent.clearer') }}
            </a-menu-item>
          </a-menu>

        </template>
        <a-button>{{ t('common.batch') }}
          <Icon icon="mdi:chevron-down"/>
        </a-button>
      </a-dropdown>
    </template>
    <template #hostName="{record}">
      <div>{{ record?.ip }}</div>
      <div class="host_name">{{ record?.hostname }}</div>
    </template>
    <!--操作栏-->
    <template #action="{ record }">
      <a-space size="5" class="action-border space-action" >
        <span @click="upgraAgent(record?.host_id)" v-if="record?.agent_enable_upgrade">
          {{ t('routes.agent.upgraagt') }}
        </span>
        <span style="cursor: not-allowed;" v-else>
          {{ t('routes.agent.upgraagt') }}
        </span>
        <a-divider type="vertical"/>
        <a-dropdown :trigger="['click']">
          <span class="ant-dropdown-link" @click="loadLogStatus(record)">
            {{t('common.more')}}
          </span>
          <template #overlay>
            <a-menu>
              <!--              <a-menu-item key="1" :disabled="record?.online === 2"-->
              <!--                           @click="restartAgent(record?.host_id)">-->
              <!--                {{ t('routes.agent.restartAgent') }}-->
              <!--              </a-menu-item>-->
              <a-menu-item key="2" :disabled="record?.online !== 1"
                           @click="unisagent(record?.host_id)">
                {{ t('routes.agent.unisagent') }}
              </a-menu-item>
              <a-menu-divider/>
              <a-menu-item key="3" :disabled="!record?.crash_status"
                           @click="clearException(record?.host_id)">
                {{ t('routes.agent.clearer') }}
              </a-menu-item>
              <a-menu-item key="4" v-if="record?.online === 1"
                           @click="applyLog(record?.host_id,'sysdump')">
                <span v-if="logStatusData.model == 'sysdump'"
                      :class="{error:logStatusData.status === 'failed'}">
                  {{
                    logStatusData.status === 'apply'
                      ? t('routes.agent.colct')
                      : logStatusData.status === 'applying'
                        ? t('routes.agent.colting')
                        : logStatusData.status === 'down'
                          ? t('routes.agent.download')
                          : t('routes.agent.reCol')
                  }}
                  <span v-if="logStatusData.status === 'applying'">{{
                      logStatusData.progress
                    }}</span>
                  {{ t('routes.agent.sysDumpLog') }}
                </span>
                <span v-else>{{
                    t('routes.agent.colct') + " " + t('routes.agent.sysDumpLog')
                  }}</span>
              </a-menu-item>
              <a-menu-item key="5" v-if="record?.online === 1"
                           @click="applyLog(record?.host_id,'appdump')">
                <span v-if="logStatusData.model == 'appdump'"
                      :class="{error:logStatusData.status === 'failed'}">
                  {{
                    logStatusData.status === 'apply'
                      ? t('routes.agent.colct')
                      : logStatusData.status === 'applying'
                        ? t('routes.agent.colting')
                        : logStatusData.status === 'down'
                          ? t('routes.agent.download')
                          : t('routes.agent.reCol')
                  }}
                  <span v-if="logStatusData.status === 'applying'">{{
                      logStatusData.progress
                    }}</span>
                  {{ t('routes.agent.agtDumpLog') }}
                </span>
                <span v-else>
                  {{ t('routes.agent.colct') + " " + t('routes.agent.agtDumpLog') }}
                </span>
              </a-menu-item>
              <a-menu-item key="6" v-if="record?.online === 1"
                           @click="applyLog(record?.host_id,'applog')">
                <span v-if="logStatusData.model == 'applog'"
                      :class="{error:logStatusData.status === 'failed'}">
                  {{
                    logStatusData.status === 'apply'
                      ? t('routes.agent.colct')
                      : logStatusData.status === 'applying'
                        ? t('routes.agent.colting')
                        : logStatusData.status === 'down'
                          ? t('routes.agent.download')
                          : t('routes.agent.reCol')
                  }}
                  <span v-if="logStatusData.status === 'applying'">{{
                      logStatusData.progress
                    }}</span>
                  {{ t('routes.agent.agtRunLog') }}
                </span>

                <span v-else>
                  {{ t('routes.agent.colct') + " " + t('routes.agent.agtRunLog') }}
                </span>
              </a-menu-item>
            </a-menu>
          </template>
        </a-dropdown>
      </a-space>
    </template>
  </BasicTable>

  <pwdConfirm @register="registerPwdConfirmWin" @ok="pwdOk"/>
</template>

<script lang="ts" setup>
import {defineExpose, ref, unref} from 'vue'
import {BasicTable} from "/@/components/Table";
import {useListPage} from "/@/hooks/system/useListPage";
import {formLayout} from "/@/settings/designSetting";
import {
  applylogRequest,
  batchUpgradeRequest,
  clearExceptionRequest,
  conflictStateBatchRequest,
  conflictStateRequest,
  loadAgentList,
  logStatusRequest,
  restartAgentRequest,
  restartBatchAgentRequest,
  uninstallAgentBatchRequest,
  upgradeRequest
} from "/@/views/agentManagement/agent.api";
import pwdConfirm from './common/pwdConfirm.vue'
import {useI18n} from "/@/hooks/web/useI18n";
import {message, Modal} from "ant-design-vue";
import {searchFormSchema, winColumns} from "/@/views/agentManagement/agent.data";
import {useModal} from "/@/components/Modal";
import {TABLE_CACHE_KEY} from "/@/utils/valueEnum";

const {t} = useI18n();
const osver = 1;

//注册table数据
const {tableContext} = useListPage({
  tableProps: {
    title: '',
    api: loadAgentList,
    rowKey: 'host_id',
    columns: winColumns,
    canResize: false,
    formConfig: {
      baseColProps: {
        lg: 5, // ≥992px
        xl: 3, // ≥1200px
        xxl: 3, // ≥1600px
      },
      labelWidth: 120,
      schemas: searchFormSchema,
      autoSubmitOnEnter: true,
      showAdvancedButton: true,
      layout: formLayout,
    },
    searchInfo: {
      "osver": osver
    },
    actionColumn: {
      align: 'left',
      width: 180,
    },
    tableSetting: {
      cacheKey: TABLE_CACHE_KEY.agentManagementWindow
    },
  },
})

const [registerTable, {reload}, {rowSelection, selectedRowKeys}] = tableContext

const [registerPwdConfirmWin, {openModal}] = useModal();

defineExpose({
  reload
})

/**
 * 重启
 * @param host_id
 */
// function restartAgent(host_id) {
//   openModal(true, {
//     isUpdate: false,
//     showFooter: true,
//     host_id: host_id,
//     title: t('routes.agent.suruinsrst'),
//     type: 'restart'
//   });
// }

/**
 * 批量重启
 * @param host_id
 */
// function restartAgentBatch(host_id) {
//   openModal(true, {
//     isUpdate: false,
//     showFooter: true,
//     host_id: host_id,
//     title: t('routes.agent.suruinsrst'),
//     type: 'restartBatch',
//     osver: osver
//   });
// }

/**
 * 批量升级
 * @param host_id
 */
function upgraAgentBatch(host_id) {
  openModal(true, {
    isUpdate: false,
    showFooter: true,
    host_id: host_id,
    title: t('routes.agent.suruinsrst'),
    type: 'upgradeBatch',
    osver: osver
  });
}


/**
 * 卸载
 * @param host_id
 */
function unisagent(host_id) {
  let param = {
    filter: {},
    host_id: host_id,
    exclude: '',
    type: 1,
    osver: osver
  }
  conflictStateRequest(param).then(data => {
    const errIps = ref([]);

    if (!data.code) {
      errIps.value = data.items ?? [];
    }
    openModal(true, {
      isUpdate: false,
      showFooter: true,
      host_id: host_id,
      title: t('routes.agent.unisagenttit'),
      msg: unref(errIps).length ? t('routes.agent.unsgtips') : t('routes.agent.agttips'),
      type: 'uninstall',
      osver: osver
    });
  })
}

/**
 * 批量卸载
 * @param host_id
 */
function unisagentBatch(host_id) {
  let param = {
    filter: {},
    include: host_id,
    exclude: '',
    type: 1,
    osver: osver
  }
  conflictStateBatchRequest(param).then(data => {
    const errIps = ref([]);
    if (!data.code) {
      errIps.value = data.items ?? [];
    }
    openModal(true, {
      isUpdate: false,
      showFooter: true,
      host_id: host_id,
      title: t('routes.agent.unisagenttit'),
      msg: unref(errIps).length
        ? unref(errIps).length === 1
          ? t('routes.agent.ukgsip', [unref(errIps)[0]])
          : t('routes.agent.ukgsips', [unref(errIps)[0], unref(errIps).length])
        : t('routes.agent.agttips'),
      type: 'uninstallBatch',
      osver: osver
    });
  })
}

/**
 * 清除异常记录
 * @param host_id
 */
function clearException(host_id) {
  openModal(true, {
    isUpdate: false,
    showFooter: true,
    host_id: host_id,
    title: t('routes.agent.clearertit'),
    type: 'clearException',
    osver: osver
  });
}

/**
 * 升级
 * @param host_id
 */
function upgraAgent(host_id) {
  openModal(true, {
    isUpdate: false,
    showFooter: true,
    host_id: host_id,
    title: t('routes.agent.cfmupgagt'),
    type: 'upgrade',
    osver: osver
  });
}

/**
 * 获取日志
 * @param host_id
 * @param model
 */
function applyLog(host_id, model) {
  if (logStatusData.value.model == model && logStatusData.value.status === 'down') {
    Modal.confirm({
      title: t('routes.agent.cfmColD'),
      content: "",
      onOk() {
        const a = document.createElement('a');
        a.href = logStatusData.value.url;
        a.click();
      },
      onCancel() {
      },
    });
    return
  }

  Modal.confirm({
    title: t('routes.agent.cfmColD'),
    content: "",
    onOk() {
      let params = {
        host_id: host_id,
        model: model,
        osver: osver
      }
      applylogRequest(params).then(data => {
        if (data.code) {
          message.warn(data.msg)
        } else {
          message.success(t('routes.agent.startCol'))
        }
      })
    },
    onCancel() {
    },
  });
}

const logStatusData = ref<any>({})

function loadLogStatus(data) {
  if (data?.online === 1) {
    logStatusRequest({host_id: data.host_id, osver: osver}).then(data => {
      logStatusData.value = data
    })
  }
}

function pwdOk(params) {
  //重启
  if (params.type == 'restart') {
    restartAgentRequest(params).then((data) => {
      if (!data.code) {
        message.success("Operation successful!")
      } else {
        message.warn(data.msg)
      }
      reload()
    })
  }
  //批量重启
  else if (params.type == 'restartBatch') {
    restartBatchAgentRequest(params).then((data) => {
      if (!data.code) {
        message.success("Operation successful!")
      } else {
        message.warn(data.msg)
      }
      reload()
    })
  }
  //清楚异常记录
  else if (params.type == 'clearException') {
    clearExceptionRequest(params).then(data => {
      if (data.count != undefined) {
        message.success("Operation successful!")
      } else {
        message.warn(data.msg ?? data.message)
      }
      reload()
    })
  }
  //升级
  else if (params.type == 'upgrade') {
    upgradeRequest(params).then(data => {
      if (data.count != undefined) {
        message.success("Operation successful!")
      } else {
        message.warn(data.msg)
      }
      reload()
    })
  }
  //批量升级
  else if (params.type == 'upgradeBatch') {
    batchUpgradeRequest(params).then(data => {
      if (data.count != undefined) {
        reload()
        message.success("Operation successful!")
      } else {
        message.warn(data.msg)
      }
      reload()
    })
  }
  //卸载
  else if (params.type == 'uninstall') {
    uninstallAgentBatchRequest(params).then(data => {
      if (data.count != undefined) {
        message.success("Operation successful!")
      } else {
        message.warn(data.msg)
      }
      reload()
    })
  }
  //批量卸载
  else if (params.type == 'uninstallBatch') {
    uninstallAgentBatchRequest(params).then(data => {
      if (data.count != undefined) {
        message.success("Operation successful!")
      } else {
        message.warn(data.msg)
      }
      reload()
    })
  }

}


</script>

<style scoped lang="less">
.body_div {
  padding: 8px 16px;
}

.font20 {
  padding: 8px 16px;
}

.border_div {
  border-bottom: 1px solid @border-color;

}

.border-right {
  border-right: 1px solid @border-color;
}

.image_div {
  width: 72px;
  height: 72px;
  text-align: center;
  margin-left: 89px;

  img {
    width: 100%;
  }
}

.host_name {
  color: #666;
}

.background_div {
  padding: 24px;
  background-color: @dark-bg1;
}

.background_span {
  background-color: rgba(48, 140, 255, 0.2);
  padding: 4px 8px;
  border-radius: 6px;
}

.fontColor6 {
  color: rgba(255, 255, 255, 0.6);
}

.error {
  color: red;
}

.upbtn {

}

.uploadprogre {
  position: absolute;
  left: 0;
  right: 0;
  bottom: -8px;
}

.extraInfo {
  position: absolute;
  bottom: 4px;
  right: 16px;
  color: rgba(255, 255, 255, 0.8);
  font-size: @fz12;

  .speed {
    margin-right: 16px;
  }
}
</style>
