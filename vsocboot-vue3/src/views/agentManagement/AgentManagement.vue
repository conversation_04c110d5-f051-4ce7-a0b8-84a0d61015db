<template>
  <div class="error_div" v-if="errorFlag">
    {{ t('routes.agent.error_msg') }}
  </div>

  <div class="flex flex-row agent_chart_wrapper h-200px m-16px">
    <div class="w-220px flex flex-col items-center justify-center gap-24px border-right">

      <img src="./image/image.png" class="w-72px h-72px"/>
      <a-button
        @click="
            upgradeVisible = true;
            loadAgentLatestVersionData();
          "
        v-if="agentInit"
      >
        {{ t('routes.agentManagement.upgradePackage') }}
      </a-button>

    </div>
    <div class="flex1 border-right" v-if="showAgentWindows">
      <div class="flex flex-col gap-24px py-16px px-24px">
        <div class="flex flex-row items-center gap-12px">
          <div class="font14">
            {{ t('routes.agentManagement.windowsVersion') }}
          </div>
          <div class="primaryColor cursor-pointer" @click="makeCmd(1)" v-if="agentInit">
            {{ t('routes.agentManagement.generateInstallationCommands') }}
          </div>
        </div>
        <div class="flex flex-row items-center h-100px gap-24px">
          <div class="w-100px" style="height: 100px;" ref="pie1"></div>
          <div class="flex-1 flex flex-row gap-24px  items-center">
            <div class="flex flex-col gap-8px">
              <div class="font14 fcolor">{{ t('routes.agent.latestUpdateHost') }}</div>
              <div class="font24 primaryColor">{{ agentStatusData.val1 }}</div>
            </div>
            <div class="flex flex-col gap-8px">
              <div class="font14 fcolor">{{ t('routes.agent.updatableHost') }}</div>
              <div class="font24" style="color:#E6DE50">{{ agentStatusData.val2 }}</div>
            </div>
            <div class="flex flex-col gap-8px">
              <div class="font14 fcolor">{{ t('routes.agent.abnormalHost') }}</div>
              <div class="font24 ax-color-orange">{{ agentStatusData.val3 }}</div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="flex1" v-if="showAgentLinux">

      <div class="flex flex-col gap-24px py-16px px-24px">
        <div class="flex flex-row items-center gap-12px">
          <div class="font14">
            {{ t('routes.agentManagement.linuxVersion') }}
          </div>
          <div class="primaryColor cursor-pointer" @click="makeCmd(2)" v-if="agentInit">
            {{ t('routes.agentManagement.generateInstallationCommands') }}
          </div>
        </div>
        <div class="flex flex-row items-center h-100px gap-24px">
          <div class="w-100px" style="height: 100px;" ref="pie2"></div>
          <div class="flex-1 flex flex-row gap-24px  items-center">
            <div class="flex flex-col gap-8px">
              <div class="font14 fcolor">{{ t('routes.agent.latestUpdateHost') }}</div>
              <div class="font24 primaryColor">{{ agentStatusData.val4 }}</div>
            </div>
            <div class="flex flex-col gap-8px">
              <div class="font14 fcolor">{{ t('routes.agent.updatableHost') }}</div>
              <div class="font24" style="color:#E6DE50">{{ agentStatusData.val5 }}</div>
            </div>
            <div class="flex flex-col gap-8px">
              <div class="font14 fcolor">{{ t('routes.agent.abnormalHost') }}</div>
              <div class="font24 ax-color-orange">{{ agentStatusData.val6 }}</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <div class="relative pt-8px">
    <a-tabs>
      <a-tab-pane key="1" tab="Linux" style="position: relative" v-if="showAgentLinux">
        <div style="max-height: calc(100vh - 230px);overflow: auto;">
          <LinuxAgentTable ref="linuxRef" v-if="agentInit"/>
        </div>
      </a-tab-pane>
      <a-tab-pane key="2" tab="Windows" style="position: relative" force-render
                  v-if="showAgentWindows">
        <div style="max-height: calc(100vh - 230px);overflow: auto;">
          <WindowsAgentTable ref="windowsRef" v-if="agentInit"/>
        </div>
      </a-tab-pane>
    </a-tabs>
  </div>


  <a-modal
    v-model:visible="upgradeVisible"
    :title="t('routes.agentManagement.upgradePackage')"
    :width="showAgentWindows ? '800px' : '400px'"
    :footer="null"
    :maskClosable="false"
  >
    <template #closeIcon>
      <div class="ax-icon-button ax-icon-large">
        <span class="soc ax-com-Close ax-icon"></span>
      </div>
    </template>

    <div class="flex p-16px gap-16px" :class="showAgentWindows ? 'flex-row' : 'flex-col'">
      <div class="flex-1 flex flex-col" v-if="showAgentWindows">
        <a-spin :tip="latestVersionData.windows.spinText"
                :spinning="latestVersionData.windows.spinning">
          <div class="background_div flex flex-col gap-24px">
            <div class="flex flex-row gap-16px items-center">
              <img src="./image/windows.png" style="width: 80px; height: 80px"/>
              <div class="flex flex-col gap-8px">
                <div class="font20">
                  {{ t('routes.agentManagement.windowsAgent') }}
                </div>
                <div class="flex">
                  <span class="ax-label ax-label-primary"> {{
                      t('routes.agentManagement.windows')
                    }} </span>
                </div>

              </div>
            </div>
            <div class="flex flex-row">
              <div class="flex flex-col gap-4px flex-1">
                <div class="fcolor1">{{ t('routes.agentManagement.version') }}</div>
                <div class="font14 fcolor">{{ latestVersionData.windows.version }}</div>
              </div>
              <div class="flex flex-col gap-4px flex-1">
                <span class="fcolor1">{{ t('routes.agentManagement.releaseTime') }}</span>
                <div class="font14 fcolor">{{ latestVersionData.windows.time }}</div>
              </div>
            </div>
            <div class="flex flex-row gap-16px mt-auto justify-end">
              <a-button
                :disabled="
                  (latestVersionData.windows.uplpadPrg !== 0 && latestVersionData.windows.uplpadPrg !== 100) ||
                  !latestVersionData.windows.enable_recall
                "
                @click="rollBack(1)"

              >

                {{ t('routes.agentManagement.rollback') }}
              </a-button>
              <a-upload
                name="file"
                accept=".exe"
                :action="winUploadUrl"
                :headers="headers"
                :maxCount="1"
                @change="handleChange"
                :showUploadList="false"
              >
                <a-button type="primary"
                          :disabled="latestVersionData.windows.uplpadPrg !== 0 && latestVersionData.windows.uplpadPrg !== 100">
                  <span class="soc ax-com-Upload ax-icon"></span> {{ t('routes.agent.updPkg') }}
                </a-button>
              </a-upload>
              <!-- 进度条 start -->
              <Progress
                class="uploadprogre"
                :strokeWidth="2"
                :showInfo="false"
                strokeColor="#18ba79"
                :percent="latestVersionData.windows.uplpadPrg"
                v-if="latestVersionData.windows.uplpadPrg !== 0 && latestVersionData.windows.uplpadPrg !== 100"
                status="active"
              />
              <!-- 进度条 end -->
            </div>
          </div>
        </a-spin>
      </div>
      <div class="flex-1 flex flex-col">
        <a-spin :tip="latestVersionData.linux.spinText"
                :spinning="latestVersionData.linux.spinning">
          <div class="background_div flex flex-col gap-24px">
            <div class="flex flex-row gap-16px items-center">
              <img src="./image/linux.png" style="width: 80px; height: 80px"/>
              <div class="flex flex-col gap-8px">
                <div class="font20">
                  {{ t('routes.agentManagement.linuxAgent') }}
                </div>
                <div class="flex">
                  <span class="ax-label ax-label-primary"> {{
                      t('routes.agentManagement.linux')
                    }} </span>
                </div>
              </div>
            </div>
            <div class="flex flex-row">
              <div class="flex flex-col gap-4px flex-1">
                <div class="fcolor1">{{ t('routes.agentManagement.version') }}</div>
                <div class="font14 fcolor">{{ latestVersionData.linux.version }}</div>
              </div>
              <div class="flex flex-col gap-4px flex-1">
                <span class="fcolor1">{{ t('routes.agentManagement.releaseTime') }}</span>
                <div class="font14 fcolor">{{ latestVersionData.linux.time }}</div>
              </div>
            </div>
            <div class="flex flex-row gap-16px mt-auto justify-end">
              <AgentUpload
                accept=".gz"
                ref="simpleUploadLinux"
                :target="uploadUrl"
                upload-type="linux"
                @uploadResult="uploadResult"
                v-model:validateMd5="latestVersionData.linux.validateMd5"
              >
                <div class="w-[100%] flex flex-row gap-16px mt-auto justify-end">
                  <a-button
                    :disabled="
                      (latestVersionData.linux.uplpadPrg !== 0 && latestVersionData.linux.uplpadPrg !== 100) || !latestVersionData.linux.enable_recall
                    "
                    @click="rollBack(2)"
                  >
                    {{ t('routes.agentManagement.rollback') }}
                  </a-button>
                  <a-button
                    type="primary"
                    @click="triggerUpload(1)"
                    class="upbtn"
                    :loading="
                      (latestVersionData.linux.uplpadPrg !== 0 && latestVersionData.linux.uplpadPrg !== 100) || latestVersionData.linux.is_merge
                    "
                  >
                    <span class="soc ax-com-Upload ax-icon"></span> {{
                      latestVersionData.linux.is_merge ? t('routes.agent.mergeing') : t('routes.agent.updPkg')
                    }}
                  </a-button>
                </div>
              </AgentUpload>
              <!-- 进度条 start -->
              <Progress
                class="uploadprogre"
                :strokeWidth="2"
                :showInfo="false"
                strokeColor="#18ba79"
                :percent="latestVersionData.linux.uplpadPrg"
                v-if="latestVersionData.linux.uplpadPrg !== 0 && latestVersionData.linux.uplpadPrg !== 100"
                status="active"
              />
              <!-- 进度条 end -->
              <!-- 速度 start -->
              <div class="extraInfo"
                   v-if="latestVersionData.linux.uplpadPrg !== 0 && latestVersionData.linux.uplpadPrg !== 100">
                <span class="speed">{{ latestVersionData.linux.speed + 'M/s' }}</span>
                <span class="timer">{{
                    t('routes.agent.shyfhs') + formatSeconds(latestVersionData.linux.timeRemaining as number)
                  }}</span>
              </div>
              <!-- 速度 end -->
            </div>
          </div>
        </a-spin>
      </div>
    </div>
    <pwdConfirm @register="registerPwdConfirm" @ok="pwdOk"/>
  </a-modal>

  <a-modal v-model:visible="cmdVisible" :title="t('routes.agentManagement.commandInstallation')"
           width="800px" :footer="null" :maskClosable="false">
    <template #closeIcon>
      <div class="ax-icon-button ax-icon-large">
        <span class="soc ax-com-Close ax-icon"></span>
      </div>
    </template>

    <div class="p-16px flex flex-col gap-16px">
      <template v-if="osverType === 2">
        <div class="flex flex-col gap-8px">
          <div class="font13 fcolor1">{{
              t('routes.agentManagement.wgetInstallationCommand')
            }}
          </div>
          <div class="cmdCopy">
            <a-textarea v-model:value="commandStrWget"/>
            <a-button type="text" size="small" class="btn" :disabled="!!!commandStrWget.length"
                      @click="copyCommand('wget')">
              <Icon icon="ant-design:copy-outlined" :size="14"/>
            </a-button>
          </div>
        </div>
        <div class="flex flex-col gap-8px">
          <div class="font13 fcolor1">{{
              t('routes.agentManagement.curlInstallationCommand')
            }}
          </div>
          <div class="cmdCopy">
            <a-textarea   v-model:value="commandStrCurl"/>
            <a-button type="text" size="small" class="btn" :disabled="!!!commandStrCurl.length"
                      @click="copyCommand('curl')">
              <Icon icon="ant-design:copy-outlined" :size="14"/>
            </a-button>
          </div>
        </div>
      </template>
      <div class="flex flex-col gap-8px" v-if="osverType === 1">
        <div class="font13 fcolor1">{{ t('routes.agentManagement.wgetInstallationCommand') }}</div>
        <div class="cmdCopy">
          <a-textarea  v-model:value="commandStrWget"/>
          <a-button type="text" size="small" class="btn" :disabled="!!!commandStrWget.length"
                    @click="copyCommand('wget')">
            <Icon icon="ant-design:copy-outlined" :size="14"/>
          </a-button>
        </div>
      </div>
    </div>
  </a-modal>
</template>

<script lang="ts" setup>
import {
  computed,
  onBeforeUnmount,
  onMounted,
  reactive,
  Ref,
  ref,
  unref,
  watch,
  nextTick
} from 'vue';
import {useECharts} from '/@/hooks/web/useECharts';
import {EChartsOption} from 'echarts';
import {
  agentRecallRequest,
  loadAgent,
  loadAgentLatestVersion,
  loadAgentStatusStatistic,
  makeInstallCmd,
} from '/@/views/agentManagement/agent.api';
import AgentUpload from './AgentUpload.vue';
import {useI18n} from '/@/hooks/web/useI18n';
import {message, Progress, UploadChangeParam} from 'ant-design-vue';
import pwdConfirm from './common/pwdConfirm.vue';
import {useModal} from '/@/components/Modal';
import {useGlobSetting} from '/@/hooks/setting';
import {useServerUpload} from '/@/views/agentManagement/server-upload';
import LinuxAgentTable from './LinuxAgentTable.vue';
import WindowsAgentTable from './WindowsAgentTable.vue';
import {getToken} from '/@/utils/auth';
import useClipboard from 'vue-clipboard3';

const {t} = useI18n();
const serverUpload = useServerUpload();
const upgradeVisible = ref(false);

const headers = reactive({
  'X-Access-Token': getToken(),
});

const showAgentLinux = ref(false);
const showAgentWindows = ref(false);
loadAgent().then((data) => {
  console.log(data);
  showAgentLinux.value = data.linux === 'true';
  showAgentWindows.value = data.windows === 'true';
  console.log(showAgentLinux.value);
  console.log(showAgentWindows.value);
});

const windowsRef = ref();
const linuxRef = ref();

const latestVersionData = reactive({
  windows: {
    version: '-',
    time: '-',
    is_merge: false,
    spinText: '',
    spinning: false,
    validateMd5: 0,
    uplpadPrg: 0,
    speed: 0,
    timeRemaining: 0,
    enable_recall: false,
  },
  linux: {
    version: '-',
    time: '-',
    is_merge: false,
    spinText: '',
    spinning: false,
    validateMd5: 0,
    uplpadPrg: computed(() => serverUpload.linux.progress),
    speed: computed(() => serverUpload.linux.speed),
    timeRemaining: computed(() => serverUpload.linux.timeRemaining),
    enable_recall: false,
  },
});

watch(
  () => latestVersionData.linux.validateMd5,
  (v) => {
    if (v === 100 || v === 0) {
      latestVersionData.linux.spinning = false;
      latestVersionData.linux.spinText = '';
    } else {
      latestVersionData.linux.spinText = t('routes.agent.pasingmd5') + v + '%';
      latestVersionData.linux.spinning = true;
    }
  }
);

const {apiUrl} = useGlobSetting();
const uploadUrl = apiUrl + '/agent/pkgUpload';
const winUploadUrl = apiUrl + '/agent/packageUpload';

onMounted(() => {
  loadAgentStatusStatisticData();
});
onBeforeUnmount(() => {
  window.clearInterval(timerAgent);
});

const agentStatusData = ref({
  val1: 0,
  val2: 0,
  val3: 0,
  val4: 0,
  val5: 0,
  val6: 0,
});
const agentInit = ref(false);
const errorFlag = ref(false);

function loadAgentStatusStatisticData() {
  loadAgentStatusStatistic({osver: 2})
    .then((data) => {
      agentInit.value = true;
      let body = data.body;
      let list = body.agent_count_items;
      for (let i in list) {
        if (list[i].upgrade_status == 1) {
          agentStatusData.value.val4 = list[i].host_count;
        } else if (list[i].upgrade_status == 2) {
          agentStatusData.value.val5 = list[i].host_count;
        } else if (list[i].upgrade_status == 5) {
          agentStatusData.value.val6 = list[i].host_count;
        }
      }
      loadPie2();
    })
    .catch((error) => {
      agentInit.value = false;
      errorFlag.value = true;
      message.destroy();
      console.log(error);
    });
  loadAgentStatusStatistic({osver: 1})
    .then((data) => {
      let body = data.body;
      let list = body.agent_count_items;
      for (let i in list) {
        if (list[i].upgrade_status == 1) {
          agentStatusData.value.val1 = list[i].host_count;
        } else if (list[i].upgrade_status == 2) {
          agentStatusData.value.val2 = list[i].host_count;
        } else if (list[i].upgrade_status == 5) {
          agentStatusData.value.val3 = list[i].host_count;
        }
      }
      loadPie1();
    })
    .catch((error) => {
      message.destroy();
      console.log(error);
    });
}

const pie1 = ref<HTMLDivElement | null>(null);
const {setOptions: setOptions1} = useECharts(pie1 as Ref<HTMLDivElement>);

const pie2 = ref<HTMLDivElement | null>(null);
const {setOptions: setOptions2} = useECharts(pie2 as Ref<HTMLDivElement>);

function loadPie1() {
  let option: EChartsOption = {
    color: ['#308cff', '#E2CD00', '#F8A556'],
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b} : {c}'
    },
    legend: {
      show: false,
    },
    series: [
      {
        name: 'Windows version',
        type: 'pie',
        radius: ['30px', '45px'],
        avoidLabelOverlap: false,
        label: {
          show: false,
          position: 'center',
        },
        labelLine: {
          show: false,
        },
        // emphasis: {
        //     scale: false // 取消鼠标悬停变大效果
        // },

        data: [
          {value: agentStatusData.value.val1, name: 'Latest update host'},
          {value: agentStatusData.value.val2, name: 'Updatable host'},
          {value: agentStatusData.value.val3, name: 'Abnormal host'},
        ],
      },
    ],
  };
  setOptions1(option);
  // Ensure the chart resizes to the container height
  nextTick(() => {
    const {resize} = useECharts(pie1 as Ref<HTMLDivElement>);
    resize();
  });
}

function loadPie2() {
  let option: EChartsOption = {
    color: ['#308cff', '#E2CD00', '#F8A556'],
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b} : {c}'
    },
    legend: {
      show: false,
    },
    emphasis: {
      scale: false // 取消鼠标悬停变大效果
    },
    series: [
      {
        name: 'Linux version',
        type: 'pie',
        radius: ['30px', '45px'],
        avoidLabelOverlap: false,
        label: {
          show: false,
          position: 'center',
        },
        labelLine: {
          show: false,
        },
        // emphasis: {
        //   scale: false // 取消鼠标悬停变大效果
        // },
        data: [
          {value: agentStatusData.value.val4, name: 'Latest update host'},
          {value: agentStatusData.value.val5, name: 'Updatable host'},
          {value: agentStatusData.value.val6, name: 'Abnormal host'},
        ],
      },
    ],
  };
  setOptions2(option);
}

let timerAgent: any = 0;

function loadAgentLatestVersionData() {
  loadAgentLatestVersion({osver: 2}).then((data) => {
    let body = data.body;
    if (body.linux) {
      latestVersionData.linux.version = body.linux.package_version;
      latestVersionData.linux.time = body.linux.created_at;
      latestVersionData.linux.is_merge = body.linux.is_merge;
      latestVersionData.linux.enable_recall = body.linux.enable_recall;
      if (body.linux.is_merge) {
        setInterGetAgentPackageVersions(2);
      }
    }
  });
  loadAgentLatestVersion({osver: '1'}).then((data) => {
    let body = data.body;
    if (body.windows) {
      latestVersionData.windows.version = body.windows.package_version;
      latestVersionData.windows.time = body.windows.created_at;
      latestVersionData.windows.is_merge = body.windows.is_merge;
      latestVersionData.windows.enable_recall = body.windows.enable_recall;
      // if (body.windows.is_merge) {
      //   setInterGetAgentPackageVersions(1)
      // }
    }
  });
}

const setInterGetAgentPackageVersions = (type) => {
  timerAgent = setInterval(async () => {
    const {
      body: {linux, windows, code, msg},
    } = await loadAgentLatestVersion({osver: type});
    console.log(linux, windows, code, msg);
    if (code) {
      message.error(msg);
      clearInterval(timerAgent);
      loadAgentLatestVersionData();
    } else {
      if (type === 2) {
        latestVersionData.linux.version = linux.package_version;
        latestVersionData.linux.time = linux.created_at;
        latestVersionData.linux.is_merge = linux.is_merge;
        latestVersionData.linux.enable_recall = linux.enable_recall;
        if (!linux.is_merge) {
          linuxRef.value.reload();
          clearInterval(timerAgent);
        }
      } else {
        latestVersionData.windows.version = linux.package_version;
        latestVersionData.windows.time = linux.created_at;
        latestVersionData.windows.is_merge = linux.is_merge;
        latestVersionData.windows.enable_recall = linux.enable_recall;
        if (!windows.is_merge) {
          clearInterval(timerAgent);
        }
      }
    }
  }, 3000);
};

const [registerPwdConfirm, {openModal}] = useModal();

function pwdOk(params) {
  //版本回退
  if (params.type == 'rollBack') {
    agentRecallRequest(params).then((data) => {
      console.log(data);
      if (!data.code) {
        message.success('Operation successful!');
      } else {
        message.warn(data.msg);
      }
      loadAgentLatestVersionData();
      windowsRef.value.reload();
      linuxRef.value.reload();
    });
  }
}

//上传组件
const simpleUploadRef = ref();
const simpleUploadLinux = ref();
const uploadResult = (result) => {
  console.log('uploadResult', result);
  if (!result.code) {
    // emit('refreshCmt');
    loadAgentLatestVersionData();
    linuxRef.value.reload();
  } else {
    message.error(result.msg || t('setting.uploadError'));
  }
};
const triggerUpload = (index) => {
  if (index === 0) {
    unref(simpleUploadRef).uploadBtnClick();
  } else {
    unref(simpleUploadLinux).uploadBtnClick();
  }
};

const formatSeconds = (second: number) => {
  //  分
  let minute = 0;
  //  小时
  let hour = 0;
  //  天
  let day = 0;
  //  如果秒数大于60，将秒数转换成整数
  if (second > 60) {
    //  获取分钟，除以60取整数，得到整数分钟
    minute = Math.floor(second / 60);
    //  获取秒数，秒数取佘，得到整数秒数
    second = Math.floor(second % 60);
    //  如果分钟大于60，将分钟转换成小时
    if (minute > 60) {
      //  获取小时，获取分钟除以60，得到整数小时
      hour = Math.floor(minute / 60);
      //  获取小时后取佘的分，获取分钟除以60取佘的分
      minute = Math.floor(minute % 60);
      //  如果小时大于24，将小时转换成天
      if (hour > 23) {
        //  获取天数，获取小时除以24，得到整天数
        day = Math.floor(hour / 24);
        //  获取天数后取余的小时，获取小时除以24取余的小时
        hour = Math.floor(hour % 24);
      }
    }
  }

  let result = '' + Math.floor(second) + 's';
  if (minute > 0) {
    result = '' + Math.floor(minute) + 'm' + result;
  }
  if (hour > 0) {
    result = '' + Math.floor(hour) + 'h' + result;
  }
  if (day > 0) {
    result = '' + Math.floor(day) + 'd' + result;
  }
  return result;
};

/**
 * 版本回退 type：1 windows，type：2 linux
 * @param type
 */
function rollBack(type) {
  console.log(type);
  openModal(true, {
    isUpdate: false,
    showFooter: true,
    title: t('routes.agent.cfmpwdtitle'),
    type: 'rollBack',
    osver: type,
  });
}

const handleChange = (info: UploadChangeParam) => {
  console.log(info);
  let percent = info.file.percent as number;
  latestVersionData.windows.uplpadPrg = percent;
  if (percent == 100) {
    latestVersionData.windows.spinning = true;
  }
  if (info.file.status === 'done') {
    console.log(info);
    latestVersionData.windows.spinning = false;
    loadAgentLatestVersionData();
    windowsRef.value.reload();
    if (info.file.response.code) {
      message.error(info.file.response.msg);
    } else {
      message.success('Upload Success');
    }
  } else if (info.file.status === 'error') {
    console.log(info);
    message.error(`file upload failed.`);
  }
};

const commandStrCurl = ref('');
const commandStrWget = ref('');
const cmdVisible = ref(false);

//1windows,2linux
const osverType = ref(1);

//生成命令
function makeCmd(osver) {
  osverType.value = osver;
  makeInstallCmd({
    osver: osver,
    group_id: 0,
    install_path: '/usr/local/',
    cpu: 5,
    memory: 500,
  }).then((data) => {
    cmdVisible.value = true;
    if (osver == 1) {
      const {command} = data.body;
      commandStrWget.value = command ?? '';
    } else {
      const {command_wget, command_curl} = data.body;
      commandStrCurl.value = command_curl ?? '';
      commandStrWget.value = command_wget ?? '';
    }
  });
}

//复制
const {toClipboard} = useClipboard();
//复制命令
const copyCommand = async (type) => {
  try {
    await toClipboard(type === 'wget' ? commandStrWget.value : commandStrCurl.value);
    message.success(t('routes.agent.copClid'));
  } catch (e) {
  }
};
</script>

<style scoped lang="less">
.agent_chart_wrapper {
  background: @dark-bg2;
  border-radius: 8px;
}


.border_div {
  border-bottom: 1px solid @border-color;
}

.border-right {
  border-right: 1px solid @border-color-01;
}

.border-bottom {
  border-bottom: 1px solid @border-color;
}

.image_div {
  width: 72px;
  height: 72px;
  text-align: center;

  img {
    width: 100%;
  }
}

.host_name {
  color: #666;
}

.background_div {
  padding: 16px;
  background-color: @dark-bg1;
  height: 280px;
  border-radius: 16px;
}


.error {
  color: red;
}


.uploadprogre {
  position: absolute;
  left: 0;
  right: 0;
  bottom: -8px;
}

.extraInfo {
  position: absolute;
  bottom: 4px;
  right: 16px;
  color: rgba(255, 255, 255, 0.8);
  font-size: @fz12;

  .speed {
    margin-right: 16px;
  }
}

:deep(.ant-tabs-nav) {
  padding-left: 16px;
  margin-bottom: 0;
}

:deep(.ant-upload-list-item) {
  height: 0px;
  margin-top: 0px;
}

:deep(.ant-upload-list-item-info) {
  display: none;
}

.cmdCopy {


  position: relative;


  :deep(textarea.ant-input) {
    height:128px
  }

  .btn {
    position: absolute;
    right: 8px;
    padding: 0 !important;
    background: transparent;
    min-width: auto;
    top: 8px;
    width: 24px;
    height: 24px;
    text-align: center;
    display: flex;
    align-items: center;
    justify-content: center;
    color: @font-color-default;
    font-size: 14px !important;
    z-index: 1111;
  }
}

.error_div {
  background-color: rgba(217, 0, 27, 0.42);
  padding: 5px 16px;
}
</style>
