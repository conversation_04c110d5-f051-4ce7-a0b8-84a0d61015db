<!--
 * @Author: Delavin.TnT
 * @LastEditors: Delevin.TnT
 * @Date: 2021-09-26 12:59:32
 * @LastEditTime: 2023-06-29 14:09:26
-->
<template>
  <BasicModal v-bind="$attrs" @register="registerModal" minHeight="0"
              :okText="t('common.confirm')" width="600px" @ok="handleOk">
    <template #title>
      {{ labelText }}
    </template>
    <div style="padding: 0 24px;">
      <div class="msg">{{ msg }}</div>
      <a-form autocomplete="off" :model="params">
        <a-form-item
          label=""
          name="pwdValue"
          autocomplete="new-password"
          :rules="[{ required: true, message: 'Please input your password!' }]">
          <a-input
            type="password"
            v-model:value.trim="params.pwdValue"
            :placeholder="t('routes.agent.pIptPwdAd')"/>
        </a-form-item>
      </a-form>
    </div>
  </BasicModal>
</template>
<script lang="ts" setup>
import {ref, defineEmits, toRaw, reactive} from 'vue';
import {useI18n} from "/@/hooks/web/useI18n";
import {BasicModal, useModalInner} from "/@/components/Modal";
import Hashes from 'jshashes';

const labelText = ref("")
const msg = ref("")
const {t} = useI18n();

const emit = defineEmits(['ok'])
const params = reactive({
  pwdValue: "",
  host_id: "",
  password: "",
  type: "",
  osver: ""
})

//表单赋值
const [registerModal, {setModalProps, closeModal}] = useModalInner(async (data) => {
  console.log(data)
  labelText.value = data.title
  msg.value = data.msg
  params.host_id = data.host_id
  params.type = data.type
  params.type = data.type
  params.pwdValue = ""
  params.osver = data.osver
  setModalProps({
    confirmLoading: false,
    showOkBtn: true
  });
});

function handleOk() {
  console.log(toRaw(params))
  //改传系统登录用户的密码，明文传
  // params.password = pCompile(params.pwdValue)
  params.password = params.pwdValue
  emit("ok", toRaw(params))
  closeModal()
}

const pCompile = (code) => {
  const SHA256 = new Hashes.SHA256();
  return SHA256.hex(code);
};

</script>
<style lang="less" scoped>
.msg {
  margin-bottom: 16px;
  font-size: @fz13;
  color: rgba(255, 255, 255, 0.5);
}
</style>
