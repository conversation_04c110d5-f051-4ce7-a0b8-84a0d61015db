<template>
  <div :style="{ color: state.color,cursor: 'pointer' }" class="node" @click="openDetaiModal">
    <component :is="state.component" class="icon"/>
    <span>{{ state.title }}</span>
  </div>

  <agentStatus2Modal @register="registerModal"/>
</template>

<script setup lang="ts">
import {reactive, watchEffect} from 'vue';
import {
  CheckCircleFilled, InfoCircleFilled, PlayCircleFilled,
} from '@ant-design/icons-vue';
import {useI18n} from "/@/hooks/web/useI18n";
import {useModal} from "/@/components/Modal";
import agentStatus2Modal from './agentStatus2Modal.vue'

const props = defineProps({
  self_verify: {
    type: Object,
    required: true,
  },
  run_status: {
    type: Number,
    required: true,
  },
  host_id: {
    type: String,
    required: true,
  },
});
const {t} = useI18n();
const state = reactive({
  title: '',
  color: '',
  component: '',
});
watchEffect(() => {
  let total = 0;
  for (const k in props.self_verify) {
    total += props.self_verify[k].count ?? 0;
  }
  if (props.run_status === 1) {
    Object.assign(state, {
      title: t('routes.agent.deteErr') + '(' + total + ')',
      color: '#F6A058',
      component: InfoCircleFilled,
    });
  } else if (props.run_status === 2) {
    Object.assign(state, {
      title: t('routes.agent.deteNor'),
      color: '#30DCA8',
      component: CheckCircleFilled,
    });
  } else if (props.run_status === 3) {
    Object.assign(state, {
      title: t('routes.agent.suspend'),
      color: '#0000004d',
      component: PlayCircleFilled,
    });
  } else if (props.run_status === 4) {
    Object.assign(state, {
      title: t('routes.agent.bfzt'),
      color: '#30DCA8',
      component: CheckCircleFilled,
    });
  } else {
    Object.assign(state, {
      title: '-',
      color: '#000',
      component: '',
    });
  }
});
const [registerModal, {openModal}] = useModal();

const openDetaiModal = () => {
  openModal(true, {
    isUpdate: false,
    showFooter: true,
    hostId: props.host_id
  });
};

</script>

<style scoped lang="less">
.title {
  font-weight: 600;
  display: inline-block;
  margin: 8px 0;
  margin-right: 16px;
}

.value {
  display: inline-block;
  margin-right: 16px;
}

.node {
  display: inline-flex;
  align-items: center;
  line-height: normal;

  .icon {
    margin-right: 4px;
  }
}
</style>
