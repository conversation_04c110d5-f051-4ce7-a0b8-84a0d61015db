<!--
 * @Author: Delavin.TnT
 * @LastEditors: Delevin.TnT
 * @Date: 2021-10-21 16:49:37
 * @LastEditTime: 2023-08-01 15:05:00
-->
<template>
  <a-popover
    v-if="crashStatus && crashStatus.type !== 0"
    trigger="hover"
    placement="right"
    destroyTooltipOnHide
  >
    <template #content v-if="crashStatus.stoptime">
      {{ crashStatus.stoptime }}
    </template>
    <template v-if="crashStatus.type === 1">
      {{ t('routes.agent.abRest') }}
    </template>
    <template v-else>
      <span class="text" :title="t('routes.agent.cpumsnfr')">{{ t('routes.agent.cpumsnfr') }}</span>
    </template>
  </a-popover>
  <span v-else>-</span>
</template>

<script lang="ts" setup>
import {useI18n} from "/@/hooks/web/useI18n";

defineProps({
  crashStatus: Object as any,
});
const {t} = useI18n();
</script>
<style lang="less" scoped>
.text {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  width: 100%;
  display: inline-block;
}
</style>
