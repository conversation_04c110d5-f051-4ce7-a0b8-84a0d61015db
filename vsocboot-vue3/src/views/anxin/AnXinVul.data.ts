import {BasicColumn} from '/@/components/Table';
import {FormSchema} from '/@/components/Table';
import { rules} from '/@/utils/helper/validator';
import { render } from '/@/utils/common/renderUtils';
import { RULE_URGENCY_LEVEL} from "/@/views/rule/aggregation/AggregationRule.data";

export const columns: BasicColumn[] = [
  {
    title: 'IP',
    dataIndex: 'hostIp'
  },
    {
    title: 'CVE Number',
    dataIndex: 'vulListCveCode'
   },
   {
    title: 'Title',
    dataIndex: 'vulListTitle',
   },
   {
    title: 'Scope',
    dataIndex: 'vulListScope'
   },
   {
    title: 'Release Time',
    dataIndex: 'vulListCreatedAt'
   },
   {
    title: 'Risk Level',
    dataIndex: 'vulListLevel',
     customRender: ({text}) => {
       return render.renderDictNative(text, RULE_URGENCY_LEVEL);
     }
   },
   {
    title: 'Score',
    dataIndex: 'vulListCvssScore'
   },
   {
    title: 'Version',
    dataIndex: 'vulListAppVersion'
   }
];

export const searchFormSchema: FormSchema[] = [
 {
    label: 'IP',
    field: 'hostIp',
    component: 'JInput'
  },
 {
   label: 'CVE Number',
   field: 'vulListCveCode',
    component: 'JInput'
  },
  {
    label: 'Title',
    field: 'vulListTitle',
    component: 'JInput'
  },
  {
    label: 'Risk Level',
    field: 'vulListLevel',
    component: 'JSelectInput',
    componentProps: {
      options: [
        {label: 'Critical', value: '1'},
        {label: 'High', value: '2'},
        {label: 'Medium', value: '3'},
        {label: 'Low', value: '4'},
      ],
    },
  },
];

export const formSchema: FormSchema[] = [
  // TODO 主键隐藏字段，目前写死为ID
  {label: '', field: 'id', component: 'Input', show: false},
  {
    label: 'ckEnterDate',
    field: 'ckEnterDate',
    component: 'DatePicker',
    componentProps: {
      showTime: true,
      valueFormat: 'YYYY-MM-DD hh:mm:ss',
    },
  },
  {
    label: 'logTime',
    field: 'logTime',
    component: 'Input',
  },
  {
    label: 'fromIp',
    field: 'fromIp',
    component: 'Input',
  },
  {
    label: 'log',
    field: 'log',
    component: 'Input',
  },
  {
    label: 'timeStr',
    field: 'timeStr',
    component: 'Input',
  },
  {
    label: 'groupId',
    field: 'groupId',
    component: 'Input',
  },
  {
    label: 'groupPairs',
    field: 'groupPairs',
    component: 'Input',
  },
  {
    label: 'hostIp',
    field: 'hostIp',
    component: 'Input',
  },
  {
    label: 'hostMac',
    field: 'hostMac',
    component: 'Input',
  },
  {
    label: 'hostMachineName',
    field: 'hostMachineName',
    component: 'Input',
  },
  {
    label: 'vulCount',
    field: 'vulCount',
    component: 'Input',
  },
  {
    label: 'vulListAppVersion',
    field: 'vulListAppVersion',
    component: 'Input',
  },
  {
    label: 'vulListCreatedAt',
    field: 'vulListCreatedAt',
    component: 'Input',
  },
  {
    label: 'vulListCveCode',
    field: 'vulListCveCode',
    component: 'Input',
  },
  {
    label: 'vulListCvssScore',
    field: 'vulListCvssScore',
    component: 'Input',
  },
  {
    label: 'vulListDescription',
    field: 'vulListDescription',
    component: 'Input',
  },
  {
    label: 'vulListLevel',
    field: 'vulListLevel',
    component: 'Input',
  },
  {
    label: 'vulListPlatforms',
    field: 'vulListPlatforms',
    component: 'Input',
  },
  {
    label: 'vulListProposal',
    field: 'vulListProposal',
    component: 'Input',
  },
  {
    label: 'vulListPublicDate',
    field: 'vulListPublicDate',
    component: 'Input',
  },
  {
    label: 'vulListRefs',
    field: 'vulListRefs',
    component: 'Input',
  },
  {
    label: 'vulListScope',
    field: 'vulListScope',
    component: 'Input',
  },
  {
    label: 'vulListTitle',
    field: 'vulListTitle',
    component: 'Input',
  },
  {
    label: 'vulListVerifyType',
    field: 'vulListVerifyType',
    component: 'Input',
  },
  {
    label: 'vulListVulCategory',
    field: 'vulListVulCategory',
    component: 'Input',
  },
  {
    label: 'vulListVulId',
    field: 'vulListVulId',
    component: 'Input',
  },
  {
    label: 'vulListVulType',
    field: 'vulListVulType',
    component: 'Input',
  },
];
