import {BasicColumn} from '/@/components/Table';
import {FormSchema} from '/@/components/Table';
import {useI18n} from "/@/hooks/web/useI18n";
import {toRaw} from "vue";
import {rules} from "/@/utils/helper/validator";

const {t} = useI18n();

//该属性是国际化中英文配置，需要在/src/locales/lang/en/routes中创建CvsFile.ts文件，把下方属性复制到文件中
/*
export default {
     'fileName': '文件名',
     'url': '文件路径',
};
*/

export const columns: BasicColumn[] = [
   {
    title: t('routes.CvsFile.FileName'),
    dataIndex: 'fileName'
   },
  {
    title: t('routes.CvsFile.attribute'),
    dataIndex: 'attrs',
    key:'attrs',
    slots: {customRender: 'attrs'},
  },
    {
      title: t('routes.CvsFile.uploadUser'),
      dataIndex: 'createBy',
      width:'10%'
    },
    {
      title: t('routes.CvsFile.uploadTime'),
      dataIndex: 'createTime',
      width:'10%'
    }
];

export const searchFormSchema: FormSchema[] = [
 {
    label: '',
    field: 'fileName',
    component: 'Input',
    componentProps:{
      placeholder: t('routes.CvsFile.fileNamePlaceholder'),
    }
  }
];

export const formSchema: FormSchema[] = [
  // TODO 主键隐藏字段，目前写死为ID
  {label: '', field: 'id', component: 'Input', show: false},
  {
    label: t('fileName'),
    field: 'fileName',
    component: 'Input',
    componentProps:{
      placeholder: t('routes.CvsFile.uploadTip')
    },
    dynamicRules: ({ model, schema }) => {
      console.log(toRaw(model));
      return [
        {
          required: true,
          message: t('routes.CvsFile.fileNamePlaceholder'),
        },
        { ...rules.limitationCheckRule(45)[0] }
      ];
    },

  },
  {
    label: '',
    field: 'url',
    component: 'Input',
    show:false
  },
];
