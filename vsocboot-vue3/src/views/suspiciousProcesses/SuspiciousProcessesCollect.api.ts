import {defHttp} from '/@/utils/http/axios';
import {Modal} from 'ant-design-vue';
import {useI18n} from "/@/hooks/web/useI18n";

const {t} = useI18n();

enum Api {
  list = '/suspiciousProcesses/suspiciousProcessesCollect/list',
  save = '/suspiciousProcesses/suspiciousProcessesCollect/add',
  queryById = '/suspiciousProcesses/suspiciousProcessesCollect/queryById',
  edit = '/suspiciousProcesses/suspiciousProcessesCollect/edit',
  deleteOne = '/suspiciousProcesses/suspiciousProcessesCollect/delete',
  deleteBatch = '/suspiciousProcesses/suspiciousProcessesCollect/deleteBatch',
  importExcel = '/suspiciousProcesses/suspiciousProcessesCollect/importExcel',
  exportXls = '/suspiciousProcesses/suspiciousProcessesCollect/exportXls',
  assign = '/suspiciousProcesses/suspiciousProcessesCollect/assign'
}

/**
 * 导出api
 * @param params
 */
export const getExportUrl = Api.exportXls;
/**
 * 导入api
 */
export const getImportUrl = Api.importExcel;
/**
 * 列表接口
 * @param params
 */
export const list = (params) =>
  defHttp.get({url: Api.list, params});

export const queryById = (params) =>
  defHttp.get({url: Api.queryById, params});

/**
 * 删除单个
 * @param params
 * @param handleSuccess
 */
export const deleteOne = (params, handleSuccess) => {
  return defHttp.delete({url: Api.deleteOne, params}, {joinParamsToUrl: true}).then(() => {
    handleSuccess();
  });
}
/**
 * 批量删除
 * @param params
 * @param handleSuccess
 */
export const batchDelete = (params, handleSuccess) => {
  Modal.confirm({
    title: t('common.delConfirmText'),
    content: t('common.delContent'),
    okText: t('common.okText'),
    cancelText: t('common.cancelText'),
    wrapClassName: 'delete_confirm',
    onOk: () => {
      return defHttp.delete({
        url: Api.deleteBatch,
        data: params
      }, {joinParamsToUrl: true}).then(() => {
        handleSuccess();
      });
    }
  });
}
/**
 * 保存或者更新
 * @param params
 * @param isUpdate 是否是更新数据
 */
export const saveOrUpdate = (params, isUpdate) => {
  const url = isUpdate ? Api.edit : Api.save;
  return defHttp.post({url: url, params});
}

export const read = (params) => {
  return defHttp.post({url: Api.edit, params}, {successMessageMode: 'none'});
}
/**
 * 分配
 * @param params
 */
export const assign = (params) => {
  return defHttp.post({url: Api.assign, params});
}

