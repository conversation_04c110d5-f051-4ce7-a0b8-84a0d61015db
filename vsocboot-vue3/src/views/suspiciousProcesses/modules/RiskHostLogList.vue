<template>
  <div>
    <!--引用表格-->
    <BasicTable @register="registerTable" :isSearch="isSearch">
      <template #expandedRowRender="{ record }">
        <a-tabs>
          <a-tab-pane key="1" tab="Table">
            <a-row style="border: 1px solid #35373a;">
              <template v-for="(key,index) in record" :key="index">
                <a-col :span="2" class="borderBottom t_text" v-if="key">
                  <div class="ant-table-cell-ellipsis">{{ index }}:</div>
                </a-col>
                <a-col :span="4" class="borderBottom borderRight t_text" v-if="key">
                  <div class="ant-table-cell-ellipsis">
                    <a-tooltip placement="topLeft">
                      <template #title>
                        <div style="max-height: 400px;overflow: auto;">{{ key }}</div>
                      </template>
                      {{ key }}
                    </a-tooltip>
                  </div>
                </a-col>
              </template>
            </a-row>
          </a-tab-pane>
          <a-tab-pane key="2" tab="Json">
            <JsonPreview :data="record"/>
          </a-tab-pane>
        </a-tabs>
      </template>
    </BasicTable>
  </div>
</template>

<script lang="ts" setup>
import {defineProps, ref, watch} from 'vue';
import {BasicColumn, BasicTable} from '/@/components/Table';
import {useListPage} from '/@/hooks/system/useListPage'
import {
  loadLogsByEventId,
} from '../SuspiciousProcesses.api';
import {JsonPreview} from "/@/components/CodeEditor";
import {getFieldText} from "/@/utils/ckTable";

const prpos = defineProps({
  eventId: {
    type: String,
    default: '-'
  }
})

watch(() => prpos.eventId, (value, oldValue) => {
  console.log("-----------------")
  console.log(value, oldValue)
  reload()
})

const isSearch = ref<boolean>(false)
const columns: BasicColumn[] = [
  // 'ck_enter_date', 'enter_date', 'from_ip', 'host_hostname','host_event_type','host_severity','host_source_name','host_domain'
  {
    title: getFieldText(2,'ck_enter_date'),
    dataIndex: 'ck_enter_date',
    width: 150
  },
  {
    title: getFieldText(2,'from_ip'),
    dataIndex: 'from_ip',
    width: 110
  },
  {
    title: getFieldText(2,'host_hostname'),
    dataIndex: 'host_hostname'
  },
  {
    title: getFieldText(2,'host_event_type'),
    dataIndex: 'host_event_type'
  },
  {
    title: getFieldText(2,'host_severity'),
    dataIndex: 'host_severity'
  },
  {
    title: getFieldText(2,'host_source_name'),
    dataIndex: 'host_source_name',
  },
  {
    title: getFieldText(2,'host_domain'),
    dataIndex: 'host_domain',
  },
];
//注册table数据
const {tableContext} = useListPage({
  tableProps: {
    rowKey: 'log_id',
    api: loadLogsByEventId,
    columns,
    canResize: false,
    // scroll: {x: 100},
    useSearchForm: false,
    searchInfo: {
      eventId: prpos.eventId
    },
    beforeFetch: beforeFetch,
    showActionColumn: false,
  },
})

const [registerTable, {reload}, {}] = tableContext

function beforeFetch(params) {
  params.eventId = prpos.eventId
}


</script>
<style scoped lang="less">
:deep(.ant-table) {
  background: @dark-bg1 !important;
}

:deep(.ant-table-thead > tr > th) {
  background: @dark-bg1 !important;
}

:deep(.soc-basic-table .ant-table-wrapper) {
  background-color: @dark-bg1 !important;
}

.t_text {
  padding: 10px;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  word-break: keep-all;
}

.borderBottom {
  border-bottom: 1px solid @border-color;
}

.borderRight {
  border-right: 1px solid @border-color;
}



</style>
