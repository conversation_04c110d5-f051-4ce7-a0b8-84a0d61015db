import {BasicColumn, FormSchema} from '/@/components/Table';
import {useI18n} from "/@/hooks/web/useI18n";
import {isAdministrator} from "/@/utils/auth";

const {t} = useI18n();

function tp(name) {
  return t('routes.SuspiciousProcessesCollect.' + name);
}

//该属性是国际化中英文配置，需要在/src/locales/lang/en/routes中创建SuspiciousProcessesCollect.ts文件，把下方属性复制到文件中
/*
export default {
     'deviceIp': 'device ip',
     'processCount': 'suspicious processes count',
     'firstDetected': ' First time a suspicious process was reported',
     'lastDetected': 'The last datetime when the same suspicious process was reported',
     'status': '1:New  2:Pending  3:Closed',
};
*/

export const getColumns = (): BasicColumn[] => [
  {
    title: t('routes.SuspiciousProcessesCollect.deviceIp'),
    dataIndex: 'deviceIp',
    minWidth: 150,
  },
  {
    title: t('common.tenantName'),
    dataIndex: 'socTenantId_dictText',
    ifShow: isAdministrator(),
    minWidth: 150,
  },
  {
    title: t('routes.SuspiciousProcessesCollect.firstDetected'),
    dataIndex: 'firstDetected',
    minWidth: 150,
  },
  {
    title: t('routes.SuspiciousProcessesCollect.lastDetected'),
    dataIndex: 'lastDetected',
    minWidth: 150,
  },
  {
    title: t('routes.SuspiciousProcessesCollect.status'),
    align: 'center',
    children: [
      {
        title: t('routes.SuspiciousProcessesCollect.status0'),
        dataIndex: 'num',
        width: 100,
        align: 'center',
      },
      {
        title: t('routes.SuspiciousProcessesCollect.status1'),
        dataIndex: 'num1',
        width: 100,
        align: 'center',
      },
      {
        title: t('routes.SuspiciousProcessesCollect.status2'),
        dataIndex: 'num2',
        width: 200,
        align: 'center',
      },
      {
        title: t('routes.SuspiciousProcessesCollect.status3'),
        dataIndex: 'num2',
        width: 200,
        align: 'center',
      },
    ],
    minWidth: 600,
  }
];

export const searchFormSchema: FormSchema[] = [
  {
    label: '',
    field: 'deviceIp',
    component: 'Input',
    componentProps: {
      placeholder: t('routes.SuspiciousProcessesCollect.deviceIp'),

    }
  },
  {
    label: '',
    field: 'processName',
    component: 'Input',
    componentProps: {
      placeholder: t('routes.SuspiciousProcessesCollect.processCount'),
    }
  },
  {
    label: '',
    field: 'firstDetectedStr',
    component: 'RangeDate',
    componentProps: {
      datetime: true,
      placeholder: [t('common.start')+' '+t('routes.SuspiciousProcessesCollect.firstDetected'),t('common.end')+' '+t('routes.SuspiciousProcessesCollect.firstDetected')]
    },
    colProps: {
      lg: 10, // ≥992px
      xl: 5, // ≥1200px
      xxl: 5, // ≥1600px
    },
  },
  {
    label: '',
    field: 'lastDetectedStr',
    component: 'RangeDate',
    componentProps: {
      datetime: true,
      placeholder: [t('common.start')+' '+t('routes.SuspiciousProcessesCollect.lastDetected'),t('common.end')+' '+t('routes.SuspiciousProcessesCollect.lastDetected')],
    },
    colProps: {
      lg: 10, // ≥992px
      xl: 5, // ≥1200px
      xxl: 5, // ≥1600px
    },
  },
  {
    label: '',
    field: 'status',
    component: 'Select',
    componentProps: {
      placeholder: t('routes.SuspiciousProcessesCollect.status'),
      options: [
        {label: tp('New'), value: "1"},
        {label: tp("Pending"), value: "2"},
        {label: tp("Closed"), value: "3"}
      ]
    }
  },
];

export const formSchema: FormSchema[] = [
  // TODO 主键隐藏字段，目前写死为ID
  {label: '', field: 'id', component: 'Input', show: false},
  {
    label: t('routes.SuspiciousProcessesCollect.deviceIp'),
    field: 'deviceIp',
    component: 'Input',
  },
  {
    label: t('routes.SuspiciousProcessesCollect.processCount'),
    field: 'processCount',
    component: 'InputNumber',
  },
  {
    label: t('routes.SuspiciousProcessesCollect.firstDetected'),
    field: 'firstDetected',
    component: 'DatePicker',
    componentProps: {
      showTime: true,
      valueFormat: 'YYYY-MM-DD hh:mm:ss',
    },
  },
  {
    label: t('routes.SuspiciousProcessesCollect.lastDetected'),
    field: 'lastDetected',
    component: 'DatePicker',
    componentProps: {
      showTime: true,
      valueFormat: 'YYYY-MM-DD hh:mm:ss',
    },
  },
  {
    label: t('routes.SuspiciousProcessesCollect.status'),
    field: 'status',
    component: 'Input',
  },
];

export const suspiciousConfig = {
  searchForm: searchFormSchema,
  column: getColumns
}
