<template>
  <div style="padding: 10px 10px 0 10px" :class="'fromAsset-' + fromAsset">
    <div class="returnBtn">
      <h2 @click="back" style="display: inline-block; cursor: pointer; margin: 0">
        <Icon icon="ant-design:left-outlined" style="margin-right: 5px" />
        {{ Info?.deviceIp }}
      </h2>
    </div>
    <div style="display: flex; margin: 16px 0; line-height: 30px">
      <div style="width: 360px">
        <!--        {{ minDate }} - {{ maxDate }}-->
        <div v-if="queryTime != null">
          <JRangeDate :datetime="true" v-model:value="queryTime" @change="changeTime" />
        </div>
      </div>
      <div style="width: calc(100% - 280px)">
        <div ref="timeLineChart" style="height: 30px; width: 100%"></div>
      </div>
    </div>
    <div>
      <a-tabs>
        <a-tab-pane key="1" :tab="tp('Summary')">
          <!--引用表格-->
          <div style="margin: -16px 0 0 0">
            <BasicTable @register="registerTable" :rowSelection="rowSelection as any" :isSearch="isSearch" @expand="expandRow">
              <!--插槽:table标题-->
              <template #form-formFooter>
                <a-dropdown :trigger="['click']" v-if="hasPermission('investigation:add') || hasPermission('investigation:join')">
                  <a-button @click.prevent="loadInvestigation">{{ t('common.Investigation') }}</a-button>
                  <template #overlay>
                    <a-menu>
                      <a-menu-item key="0" @click="showAddInvestigation('rows')" v-if="hasPermission('investigation:add')">
                        <Icon icon="ant-design:plus-outlined" />
                        {{ t('routes.riskLogs.add') }}
                      </a-menu-item>
                      <a-menu-divider />
                      <a-menu-item v-for="item in investigationData" :key="item.id" @click="addInvestigation(item, null)">
                        <span>{{ item['investigation'] }}</span>
                      </a-menu-item>
                      <a-menu-divider />
                      <a-menu-item key="1" @click="showMoreInvestigation(null)">
                        <span>{{ t('routes.riskLogs.addMore') }}</span>
                      </a-menu-item>
                    </a-menu>
                  </template>
                </a-dropdown>
                <!--              <a-button :class="{'btn-checked': isSearch == true}" type="text"-->
                <!--                        @click="isSearch=!isSearch"-->
                <!--                        preIcon="ant-design:filter-outlined"> {{ t('common.filter') }}-->
                <!--              </a-button>-->
              </template>
              <!--操作栏-->
              <template #action="{ record }">
                <a-space size="5" class="action-border">
                  <a-dropdown :trigger="['click']" v-if="hasPermission('investigation:add') || hasPermission('investigation:join')">
                    <span class="ant-dropdown-link" @click.prevent="loadInvestigation">
                      {{ t('common.Investigation') }}
                    </span>
                    <template #overlay>
                      <a-menu>
                        <a-menu-item key="0" @click="showAddInvestigation(record?.id)" v-if="hasPermission('investigation:add')">
                          <Icon icon="ant-design:plus-outlined" />
                          {{ t('routes.riskLogs.add') }}
                        </a-menu-item>
                        <a-menu-divider />
                        <a-menu-item v-for="item in investigationData" :key="item.id" @click="addInvestigation(item, record?.id)">
                          <span>{{ item['investigation'] }}</span>
                        </a-menu-item>
                        <a-menu-divider />
                        <a-menu-item key="1" @click="showMoreInvestigation(record?.id)">
                          <span>{{ t('routes.riskLogs.addMore') }}</span>
                        </a-menu-item>
                      </a-menu>
                    </template>
                  </a-dropdown>
                  <a-divider type="vertical" />
                  <a-dropdown>
                     <span class="ax-dropdown-link" @click.prevent>
                      {{ t('common.moreBtn') }}
                      <span class="soc ax-com-Arrow-down"></span>
                    </span>
                    <template #overlay>
                      <a-menu>
                        <a-menu-item
                          v-if="
                            hasPermission('ticket:useinternel-2') ||
                            hasPermission('ticket:useSS-2') ||
                            hasPermission('ticket:useinternel-1') ||
                            hasPermission('ticket:useIssued-1')
                          "
                        >
                          <a-dropdown>
                            <span>{{ t('common.ticketBtn') }}</span>
                            <template #overlay>
                              <a-menu>
                                <ApplyMenu v-model:workflowList="workflowList" eventType="5" type="2" :record="record" />
                              </a-menu>
                            </template>
                          </a-dropdown>
                        </a-menu-item>
                        <a-menu-item v-if="hasPermission('risk:assign_other') && record?.status !== 2" @click.stop="assignUser(record)">
                          {{ t('common.assignBtn') }}
                        </a-menu-item>
                        <a-menu-item v-else-if="hasPermission('risk:assign_self') && record?.status !== 2" @click.stop="assignSelf(record)">
                          {{ t('common.assignBtn') }}
                        </a-menu-item>
                        <!-- 有权限且没关闭且分配给登录人 -->
                        <a-menu-item
                          v-if="hasPermission('risk:triage') && record?.status !== 2 && record?.owner == userStore.getUserInfo.id"
                          @click.stop="showTriage(record)"
                        >
                          {{ t('common.triageBtn') }}
                        </a-menu-item>
                        <!-- 有权限且已验证且分配给登录人且没有关闭 -->
                        <a-menu-item
                          v-if="
                            hasPermission('risk:close') &&
                            record?.triageStatus !== 0 &&
                            record?.owner == userStore.getUserInfo.id &&
                            record?.status !== 2
                          "
                          @click="close(record)"
                        >
                          {{ t('common.closeBtn') }}
                        </a-menu-item>
                        <a-menu-item
                          v-if="hasPermission('risk:close') && record?.owner == userStore.getUserInfo.id && record?.status === 2"
                          @click="close(record)"
                        >
                          {{ t('common.openBtn') }}
                        </a-menu-item>
                        <a-menu-item @click="showRecord(record)" v-if="hasPermission('risk:record')">
                          {{ t('common.recordBtn') }}
                        </a-menu-item>
                      </a-menu>
                    </template>
                  </a-dropdown>
                </a-space>
              </template>
              <template #expandedRowRender="{ record }">
                <div style="display: flex; padding-left: 90px" v-if="expandData[record?.id]">
                  <div style="width: 240px" class="border-right">
                    <div
                      class="event_card"
                      v-for="(item, index) in expandData[record?.id].event"
                      :class="{ active: index == 0 }"
                      :key="'event' + index"
                      @click="showEventLog(item.eventId, record?.id, $event)"
                    >
                      <div style="font-weight: bold" class="ant-table-cell-ellipsis">
                        <a-tooltip placement="topLeft">
                          <template #title>
                            {{ item?.name }}
                          </template>
                          {{ item?.name }}
                        </a-tooltip>
                      </div>
                      <div>
                        {{ tp('Type') }} :
                        <span v-if="item?.type == 1">{{ tp('endpoint') }}</span>
                      </div>
                      <div>{{ tp('severity') }} : {{ item?.severity }}</div>
                      <div>{{ tp('AlertTime') }} : {{ item?.alertTime }}</div>
                    </div>
                  </div>
                  <div style="width: calc(100% - 241px)">
                    <RiskHostLogList v-if="expandData[record?.id].eventId" :eventId="expandData[record?.id].eventId" />
                  </div>
                </div>
              </template>
            </BasicTable>
          </div>
        </a-tab-pane>

        <a-tab-pane key="2" :tab="tp('ProcessActivity')" :forceRender="true" style="margin: 0">
          <SuspiciousProcessesActivityList ref="activityRef" v-if="Info.deviceIp" :ip="Info.deviceIp" :socTenantId="Info.socTenantId" />
        </a-tab-pane>
      </a-tabs>
    </div>

    <InvestigationListModal ref="registerRiskLogsModal" :eventId="LogId" type="5" :socTenantId="Info.socTenantId" />
    <a-modal v-model:visible="inveVisible" :title="t('common.confirm')" @ok="saveToInve" :maskClosable="false">
      <a-row style="padding: 0 24px">
        <a-col :span="24">
          {{ t('routes.riskLogs.addInvestigationPrompt') }}
        </a-col>
        <a-col :span="24">
          <a-form class="antd-modal-form" autocomplete="off" :layout="formLayout">
            <a-form-item :label="t('common.conclusion')">
              <a-textarea v-model:value="conclusion" />
            </a-form-item>
          </a-form>
        </a-col>
      </a-row>
    </a-modal>
  </div>

  <UserTableModal @register="registerSelUserModal" @select="assignSelectUserOk" />
  <RiskEventTriageModal ref="triageModalRef" @ok="triageResult" />
  <RiskEventRecordList ref="recordModalRef" />
</template>

<script lang="ts" name="suspiciousProcesses-suspiciousProcesses" setup>
import {useRoute, useRouter} from 'vue-router';
import {defineExpose, onMounted, ref, Ref, toRaw} from 'vue';
import {BasicTable} from '/@/components/Table';
import {useListPage} from '/@/hooks/system/useListPage';
import {columns, searchFormSchema} from './SuspiciousProcesses.data';
import {list, loadEventInfoByProcessesId} from './SuspiciousProcesses.api';
import {useI18n} from '/@/hooks/web/useI18n';
import {formLayout} from '/@/settings/designSetting';
import {useECharts} from '/@/hooks/web/useECharts';
import {EChartsOption} from 'echarts';
import RiskHostLogList from './modules/RiskHostLogList.vue';
import SuspiciousProcessesActivityList from './modules/SuspiciousProcessesActivityList.vue';
import JRangeDate from '/@/components/Form/src/jeecg/components/JRangeDate.vue';
import {list as inveList} from '/@/views/investigation/InvestigationVO.api';
import {useUserStore} from '/@/store/modules/user';
import {saveOrUpdate2} from '/@/views/risk/InvestigationRiskEventlogs.api';
import InvestigationListModal from '/@/views/investigation/modules/InvestigationListModal.vue';
import echarts from '/@/utils/lib/echarts';
import dayjs from 'dayjs';
import {useMessage} from '/@/hooks/web/useMessage';
import Icon from '/@/components/Icon';
import {usePermission} from '/@/hooks/web/usePermission';
import {assign, closed, triage} from '/@/views/suspiciousProcesses/SuspiciousProcesses.api';
import RiskEventRecordList from '/@/views/aggregationRiskEventView/modules/RiskEventRecordList.vue';
import RiskEventTriageModal
  from '/@/views/aggregationRiskEventView/modules/RiskEventTriageModal.vue';
import {Modal} from 'ant-design-vue';
import {useModal} from '/@/components/Modal';
import UserTableModal from '/@/components/Form/src/jeecg/components/userSelect/UserTableModal.vue';
import ApplyMenu from '/@/views/workflow/view/ApplyMenu.vue';
import {queryEntryTicket} from '/@/views/workflow/view/ts/TicketUtils';
import {TABLE_CACHE_KEY} from '/@/utils/valueEnum';

const props = defineProps({
    closeDrawer: Function,
  });

  const { hasPermission } = usePermission();
  const { t } = useI18n();
  const isSearch = ref<boolean>(true);
  const fromAsset = ref(false);
  const route = useRoute();
  const router = useRouter();
  const Info = ref<any>({});
  function tp(name) {
    return t('routes.SuspiciousProcesses.' + name);
  }
  let paramJson = sessionStorage.getItem('suspiciousProcessesView');
  if (paramJson) {
    let param = JSON.parse(paramJson);
    Info.value.deviceIp = param.deviceIp;
    Info.value.socTenantId = param.socTenantId;
  }
  if (route.query.ip) {
    Info.value.deviceIp = route.query.ip;
    fromAsset.value = true;
  }

  //注册table数据
  const { tableContext } = useListPage({
    tableProps: {
      api: list,
      columns,
      canResize: false,
      formConfig: {
        labelWidth: 120,
        schemas: searchFormSchema,
        autoSubmitOnEnter: true,
        showAdvancedButton: true,
        layout: formLayout,
      },
      defSort: {
        column: 'lastDetected',
        order: 'desc',
      },
      tableSetting: {
        cacheKey: TABLE_CACHE_KEY.suspiciousProcesses,
      },
      searchInfo: {
        deviceIp: Info.value.deviceIp,
        socTenantId: Info.value.socTenantId,
      },
      beforeFetch: beforeFetch,
      afterFetch: calcDataSource,
      actionColumn: {
        width: 230,
      },
    },
  });

  const [registerTable, { reload }, { rowSelection, selectedRowKeys }] = tableContext;

  //工单列表
  const workflowList: any = ref([]);
  onMounted(() => {
    getTickets();
  });

  /**
   * 获取entry ticket
   */
  async function getTickets() {
    workflowList.value = await queryEntryTicket(2);
    console.log('workflowList.value---------->', workflowList.value);
  }

  // function handleClose(record: Recordable) {
  //   console.log(record)
  //   let status = 1;
  //   if (record.status == 1) {
  //     status = 2;
  //   }
  //   updStatus({id: record.id, status: status}).then(() => {
  //     reload()
  //   })
  // }

  const minDate = ref('');
  const maxDate = ref('');
  const queryTime = ref('');
  let first = true;

  function calcDataSource(data) {
    if (!first) {
      return;
    }
    first = false;
    console.log(data);
    minDate.value = '';
    maxDate.value = '';
    for (let i in data) {
      if (i == '0') {
        minDate.value = data[i].firstDetected;
        maxDate.value = data[i].lastDetected;
      } else {
        if (minDate.value > data[i].firstDetected) {
          minDate.value = data[i].firstDetected;
        }
        if (maxDate.value < data[i].lastDetected) {
          maxDate.value = data[i].lastDetected;
        }
      }
    }
    queryTime.value = minDate.value + ',' + maxDate.value;
    initTileLine();
  }

  const activityRef = ref();

  function changeTime(value) {
    queryTime.value = value;
    reload();
    activityRef.value.refInit(value);
  }

  function beforeFetch(params) {
    console.log('Info.value.deviceIp', Info.value.deviceIp);
    console.log(queryTime.value);
    params.queryTime = queryTime.value;
    return params;
  }

  const timeLineChart = ref<HTMLDivElement | null>(null);
  const { setOptions, getInstance } = useECharts(timeLineChart as Ref<HTMLDivElement>);

  function initTileLine() {
    let start = minDate.value;
    let end = maxDate.value;

    let step = 1000; //毫秒
    let format = 'YYYY-MM-DD HH:mm:ss';
    if (dayjs(end).diff(dayjs(start), 'month') > 1) {
      step = 1000 * 60 * 60 * 24;
      format = 'YYYY-MM-DD';
    } else if (dayjs(end).diff(dayjs(start), 'day') > 1) {
      step = 1000 * 60 * 60;
      format = 'YYYY-MM-DD HH';
    }

    let startNum = +new Date(start);
    let endNum = +new Date(end);
    let date: any = [];
    console.log(startNum, endNum);
    while (startNum < endNum) {
      let now = new Date(startNum);
      let str = dayjs(now).format(format);
      date.push(str);
      startNum += step;
    }
    let option: EChartsOption = {
      xAxis: {
        show: false,
        type: 'category',
        data: date,
      },
      yAxis: {
        type: 'value',
      },
      dataZoom: [
        {
          start: 0,
          end: date.length - 1,
          realtime: false,
          top: 0,
          bottom: 0,
          height: 20,
        },
      ],
      series: [
        {
          name: 'Fake Data',
          type: 'line',
        },
      ],
    };
    setOptions(option);

    setTimeout(() => {
      let echart: echarts.ECharts = getInstance() as echarts.ECharts;
      if (echart) {
        echart.on('datazoom', () => {
          let xData = echart?.getOption().xAxis[0].data;
          const datazoom = echart?.getOption()?.dataZoom[0]; // 用getOption获取改变的值
          let start = xData[datazoom.startValue];
          let end = xData[datazoom.endValue];
          console.log(start, end);
          if (start.length == 10) {
            start += ' 00:00:00';
            end += ' 23:59:59';
          } else if (start.length == 13) {
            start += ':00:00';
            end += ':59:59';
          }
          queryTime.value = start + ',' + end;
          reload();
          activityRef.value.refInit(queryTime.value);
        });
      }
    }, 1000);
  }

  function expandRow(expand, row) {
    console.log(expand, row);
    if (expand) {
      loadEventInfoByProcessesIdData(row.id);
    }
  }

  const expandData = ref<{
    [id: string]: {
      event: any;
      eventId: string;
    };
  }>({});

  function loadEventInfoByProcessesIdData(id) {
    loadEventInfoByProcessesId({ id: id }).then((data) => {
      console.log(data);
      if (data && data.length > 0) {
        expandData.value[id] = {
          event: data,
          eventId: '',
        };
        showEventLog(data[0].eventId, id, null);
      }
    });
  }

  function showEventLog(eventId, id, $event) {
    console.log(eventId, id);
    expandData.value[id].eventId = eventId;
    if ($event) {
      activeElement($event.target);
    }
  }

  function activeElement($event) {
    console.log('------', $event);
    if ($event) {
      if ($event?.className != 'event_card') {
        activeElement($event.parentElement);
      } else if ($event.className?.indexOf('event_card') > -1) {
        let list = $event.parentElement.getElementsByClassName('event_card');
        for (let i = 0; i < list.length; i++) {
          list[i].className = 'event_card';
        }
        $event.className = 'event_card active';
      }
    }
  }

  const investigationData = ref<any[]>([]);
  const LogId = ref<string>(''); //记录当前点击的日志id
  const loadInvestigation = () => {
    inveList({ pageSize: 5, socTenantId: Info.value.socTenantId }).then((result) => {
      console.log(result);
      let list = result.records;
      investigationData.value = list;
    });
  };
  const { createMessage } = useMessage();

  function addInvestigation(data, id): void {
    if (!id) {
      if (selectedRowKeys.value.length == 0) {
        createMessage.error(t('common.chooseDataText'));
        return;
      }
      id = selectedRowKeys.value.join(',');
    }
    inveId = data.id;
    LogId.value = id;
    inveVisible.value = true;
  }

  const inveVisible = ref(false);
  let inveId = '';
  const conclusion = ref('');
  const userStore = useUserStore();
  const saveToInve = () => {
    if (addInveFlag) {
      addInveFlag = false;
      let param = {
        eventId: LogId.value,
        socTenantId: Info.value.socTenantId,
        conclusion: conclusion.value,
        conclusionBy: userStore.userInfo?.username,
        type: '5',
      };
      sessionStorage.setItem('addInvestigationParam', JSON.stringify(param));
      router.push({
        path: '/investigation/modules/InvestigationNewModal',
      });
      return;
    }

    saveOrUpdate2(
      {
        investigationId: inveId,
        eventId: LogId.value,
        conclusion: conclusion.value,
        conclusionBy: userStore.userInfo?.username,
        type: '5',
      },
      false
    ).then(() => {
      inveVisible.value = false;
      conclusion.value = '';
      reload();
    });
  };
  const registerRiskLogsModal = ref();

  function showMoreInvestigation(id) {
    if (!id) {
      if (selectedRowKeys.value.length == 0) {
        createMessage.error(t('common.chooseDataText'));
        return;
      }
      id = selectedRowKeys.value.join(',');
    }
    console.log(id);
    LogId.value = id;
    registerRiskLogsModal.value.visible = true;
  }

  let addInveFlag = false;
  const showAddInvestigation = (id) => {
    console.log(id);
    if ('rows' == id) {
      if (selectedRowKeys.value.length > 0) {
        id = selectedRowKeys.value.join(',');
      } else {
        createMessage.error(t('common.chooseDataText'));
        return;
      }
    }
    LogId.value = id;
    addInveFlag = true;
    inveVisible.value = true;
  };

  function back() {
    props.closeDrawer && props.closeDrawer();
    // router.back()
  }

  function loadFromAsset(ip) {
    fromAsset.value = true;
    Info.value.deviceIp = ip;
  }

  defineExpose({
    loadFromAsset,
  });

  //Assign 分配代码开始

  const [registerSelUserModal, assignUserModal] = useModal();
  let assignEventData;

  /**
   * 弹出选择用户列表
   * @param record
   */
  function assignUser(record) {
    console.log(record);
    assignEventData = toRaw(record);
    assignUserModal.openModal(true, { record });
  }

  /**
   * 分配给自己
   * @param record
   */
  function assignSelf(record) {
    assignEventData = toRaw(record);
    console.log(userStore.getUserInfo);
    Modal.confirm({
      type: 'confirm',
      title: t('common.assignSelfTitle'),
      content: t('common.assignSelfContent'),
      onOk: () => {
        let value = [userStore.getUserInfo.id];
        assignSelectUserOk(value);
      },
    });
  }

  /**
   * 选择用户确定
   * @param value
   */
  function assignSelectUserOk(value) {
    if (value && value[0]) {
      //保存分配的用户
      assign({
        id: assignEventData.id,
        owner: value[0],
      }).then(() => {
        reload();
      });
    }
  }

  //Assign 分配代码结束

  //triage 代码开始

  const triageModalRef = ref();

  function showTriage(record) {
    let data = {
      eventId: record.id,
    };
    triageModalRef.value.open(data);
  }

  function triageResult(data) {
    triage({
      triageStatus: data.triageStatus,
      id: data.eventId,
    }).then(() => {
      reload();
    });
  }

  //triage 代码结束

  //record开始

  const recordModalRef = ref();

  /**
   * 弹出record页
   * @param record
   */
  function showRecord(record) {
    const data = {
      eventType: 5,
      eventId: record.id,
    };
    recordModalRef.value.open(data);
  }

  //record结束

  /**
   * 关闭
   * @param data
   * @constructor
   */
  function close(data) {
    let content = '';
    let title = '';
    let status = 1;
    if (data.status == 1) {
      content = t('common.closeConfirmText');
      title = t('common.closeText');
      status = 2;
    } else if (data.status == 2) {
      content = t('common.openConfirmText');
      title = t('common.openText');
      status = 1;
    }
    Modal.confirm({
      title: title,
      content: content,
      okText: t('common.okText'),
      cancelText: t('common.cancelText'),
      onOk: () => {
        let params = {
          id: data.id,
          status: status,
        };
        closed(params).then(() => {
          reload();
        });
      },
    });
  }
</script>
<style scoped lang="less">
  :deep(.soc-basic-form) {
    padding: 0;
  }

  .event_card {
    margin: 5px;
    padding: 5px 10px;
    border: 1px solid @border-color;
    border-radius: 10px;
    line-height: 30px;
  }

  .active {
    background-color: rgba(@m-text-color, 0.4);
  }

  .fromAsset-true {
    padding: 0px !important;

    .returnBtn {
      display: none;
    }
  }

  :deep(.searchForm) {
    background-color: transparent !important;
  }
</style>
