import {defHttp} from '/@/utils/http/axios';
import {Modal} from 'ant-design-vue';
import {useI18n} from "/@/hooks/web/useI18n";

const {t} = useI18n();

enum Api {
  list = '/suspiciousProcesses/suspiciousProcesses/list',
  save = '/suspiciousProcesses/suspiciousProcesses/add',
  queryById = '/suspiciousProcesses/suspiciousProcesses/queryById',
  edit = '/suspiciousProcesses/suspiciousProcesses/edit',
  deleteOne = '/suspiciousProcesses/suspiciousProcesses/delete',
  deleteBatch = '/suspiciousProcesses/suspiciousProcesses/deleteBatch',
  importExcel = '/suspiciousProcesses/suspiciousProcesses/importExcel',
  exportXls = '/suspiciousProcesses/suspiciousProcesses/exportXls',
  loadEventInfoByProcessesId = '/suspiciousProcesses/suspiciousProcessesEvents/loadEventInfoByProcessesId',
  loadLogsByEventId = '/suspiciousProcesses/suspiciousProcessesEvents/loadLogsByEventId',
  queryList = '/suspiciousProcesses/suspiciousProcesses/queryList',

  closed = '/suspiciousProcesses/suspiciousProcesses/closed',
  triage = '/suspiciousProcesses/suspiciousProcesses/triage',
  assign = '/suspiciousProcesses/suspiciousProcesses/assign'


}

/**
 * 导出api
 * @param params
 */
export const getExportUrl = Api.exportXls;
/**
 * 导入api
 */
export const getImportUrl = Api.importExcel;
/**
 * 列表接口
 * @param params
 */
export const list = (params) =>
  defHttp.get({url: Api.list, params});

export const queryList = (params) =>
  defHttp.get({url: Api.queryList, params});


export const queryById = (params) =>
  defHttp.get({url: Api.queryById, params});

/**
 * 删除单个
 * @param params
 * @param handleSuccess
 */
export const deleteOne = (params, handleSuccess) => {
  return defHttp.delete({url: Api.deleteOne, params}, {joinParamsToUrl: true}).then(() => {
    handleSuccess();
  });
}
/**
 * 批量删除
 * @param params
 * @param handleSuccess
 */
export const batchDelete = (params, handleSuccess) => {
  Modal.confirm({
    title: t('common.delConfirmText'),
    content: t('common.delContent'),
    okText: t('common.okText'),
    cancelText: t('common.cancelText'),
    onOk: () => {
      return defHttp.delete({
        url: Api.deleteBatch,
        data: params
      }, {joinParamsToUrl: true}).then(() => {
        handleSuccess();
      });
    }
  });
}
/**
 * 保存或者更新
 * @param params
 * @param isUpdate 是否是更新数据
 */
export const saveOrUpdate = (params, isUpdate) => {
  const url = isUpdate ? Api.edit : Api.save;
  return defHttp.post({url: url, params});
}

export const updStatus = (params) => {
  return defHttp.post({url: Api.edit, params});
}

export const loadEventInfoByProcessesId = (params) =>
  defHttp.get({url: Api.loadEventInfoByProcessesId, params});

export const loadLogsByEventId = (params) =>
  defHttp.get({url: Api.loadLogsByEventId, params});

/**
 * 改变验证状态
 * @param params
 */
export const triage = (params) => {
  return defHttp.post({url: Api.triage, params});
}
/**
 * 关闭
 * @param params
 */
export const closed = (params) => {
  return defHttp.post({url: Api.closed, params});
}

/**
 * 分配
 * @param params
 */
export const assign = (params) => {
  return defHttp.post({url: Api.assign, params});
}
