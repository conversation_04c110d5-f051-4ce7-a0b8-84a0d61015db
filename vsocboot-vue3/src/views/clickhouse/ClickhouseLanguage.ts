import * as monaco from 'monaco-editor';
import {loadList} from "/@/views/parseRule/modules/LogField.api";
import {toRaw} from "vue";

function loadField() {
  return loadList({fieldSourceType: 1});
}
const DEFINE_VARIABLE = {
  'eventType' : ['security','endpoint'],
  'eventLevel' : ['high','middle','low']
}
const OPERTATE_KIND = monaco.languages.CompletionItemKind.Constant;
const BUILTIN_KIND = monaco.languages.CompletionItemKind.Method;
const VARIABLE_KIND = monaco.languages.CompletionItemKind.Variable;
const suggestType = {
  1: 'keywords',
  2: 'operators',
  0: 'builtinFunctions',
}
const suggestTitle = {
  1: 'Common variables',
  2: 'Relationship Characters',
  0: 'Logical operation',
}
export const symbolType = {
  '==' : {
    before : "\"",
    end :  "\""
  },
  '>' : {
    before : "\"",
    end :  "\""
  },
  '<' : {
    before : "\"",
    end :  "\""
  },
  '!=' : {
    before : "\"",
    end :  "\""
  },
  'in' : {
    before : "[\"",
    end :  "\"]"
  },
}
export const clickhouseLanguage = {
  keywords: [],
  operators: [
    {
      label: '==',
      insertText: '== ""',
      kind: OPERTATE_KIND,
      type: "2"
    },
    {
      label: 'in',
      kind: OPERTATE_KIND,
      insertText: 'in [""]',
      type: "2"
    },
    {
      label: '>',
      kind: OPERTATE_KIND,
      insertText: '> ""',
      type: "2"
    },
    {
      label: '<',
      kind: OPERTATE_KIND,
      insertText: '<  ""',
      type: "2"
    },
    {
      label: '!=',
      kind: OPERTATE_KIND,
      insertText: '!=  ""',
      type: "2"
    }
  ],
  builtinFunctions: [
    {
      label: 'AND',
      insertText: 'AND',
      kind: BUILTIN_KIND,
      type: "0"
    }, {
      label: 'OR',
      insertText: 'OR',
      kind: BUILTIN_KIND,
      type: "0"
    }]
}

/**
 * 自定义补全【下一步】
 */
export function getSuggestionItems(value) {
  let suggestions: any[] = [];
  value = value.toLowerCase();
  suggestions = clickhouseLanguage.keywords.filter(item =>  item.label.toLowerCase() == value);
  if (suggestions.length > 0) {
    return {
      dataSource: clickhouseLanguage.operators,
      title: suggestTitle[2],
    };
  }
  suggestions = clickhouseLanguage.operators.filter(item =>  item.label.toLowerCase() == value);

  if (suggestions.length > 0) {
    return {
      dataSource: clickhouseLanguage.builtinFunctions,
      title: suggestTitle[3],

    };
  }
 ;
  if (isValueType(value) != '') {
    return {
      dataSource: clickhouseLanguage.builtinFunctions,
      title: suggestTitle[3],

    };
  }

  return {
    dataSource: clickhouseLanguage.keywords,
    title: suggestTitle[1],

  };

}

/**
 * 获取常量
 * @param arr
 */
function getVariableArray(arr){
  let array = [];
  arr.forEach(item=>{
    array.push({
      label : item,
      insertText : item,
      kind : VARIABLE_KIND,
      type : 3
    })
  })
  return array;
}

/**
 * 关键字段
 */
export const getKeywords = () => {
  loadField().then(data => {
    clickhouseLanguage.keywords = data.map((item) => ({
      label: item.fieldName,
      kind: monaco.languages.CompletionItemKind.Variable,
      insertText: item.fieldName + "",
      type: "1"
    }));

  });
}

/**
 * 选项值（type = 3）
 */
export function getSelectTypeValue(filter){
  let suggestionAction = [];
  let val = filter.beforeArr[filter.beforeArr.length - 3];
  val = trimValue(val);
  let arr = DEFINE_VARIABLE[val] || [];
  console.log('arr',arr)
  if(arr.length > 0){
    suggestionAction = getVariableArray(arr);
  }
  return suggestionAction;

}
/**
 * 自定义补全【当前内容】
 * @param filter
 */
export function getSuggestionByType(filter) {
  let suggestionAction = [];
  // if(filter.filterStr && (filter.filterStr == '(' || filter.filterStr.trim() == '(') ){
  //   filter.type = '1';
  // }
  if(filter.type === ""){
    console.log('找不到类型啦！！！')
    filter.type = 0;
    return resultData( clickhouseLanguage[suggestType[0]], filter);
  }
  if (filter.type != 3) {
    suggestionAction = clickhouseLanguage[suggestType[filter.type]] ?? [];
  }else if(filter.beforeArr && filter.beforeArr.length > 2){
    suggestionAction = getSelectTypeValue(filter);
    console.log('filter ssuggestionAction==========',suggestionAction)
    //判断in类型
    if(filter.filterStr){
      let str = filter.filterStr;
      str = str.replace(/\"/g,"").replace("\[","").replace("\()","");
      let inArr = str.split(",");
      if(inArr.length > 1){
        filter.filterStr = inArr[inArr.length - 1];
      }else {
        filter.filterStr = str;
      }
    }
  }

  if (suggestionAction.length > 0 && filter.filterStr && filter.filterStr.trim() != "(") {
    console.log('filter str to filter==========')
    let str = filter.filterStr.replace(/\"/g,"");
    console.log('filter str ===============',str)
    if(filter.type == '1'){
      str = str.replace("\(","");
    }
    suggestionAction = suggestionAction.filter(item => {
      let label = item.label.toUpperCase();
      if (-1 != label.indexOf(str.toUpperCase())) {
        return item;
      }
    })
  }

  return resultData(suggestionAction,filter)

}

function resultData(suggestionAction,filter){
  let result =  {
    dataSource: suggestionAction,
    type : filter.type

  };
  if(filter.endLen > 0){
    result.filter = filter;
  }

  return result;
}

/**
 * 去掉特殊字符
 * @param val
 */
function trimValue(val){
  return val.replace(/\"/g,"").replace(/\(/g,"").replace(/\)/g,"").replace(/\[/g,"").replace(/\]/g,"");
}

/**
 * 是否还有特殊字符串
 * @param str
 */
export function isSpecialWord(str){
  let values = ['(','[',']',')'];
  for(let i in values){
    if(str.indexOf(values[i]) != -1){
      return true;
    }
  }
  return false;
}
/**
 * 根据字符串，判断所属类型
 * @param value
 */
export function getSuggestionItemType(value) {
  console.log('getSuggestionItemType value',value)
  value = value.toLowerCase();
  let type = '';
  let arr = clickhouseLanguage.keywords.filter(item =>item.label.toLowerCase() == value);
  console.log('keywords arr',arr)
  if (arr.length > 0) {
     return 1;
  }
  arr = clickhouseLanguage.operators.filter(item =>item.label.toLowerCase() == value);
  if (arr.length > 0) {
    return 2;
  }
  arr = clickhouseLanguage.builtinFunctions.filter(item =>item.label.toLowerCase() == value);
  if (arr.length > 0) {
    return 0;
  }

  return  isValueType(value);
}

/**
 * 获取应该提示的类型
 * @param len 字符串按【空格】分组的长度
 */
export function getTypeByTokensLen(len){
  return parseInt(len % 4);
}

/**
 * 特殊字符串
 * @param str
 */
export function getTypeBySpecialStr(str){
  if(str && str.trim() == "("){
    return 0;
  }
  if(str && str.trim() == ")"){
    return 3;
  }

  // if( str && -1 != str.indexOf("\(") ){
  //   return 2;
  // }
  let values = [',','[',']','"'];
  for(let i in values){
    if(str.indexOf(values[i]) != -1){
      return 3;
    }
  }


  values = ['>','!','=','<'];
  for(let i in values){
    if(str.indexOf(values[i]) != -1){
      return 2;
    }
  }
  return "";
}
/**
 * 判断是否是配置值
 * @param value
 */
function isValueType(value){
  var exp1 = "^\".*\"$";
  var exp2 = "^\[.*\]$";
  let reg = new RegExp(exp1)
  let reg2 = new RegExp(exp2)
  let val = reg.test(value);
  let val2 = reg2.test(value);
  if (val || val2) {
    return 3;
  }
  return '';
}

function isType(content){
  //起点判断
  if(content.length == 1 && content == "("){
    // emit('showType',{type : -1});
    return;
  }
  if(content && content.trim().length == 1 && content.trim() == "("){
    // emit('showType',{type : 1});
    // return;
  }
}

export function findLastType(arr){
  console.log('findLastType arr===========',arr)
  let type = -1;
  // if(arr.length == 1){
  //   return 1;
  // }
  let len = arr.length;
  let str = arr[len - 1];
  type = findStrType(str);

  console.log('===========findStrType',type)
  return type;
}

/**
 * 解析字符串类型
 * @param str
 */
export function findStrType(str){
  console.log('============findStrType str:',str)
  let values = ['>','!','=','<'];
  if(str == '('){
    return 0;
  }else if(str == ')'){
    return -1;
  }
  for(let i in values){
    if(str.indexOf(values[i]) != -1){
      return 2;
    }
  }

  if(-1 != str.indexOf("\"")){
    return 3;
  }

  return getSuggestionItemType(str);
}
