<template>
  <div ref="editorContainer" id="editorContainerDiv"
       :style="{width:cW + 'px' ,height:cH + 'px'}"></div>
</template>

<script lang="ts" setup>
import * as monaco from 'monaco-editor';
import {
  defineExpose,
  defineProps,
  inject,
  nextTick,
  onBeforeUnmount,
  onMounted,
  ref,
  toRaw,
  watch
} from "vue";
import {OPERATE_TYPE, symbolType} from '../ts/ThreatHuntingLanguage'
import {E_EditType, E_Rel, languageColor} from "/@/views/clickhouse/ts/ToolUtils";
import {SuggestResult, useSuggestContent} from "/@/views/clickhouse/ts/useSuggestContent";

const { suggestHandle } = useSuggestContent();
const emit = defineEmits(['search' ,'showType','filter','focus','check','update:height','showFieldInfo']);
const props = defineProps({
  width: {
    type: Number
  },
  height: {
    type: String
  },
  readOnly : {
    type : <PERSON>olean
  }
})
const cH = ref(32);
const cW = ref(props.width);
const selectOpen = inject('selectOpen');
//点击目标
const clickTarget = ref({});
watch(()=>props.readOnly,(n)=>{
  if(editor.value){
    toRaw(editor.value).updateOptions({readOnly: props.readOnly});
  }
})

watch(()=>props.width,(n)=>{
  console.log('=========cW.props.width',n)
  if(n > 0){
    cW.value = n;
  }

  console.log('=========cW.value',cW.value)
},{deep:true,immediate:true})
const editorContainer = ref();
const editor = ref<any>(null);
const language = "mylanguages";
const defineTheme = "SelfTheme";

const isReplace = ref(false);
// 高度缓存
onBeforeUnmount(() => {
  if (editor.value) {
    toRaw(editor.value).dispose()
    toRaw(editor.value)?.getDomNode()?.removeEventListener('scroll', scrollTop, true);
  }

})

onMounted(() => {

// 自定义灰色背景主题
  monaco.editor.defineTheme(defineTheme, {
    base: 'vs-dark',
    inherit: true,
    rules: [
      // { background: '#1A1B1F' },
      // 设置token颜色，
      { token: 'OPERTATE', foreground: languageColor[E_EditType.LOGICAL], fontStyle: 'bold' },
      { token: 'BUILTIN', foreground: languageColor[E_EditType.OPERATE], fontStyle: 'bold' },
      { token: 'KEYWORD', foreground: languageColor[E_EditType.KEYWORD], fontStyle: 'bold' },
      { token: 'VARIABLE', foreground: languageColor[E_EditType.VARIABLE], fontStyle: 'bold' },
      { token: 'REL', foreground: languageColor[E_EditType.VARIABLE]}

    ],
    colors: {
      // 相关颜色属性配置
      'editor.background': '#1A1B1F',     //背景色
    }
  });
  //自定义语言
  monaco.languages.register({id: language});
  //设置自定义主题
  monaco.editor.setTheme(defineTheme);

  editor.value = monaco.editor.create(editorContainer.value, {
    value: "",
    theme: 'SelfTheme', //官方自带三种主题vs, hc-black, or vs-dark,SelfTheme
    language: language,
    minimap: {
      enabled: false // 是否启用预览图
    },
    codeLens: false,
    codeLensFontFamily: '', // codeLens的字体样式
    codeLensFontSize: 44, // codeLens的字体大小
    contextmenu: true, // 启用上下文菜单
    columnSelection: false,
    automaticLayout: true, // 自动布局
    folding: false, // 是否折叠
    foldingHighlight: false, // 折叠等高线
    cursorWidth: 2, // <=25 光标宽度
    lineHeight : 32,
    fontSize : 14,
    acceptSuggestionOnEnter : "off",// 接受输入建议 "on" | "off" | "smart"
    scrollBeyondLastLine: false,
    wrappingStrategy: 'advanced',
    wordWrap : 'on',
    selectionClipboard: false, // 选择剪切板
    colorDecorators: true, // 颜色装饰器
    accessibilitySupport: "auto", // 辅助功能支持  "auto" | "off" | "on"
    lineNumbers: "off", // 行号 取值： "on" | "off" | "relative" | "interval" | function
    readOnly: false, //是否只读  取值 true | false
    autoClosingBrackets: 'never', // 是否自动添加结束括号(包括中括号) "always" | "languageDefined" | "beforeWhitespace" | "never"
    autoClosingDelete: 'always', // 是否自动删除结束括号(包括中括号) "always" | "never" | "auto"
    autoClosingOvertype: 'always', // 是否关闭改写 即使用insert模式时是覆盖后面的文字还是不覆盖后面的文字 "always" | "never" | "auto"
    autoClosingQuotes: 'always', // 是否自动添加结束的单引号 双引号 "always" | "languageDefined" | "beforeWhitespace" | "never"
    autoIndent: 'None', // 控制编辑器在用户键入、粘贴、移动或缩进行时是否应自动调整缩进
    scrollbar : {vertical :'hidden'}
  });

  monaco.languages.typescript.javascriptDefaults.setCompilerOptions({
    target:monaco.languages.typescript.ScriptTarget.ES2020,
    allowNonTsExtensions:true,
    lib:[]
  })

  //editor focus
  editor.value.onDidFocusEditorWidget((e) => {
    let content = toRaw(editor.value).getValue();
    console.log('onDidFocusEditorWidget content' ,content == '')
    if(content == ""){
      console.log('onDidFocusEditorWidget')
      emit('showType',{type : E_EditType.KEYWORD});
    }
  })

  //cursor 选中位置改变
  editor.value.onDidChangeCursorSelection((e) => {
    let content = toRaw(editor.value).getValue();
    let select = editor.value.getSelection();

    //没有内容
    if(content.trim().length == 0){
      emit('showType',{type : E_EditType.KEYWORD});
      return ;
    }
    nextTick(()=>{
      const result:SuggestResult = suggestHandle(content,select,clickTarget.value);
      // console.log('SuggestResult=',result)
      emit('showType',result);
    })
    //处理内容


  });
  //键盘keydown
  editor.value.onKeyDown((e: monaco.IKeyboardEvent) => {
    console.log('onKeyDown',e.keyCode)
    if(e.keyCode == 16 || e.keyCode == 18){//上下选择键
      emit('focus');
    }
    if(e.keyCode == 3){//回车
      console.log('回车--------')
      let content = toRaw(editor.value).getValue();
      if(content && content.trim()){
        emit('search');
      }
      // e.cancelBubble = true;
      e.stopPropagation();
      e.preventDefault();
    }
  });
  //内容改变
  editor.value.onDidChangeModelContent((e) => {
    // console.log('onDidChangeModelContent:', e.changes[0].range.endColumn);
    console.log('props.width',props.width)
    let num = parseInt(props.width / 8);
    const content = toRaw(editor.value).getValue();
    const len = content.length;
    // console.log('row num:', num);
    if(len > num){
      let height = (parseInt(len / num) + 1) * 32;
      cH.value = height;
      toRaw(editor.value).layout({height:height,width:props.width});
      // toRaw(editor.value).getModel().height
    }else{
      cH.value = 32;
      toRaw(editor.value).layout({height:cH.value,width:props.width});
    }
    console.log('cH.value',cH.value)
    emit('update:height',cH.value);
  });

  editor.value.onMouseDown((e: monaco.editor.IEditorMouseEvent) => {
    console.log('onMouseDown onMouseDown',e)
    clickTarget.value = e.target;
    if (e.event.detail === 2) { // detail 属性表示点击次数
      emit('showFieldInfo',e)
      // 你可以在这里添加更多的代码来执行双击时需要进行的操作
    }
  });

})

/**
 * 设置字体颜色
 * @param data 查询项
 */
function refreshLanguage(data){
  let keywords = [];
  data.sourceData.forEach((item) => {
    keywords.push(item.fieldValue);
  })

  monaco.languages.setMonarchTokensProvider(language, {
    ignoreCase: true, // 忽略大小写
    keywords : keywords,
    builtins : ['AND','OR','NOT'],
    operators : OPERATE_TYPE,
    // 设置语法规则
    tokenizer: {
      root: [
         [ /@?[a-zA-Z][\w$]*/, {
          cases: {
            '@keywords': 'KEYWORD',
            '@builtins': 'BUILTIN',
            '@operators': 'OPERTATE',
          }
        }],
        [/".*?"/, 'REL'],
        [/[{}()\[\]]/, 'REL'],
        [/[==<>!=]/, 'OPERTATE'],
      ]
    }

  });
}







/**
 * 获取查询值
 */
function getContentValue() {
  return toRaw(editor.value).getValue();
}

/**
 * 清空查询值
 */
function clearContentValue(){
  toRaw(editor.value).setValue('');
}





/**
 * 回显设置的值
 * @param item
 */
function setContentValue(item,filter){
  // console.log('====setContentValue filter============',filter)
  // console.log('====setContentValue item============',item)
  let content = toRaw(editor.value).getValue();
  // console.log('====setContentValue content============',content)
  const tokens = content.split(/\s+/);
  if(filter && filter.isReplace){//固定位置替换
    let start = content.substring(0,filter.startLen);
    if(!item){//手动输入
      let endStr = "\"";
      if(start.trim().substring(start.trim().length - 1) == '\"'){
        let end = content.substring(filter.startLen).indexOf('\"');
        endStr = content.substring(start.length + end - 1);
        start = start + filter.filterStr  + " ";
        filter.startLen = start.length + 1;
        filter.endLen = start.length + 1;
        // console.log('@@@@@@@@@@@@赋值endStr3==========',endStr)
      }else{
        start = start + filter.filterStr + " ";
        endStr = content.substring(start.length - 1);
        filter.startLen = start.length ;
        filter.endLen = start.length;
        console.log('start=============',start)
        // console.log('@@@@@@@@@@赋值endStr=====================',endStr)
      }

      content = start + endStr;

      // console.log('赋值',content)
      content = content.replace(/\r\n/g,"")
      content = content.replace(/\n/g,"");

      //赋值
      toRaw(editor.value).setValue(content);
      toRaw(editor.value).setPosition({lineNumber:1,column: filter.startLen + 1});
      toRaw(editor.value).focus();
      return;

    }


    let end = content.substring(filter.endLen,content.length );
    let len = end.length;

    if(filter.filterStr && -1 != filter.filterStr.indexOf("\(")){
      item.insertText = '(' + item.insertText ;
    }else if(filter.filterStr && -1 != filter.filterStr.indexOf("\)")){
      item.insertText =  item.insertText + ')';
    }
    else if(filter.type == '3' && filter.beforeArr && filter.beforeArr.length > 2){
      autoSetInValue(filter.beforeArr,item,len);
     }
    //解决逻辑符号改变对字符串的影响
    if(filter.type == E_EditType.LOGICAL){
      if(filter.nextVal){
        let subEnd = 1 + filter.nextVal.length;
        // console.log('subEnd.subEnd============',subEnd)
        // console.log('filter.end1============',end)
        if(filter.replaceStr.toLowerCase() == 'in'){
          console.log('从in改为字符串')
          subEnd = subEnd + 2;
        }
        end = end.substring(subEnd ,end.length );
        // console.log('filter.end2============',end)
        if(item.label.toLowerCase() == 'in'){//从字符串改为in
          content = start + item.label + " [" + filter.nextVal + "] " ;
        }else{
          content = start + item.label  + " " + filter.nextVal ;
        }
      }else if(filter.replaceStr.toLowerCase() == 'in'){
        content = start + item.label;
      }else{
        content = start + item?.insertText ;
      }


    }else{
      if(item){
        content = start + item?.insertText ;
      }

    }

    //替换字符串后面没有空格【加空格】
    if(len == 0 || end.trim().length == len ){
      end = " " + end;
    }
    filter.endLen = content.length + 1;
    content = content + end;
    // console.log('setContentValue text', content)

    isReplace.value = true;
  }



  else{//追加
  console.log('=============追加',item)
    // let end = content.substring(filter.endLen,content.length );

    if(!item){
      content = content + " ";
      content = content.replace(/\r\n/g,"")
      content = content.replace(/\n/g,"");
      //赋值
      console.log('=============追加 content',content)
      toRaw(editor.value).setValue(content);
      toRaw(editor.value).setPosition({lineNumber:1,column:content.length + 1});
      toRaw(editor.value).focus();
      return;
    }

    if(filter && filter.filterStr){
      content = content.substring(0,content.length - filter.filterStr.length);
    }

    if(item){
      if(item.type == 3){
        autoSetInValue(tokens,item,0);
      }
      content = content + item.insertText ;

      //改为加空格，解决连续回车选中
      if(item.type != '2'){
        content = content + ' ';
      }
    }



  }

  //去掉所有的换行符
  content = content.replace(/\r\n/g,"")
  content = content.replace(/\n/g,"");
  console.log('赋值',content)
  //赋值
  toRaw(editor.value).setValue(content);

  //光标显示
  if(filter && filter.isReplace){
    toRaw(editor.value).setPosition({lineNumber:1,column:filter.endLen + 1});
    toRaw(editor.value).focus();
    return;
  }
  setCursor(item,content);


}

/**
 * 自动更正值
 * @param contentArr
 * @param item
 * @param len
 */
function autoSetInValue(contentArr,item,len){
  // console.log('==autoSetInValue item',item)
  if(!item){
    return
  }
  // console.log('==autoSetInValue contentArr',contentArr)
  // console.log('==autoSetInValue item len=============',len)
  let str = contentArr[contentArr.length - 2];
  let str2 = contentArr[contentArr.length - 1];
  // console.log('==autoSetInValue str',str)
  // console.log('==autoSetInValue str2',str2)
  if(str == E_Rel.IN ){
    let start = symbolType[str].before;
    if(str2 && str2.split(",").length > 1){
      let strArr = str2.split(",");
      start = strArr.splice(0,strArr.length -1).toString()  + ',"';
    }
    // console.log('item.insertText start===========',start)
    item.insertText = start + item.insertText + (len<3 ?  symbolType[str].end : '');
    // console.log('item.insertText===========',item.insertText)

  }else{
    if(-1 == item.insertText.indexOf(symbolType[str].end)){
      item.insertText = symbolType[str].before + item.insertText + symbolType[str].end;
    }else{
      item.insertText = symbolType[str].before + item.insertText;
    }

  }
}
/**
 * 显示光标
 * @param item 选中对象
 * @param content 输入框内容
 */
function setCursor(item,content){
  // const currentModel = toRaw(editor.value).getModel();
  //
  // console.log('currentModel ',currentModel)
  // const position = toRaw(editor.value).getPosition();
  // console.log('position ',position)
  // console.log('显示光标~~~~')
  // console.log('显示光标 item~~~~',item)
  // console.log('显示光标~~content~~',content)
  if(item){
    if(item.type == '2'){//连接符 == > < in等
      let len = content.trim().length;
      if(item.label != 'in'){//光标在""的里面
        toRaw(editor.value).setPosition({lineNumber:1,column:len });
      }else{//光标在[""]的""的里面
        toRaw(editor.value).setPosition({lineNumber:1,column:len - 1});
      }
    }else if(item.type == '3' && -1 != item.insertText.indexOf("]")){
      toRaw(editor.value).setPosition({lineNumber:1,column:content.length - 1});
    }else{
      toRaw(editor.value).setPosition({lineNumber:1,column:content.length + 1});
    }
  }
  else{//空格后面
    console.log('//----------------空格后面')
    toRaw(editor.value).setPosition({lineNumber:1,column:content.length + 1});
  }
  toRaw(editor.value).focus();
}


function setStringValue(content){
  // console.log(content)
  //去掉所有的换行符
  content = content.replace(/\r\n/g,"")
  content = content.replace(/\n/g,"");
  toRaw(editor.value).setValue(content);
  // toRaw(editor.value).setPosition({lineNumber:1,column:content.length + 1});
  // toRaw(editor.value).focus();
}

function setInitValue(content){
  // console.log(content)
  //去掉所有的换行符
  content = content.replace(/\r\n/g,"")
  content = content.replace(/\n/g,"");
  toRaw(editor.value).setValue(content);
  toRaw(editor.value).setPosition({lineNumber:1,column:content.length + 1});
  toRaw(editor.value).focus();
}
function setCursorEnd(){
  const content = getContentValue();
  toRaw(editor.value).setPosition({lineNumber:1,column:content.length + 1});
  toRaw(editor.value).focus();
}



defineExpose({
  setContentValue,
  refreshLanguage,
  getContentValue,
  clearContentValue,
  setStringValue,
  setInitValue,
  setCursorEnd
});



</script>

<style lang="less" scoped>
/deep/ .monaco-custom-toggle {
  display: none !important;
}

/*关闭自带提示*/
/deep/ .suggest-widget {
  display: none !important;
}
/*改写自带样式*/

/deep/ .monaco-editor{
  height: 100%!important;
  width: 100%!important;
  overflow: hidden;
}
/deep/ .monaco-editor, /deep/ .monaco-editor-background, /deep/ .monaco-editor .margin {
  background: transparent !important;

}
</style>
