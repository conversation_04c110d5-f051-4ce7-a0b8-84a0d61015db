<template>
  <a-form style="height: 32px" ref="formRef" :model="formModel" autocomplete="off">

    <a-form-item
      label=""
      name="inputVal"
      :rules="[{ required: true, validator: isValidValue }]"
      v-if="fieldType == 'number'">
      <a-input-number v-model:value="formModel.inputVal" style="width: 150px;" @keyup.enter="submitValue"></a-input-number>
    </a-form-item>
    <a-form-item
      label=""
      name="inputVal"
      :rules=" [{ required: true, validator: isValidIp  }]"
      v-else-if="fieldType == 'ipv4'">
      <a-input v-model:value="formModel.inputVal" style="width: 150px;" @keyup.enter="submitValue"></a-input>
    </a-form-item>
    <a-form-item
      label=""
      name="inputVal"
      :rules="[{ required: true, validator: isValidValue}]"
      v-else>
      <a-input v-model:value="formModel.inputVal" style="width: 150px;" @keyup.enter="submitValue"></a-input>
    </a-form-item>


  </a-form>

</template>

<script lang="ts" setup>

import {defineProps, inject, ref, watch} from "vue";
import {checkedElRuleIp, checkedElRuleRequired, checkedRule} from "/@/utils/checkedRule";
const emit = defineEmits(['ok']);
const formRef = ref();
const item = ref({});
const formModel = ref({
  inputVal: ''
})

const props = defineProps({
  value: {
    type: Object
  },
  isTag : {
    type : Boolean,
  }
})
const isTag = props.isTag;
const fieldType = ref('');
const fieldDataType = inject('fieldDataType');

const inOption = ref([]);
watch(() => props.value, (n) => {
  item.value = n;
  fieldType.value = fieldDataType.value[n.field];
  bindValue();
}, {deep: true, immediate: true})

watch(() => formModel.value.inputVal, (n) => {
  console.log('inputVal.value', n)
  update();
})

function bindValue(){
  if(!isTag && item.value){
    if(fieldType.value == 'number'){
      formModel.value.inputVal = Number(item.value.value);
    }else{
      formModel.value.inputVal =  item.value.value;
    }

  }
}
async function update() {
  formRef.value
    .validate()
    .then(() => {
      console.log('isTag',isTag)
      if(!isTag){
        item.value.value = formModel.value.inputVal + "";
      }
    })
    .catch(error => {
      item.value.value = "";
      console.log('error', error);
    });
}

/**
 * 校验值是否重复，是否为空
 * @param rule
 * @param value
 * @param callback
 */
const isValidValue = (rule, value, callback) => {
  if(!value){
    return callback(new Error('Please input your value!'))
  }
  if(item.value.tag){
    let arr = item.value.tag.filter(v=>v==value);
    if (arr.length > 0) {
      return callback(new Error('value duplication'))
    }
  }
  callback();
  return true
}
/**
 * 校验IP是否符合规则
 * @param rule
 * @param value
 * @param callback
 */
const isValidIp = (rule, value, callback) => {
  if(!value){
    return callback(new Error('Please input your value!'))
  }
  var regEx = /,/g
  var ipList = value.toString().replace(regEx, ',').split(',')
  var reg = /^(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])$/
  for (var i in ipList) {
    if (!reg.test(ipList[i])) {
      return callback(new Error('Incorrect IP format'))
    }
  }
  //校验ip重复
  if(item.value.tag){
    let arr = item.value.tag.filter(v=>v==value);
    if (arr.length > 0) {
      return callback(new Error('IP address duplication'))
    }
  }
  callback();
  return true
}
function submitValue(){
  if(!isTag){
    return;
  }
  formRef.value
    .validate()
    .then(() => {
      if(!item.value.tag){
        item.value.tag = [];
      }
      if(formModel.value.inputVal ){
        item.value.tag.push(formModel.value.inputVal);
      }

      emit('ok');
    })
    .catch(error => {
      item.value.value = "";
      console.log('error', error);
    });


}
</script>

<style lang="less" scoped>
@import "../less/tree";
</style>
