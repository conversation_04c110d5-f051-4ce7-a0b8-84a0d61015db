.search-container {
  display: flex;
  flex-direction: row;
  align-items: center;
  position: relative;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 4px;
  width: 100%;
  .fieldInfoDiv {
    position: absolute;
    background-color: @dark-bg2;
    border: 1px solid @border-color;
    border-radius: 6px;
    padding: 8px;
    max-width: 400px;
    min-width: 190px;
    z-index: 9;
  }


}

.code_wrapper {
  position: relative;
  flex: 1;
  display: flex;
  flex-direction: row;

  width: 100%;
  /deep/.ant-list-split .ant-list-item {
    border-bottom: 1px solid @border-color-01;
  }
  //操作按钮
  .option_btns {
    display: flex;
    flex-direction: row;
    gap: 4px;
    align-items: center;
    justify-content: right;
    margin-left: auto;
    position: absolute;
    right: 8px;
    top: 4px;
  }


  .tab-head {
    display: flex;
    flex-direction: row;
    gap: 24px;
    padding: 10px 16px;
    border-bottom: 1px solid @border-color-01;
    .tab-head_title {
      color: @font-color-1;
      cursor: pointer;

    }
  }
}

/deep/ .current-line {
  width: 100%;
  border: 0px !important;
}

/deep/ .decorationsOverviewRuler {
  display: none !important;
}
#listContent{
  overflow: auto;
  min-width: 400px;
  max-height: 290px;
}
.check_wrapper {
  position: absolute;
  z-index: 100;
  width: 100%;
  background: @dark-bg3;
  border-radius: 0 0 4px 4px;
  overflow: auto;
  /deep/.ant-list{
    width: 100%;
    //min-width: 400px;
    //max-height: 330px;
    //overflow:auto ;
    .ant-list-items{
      width: 100%;

      .is_del{
        display: none;
      }

    }
  }
  .source-item {
    cursor: pointer;
    padding: 10px 16px;
    display: flex;
    align-items: center;
    font-size: 13px;
    font-weight: 600;
    line-height: 20px;
    gap: 8px;
    color: @font-color-1;
    .item-filter{
      width: calc(100% - 200px);
    }
    &.active {
      background: @dark-table-hover;
      .item-filter{
        width: calc(100% - 230px);
      }
      .is_del{
        display: block;
      }
    }
  }


}



.tree-search {
  background: @dark-bg3;
  min-height: 200px;
  min-width: 630px;
  max-height: 600px;
  overflow: auto;
  position: absolute;
  z-index: 101;
  top: 33px;
  padding: 16px;
  border-radius: 0 0 4px 4px;
}


.search-tree-btn{
  font-size: 14px!important;
}
