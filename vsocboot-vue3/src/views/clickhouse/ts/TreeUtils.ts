import { checkedIpv4 } from "/@/utils/checkedRule";
import { message } from "ant-design-vue";
import { E_EditType, E_Rel } from "/@/views/clickhouse/ts/ToolUtils";
import { globalLanguage } from "/@/views/clickhouse/ts/useSuggestContent";

// 常量定义 - 关系运算符选项
export const TREE_REL_OPTION = [
  { value: E_Rel.AND, label: 'AND' },
  { value: E_Rel.OR, label: 'OR' }
];

// 常量定义 - 操作运算符选项
export const TREE_OPERATE_OPTION = [
  { value: E_Rel.EQ, label: '==' },
  { value: E_Rel.NOT_EQ, label: '!=' },
  { value: E_Rel.RT, label: '>' },
  { value: E_Rel.LT, label: '<' },
  { value: E_Rel.LIKE, label: 'Like' },
  { value: E_Rel.IN, label: 'IN' },
  { value: E_Rel.START_WITH, label: 'Startwith' },
  { value: E_Rel.END_WITH, label: 'Endwith' },
  { value: E_Rel.CONTAIN, label: 'Contain' },
  { value: E_Rel.NOT_CONTAIN, label: 'Notcontain' }
];

// 常量定义 - 按数据类型分类的操作运算符选项
export const TREE_OPERATE_OPTION_MAP = {
  string: [
    { value: E_Rel.EQ, label: '==' },
    { value: E_Rel.NOT_EQ, label: '!=' },
    { value: E_Rel.LIKE, label: 'Like' },
    { value: E_Rel.IN, label: 'IN' },
    { value: E_Rel.START_WITH, label: 'Startwith' },
    { value: E_Rel.END_WITH, label: 'Endwith' },
    { value: E_Rel.CONTAIN, label: 'Contain' },
    { value: E_Rel.NOT_CONTAIN, label: 'Notcontain' }
  ],
  number: [
    { value: E_Rel.EQ, label: '==' },
    { value: E_Rel.NOT_EQ, label: '!=' },
    { value: E_Rel.RT, label: '>' },
    { value: E_Rel.LT, label: '<' },
    { value: E_Rel.IN, label: 'IN' }
  ],
  ipv4: [
    { value: E_Rel.EQ, label: '==' },
    { value: E_Rel.NOT_EQ, label: '!=' },
    { value: E_Rel.RT, label: '>' },
    { value: E_Rel.LT, label: '<' },
    { value: E_Rel.IN, label: 'IN' },
  ],
  exception_number: [
    { value: E_Rel.EQ, label: '==' },
    { value: E_Rel.NOT_EQ, label: '!=' },
    { value: E_Rel.IN, label: 'IN' }
  ]
};

// 特殊运算符映射关系
const SPECIAL_OPERATOR_MAP = {
  'contain': { prefix: ' like "%', suffix: '%"' },
  'start_with': { prefix: ' like "', suffix: '%"' },
  'end_with': { prefix: ' like "%', suffix: '"' },
  'not_contain': { prefix: ' not like "%', suffix: '%"' },
  [E_Rel.CONTAIN]: { prefix: ' like "%', suffix: '%"' },
  [E_Rel.NOT_CONTAIN]: { prefix: ' not like "%', suffix: '%"' },
  [E_Rel.START_WITH]: { prefix: ' like "', suffix: '%"' },
  [E_Rel.END_WITH]: { prefix: ' like "%', suffix: '"' },
  [E_Rel.LIKE]: { prefix: ' like "', suffix: '"' }
};

/**
 * 转换为 Monaco 查询代码
 * @param {Object} data - 树形结构数据
 * @returns {string} 生成的查询字符串
 */
export function toMonacoCode(data) {
  return data ? generateCode(data.rel, data.list) : '';
}

/**
 * 递归生成查询代码
 * @param {string} rel - 关系运算符(AND/OR)
 * @param {Array} list - 条件列表
 * @returns {string} 生成的查询片段
 */
function generateCode(rel, list) {
  let code = '';

  if (!Array.isArray(list)) {
    return code;
  }

  list.forEach((item, index) => {
    if (item.rel) {
      const groupCode = generateCode(item.rel, item.list);
      if (groupCode) {
        code += index > 0 ? ` ${rel} ( ${groupCode} )` : `( ${groupCode} )`;
      }
    } else if (item.field) {
      const condition = generateCondition(item);
      if (condition) {
        code += index > 0 ? ` ${rel} ${condition}` : condition;
      }
    }
  });

  return code;
}

/**
 * 生成单个条件表达式
 * @param {Object} item - 条件项
 * @returns {string} 条件字符串
 */
function generateCondition(item) {
  const { field, operate, tag, value } = item;

  if (operate.toLowerCase() === "in") {
    const values = Array.isArray(tag) ? tag : [tag];
    return `${field} IN [ "${values.join('", "')}" ]`;
  }

  return `${field} ${operate} "${value}"`;
}

/**
 * 转换为树形结构数据
 * @param {string} str - 查询字符串
 * @returns {Object} 树形结构数据
 */
export function toTreeCode(str) {
  if (!str) return { rel: 'AND', list: [] };

  const processedStr = preprocessString(str);
  const parsedData = parseExpression(processedStr);

  if (parsedData.rel) {
    return parsedData;
  }
  // 确保最外层结构统一
  return {
    rel: 'AND',
    list: [parsedData]
  };
}

/**
 * 预处理字符串，替换特殊字符
 * @param {string} str - 原始字符串
 * @returns {string} 处理后的字符串
 */
function preprocessString(str) {
  return str.replace(/"([^"]*)"/g, match =>
    match.replace(/ /g, '<<SPACE>>')
      .replace(/\(/g, 'leftkuohao')
      .replace(/\)/g, 'rightkuohao')
  );
}

/**
 * 解析表达式为树形结构（核心逻辑）
 * @param {string} expr - 表达式字符串
 * @returns {Object} 树形结构数据
 */
function parseExpression(expr) {
  expr = expr.trim();
  if (!expr) return { list: [] };

  // 处理外层括号
  if (expr.startsWith('(') && expr.endsWith(')')) {
    const innerExpr = expr.slice(1, -1).trim();
    if (innerExpr) {
      expr = innerExpr;
    }
  }

  const topLevelOperators = findTopLevelOperators(expr);

  if (topLevelOperators.length === 0) {
    // 无顶层运算符，解析为叶子节点
    return parseSingleCondition(expr);
  }

  // 有顶层运算符，解析为分组节点
  const rootRel = topLevelOperators[0].rel;
  const treeData = { rel: rootRel, list: [] };

  let lastIndex = 0;
  topLevelOperators.forEach(op => {
    const segment = expr.substring(lastIndex, op.index).trim();
    if (segment) {
      const parsed = parseExpression(segment);
      if (parsed.list && parsed.list.length > 0 || parsed.field) {
        treeData.list.push(parsed);
      }
    }
    lastIndex = op.index + op.rel.length;
  });

  const lastSegment = expr.substring(lastIndex).trim();
  if (lastSegment) {
    const parsed = parseExpression(lastSegment);
    if (parsed.list && parsed.list.length > 0 || parsed.field) {
      treeData.list.push(parsed);
    }
  }

  if (treeData.list.length === 0) {
    return { list: [] };
  }

  return treeData;
}

/**
 * 查找顶层的AND/OR运算符
 * @param {string} expr - 表达式字符串
 * @returns {Array} 运算符位置和类型
 */
/**
 * 查找顶层的AND/OR运算符（优化版）
 * @param {string} expr - 表达式字符串
 * @returns {Array} 运算符位置和类型
 */
function findTopLevelOperators(expr) {
  const operators = [];
  let parenCount = 0;
  let inQuotes = false;
  let quoteChar = '';
  const exprChars = expr.split('');
  const len = exprChars.length;

  exprChars.forEach((char, i) => {
    // 处理引号（考虑转义情况）
    if (char === '"' || char === "'") {
      if (!inQuotes) {
        inQuotes = true;
        quoteChar = char;
      } else if (char === quoteChar && (i === 0 || exprChars[i - 1] !== '\\')) {
        inQuotes = false;
      }
    }

    // 处理括号嵌套
    if (!inQuotes) {
      if (char === '(') parenCount++;
      else if (char === ')') parenCount--;
    }

    // 只检查顶层（不在括号内、不在引号内）的运算符
    if (parenCount === 0 && !inQuotes) {
      // 检查AND运算符（全大写或全小写）
      if (i + 2 < len &&
        exprChars[i].toUpperCase() === 'A' &&
        exprChars[i + 1].toUpperCase() === 'N' &&
        exprChars[i + 2].toUpperCase() === 'D') {
        // 验证AND前后是否为空白字符或边界（避免作为其他单词的一部分）
        const prevIsValid = i === 0 || /\s/.test(exprChars[i - 1]); // 前面是空白或表达式开头
        const nextIsValid = i + 3 >= len || /\s/.test(exprChars[i + 3]); // 后面是空白或表达式结尾
        if (prevIsValid && nextIsValid) {
          operators.push({ rel: 'AND', index: i });
        }
      }
      // 检查OR运算符（全大写或全小写）
      else if (i + 1 < len &&
        exprChars[i].toUpperCase() === 'O' &&
        exprChars[i + 1].toUpperCase() === 'R') {
        // 验证OR前后是否为空白字符或边界
        const prevIsValid = i === 0 || /\s/.test(exprChars[i - 1]);
        const nextIsValid = i + 2 >= len || /\s/.test(exprChars[i + 2]);
        if (prevIsValid && nextIsValid) {
          operators.push({ rel: 'OR', index: i });
        }
      }
    }
  });

  return operators;
}

/**
 * 分割条件表达式为令牌
 * @param {string} expr - 条件表达式
 * @returns {Array} 令牌数组
 */
function splitConditionTokens(expr) {
  const operators = ['==', '!=', '>', '<', 'like', 'in', 'start_with', 'end_with', 'contain', 'not_contain']
    .sort((a, b) => b.length - a.length);

  const result = expr.split('').reduce((acc, char, i) => {
    const { tokens, currentToken, inQuotes, quoteChar, skip } = acc;

    if (skip > 0) {
      return { ...acc, skip: skip - 1 };
    }

    // 处理引号
    if ((char === '"' || char === "'") && (i === 0 || expr[i-1] !== '\\')) {
      const newInQuotes = !inQuotes;
      const newQuoteChar = newInQuotes ? char : '';
      return {
        ...acc,
        currentToken: currentToken + char,
        inQuotes: newInQuotes,
        quoteChar: newQuoteChar
      };
    }

    if (inQuotes) {
      return { ...acc, currentToken: currentToken + char };
    }

    // 检查运算符
    let matchedOp = null;
    for (const op of operators) {
      if (expr.substring(i, i + op.length).toLowerCase() === op.toLowerCase()) {
        matchedOp = op;
        break;
      }
    }

    if (matchedOp) {
      const newTokens = currentToken.trim()
        ? [...tokens, currentToken.trim(), matchedOp]
        : [...tokens, matchedOp];
      return {
        ...acc,
        tokens: newTokens,
        currentToken: '',
        skip: matchedOp.length - 1
      };
    } else if (/\s/.test(char)) {
      if (currentToken.trim()) {
        return {
          ...acc,
          tokens: [...tokens, currentToken.trim()],
          currentToken: ''
        };
      }
      return acc;
    } else {
      return { ...acc, currentToken: currentToken + char };
    }
  }, {
    tokens: [],
    currentToken: '',
    inQuotes: false,
    quoteChar: '',
    skip: 0
  });

  if (result.currentToken.trim()) {
    result.tokens.push(result.currentToken.trim());
  }
  return result.tokens;
}

/**
 * 解析单个条件（叶子节点）
 * @param {string} expr - 条件表达式
 * @returns {Object} 条件对象
 */
function parseSingleCondition(expr) {
  expr = expr.trim();
  if (expr.startsWith('(') && expr.endsWith(')')) {
    return parseExpression(expr.slice(1, -1));
  }

  const tokens = splitConditionTokens(expr);
  if (tokens.length < 3) {
    return {
      field: '',
      operate: '',
      value: '',
      tip: [`Invalid condition: ${expr}`],
      list: []
    };
  }

  const [field, operate, ...valueParts] = tokens;
  let rawValue = valueParts.join(' ');
  rawValue = rawValue.trim().replace(/^["']|["']$/g, '');

  const fieldInfo = globalLanguage[E_EditType.KEYWORD].find(
    item => item.label.toLowerCase() === field.toLowerCase()
  );
  const fieldType = fieldInfo?.fieldType || 'string';

  let value = rawValue;
  let tag = [];
  if (operate.toLowerCase() === 'in') {
    const inValues = rawValue.match(/^\[\s*(.*?)\s*\]$/);
    const valueStr = inValues ? inValues[1] : rawValue;

    tag = valueStr
      .split(/\s*,\s*/)
      .map(v => v.trim().replace(/^["']|["']$/g, ''))
      .filter(v => v !== '');

    if (fieldType === 'number') {
      tag = tag.map(v => {
        const num = Number(v);
        return isNaN(num) ? v : num;
      });
    }

    // 生成带双引号的字符串，用于value字段
    value = tag.length > 0
      ? tag.map(item => `"${item}"`).join(', ')
      : '';
  }

  const tip = [];
  validateCondition(field, operate, value, tip, tag);

  return {
    field,
    operate,
    value: value.replace(/leftkuohao/g, '(')
      .replace(/rightkuohao/g, ')')
      .replace(/<<SPACE>>/g, ' '),
    tip,
    tag,
    list: []
  };
}

/**
 * 恢复特殊字符
 * @param {string} str - 字符串
 * @returns {string} 恢复后的字符串
 */
function restoreSpecialChars(str) {
  str = String(str);
  return str
    .replace(/leftkuohao/g, '(')
    .replace(/rightkuohao/g, ')')
    .replace(/<<SPACE>>/g, ' ')
    .replace(/\\"/g, '"');
}

/**
 * 验证条件的基本有效性
 * @param {string} field - 字段名
 * @param {string} operate - 运算符
 * @param {any} value - 值
 * @param {Array} tip - 错误信息容器
 * @param {Array} tag - IN运算符的数组值
 */
function validateCondition(field, operate, value, tip, tag) {
  const fieldInfo = globalLanguage[E_EditType.KEYWORD].find(
    item => item.label.toLowerCase() === field.toLowerCase()
  );

  if (!fieldInfo) {
    tip.push(`Field "${field}" does not exist`);
    return;
  }

  const isValidOp = globalLanguage[E_EditType.LOGICAL].some(
    item => item.labelType.includes(fieldInfo.fieldType) &&
      item.label.toLowerCase() === operate.toLowerCase()
  );

  if (!isValidOp) {
    tip.push(`Operator "${operate}" is not supported for ${field}`);
  }

  validateValueType(fieldInfo.fieldType, value, tip, operate, tag);
}

/**
 * 验证值的类型有效性
 * @param {string} type - 字段类型
 * @param {any} value - 值
 * @param {Array} tip - 错误信息容器
 * @param {string} operate - 运算符
 * @param {Array} tag - IN运算符的数组值
 */
function validateValueType(type, value, tip, operate, tag) {
  const valueStr = String(value);

  switch (type) {
    case 'number':
      // 对IN运算符单独校验tag数组
      if (operate?.toLowerCase() === 'in' && Array.isArray(tag)) {
        tag.forEach((item, index) => {
          const itemStr = String(item);
          if (isNaN(Number(itemStr))) {
            tip.push(`IN value at index ${index} ("${item}") is not a valid number`);
          }
        });
      }
      // 非IN运算符校验单个值
      else {
        if (isNaN(Number(valueStr))) {
          tip.push(`Value "${value}" is not a valid number`);
        }
      }
      break;
    case 'ipv4':
      const values = valueStr.split(',').map(v => v.trim().replace(/["']/g, ''));
      values.forEach(v => {
        if (!checkedIpv4(v)) {
          tip.push(`"${v}" is not a valid IPv4 address`);
        }
      });
      break;
  }
}

/**
 * 验证树形结构的有效性
 * @param {string} rel - 关系运算符
 * @param {Array} list - 条件列表
 * @returns {Array} 错误信息列表
 */
export function validateCode(rel, list) {
  const errors = [];

  if (!Array.isArray(list)) {
    errors.push("Invalid query structure: list is not an array");
    return errors;
  }

  list.forEach((item, index) => {
    if (item.rel) {
      const childErrors = validateCode(item.rel, item.list);
      childErrors.forEach(err => {
        errors.push(`${err}`);
      });
    } else if (item.field) {
      validateConditionDetails(item, errors, index);
    } else {
      errors.push(`Invalid condition structure`);
    }
  });

  return errors;
}

/**
 * 验证单个条件的详细有效性
 * @param {Object} condition - 条件对象
 * @param {Array} errors - 错误信息数组
 * @param {number} index - 条件索引
 */
function validateConditionDetails(condition, errors, index) {
  const { field, operate, value, tag } = condition;

  if (!field) {
    errors.push(`Field is required`);
    return;
  }

  if (!operate) {
    errors.push(`Operator is required`);
    return;
  }

  if (value === undefined || value === null) {
    errors.push(`Value is required`);
    return;
  }

  const fieldInfo = globalLanguage[E_EditType.KEYWORD].find(
    item => item.label.toLowerCase() === field.toLowerCase()
  );

  if (!fieldInfo) {
    errors.push(`Unknown field "${field}"`);
    return;
  }

  const validOperators = TREE_OPERATE_OPTION_MAP[fieldInfo.fieldType] || [];
  if (!validOperators.some(op => op.value.toLowerCase() === operate.toLowerCase())) {
    errors.push(`Operator "${operate}" is not supported for field type "${fieldInfo.fieldType}"`);
  }

  if (fieldInfo.fieldType === 'ipv4') {
    validateIpv4Condition(condition, errors, index);
  } else if (fieldInfo.fieldType === 'number') {
    validateNumberCondition(condition, errors, index);
  } else if (operate.toLowerCase() === 'in') {
    validateInCondition(condition, errors, index);
  }
}

/**
 * 验证IPv4条件
 * @param {Object} condition - 条件对象
 * @param {Array} errors - 错误信息数组
 * @param {number} index - 条件索引
 */
function validateIpv4Condition(condition, errors, index) {
  const { operate, value, tag } = condition;
  const values = Array.isArray(tag) && operate.toLowerCase() === 'in' ? tag : [value];
  values.forEach((val, i) => {
    const cleanVal = String(val).replace(/["']/g, '');
    if (!checkedIpv4(cleanVal)) {
      errors.push(`Invalid IPv4 address "${cleanVal}"`);
    }

    if (operate.toLowerCase() === 'in' && values.length === 1) {
      errors.push(`IN operator requires multiple values`);
    }
  });
}

/**
 * 验证数值条件
 * @param {Object} condition - 条件对象
 * @param {Array} errors - 错误信息数组
 * @param {number} index - 条件索引
 */
function validateNumberCondition(condition, errors, index) {
  const { value, tag, operate } = condition;
  const values = Array.isArray(tag) && operate.toLowerCase() === 'in' ? tag : [value];

  values.forEach(val => {
    const cleanVal = String(val).replace(/["']/g, '');
    if (isNaN(Number(cleanVal))) {
      errors.push(`Invalid number "${cleanVal}"`);
    }
  });
}

/**
 * 验证IN条件
 * @param {Object} condition - 条件对象
 * @param {Array} errors - 错误信息数组
 * @param {number} index - 条件索引
 */
function validateInCondition(condition, errors, index) {
  const { tag } = condition;

  if (!Array.isArray(tag) || tag.length === 0) {
    errors.push(`IN operator requires at least one value`);
  }
}

/**
 * 转换为SQL查询语句
 * @param {string} rel - 关系运算符
 * @param {Array} list - 条件列表
 * @param {Object} fieldType - 字段类型映射
 * @returns {string} 生成的SQL语句
 */
export function getSqlCode(rel, list, fieldType) {
  sessionStorage.setItem("searchCheckedIpv4", "");
  let sql = '';

  if (!Array.isArray(list)) {
    return sql;
  }

  list.forEach((item, index) => {
    if (item.rel) {
      const groupSql = getSqlCode(item.rel, item.list, fieldType);
      if (groupSql) {
        sql += index > 0 ? ` ${rel} ( ${groupSql} )` : `( ${groupSql} )`;
      }
    } else if (item.field) {
      const condition = generateSqlCondition(item, fieldType);
      if (condition) {
        sql += index > 0 ? ` ${rel} ${condition}` : condition;
      }
    }
  });

  return sql;
}

/**
 * 生成SQL条件表达式
 * @param {Object} item - 条件项
 * @param {Object} fieldType - 字段类型映射
 * @returns {string} SQL条件字符串
 */
function generateSqlCondition(item, fieldType) {
  const { field, operate, value, tag } = item;
  const type = fieldType[field] || '';
  const quote = type === 'number' ? '' : "'";

  if (operate.toLowerCase() === 'in') {
    const values = Array.isArray(tag) ? tag : [];
    const valueStr = values.map(v => {
      if (v === undefined || v === null) return '';
      return type === 'number' ? String(v) : `${quote}${String(v).replace(/'/g, "\\'")}${quote}`;
    }).filter(v => v !== '').join(', ');

    return valueStr ? `${field} IN (${valueStr})` : `${field} IN ()`;
  }

  if (type === 'ipv4') {
    validateIpv4Value(operate, value, field);
    return `${field} ${operate} toIPv4(${quote}${value}${quote})`;
  }

  if (SPECIAL_OPERATORS.includes(operate.toLowerCase())) {
    const { prefix, suffix } = SPECIAL_OPERATOR_MAP[operate];
    return `${field}${prefix}${value}${suffix}`;
  }

  const normalizedOp = operate === '==' ? '=' : operate;
  return `${field} ${normalizedOp} ${quote}${value}${quote}`;
}

/**
 * 验证IPv4值的有效性
 * @param {string} operate - 运算符
 * @param {any} value - 值
 * @param {string} field - 字段名
 */
function validateIpv4Value(operate, value, field) {
  if (['=', '!='].includes(operate.toLowerCase())) {
    const ip = removeQuotationMark(String(value));
    if (!checkedIpv4(ip)) {
      sessionStorage.setItem("searchCheckedIpv4", "error");
      message.error(`The ${field} value "${ip}" is not a valid IPv4 address!`);
    }
  }
}

/**
 * 移除字符串中的引号
 * @param {string} str - 可能包含引号的字符串
 * @returns {string} 移除引号后的字符串
 */
function removeQuotationMark(str) {
  if (str && str[0] === '"' && str[str.length - 1] === '"') {
    return str.substring(1, str.length - 1);
  }
  return str;
}

// 特殊运算符列表
const SPECIAL_OPERATORS = ['start_with', 'end_with', 'contain', 'not_contain', 'like'];
