// custom-completion.js
/* eslint-disable no-template-curly-in-string
  API 文档地址
  https://microsoft.github.io/monaco-editor/api/enums/monaco.languages.completionitemkind.html#snippet
 */
import * as monaco from 'monaco-editor';
export default [
  /**   * 内置函数   */
  {

    label: 'srcIp',
    kind: monaco.languages.CompletionItemKind.Field,
    insertText: 'srcIp ',
    insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
    detail: '返回指定参数的绝对值'
  },  {
    label: 'in(val:string)',
    kind: monaco.languages.CompletionItemKind.Text,
    insertText: 'in [${1:""}]',
    insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
    detail: '求指定角度的余弦值'
  }
]
