import {BasicColumn, FormSchema} from '/@/components/Table';
import {useI18n} from '/@/hooks/web/useI18n';
import {isAdministrator} from '/@/utils/auth';
import {createVNode} from 'vue';
import {calcMillisecond} from '/@/utils/ckTable';
import {
  RISK_STATUS_SELECT,
  SEVERITY_NUM_STR,
  TIME_SELECT,
  TRIAGE_STATUS_SELECT
} from '/@/utils/valueEnum';

const {t} = useI18n();


export const getColumns = (): BasicColumn[] => {
  const col: BasicColumn[] = [
    {
      title: t('routes.MlEvent.ruleName'),
      dataIndex: 'ruleName',
    },
    {
      title: t('routes.MlEvent.alarmTime'),
      dataIndex: 'alarmTime',
      minWidth: 180,
      width: 180,
    },
    {
      title: t('routes.MlEvent.urgency'),
      dataIndex: 'urgency',
      slots: {customRender: 'severity'},
    },

    {
      title: t('routes.MlEvent.owner'),
      dataIndex: 'owner_dictText',
      customRender: ({text, record}) => {
        const data: any = record;
        if (data?.owner === 'unassign') {
          return createVNode('div', {class: 'redClass'}, [t('common.Unassigned')]);
        }
        return text;
      },
    },
    {
      title: t('routes.MlEvent.triage'),
      dataIndex: 'triageStatus',
      slots: {customRender: 'triageStatus'},
    },
    {
      title: t('routes.MlEvent.riskStatus'),
      dataIndex: 'riskStatus',
      customRender: ({text}) => {
        if (text === 1) {
          return createVNode('div', {class: 'redClass'}, [t('common.Unclosed')]);
        } else if (text === 2) {
          return createVNode('div', {class: 'greenClass'}, [t('common.Closed')]);
        }
      },
    },
    {
      title: t('routes.MlEvent.assignmentTime'),
      dataIndex: 'assignmentConsume',
      defaultHidden: true,
      customRender(opt) {
        return calcMillisecond(opt.value, t);
      },
    },
    {
      title: t('routes.MlEvent.triageTime'),
      dataIndex: 'triageConsume',
      defaultHidden: true,
      customRender(opt) {
        return calcMillisecond(opt.value, t);
      },
    },
    {
      title: t('routes.MlEvent.closeTime'),
      dataIndex: 'closeConsume',
      defaultHidden: true,
      customRender(opt) {
        return calcMillisecond(opt.value, t);
      },
    },
    {
      title: t('routes.MlEvent.totalTime'),
      dataIndex: 'totalConsume',
      defaultHidden: true,
      customRender(opt) {
        return calcMillisecond(opt.value, t);
      },
    },
    {
      title: t('routes.RiskEventLogView.ticket'),
      dataIndex: 'ticketName',
      defaultHidden: false,
      slots: {customRender: 'ticket'},
    },
  ];
  if (isAdministrator()) {
    col.splice(0, 0, {
      title: t('common.tenant'),
      dataIndex: 'socTenantId_dictText',
    });
  }

  return col;
};


export const getCardColumns = (): BasicColumn[] => {
  const col: BasicColumn[] = [
    {
      title: '',
      dataIndex: 'riskStatus',
      width: 80,
    },
    {
      title: t('routes.MlEvent.ruleName'),
      dataIndex: 'ruleName',
      width: 560,
    },
    {
      title: t('routes.RiskEventLogView.ticket'),
      dataIndex: 'ticketName',
      width: 140,
    },
    {
      title: t('routes.MlEvent.urgency'),
      dataIndex: 'urgency',
      ellipsis: true,
      width: 120,
    },
    {
      title: t('routes.MlEvent.alarmTime'),
      dataIndex: 'alarmTime',
      width: 170,
      ellipsis: true,
    },
    {
      title: t('routes.MlEvent.triage'),
      dataIndex: 'triageStatus',
      ellipsis: true,
      width: 170,
    },
    {
      title: t('routes.MlEvent.owner'),
      dataIndex: 'owner_dictText',
      ellipsis: true
    },
  ];
  if (isAdministrator()) {
    col.splice(2, 0, {
      title: t('common.tenant'),
      width: 140,
      dataIndex: 'socTenantId_dictText',
      ellipsis: true,
    });
  }

  return col;
};

export const searchFormSchema = (ruleType?,socTenantId?): FormSchema[] => [

  {
    label: '',
    field: 'ruleName',
    component: 'JInput',
    componentProps: {
      search:true,
      placeholder: t('routes.MlEvent.ruleName'),
    },
  },
  {
    label: '',
    field: 'socTenantId',
    component: 'JSearchSelect',
    componentProps: {
      dict: 'tenantDict',
      placeholder: t('common.tenantName'),
    },
    ifShow: isAdministrator() && !socTenantId,
  },
  // {
  //   label: '',
  //   field: 'ruleType',
  //   component: 'Select',
  //   componentProps: {
  //     options: [
  //       {label: t('routes.MlEvent.Statistic'), value: 1},
  //       {label: t('routes.MlEvent.Order'), value: 2},
  //       {label: t('routes.MlEvent.Content'), value: 3},
  //     ],
  //     placeholder: t('routes.MlEvent.ruleType'),
  //   },
  //   ifShow: !ruleType,
  // },
  {
    label: '',
    field: 'hasTicket',
    component: 'JSearchSelect',
    componentProps: {
      options: [
        {
          label: t('routes.RiskEventLogView.noTicket'),
          text: t('routes.RiskEventLogView.noTicket'),
          value: 'noTicket'
        },
        {
          label: t('routes.RiskEventLogView.hasTicket'),
          text: t('routes.RiskEventLogView.hasTicket'),
          value: 'hasTicket'
        },
      ],
      placeholder: t('routes.RiskEventLogView.ticket'),
    },
  },
  {
    label: '',
    field: 'owner',
    component: 'JSearchSelect',
    componentProps: {
      dict: 'sysUserActiveDict',
      placeholder: t('routes.MlEvent.owner'),
      appendFirstData: [{text: t('common.Unassigned'), value: 'unassign'}],
    },
  },
  {
    label: '',
    field: 'triageStatus',
    component: 'Select',
    componentProps: {
      options: TRIAGE_STATUS_SELECT,
      placeholder: t('routes.MlEvent.triage'),
    },
  },
  {
    label: '',
    field: 'riskStatus',
    component: 'Select',
    componentProps: {
      options: RISK_STATUS_SELECT,
      placeholder: t('routes.MlEvent.riskStatus'),
    },
  },
  {
    label: '',
    field: 'lastTime',
    component: 'Select',
    componentProps: {
      options: TIME_SELECT,
      allowClear: false,
      placeholder: t('routes.MlEvent.time'),
    },
  },
];

export const searchReportFormSchema = (): FormSchema[] => [
  {
    label: t('routes.MlEvent.ruleName'),
    field: 'ruleName',
    component: 'JInput',

  },
  // {
  //   label: t('routes.MlEvent.ruleType'),
  //   field: 'ruleType',
  //   component: 'Select',
  //   componentProps: {
  //     options: [
  //       {label: 'Statistic', value: 1},
  //       {label: 'Order', value: 2},
  //       {label: 'Content', value: 3},
  //     ],
  //   },
  // },
  {
    label: t('routes.MlEvent.owner'),
    field: 'owner',
    component: 'JSearchSelect',
    componentProps: {
      dict: 'sysUserActiveDict',
    },
  },

  {
    label: t('routes.MlEvent.riskStatus'),
    field: 'riskStatus',
    component: 'Select',
    componentProps: {
      options: [
        {label: 'Unclosed', value: 1},
        {label: 'Closed', value: 2},
      ],
    },
  },

  {
    label: ' ',
    field: '-',
    component: 'Input',
    slot: 'search',
  },
];

const array = ['', 'mlStatistic', 'mlOrder', 'mlContent'];
export function handleData(data){
  return {
    eventId: data.id,
    severity: SEVERITY_NUM_STR[data.urgency],
    eventName: data.ruleName,
    owner: data.owner_dictText,
    triageStatus: data.triageStatus,
    closeStatus: data.riskStatus,
    type: array[data.ruleType],
    riskType: 3,
    updateTime: data.alarmTime,
    conclusion: data.conclusion,
    socTenantId: data.socTenantId,
  }
}

export function handleData2(data){
  return {
    eventId: data.eventId,
    severity: data.severity,
    eventName: data.eventName,
    owner: data.owner,
    triageStatus: data.triageStatus,
    closeStatus: data.closeStatus,
    type: data.type,
    riskType: data.riskType,
    updateTime: data.updateTime,
    conclusion: data.conclusion,
    socTenantId: data.socTenantId,
  }
} 
