import { useI18n } from '/@/hooks/web/useI18n';

const { t } = useI18n('routes.MlRuleVO');

export const contentRuleInfo = [
  { label: t('ruleName'), value: 'ruleName' },
  { label: t('severity'), value: 'urgency' },
  { label: t('executionFrequency'), value: 'timeVal' },
  { label: t('creator'), value: 'createBy' },
  { label: t('creationTime'), value: 'createTime' },
  { label: t('lastModificationTime'), value: 'updateTime' },
  // { label: t('logType'), value: 'dataset' },
  { label: t('periodOfFocus'), value: 'periodOfFocus' },
  { label: t('searchCondition'), value: 'searchStr' },
  { label: t('summaryFields'), value: 'summaryField' },
];

export const orderRuleInfo = [
  { label: t('ruleName'), value: 'ruleName' },
  { label: t('severity'), value: 'urgency' },
  { label: t('executionFrequency'), value: 'timeVal' },
  // { label: t('creator'), value: 'createBy' },
  // { label: t('creationTime'), value: 'createTime' },
  { label: t('summaryFields'), value: 'summaryField' },
];
