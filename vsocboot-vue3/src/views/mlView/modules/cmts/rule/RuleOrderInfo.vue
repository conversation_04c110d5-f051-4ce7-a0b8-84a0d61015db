<template>
  <div>
    <div class="r_div f-color-08" v-for="item in orderRuleInfo" :key="item.value">
      <div class="r_h_div">
        {{ item.label }}
      </div>
      <div class="r_c_div">
        <div v-if="item.value == 'ruleName'">
          {{ ruleInfo[item.value] }}
        </div>
        <div v-else-if="item.value == 'urgency'">
          <Severity2 :value="ruleInfo[item.value]" type="number" />
        </div>
        <div v-else-if="item.value == 'timeVal'" class="r_c_text">
          {{ ruleInfo[item.value] }}
          {{ ruleInfo.timeType == 1 ? t('common.min') : t('common.hour') }}
        </div>
        <div v-else-if="item.value == 'summaryField'" class="r_c_text" v-for="item2 in ruleInfo[item.value]" :key="item2">
          {{ item2 }}
        </div>
      </div>
    </div>
    <div>
      <FlowmlRuleView ref="flowmlRule" :nodeTree="nodeMap" :id="mlRuleId" :isShow="true" :nodeTreeMap="nodeDataMap" />
    </div>
  </div>
</template>

<script setup lang="ts">
import {onMounted, ref} from 'vue';
  import { orderRuleInfo } from '/@/views/mlView/modules/cmts/rule/RuleInfo';
  import Severity2 from '/@/components/Severity/Severity2.vue';
  import { useI18n } from '/@/hooks/web/useI18n';
  import { queryById } from '/@/views/mlRule/MlRuleVO.api';
  import FlowmlRuleView from '/@/views/mlView/modules/cmts/rule/FlowmlRuleView.vue';
  import { ThreatHuntingLogOptions } from '/@/utils/ckTable';

  const { t } = useI18n();
  const props = defineProps({
    record: Object as any,
    source: String
  });
  const ruleInfo = ref<any>({});
  const nodeMap = ref({});
  const nodeDataMap = ref({});
  const flowmlRule = ref();
  const TableSelectLogOptions = ref();

  const mlRuleId = ref('');
  let id = sessionStorage.getItem('MlRuleVOModal_id');
  if (id) {
    mlRuleId.value = id;
  }

  onMounted(() => {
    loadInfo();
  })

  function loadInfo() {
    let data: any = {};
    console.log('source',props.source)
    if(props.source == 'event'){
      console.log(props.record);
      const res = JSON.parse(props.record.ruleJson);
      console.log(res)
      data.summaryField = props.record.summaryField.split(',') || [];
      data.urgency = res.basic_info.urgency;
      data.ruleName = res.basic_info.rule_name;
      data.timeVal = res.basic_info.time_val;
      data.timeType = res.basic_info.time_type;
      const map:any = handleNodeData(res);
      nodeMap.value = map.nodeMap;
      nodeDataMap.value = map.nodeDataMap;
      flowmlRule.value.viewInit(nodeMap.value, nodeDataMap.value);
      TableSelectLogOptions.value = ThreatHuntingLogOptions(t);
      ruleInfo.value = data;
      return;
    }
    queryById({ id: props.record.ruleId }).then((res) => {
      data.urgency = res.urgency;
      data.ruleName = res.ruleName;
      data.timeVal = res.timeVal;
      data.timeType = res.timeType;
      data.createBy = res.createBy;
      data.createTime = res.createTime;
      data.summaryField = res.summaryField.split(',') || [];
      nodeMap.value = JSON.parse(res.nodeMap);
      nodeDataMap.value = JSON.parse(res.nodeDataMap);
      flowmlRule.value.viewInit(nodeMap.value, nodeDataMap.value);
      TableSelectLogOptions.value = ThreatHuntingLogOptions(t);
      ruleInfo.value = data;
      console.log('ruleInfo.value', ruleInfo.value);
    });
  }

  function handleNodeData(json) {
    const lines = json.lines;
    console.log(lines)
    const nodes = json.nodes;
    const map = {};
    nodes.forEach(node => {
      map[node.code] = node;
    })
    const nodeMap = {};
    const nodeDataMap = {
      "start": {
        "name": "Start",
        "code": "start",
        "type": "start"
      },
    };
    lines.forEach(line => {
      let a = nodeMap[line.from_node] ?? [];
      const d = map[line.to_node];
      console.log('ssssss', d, line)
      if (line.to_node != 'end') {
        d.line = line.condition;
        d.time_type = line.time_type;
        d.time_value = line.time_value;
        d.parentCode = line.from_node;
        d.handelStr = eval("(" + d.queryStr + ")");
        d.condition = JSON.stringify(d.handelStr);
        d.queryStr = d.searchStr
      }
      a.push(d);
      nodeMap[line.from_node] = a;
      nodeDataMap[d.code] = d;
    })
    console.log(nodeMap,nodeDataMap);
    return {nodeMap: nodeMap, nodeDataMap: nodeDataMap};
  }
</script>

<style scoped lang="less">
  .r_div:nth-child(odd) {
    background: rgba(255, 255, 255, 0.04);
  }

  .r_div {
    display: flex;
    gap: 8px;
    padding: 8px 16px;
    align-items: center;
    min-height: 46px;

    .r_h_div {
      min-width: 200px;
    }

    .r_c_div {
      display: flex;
      gap: 4px;
      flex-wrap: wrap;

      .r_c_text {
        padding: 4px 8px;
        background: rgba(255, 255, 255, 0.1);
        border-radius: 4px;
      }
    }
  }
</style>
