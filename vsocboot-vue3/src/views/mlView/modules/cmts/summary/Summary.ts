import {useI18n} from "/@/hooks/web/useI18n";

const {t} = useI18n('routes.riskEvent');

export const fixedFieldData = [
  {"label": t("eventName"), "value": "ruleName"},
  {"label": t("severity"), "value": "urgency"},
  {"label": t("firstAlertTime"), "value": "minAlarmTime"},
  {"label": t("lastAlertTime"), "value": "maxAlarmTime"},
  {"label": t("assignee"), "value": "owner_dictText"},
  {"label": t("triage"), "value": "triageStatus"},
  {"label": t("closeOrNot"), "value": "riskStatus"},
  {"label": t("investigation"), "value": "investigation"},
  {"label": t("ticket"), "value": "ticketName"},
];

export const allField = [
  {"label": t("eventName"), "value": "ruleName"},
  {"label": t("severity"), "value": "urgency"},
  {"label": t("firstAlertTime"), "value": "minAlarmTime"},
  {"label": t("lastAlertTime"), "value": "maxAlarmTime"},
  {"label": t("assignee"), "value": "owner_dictText"},
  {"label": t("triage"), "value": "triageStatus"},
  {"label": t("closeOrNot"), "value": "riskStatus"},
  {"label": t("investigation"), "value": "investigation"},
  {"label": t("ticket"), "value": "ticketName"},
  {"label": t("ownerTime"), "value": "ownerTime"},
  {"label": t("triageTime"), "value": "triageTime"},
  {"label": t("closeTime"), "value": "closeTime"},
  {"label": t("assignmentConsume"), "value": "assignmentConsume"},
  {"label": t("triageConsume"), "value": "triageConsume"},
  {"label": t("closeConsume"), "value": "closeConsume"},
  {"label": t("totalConsume"), "value": "totalConsume"},
  {"label": t("conclusion"), "value": "conclusion"},
]

