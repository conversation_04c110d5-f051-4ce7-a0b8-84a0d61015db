<template>
  <div ref="chartRef" :style="{ height, width }" v-show="ifShow"></div>
</template>
<script lang="ts">
  import { defineComponent, PropType, ref, Ref, reactive, watchEffect, defineEmits } from 'vue';
  import { useECharts } from '/@/hooks/web/useECharts';
  export default defineComponent({
    name: 'bar',
    props: {
      chartData: {
        type: Array,
        default: () => [],
      },
      option: {
        type: Object,
        default: () => ({}),
      },
      width: {
        type: String as PropType<string>,
        default: '100%',
      },
      height: {
        type: String as PropType<string>,
        default: '100%',
      },
      seriesColor: {
        type: String,
        default: '#1890ff',
      },
      selectKeys: {
        type: Array as PropType<string[]>,
        default: () => [],
      },
      selectValues: {
        type: Array as PropType<string[]>,
        default: () => [],
      },
      selectData: {
        type: Array as PropType<string[]>,
        default: () => [],
      },

      compareLine: {
        type: Array as any,
        default: () => [],
      },
    },
    emits: ['clickEchart'],
    setup(props, { expose, emit }) {
      const ifShow = ref(true);
      const chartRef = ref<HTMLDivElement | null>(null) as any;
      const { setOptions, getInstance } = useECharts(chartRef as Ref<HTMLDivElement>);
      let option = reactive({
        tooltip: {
          trigger: 'axis',
          formatter: (params) => {
            let map: any = {};
            for (let i in params) {
              let name = params[i].seriesName;
              if (!map[name]) {
                map[name] = 0;
              }
              map[name] += params[i].value * 1;
            }
            let html = '';
            for (let key in map) {
              if (html) {
                html += '<br>';
              }
              html += key + ' : ' + map[key];
            }
            return html;
          },
          axisPointer: {
            type: 'shadow',
            label: {
              show: true,
              backgroundColor: '#333',
            },
          },
        },
        legend: {
          data: [],
        },
        calculable: true,
        xAxis: [
          {
            type: 'category',
            data: [],
            axisLabel: {
              formatter: function (value) {
                // 使用\n手动折行文本
                return value.split(',').join('\n');
              },
            },
          },
        ],
        yAxis: [
          {
            type: 'value',
          },
        ],
        series: [
          {
            name: '',
            type: 'bar',
            data: [],
            label: {
              show: true, // 显示标签
              position: 'top', // 标签位置
            },
            markLine: {
              data: [
                {
                  yAxis: 0, // 设置虚线的固定值
                  lineStyle: {
                    type: 'dashed', // 设置虚线样式
                  },
                },
              ],
            },
          },
        ],
      }) as any;

      watchEffect(() => {
        props.selectKeys && props.selectValues && initCharts();
      });

      function initCharts() {
        console.log('props.selectKeys', props.selectKeys);
        console.log('props.selectValues', props.selectValues);
        console.log('option', option);

        // 确保 xAxis 数据填充
        let xAxisData = props.selectKeys;
        if (xAxisData && Array.isArray(xAxisData)) {
          option.xAxis[0].data = [];
          option.xAxis[0].data = xAxisData;
        }

        let seriesData: any = props.selectValues;
        option.series = [];
        // 初始化 series 配置并填充数据
        let seriesName = seriesData[0]?.map((e) => e.key) || [];
        console.log('seriesName', seriesName);
        option.legend.data = seriesName;
        if (xAxisData.length == 0 || xAxisData[0] == '') {
          option.xAxis[0].data = [];
          option.xAxis[0].data.push('');
        }
        seriesName.forEach((seriesName: any, index) => {
          option.series[index] = {
            name: seriesName,
            type: 'bar',
            data: [],
            label: {
              show: false, // 显示标签
              position: 'top', // 标签位置
            },
            markLine: {
              data: [
                {
                  yAxis: 0, // 设置虚线的固定值
                  lineStyle: {
                    type: 'dashed', // 设置虚线样式
                  },
                },
              ],
            },
          };
          // 为每个 series 填充 data
          console.log('seriesDataseriesData', seriesData);
          option.series[index].data = [];
          seriesData.forEach((itemArray) => {
            const value = itemArray.find((item) => item.key === seriesName)?.value || 0;
            console.log('value', value);
            option.series[index].data.push(value);
            console.log('option.series[index].data', option.series[index].data);

            if (props.compareLine.length > 0) {
              let arr = [] as any;
              arr = props.compareLine.filter((item) => seriesName.includes(item.field_name));
              if (seriesName === 'Count_of_logs') {
                const customNumItem = props.compareLine.filter((item) => item.field_name === 'custom_num');
                arr = customNumItem;
              }
              console.log('arr', arr);
              if (arr.length > 0) {
                option.series[index].markLine.data = [] as any;
                arr.forEach((element) => {
                  option.series[index].markLine.data.push({
                    yAxis: element.value,
                    lineStyle: {
                      type: 'dashed', // 设置虚线样式
                    },
                    element: element,
                  });
                });
              }
            }
          });
        });

        console.log('option', option);
        const allList: any = [];
        option.series.forEach((item: any) => {
          const markLineData = item.markLine.data;
          const dataList = handleMarkLineData(markLineData, item);
          allList.push(...dataList);
        });
        option.series.unshift(...allList);
        console.log('option2', option);
        setOptions(option);
        ifShow.value = true;
        getInstance()?.on('click', function (params: any) {
          console.log('params', params);
          if (params.name != '') {
            let obj = {
              rel: 'AND',
              list: [
                {
                  field: '',
                  operate: '==',
                  value: '',
                  tip: [],
                },
              ],
            };

            // 根据逗号分割
            let parts = params.name.split(',');
            let conditionString = '';
            // 遍历每个部分并分割冒号
            parts.forEach((part, index) => {
              let [field, value] = part.split(':'); // 分割冒号前后部分

              // 创建新的对象
              let newItem = {
                field: field,
                operate: '==',
                value: value,
                tip: [],
              };

              // 推入 obj.list
              obj.list.push(newItem);

              // 生成条件字符串
              conditionString += `${field} == '${value}'`;
              if (index < parts.length - 1) {
                conditionString += ' AND ';
              }
            });
            // 移除初始的空对象（如果不需要保留）
            obj.list.shift();
            emit('clickEchart', JSON.stringify(obj), conditionString);
          }
        });
      }

      function handleMarkLineData(lineData, data) {
        console.log('lineData', lineData);
        console.log('data', data);
        const list: any = [];
        //一条线
        if (lineData.length == 1) {
          const lowerValues: any = [];
          const upperValues: any = [];
          let colorFlag = 0;
          for (let i = 0; i < data.data.length; i++) {
            const { lowerValue, upperValue, flag } = handlecompareData(data.data[i], lineData[0].element);
            console.log(lowerValue, upperValue);
            lowerValues.push(lowerValue);
            upperValues.push(upperValue);
            colorFlag = flag;
          }
          data.data = upperValues;
          data.stack = data.name;
          data.itemStyle = colorFlag == 1 ? { color: 'rgba(213, 102, 102, 1)' } : '';
          const d = {
            data: lowerValues,
            label: data.label,
            name: data.name,
            type: data.type,
            stack: data.name,
            itemStyle: colorFlag == 2 ? { color: 'rgba(213, 102, 102, 1)' } : '',
          };
          list.push(d);
        } else if (lineData.length > 1) {
          //多条线
          const lowerValues: any = [];
          const upperValues: any = [];
          let colorFlagLow = 0;
          let colorFlagUp = 0;
          for (let i = 0; i < data.data.length; i++) {
            const { lowerValue, upperValue, flagList } = handlecompareDataByMore(data.data[i], lineData);
            console.log(lowerValue, upperValue);
            lowerValues.push(lowerValue);
            upperValues.push(upperValue);
            colorFlagUp = flagList.find((item) => item.label == upperValue)?.value;
            colorFlagLow = flagList.find((item) => item.label == lowerValue)?.value;
            console.log('colorFlagUp', colorFlagUp);
            console.log('colorFlagLow', colorFlagLow);
          }
          data.data = upperValues;
          data.stack = data.name;
          data.itemStyle = colorFlagUp == 2 ? { color: 'rgba(213, 102, 102, 1)' } : '';
          const d = {
            data: lowerValues,
            label: data.label,
            name: data.name,
            type: data.type,
            stack: data.name,
            itemStyle: colorFlagLow == 1 ? '' : { color: 'rgba(213, 102, 102, 1)' },
          };
          list.push(d);
        }
        return list;
      }

      function handlecompareData(value, element) {
        let lowerValue = 0;
        let upperValue = 0;
        let flag = 1;
        console.log('element.compare', element.compare);

        switch (element.compare) {
          case '>':
            // 如果 dataItem 大于 element.value，分割柱子为两部分
            if (value > element.value) {
              lowerValue = element.value; // 小于阈值的部分
              upperValue = value - element.value; // 大于阈值的部分
            } else {
              lowerValue = value; // 整个柱子小于阈值
              upperValue = 0;
            }
            break;
          case '<':
            // 如果 dataItem 小于 element.value，分割柱子为两部分
            if (value < element.value) {
              lowerValue = value; // 小于阈值的部分
              upperValue = 0;
            } else {
              lowerValue = element.value; // 小于阈值的部分
              upperValue = value - element.value; // 大于阈值的部分
            }
            flag = 2;
            break;
          case '>=':
            // 类似于 '> '，但是包括等于的部分
            if (value >= element.value) {
              lowerValue = element.value;
              upperValue = value - element.value;
            } else {
              lowerValue = value;
              upperValue = 0;
            }
            break;
          case '<=':
            // 类似于 '< '，但是包括等于的部分
            if (value <= element.value) {
              lowerValue = value;
              upperValue = 0;
            } else {
              lowerValue = element.value;
              upperValue = value - element.value;
            }
            flag = 2;
            break;
          case '==':
            // 如果 dataItem 等于 element.value，整个柱子颜色为红色
            if (value === element.value) {
              lowerValue = value;
              upperValue = 0;
            }
            flag = 2;
            break;
          case '<>':
            // 如果 dataItem 不等于 element.value，整个柱子颜色为默认颜色
            if (value !== element.value) {
              lowerValue = value;
              upperValue = 0;
            }
            flag = 2;
            break;
          default:
            lowerValue = element.value;
            upperValue = 0;
            flag = 0;
            break;
        }
        return { lowerValue, upperValue, flag };
      }

      function handlecompareDataByMore(value, lineData) {
        let lowerValue = 0;
        let upperValue = 0;
        let flagList: any = [];
        console.log('value', value);

        console.log('lineData', lineData);
        // 初始化区间上下限
        let lowerArr: any = [];
        let upperArr: any = [];
        // 遍历 lineData，更新区间上下限
        lineData.forEach((e) => {
          switch (e.element.compare) {
            case '>':
              // 如果 dataItem 大于 element.value，分割柱子为两部分
              if (value > e.element.value) {
                lowerValue = e.element.value; // 小于阈值的部分
                upperValue = value - e.element.value; // 大于阈值的部分
              }
              break;
            case '>=':
              // 类似于 '> '，但是包括等于的部分
              if (value >= e.element.value) {
                lowerValue = e.element.value;
                upperValue = value - e.element.value;
              }
              break;
            default:
              lowerValue = 0;
              upperValue = 0;
              break;
          }
          lowerArr.push(lowerValue);
          upperArr.push(upperValue);
        });
        console.log('lowerValue', lowerValue);
        console.log('upperValue', upperValue);
        console.log('lowerArr', lowerArr);
        console.log('upperArr', upperArr);

        lowerValue = lowerArr.find((item) => item != 0);
        upperValue = upperArr.find((item) => item != 0);
        flagList.push({
          label: upperValue,
          value: 2,
        });
        flagList.push({
          label: lowerValue,
          value: 1,
        });
        return { lowerValue, upperValue, flagList };
      }

      function delInitCharts() {
        ifShow.value = false;
      }

      expose({
        initCharts,
        delInitCharts,
        ifShow,
      });
      return { chartRef, ifShow };
    },
  });
</script>
