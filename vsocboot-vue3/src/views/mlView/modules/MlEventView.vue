<template>
  <BasicDrawer v-bind="$attrs" @register="register" :show-footer="false" @close="close" :destroyOnClose="true" :title="title" width="1200px">
    <MlEventInfo v-if="ruleType" :ruleType="ruleType" :reload="reload" />
  </BasicDrawer>
</template>
<script setup lang="ts">
  import { useDrawerInner } from '/@/components/Drawer';
  import { ref } from 'vue';
  import BasicDrawer from '/@/components/Drawer/src/BasicDrawer.vue';
  import MlEventInfo from '/@/views/mlView/modules/MlEventInfo.vue';

  defineProps({
    reload: Function,
  });
  const ruleType = ref(undefined);
  const title = ref('');
  const [register, { setDrawerProps, closeDrawer }] = useDrawerInner(async (data) => {
    console.log(data);
    ruleType.value = data.ruleType;
    title.value = data.record.ruleName;
    setDrawerProps({
      confirmLoading: false,
      showFooter: false,
    });
  });

  function close() {
    ruleType.value = undefined;
    closeDrawer();
  }
</script>

<style lang="less"></style>
