<template>
  <div style="padding: 10px">
    <div style="position: relative;">
      <span class="goBack" @click="goBack">
        <Icon icon="ant-design:left-outlined" style="margin-right: 5px;cursor: pointer;"/>
        {{ dataInfo?.ruleName }}
      </span>
      <div style="position: absolute;right: 10px;z-index: 9;">

        <a-dropdown :trigger="['click']" v-if="hasPermission('ticket:useinternel-2') ||
                          hasPermission('ticket:useSS-2') ||
                          hasPermission('ticket:useinternel-1') ||
                          hasPermission('ticket:useIssued-1')">
          <a-button style="margin-right: 10px;">
            {{ t('common.ticketBtn') }}
          </a-button>
          <template #overlay>
            <a-menu>
              <ApplyMenu v-model:workflowList="workflowList" :eventType="2" :type="2"
                         :record="dataInfo"/>
            </a-menu>
          </template>
        </a-dropdown>

        <a-button style="margin-right: 10px;" @click="showAddWhite">
          {{ t('routes.WhitelistVO.addWhitelist') }}
        </a-button>
        <a-dropdown :trigger="['click']" v-if="hasPermission('investigation:add') ||
                            hasPermission('investigation:join')">
          <a-button style="margin-right: 10px;" @click.prevent="loadInvestigation">
            {{ t('common.investigation') }}
          </a-button>
          <template #overlay>
            <a-menu>
              <a-menu-item key="0" @click="showAddInvestigation(mlEventId)"
                           v-if="hasPermission('investigation:add')">
                <Icon icon="ant-design:plus-outlined"/>
                {{ t('routes.riskLogs.add') }}
              </a-menu-item>
              <a-menu-divider/>
              <a-menu-item v-for="item in investigationData" :key="item.id"
                           @click="addInvestigation(item,mlEventId)">
                <span>{{ item['investigation'] }}</span>
              </a-menu-item>
              <a-menu-divider/>
              <a-menu-item key="1" @click="showMoreInvestigation(mlEventId)">
                <span>{{ t('routes.riskLogs.addMore') }}</span>
              </a-menu-item>
            </a-menu>
          </template>
        </a-dropdown>
        <a-button style="margin-right: 0px;" @click="CloseMl(dataInfo)"
                  v-if="hasPermission('risk:close')
                              && dataInfo?.triageStatus !== 0
                              && dataInfo?.owner == userStore.getUserInfo.id
                              && riskStatus !== 2">
          {{ t('common.closeText') }}
        </a-button>
        <a-button style="margin-right: 0px;" @click="CloseMl(dataInfo)"
                  v-if="hasPermission('risk:close')
                              && dataInfo?.triageStatus !== 0
                              && dataInfo?.owner == userStore.getUserInfo.id
                              && riskStatus == 2">
          {{ t('common.openBtn') }}
        </a-button>

      </div>
    </div>
    <div>
      <a-tabs>
        <a-tab-pane key="1" :tab="tp('Summary')">
          <div>
            <a-tabs>
              <a-tab-pane v-for="(item,index) in ruleJson?.trigger" :key="index" :forceRender="true"
                          :tab="item.condition_name">
                <div>
                  {{ tp('Triggers') }}
                </div>
                <div class="border block_div">
                  <div v-for="(item2,index2) in item.trigger_statistic_list"
                       :key="'trigger_filter2'+index2">
                    <div v-if="index2 > 0">
                      AND
                    </div>
                    <div>
                      <span class="color5">{{ statisticMap[item2.statistic_key].title }}</span>
                      <span v-if="item2.field_type == 'dataset'">
                        ({{ t('routes.MlRuleVO.totalLogNumberOf') }} {{
                          datasetMap[item2.field_type_value].title
                        }})
                      </span>
                      <span v-else>
                        (
                        {{ tp('Step') }}#{{ getStep(item2) }} ,
                        <span v-if="item2.field_name == 'custom_num'">{{ tp('countLog') }}</span>
                        <!--                        <span v-else>Count of {{ getHump(item2.field_name) }}</span>-->
                        <span v-else>
                          <FieldText :text="item2.field_name"
                                     type="count"
                                     :table="datasetMap[statisticMap[item2.statistic_key].dataset_key].table_name_type"/>
                        </span>
                        )
                      </span>
                      {
                      <div style="display: flex;flex-flow: wrap;margin-left: 50px;">
                        <div v-for="(item3,index3) in item2.statistic_trigger"
                             :key="'trigger_filter_list'+index3">
                          <span v-if="index3 > 0" style="margin: 0 10px;">OR</span>
                          <span class="color3">{{ compareMap[item3.compare] }}</span>
                          <span v-if="item3.type == 'value'" class="color4"
                                style="margin-left: 5px;">
                            {{ item3.value }}
                          </span>
                          <span v-if="item3.type == 'statistic'" style="margin-left: 5px;">
                            <span class="color5">{{
                                statisticMap[item3.statistic_key].title
                              }}</span>
                            <span v-if="item3.field_type == 'dataset'">
                              ( {{ t('routes.MlRuleVO.totalLogNumberOf') }}
                              {{ datasetMap[item3.field_type_value].title }} )
                              <span
                                v-if="item3.field_name == 'custom_num'">{{ tp('countLog') }}</span>
                              <span v-else>
                                <FieldText :text="item3.field_name"
                                           type="count"
                                           :table="datasetMap[item2.field_type_value].table_name_type"/>
                              </span>

                            </span>
                            <span v-else>
                              ( {{ tp('Step') }}#{{ getStep(item3) }} ,
                              <span
                                v-if="item3.field_name == 'custom_num'">{{ tp('countLog') }}</span>
                              <span v-else>
                                <FieldText :text="item3.field_name"
                                           type="count"
                                           :table="datasetMap[statisticMap[item3.statistic_key].dataset_key].table_name_type"/>
                              </span>

                            </span>
                            )
                          </span>
                        </div>
                      </div>
                      }
                    </div>
                  </div>
                </div>
                <div v-if="showChart">
                  <div v-for="(item2,index2) in item.trigger_statistic_list"
                       :key="'chart'+index2" class="border block_div">
                    <div>
                      {{ getChartTitle(item2) }}
                    </div>
                    <BarMulti :chartData="getChartData(item2)" :markLine="getMarkLine(item2)"
                              height="200px" width="calc(100vw - 50px)"/>
                  </div>
                </div>
              </a-tab-pane>
            </a-tabs>
            <div>
              <div style="text-align: right;margin: 10px 0;position: relative;">
                <div style="display: flex;width: 300px;position: absolute;left: 0;">
                  <a-input v-model:value="showFieldSearch"/>
                  <a-button @click="searchField" type="primary" style="margin-left: 10px;">
                    {{ t('common.searchText') }}
                  </a-button>
                </div>
                <a-button type="primary" @click="showChart = !showChart">
                  <span v-if="showChart">{{ tp('HideChart') }}</span>
                  <span v-else>{{ tp('DisplayChart') }}</span>
                </a-button>
                <a-button type="primary" style="margin-left: 5px;" @click="showSetting">
                  {{ tp('Setting') }}
                </a-button>
              </div>
              <div>
                <a-spin :spinning="showFieldLoading">
                  <div v-for="(item,index) in showFieldList" :key="'f_'+index" class="border_div"
                       style="display: flex;padding: 5px;margin-bottom: 10px;">
                    <div class="show_field">
                      {{ item['field'] }}
                    </div>
                    <div style="display: flex;flex-flow: row wrap;padding-left: 10px;width: 100%;">
                      <template v-for="(item2,index2) in showFieldData[item.field]"
                                :key="item.field+'_'+index2">
                        <div class="show_span" :class="{'showFieldSelect':item2.select}"
                             v-if="!!item2.value">
                          {{ item2.value }}
                        </div>
                      </template>
                    </div>
                    <div style="display: flex;align-items: center;">
                      <img src="./image/u45.svg" style="width: 25px;height: 25px;cursor: pointer;"
                           v-if="item.type"
                           @click="addShowField(item)"/>
                      <img src="./image/u40.svg" style="width: 25px;height: 25px;cursor: pointer;"
                           v-else @click="delShowField(item)"/>
                    </div>
                  </div>
                </a-spin>
              </div>
            </div>
          </div>
        </a-tab-pane>
        <a-tab-pane key="2" :tab="t('routes.MlEvent.Statistic')">
          <div v-for="(item) in tab2Data" :key="item.stepKey">
            <span class="font16">{{ item.statisticTitle }}</span>
            <template v-if="item?.listData?.list">
              <div style="margin-bottom: 10px;">
                {{ tp('Step') }}#{{ item.num }} <span v-if="item.triggerName">({{ item.triggerName }})</span>
                <a-radio-group v-model:value="statisticShowType[item.stepKey]"
                               name="radioGroup"
                               @change="changeCountOfLogs(item,statisticShowType[item.stepKey],statisticShowCount[item.stepKey],item.table_name_type)"
                               style="margin-left: 50px;">
                  <a-radio value="1">{{ tp('list') }}</a-radio>
                  <a-radio value="2">{{ tp('bar') }}</a-radio>
                  <a-radio value="3">{{ tp('line') }}</a-radio>
                  <a-radio value="4">{{ tp('pie') }}</a-radio>
                </a-radio-group>
                <span v-if="item.num == 1">
                  {{ tp('countLog') }}
                  <a-switch size="small" v-model:checked="statisticShowCount[item.stepKey]"
                            @change="changeCountOfLogs(item,statisticShowType[item.stepKey],statisticShowCount[item.stepKey],item.table_name_type)"/>
                </span>
                <span style="margin-left: 10px;"
                      v-if="['2','3','4'].indexOf(statisticShowType[item.stepKey]) > -1">
                  Top
                  <a-input-number v-model:value="statisticTop[item.stepKey]" :min="0" :max="99"/>
                  <a-button type="primary" size="small" style="margin-left: 5px;"
                            @click="searchData">
                    {{ t('common.Search') }}
                  </a-button>
                </span>
              </div>
              <div v-if="statisticShowType[item.stepKey] == '1'" class="border block_div">
                <a-table v-if="item?.listData?.list" :data-source="item.listData.list"
                         :columns="item.listData.columns"/>
              </div>
              <div v-if="statisticShowType[item.stepKey] == '2'" class="border block_div">
                <BarMulti :chartData="item.barData" :markLine="getMarkLine2(item)"
                          height="200px"/>
              </div>
              <div v-if="statisticShowType[item.stepKey] == '3'" class="border block_div">
                <LineMulti :chartData="item.barData" :markLine="getMarkLine2(item)"
                           height="200px"/>
              </div>
              <div v-if="statisticShowType[item.stepKey] == '4'">
                <a-row>
                  <template v-for="(data,key) in item.pieData" :key="'pi_'+key">
                    <a-col :span="24" v-if="calcPieNum(item.pieData) == 1">
                      <div class="border block_div">
                        {{ key }}
                        <Pie :chartData="data" height="200px"/>
                      </div>
                    </a-col>
                    <a-col :span="12" v-else-if="calcPieNum(item.pieData) == 2">
                      <div class="border block_div">
                        {{ key }}
                        <Pie :chartData="data" height="200px"/>
                      </div>
                    </a-col>
                    <a-col :span="8" v-else>
                      <div class="border block_div">
                        {{ key }}
                        <Pie :chartData="data" height="200px"/>
                      </div>
                    </a-col>
                  </template>
                </a-row>
              </div>
            </template>
          </div>


        </a-tab-pane>
        <a-tab-pane key="3" :tab="t('routes.MlEvent.Dataset')">
          <a-tabs>
            <a-tab-pane v-for="(item,index) in datasetTable" :key="'d_'+index"
                        :tab="LOG_TABLE[item]">
              <LogViewList ref="logListRef" :log-type="item"
                           :start="ruleJson.startTime"
                           :end="ruleJson.endTime"
                           :mlEventId="mlEventId"
                           style="margin-top: 10px;"/>
            </a-tab-pane>
          </a-tabs>
        </a-tab-pane>
        <a-tab-pane key="4" :tab="t('routes.MlEvent.Rule')">
          <MlRuleViewModal page-source="mlView" :dataJson="ruleJson" :mlId="mlId"/>
        </a-tab-pane>
      </a-tabs>
    </div>
  </div>

  <WhitelistVOModal @register="registerModal" type="2"/>

  <InvestigationListModal ref="registerRiskLogsModal" :eventId="LogId" type="3"
                          :socTenantId="dataInfo.socTenantId"/>
  <a-modal v-model:visible="inveVisible" :title="t('common.confirm')" @ok="saveToInve"
           @cancel="conclusion = ''"
           :maskClosable=false :destroyOnClose="true">
    <a-row style="padding: 0 24px;">
      <a-col :span="24">
        {{ t('routes.riskLogs.addInvestigationPrompt') }}
      </a-col>
      <a-col :span="24">
        <a-form class="antd-modal-form"
                autocomplete="off" :layout="formLayout">
          <a-form-item :label="tp('conclusion')">
            <a-textarea v-model:value="conclusion"/>
          </a-form-item>
        </a-form>
      </a-col>
    </a-row>
  </a-modal>
  <MlEventSettingField ref="settingFieldRef" @refShowField="refShowField"/>
</template>

<script setup lang="ts">
import {useRouter} from "vue-router";
import {closed, invMlSave, queryById} from "/@/views/mlView/MlEvent.api";
import {onMounted, ref} from "vue";
import {getCountFieldText, getFieldName, getTabField} from "/@/utils/ckTable";
import BarMulti from '/@/components/chart/BarMulti.vue'
import LineMulti from '/@/components/chart/LineMulti.vue'
import Pie from '/@/components/chart/Pie.vue'
import MlRuleViewModal from '/@/views/mlRule/modules/MlRuleViewModal.vue'
import LogViewList from "/@/views/mlView/modules/LogViewList.vue";
import {useModal} from "/@/components/Modal";
import WhitelistVOModal from "/@/views/whitelist/modules/WhitelistVOModal.vue";
import {loadInvestigationTop5} from "/@/views/investigation/InvestigationVO.api";
import {useUserStore} from "/@/store/modules/user";
import {Modal} from "ant-design-vue";
import {formLayout} from "/@/settings/designSetting";
import InvestigationListModal from "/@/views/investigation/modules/InvestigationListModal.vue";
import {useI18n} from "/@/hooks/web/useI18n";
import {usePermission} from "/@/hooks/web/usePermission";
import ApplyMenu from "/@/views/workflow/view/ApplyMenu.vue";
import {queryEntryTicket} from "/@/views/workflow/view/ts/TicketUtils";

import {
  loadFieldData,
  queryList,
  saveOrUpdate as saveShowField
} from "/@/views/mlView/modules/MlEventShowField.api";
import MlEventSettingField from '/@/views/mlView/modules/MlEventSettingField.vue'
import FieldText from "/@/views/mlRule/modules/FieldText.vue";
import dayjs from "dayjs";
import {LOG_TABLE} from "/@/utils/valueEnum";

const {hasPermission} = usePermission();
const router = useRouter()
const {t} = useI18n();
const tp = (name) => {
  return t('routes.MlEvent.' + name);
}

const dataInfo = ref<any>({})
const ruleJson = ref<any>({})
const datasetMap = ref<any>({})
const statisticMap = ref<any>({})
const stepDataMap = ref<any>({})
const stepDataMap2 = ref<any>({})
const stepDataMap3 = ref<any>({})
const datasetDataMap = ref<any>({})
const mlId = ref<any>("")
const mlEventId = ref<any>("")
const statisticShowType = ref<any>({})
const statisticShowCount = ref<any>({})
const statisticTop = ref<any>({})
const riskStatus = ref(1)
const settingFieldRef = ref()
const datasetTable = ref<any>({})

const props = defineProps({
  isModal: {
    type: Boolean,
    default: false
  },
  closeDrawer: Function
});
//工单列表
const workflowList: any = ref([]);
onMounted(() => {
  loadInfo()
  getTickets();
})

/**
 * 获取entry ticket
 */
async function getTickets() {
  workflowList.value = await queryEntryTicket(2);
}

//是否显示图表
const showChart = ref(true)
//tab statistic页面的数据
const tab2Data = ref<any>([]);

function loadInfo() {
  let id = sessionStorage.getItem("MlEventModalId")
  mlEventId.value = id
  queryById({id: id}).then((data) => {
    dataInfo.value = data
    riskStatus.value = data.riskStatus
    mlId.value = data.ruleId
    if (data.datasetList && data.datasetList.length > 0) {
      datasetDataMap.value = JSON.parse(data.datasetList[0].dataJson)
    }
    tab2Data.value = [];
    if (data.stepList && data.stepList.length > 0) {
      for (let i in data.stepList) {
        if (data.stepList[i].data) {
          stepDataMap.value[data.stepList[i].stepKey] = data.stepList[i];
          tab2Data.value.push(data.stepList[i])
        }
        stepDataMap2.value[data.stepList[i].stepKey] = data.stepList[i].data
        // stepDataMap3.value[data.stepList[i].stepKey] = data.stepList[i].data
      }
    }
    console.log('stepDataMap', stepDataMap)
    console.log('tab2Data', tab2Data.value)
    let json = JSON.parse(data.ruleJson);
    let startTime = json.begin
    let endTime = json.end
    if (!json.dbConfigJson) {
      json.dbConfigJson = json.dbConfig
    } else {
      json.dbConfigJson = JSON.parse(json.dbConfigJson)
    }
    let dataset_json = json.dbConfigJson.dataset
    let statistic_json = json.dbConfigJson.statistic
    let datasetJson: any = {}
    let statisticJson: any = {}
    for (let i in dataset_json) {
      datasetJson[dataset_json[i].key] = dataset_json[i]
    }
    for (let i in statistic_json) {
      statisticJson[statistic_json[i].key] = statistic_json[i]
    }


    datasetMap.value = datasetJson
    statisticMap.value = statisticJson
    ruleJson.value = json.dbConfigJson


    startTime =
      startTime.replace("T", " ").replace("[Asia/Shanghai]", "").replace("[Africa/Cairo]", "")
    endTime = endTime.replace("T", " ").replace("[Asia/Shanghai]", "").replace("[Africa/Cairo]", "")

    startTime = dayjs(startTime).format('YYYY-MM-DD HH:mm:ss');
    endTime = dayjs(endTime).format('YYYY-MM-DD HH:mm:ss');

    ruleJson.value.startTime = startTime
    ruleJson.value.endTime = endTime

    for (let i in ruleJson.value.dataset) {
      let tableType = ruleJson.value.dataset[i].table_name_type;
      datasetTable.value[tableType] = tableType;
    }
    loadShowField()

    //处理tab2里的数据，另起进程处理，防止卡顿页面

    setTimeout(() => {

      for (let key in stepDataMap.value) {
        let data = stepDataMap.value[key];
        statisticTop.value[key] = 20
        statisticShowType.value[key] = "1"
        statisticShowCount.value[key] = false
        data.statisticTitle = statisticMap.value[data.statisticKey].title
        getTableData(data, datasetJson[statisticMap.value[data.statisticKey].dataset_key].table_name_type)
        getChartData2(data, datasetJson[statisticMap.value[data.statisticKey].dataset_key].table_name_type)
        getPieData(data, datasetJson[statisticMap.value[data.statisticKey].dataset_key].table_name_type)
      }
    }, 50)
  })
}


function getStep(item2) {
  if (item2.field_type_value) {
    return item2.field_type_value?.replace(item2.statistic_key + '_step', '')
  }
  return ""
}

function getMarkLine(data) {
  let list = data.statistic_trigger
  let value: any = []
  for (let i in list) {
    value.push({
      value: list[i].value,
      compare: list[i].compare,
    })
  }
  return value

}

function getChartTitle(data) {
  let text = ""
  if (data.field_type == 'dataset') {
    text += t('routes.MlRuleVO.totalLogNumberOf') + " " +
      datasetMap.value[data.field_type_value].title
  } else {
    text += tp('Step') + "#" + getStep(data)
    if (data.field_name == 'custom_num') {
      text += tp('countLog')
    } else {
      const table = datasetMap.value[statisticMap.value[data.statistic_key].dataset_key].table_name_type
      const fieldMap = getTabField(table);
      // text += " Count of " + getHump(data.field_name)
      text += getCountFieldText(fieldMap, data.field_name)
    }
  }
  return statisticMap.value[data.statistic_key].title + " " + text
}

function changeCountOfLogs(data, showType, val, table) {
  if (showType == "1") {
    getTableData(data, table, val)
  } else if (showType == "2" || showType == "3") {
    getChartData2(data, table, val)
  } else if (showType == "4") {
    getPieData(data, table, val)
  }
}

function getTableData(data, table, showCount?) {
  if (data.listData && showCount === undefined) {
    return data.listData
  }
  data.table_name_type = table;

  console.log(data)
  const fieldMap = getTabField(table);
  let th: any = []


  let stepConfig = statisticMap.value[data.statisticKey].step;
  let group_fields: any = [];
  let statistic_fields: any = [];
  for (let i in stepConfig) {
    if (stepConfig[i].key == data.stepKey) {
      group_fields = stepConfig[i].group_fields;
      statistic_fields = stepConfig[i].statistic_fields;
      data.num = stepConfig[i].num;
    }
  }
  console.log(group_fields, statistic_fields)

  for (let i in group_fields) {
    th.push({
      title: getFieldName(fieldMap, group_fields[i].field_name),
      dataIndex: group_fields[i].field_name,
    })
  }
  //第一步，肯定有count of logs
  for (let i in statistic_fields) {
    let title = getCountFieldText(fieldMap, statistic_fields[i]);
    th.push({
      title: title,
      dataIndex: 'agg_' + statistic_fields[i]
    })
  }
  if (data.num == 1 && showCount) {
    th.push({
      title: tp('countLog'),
      dataIndex: 'agg_log_id'
    })
  }

  let json = stepDataMap.value[data.stepKey].data
  if (json[0].triggerName) {
    data.triggerName = json[0].triggerName.join(',')
  } else {
    data.triggerName = "";
  }
  let result = {
    columns: th,
    list: json
  }
  data.listData = result
  return result
}


function calcPieNum(data) {
  let index = 0;
  for (let key in data) {
    index++
  }
  return index
}

function getPieData(data, table, showCount?) {
  if (data.pieData && showCount == undefined) {
    return data.pieData
  }
  const fieldMap = getTabField(table);

  let json = stepDataMap.value[data.stepKey].data
  let value = {}
  for (let i = 0; i < json.length; i++) {
    let valueMap: any = {}
    let name = ""
    let data = json[i]
    for (let key in data) {
      if ("triggerName" == key) {
        continue;
      }
      if (key.startsWith("agg_")) {
        valueMap[key.replace("agg_", "")] = data[key]
      } else {
        name += getFieldName(fieldMap, key) + "=" + data[key] + "@"
      }
    }
    if (name.length > 0) {
      name = name.substring(0, name.length - 1)
    }
    value[name] = valueMap
  }
  let result: any = {}
  for (let key in value) {
    let a = value[key]
    let name = key.split("@").join("\r\n")

    for (let k in a) {
      let type = k
      // type = "Count of " + getHump(k)
      type = getCountFieldText(fieldMap, k);
      if (!result[type]) {
        result[type] = []
      }
      result[type].push({
        name: name,
        value: a[k],
      });
    }
    if (data.num == 1 && showCount) {
      const type = tp("countLog")
      if (!result[type]) {
        result[type] = []
      }
      result[type].push({
        name: name,
        value: a['log_id'],
      });
    }
  }
  data.pieData = result
  return result
}


function getChartData2(data, table, showCount?) {
  if (data.barData && showCount === undefined) {
    return data.barData
  }
  let json = stepDataMap.value[data.stepKey]
  json = JSON.parse(JSON.stringify(json)).data;
  let value = {}

  const fieldMap = getTabField(table);

  for (let i = 0; i < json.length; i++) {
    let valueMap: any = {}
    let name = ""
    let data = json[i]
    for (let key in data) {
      if ("triggerName" == key) {
        continue;
      }
      if (key.startsWith("agg_")) {
        valueMap[key.replace("agg_", "")] = data[key]
      } else {
        name += getFieldName(fieldMap, key) + "=" + data[key] + "@"
      }
    }
    if (name.length > 0) {
      name = name.substring(0, name.length - 1)
    }
    value[name] = valueMap
  }
  let result: any = []
  for (let key in value) {
    let a = value[key]
    let name = key.split("@").join("\r\n")

    for (let k in a) {
      let type = k
      // type = "Count of " + getHump(k)
      type = getCountFieldText(fieldMap, k);
      result.push({
        type: type,
        name: name,
        value: a[k],
      });
    }
    if (data.num == 1 && showCount) {
      result.push({
        type: tp('countLog'),
        name: name,
        value: a['log_id'],
      });
    }
  }
  data.barData = result
  return result
}

function getMarkLine2(data) {
  let list = data.filter_list
  let value: any = []
  for (let i in list) {
    value.push({
      value: list[i].value,
      compare: list[i].compare,
    })
  }
  return value
}


function getChartData(data) {

  const table = datasetMap.value[statisticMap.value[data.statistic_key].dataset_key].table_name_type
  const fieldMap = getTabField(table);
  let field_name = data.field_name
  let field_name2 = field_name
  if ("custom_num" == field_name) {
    field_name2 = "log_id"
    field_name = tp('countLog');
  } else {
    field_name = getCountFieldText(fieldMap, field_name);
  }
  let list = data.statistic_trigger
  if (data.field_type == "step") {
    let json = stepDataMap2.value[data.field_type_value]
    let value = {}
    for (let i = 0; i < json.length; i++) {
      let valueMap: any = {}
      let name = ""
      let data = json[i]
      let v = data["agg_" + field_name2]
      if (!calcValue(list, v)) {
        continue
      }
      for (let key in data) {
        if (key.startsWith("agg_")) {
          if (key == "agg_" + field_name2) {
            valueMap[key.replace("agg_", "")] = data[key]
          }
        } else {
          name += getFieldName(fieldMap, key) + "=" + data[key] + "@"
        }
      }
      if (name.length > 0) {
        name = name.substring(0, name.length - 1)
      }
      value[name] = valueMap
    }
    let result: any = []
    for (let key in value) {
      let a = value[key]
      let name = key.split("@").join("\r\n")
      for (let k in a) {
        result.push({
          type: field_name,
          name: name,
          value: a[k],
        });
      }
    }
    return result
  } else if (data.field_type == "dataset") {
    let json = datasetDataMap.value[data.field_type_value]
    let result: any = []
    result.push({
      type: t('routes.MlRuleVO.totalLogNumberOf') + datasetMap.value[data.field_type_value].title,
      name: "",
      value: json,
    });
    return result
  }
}

/**
 * list 条件
 * v 统计结果值
 * @param list
 * @param v
 */
function calcValue(list, v) {
  let flag = false
  for (let i = 0; i < list.length; i++) {
    //大于
    if (list[i].compare == "gt") {
      if (v > list[i].value) {
        flag = true
        break
      }
    } else if (list[i].compare == "lt") {
      if (v < list[i].value) {
        flag = true
        break
      }
    } else if (list[i].compare == "eq") {
      if (v == list[i].value) {
        flag = true
        break
      }
    }
  }
  return flag
}

const compareMap = {
  'eq': t('common.compare.equal'),
  'noteq': t('common.compare.notEqual'),
  'like': t('common.compare.contains'),
  'notlike': t('common.compare.notContains'),
  'startWith': t('common.compare.startsWith'),
  'endWith': t('common.compare.endsWith'),
  'gt': t('common.compare.larger'),
  'lt': t('common.compare.smaller'),
}

function goBack() {
  if(props.isModal){
    props.closeDrawer && props.closeDrawer();
    return;
  }
  let json = sessionStorage.getItem("inRiskEvent_1")
  if (json) {
    sessionStorage.removeItem("inRiskEvent_1")
    sessionStorage.setItem("inRiskEvent_2", json)
  }
  router.go(-1)
}


function searchData() {
  let val = JSON.stringify(statisticTop.value)
  let id = sessionStorage.getItem("MlEventModalId")
  queryById({id: id, statisticTop: val}).then((data) => {
    dataInfo.value = data
    mlId.value = data.ruleId
    if (data.datasetList && data.datasetList.length > 0) {
      datasetDataMap.value = JSON.parse(data.datasetList[0].dataJson)
    }
    tab2Data.value = [];
    if (data.stepList && data.stepList.length > 0) {
      for (let i in data.stepList) {
        stepDataMap.value[data.stepList[i].stepKey] = data.stepList[i];
        tab2Data.value.push(data.stepList[i])
        stepDataMap2.value[data.stepList[i].stepKey] = data.stepList[i].data
      }
    }
    let json = JSON.parse(data.ruleJson);
    let startTime = json.begin
    let endTime = json.end
    if (!json.dbConfigJson) {
      json.dbConfigJson = json.dbConfig
    } else {
      json.dbConfigJson = JSON.parse(json.dbConfigJson)
    }
    let dataset_json = json.dbConfigJson.dataset
    let statistic_json = json.dbConfigJson.statistic
    let datasetJson: any = {}
    let statisticJson: any = {}
    for (let i in dataset_json) {
      datasetJson[dataset_json[i].key] = dataset_json[i]
    }
    for (let i in statistic_json) {
      statisticJson[statistic_json[i].key] = statistic_json[i]
    }


    datasetMap.value = datasetJson
    statisticMap.value = statisticJson
    ruleJson.value = json.dbConfigJson


    startTime =
      startTime.replace("T", " ").replace("[Asia/Shanghai]", "").replace("[Africa/Cairo]", "")
    endTime = endTime.replace("T", " ").replace("[Asia/Shanghai]", "").replace("[Africa/Cairo]", "")


    startTime = dayjs(startTime).format('YYYY-MM-DD HH:mm:ss');
    endTime = dayjs(endTime).format('YYYY-MM-DD HH:mm:ss');

    ruleJson.value.startTime = startTime
    ruleJson.value.endTime = endTime

    //处理tab2里的数据，另起进程处理，防止卡顿页面
    setTimeout(() => {
      for (let key in stepDataMap.value) {
        let data = stepDataMap.value[key];
        statisticTop.value[key] = 20
        statisticShowType.value[key] = "1"
        statisticShowCount.value[key] = false
        data.statisticTitle = statisticMap.value[data.statisticKey].title
        getTableData(data, datasetJson[statisticMap.value[data.statisticKey].dataset_key].table_name_type)
        getChartData2(data, datasetJson[statisticMap.value[data.statisticKey].dataset_key].table_name_type)
        getPieData(data, datasetJson[statisticMap.value[data.statisticKey].dataset_key].table_name_type)
      }
    }, 50)

  })
}


const [registerModal, {openModal}] = useModal();

function showAddWhite() {
  openModal(true, {
    isUpdate: false,
    showFooter: true,
  });
}


const investigationData = ref<any[]>([]);
const LogId = ref<string>("");//记录当前点击的日志id
const loadInvestigation = () => {
  loadInvestigationTop5({socTenantIds: [dataInfo.value.socTenantId]}).then((result) => {
    investigationData.value = result.records;
  });
}

function addInvestigation(data, id): void {
  inveId = data.id
  LogId.value = id
  inveVisible.value = true
}


const inveVisible = ref(false)
let inveId = ""
const conclusion = ref("")
const userStore = useUserStore();
const saveToInve = () => {

  if (addInveFlag) {
    addInveFlag = false
    let param = {
      eventId: LogId.value,
      conclusion: conclusion.value,
      conclusionBy: userStore.userInfo?.username,
      socTenantId: dataInfo.value.socTenantId,
      type: "3"
    }
    sessionStorage.setItem("addInvestigationParam", JSON.stringify(param));
    router.push({
      path: "/investigation/modules/InvestigationNewModal"
    });
    return
  }

  invMlSave({
    investigationId: inveId,
    eventId: LogId.value,
    conclusion: conclusion.value,
    conclusionBy: userStore.userInfo?.username,
    type: "3"
  }).then(() => {
    inveVisible.value = false
    conclusion.value = ""
  })
}
const registerRiskLogsModal = ref();

function showMoreInvestigation(id) {
  LogId.value = id;
  registerRiskLogsModal.value.visible = true;
}

let addInveFlag = false
const showAddInvestigation = (id) => {
  LogId.value = id;
  addInveFlag = true
  inveVisible.value = true
}

function CloseMl(data) {
  let content = ""
  let title = ""
  let status = 1
  if (data.riskStatus === 1) {
    content = t('common.closeConfirmText')
    title = t('common.closeText')
    status = 2
  } else if (data.riskStatus === 2) {
    content = t('common.openConfirmText')
    title = t('common.openText')
    status = 1
  }
  Modal.confirm({
    title: title,
    content: content,
    okText: t('common.okText'),
    cancelText: t('common.cancelText'),
    onOk: () => {
      let params = {
        id: data.id,
        riskStatus: status
      }
      closed(params).then(() => {
        riskStatus.value = 2
      })

    }
  });
}

const showFieldList = ref<any[]>([])
const showFieldLoading = ref(false)

function loadShowField() {
  queryList({eventId: mlEventId.value}).then(result => {
    let data = result.list;

    let tableFieldMap = {}
    let dataset = ruleJson.value.dataset
    let datasetMap = {}

    //全局固定字段
    if (data && data.length > 0) {
      for (let i in data) {
        let tableType = data[i].tableType
        if (!tableFieldMap[tableType]) {
          tableFieldMap[tableType] = {}
        }
        tableFieldMap[tableType][data[i].fieldValue] = 1
      }
    }

    let eventFlag = false
    //事件的字段
    let data2 = result.list2;
    if (data2 && data2.length > 0) {
      for (let i in data2) {
        let tableType = data2[i].tableType
        if (!tableFieldMap[tableType]) {
          tableFieldMap[tableType] = {}
        }
        if (!tableFieldMap[tableType][data2[i].fieldValue]) {
          tableFieldMap[tableType][data2[i].fieldValue] = 2
        }
      }
    } else {
      eventFlag = true
      //规则里使用的字段
      for (let i in dataset) {
        let tableType = dataset[i].table_name_type
        datasetMap[dataset[i].key] = tableType
        if (!tableFieldMap[tableType]) {
          tableFieldMap[tableType] = {}
        }

        let filters = dataset[i].filterJson;
        if (filters) {
          const json = JSON.parse(filters);
          let fields = []
          loadTreeField(json, fields)
          for (let j in fields) {
            let field = fields[j]
            if (!tableFieldMap[tableType][field]) {
              tableFieldMap[tableType][field] = 3
            }
          }
        } else {
          filters = dataset[i].filter
          if (filters && filters instanceof Array) {
            for (let j in filters) {
              let field = filters[j].field_name
              if (!tableFieldMap[tableType][field]) {
                tableFieldMap[tableType][field] = 3
              }
            }
          }
        }


      }
      let statistic = ruleJson.value.statistic

      for (let i in statistic) {
        let tableType = datasetMap[statistic[i].dataset_key]
        if (!tableFieldMap[tableType]) {
          tableFieldMap[tableType] = {}
        }

        let step = statistic[i].step
        for (let j in step) {

          let group_fields = step[j].group_fields
          for (let k in group_fields) {
            let field = group_fields[k].field_name
            if (!tableFieldMap[tableType][field]) {
              tableFieldMap[tableType][field] = 3
            }
          }

          let statistic_fields = step[j].statistic_fields
          for (let k in statistic_fields) {
            let field = statistic_fields[k]
            if (field == 'log_id') {
              continue
            }
            if (!tableFieldMap[tableType][field]) {
              tableFieldMap[tableType][field] = 3
            }
          }
        }
      }
    }


    let setMap: any = {}
    let tableFieldList = {}
    let sList = []
    for (let key in tableFieldMap) {
      let array: any = []
      let array2: any = []
      let map = tableFieldMap[key]
      for (let field in map) {
        let flag = true;
        if (map[field] == 1) {
          array.push(field)
          flag = false;
        } else {
          array2.push(field)
        }
        if (map[field] == 3) {
          sList.push(field + ":" + key)
        }

        setMap[field] = {
          table: key,
          flag: flag
        }
      }
      tableFieldList[key] = [...array, ...array2]
    }
    let list: any = []
    for (let key in setMap) {
      //flag=true表示显示默认的统计字段，只显示固定按钮
      list.push({
        field: key,
        type: setMap[key].flag,
        table: setMap[key].table
      })
    }
    showFieldList.value = list
    loadFieldDataFn(tableFieldList)
    if (eventFlag) {
      //第一次查看，把规则中使用的字段存到事件默认字段中
      let params = {
        fieldValue: sList.join(","),
        eventId: mlEventId.value
      }
      saveShowField(params, false).then(() => {
        data.type = false
      })
    }
  })
}

function loadTreeField(data, fields) {
  let list = data.list;
  for (let i in list) {
    if (list[i].list) {
      loadTreeField(list[i], fields)
    } else {
      fields.push(list[i].field)
    }
  }
}

const showFieldData = ref<any>({})

/**
 * 查询要显示字段的值，去重后的值
 */
function loadFieldDataFn(tableFieldList) {
  showFieldLoading.value = true;

  loadFieldData({
    tableField: tableFieldList,
    eventId: mlEventId.value,
    startTime: ruleJson.value.startTime,
    endTime: ruleJson.value.endTime
  }).then(data => {
    for (let key in data) {
      let list = data[key]
      if (list && list.length > 0) {
        for (let i in list) {
          let val = list[i]
          list[i] = {
            value: val
          }
        }
      }
    }
    showFieldData.value = data
    showFieldLoading.value = false
  })
}


function showSetting() {
  let dataset = ruleJson.value.dataset
  let datasetList: any = []
  for (let i in dataset) {
    let tableType = dataset[i].table_name_type
    datasetList.push(tableType)
  }
  settingFieldRef.value.init(datasetList, mlEventId.value)
}

function addShowField(data) {
  let params = {
    fieldValue: data.field,
    tableType: data.table,
    status: true,
    fieldType: 1
  }
  saveShowField(params, true).then(() => {
    data.type = false
  })
}

function delShowField(data) {
  let params = {
    fieldValue: data.field,
    tableType: data.table,
    status: false,
    fieldType: 1
  }
  saveShowField(params, true).then(() => {
    let list = showFieldList.value
    let newList: any = []
    for (let i = 0; i < list.length; i++) {
      if (list[i].field != data.field) {
        newList.push(list[i])
      }
    }
    if (newList.length == 0) {
      refShowField()
    }
    showFieldList.value = newList
  })
}

function refShowField() {
  loadShowField()
}

const showFieldSearch = ref("")

function searchField() {
  // showFieldList
  // showFieldData
  let haveSearchKey = {}
  for (let key in showFieldData.value) {
    let list = showFieldData.value[key];
    if (list && list.length > 0) {
      for (let i in list) {
        let val = list[i].value + "";
        if (showFieldSearch.value && val.indexOf(showFieldSearch.value) > -1) {
          list[i].select = true
          if (!haveSearchKey[key]) {
            haveSearchKey[key] = 1
          } else {
            haveSearchKey[key]++
          }

        } else {
          list[i].select = false
        }
      }
    }
  }
  for (let key in showFieldData.value) {
    let list = showFieldData.value[key]
    if (list && list.length > 0) {
      list.sort((a, b) => {
        if (a.select == b.select) {
          return 0
        } else if (a.select == true) {
          return -1
        } else {
          return 1
        }
      })
    }
  }

  showFieldList.value.sort((a, b) => {
    let num1 = haveSearchKey[a.field] ?? 0
    let num2 = haveSearchKey[b.field] ?? 0
    return num2 - num1
  })

}
</script>

<style scoped lang="less">
.border {
  border: 1px solid @border-color;
}

.block_div {
  margin: 5px 0px;
  padding: 10px;
}


.field_where_div {
  display: flex;
  flex-flow: row wrap;
  background: rgba(48, 140, 255, 0.102);
  padding: 8px 0px 0 16px;
}

.where_div {
  width: 255px;
  position: relative;
  margin-bottom: 8px;

  .delImg {
    position: absolute;
    right: -8px;
    top: -8px;
    cursor: pointer;
    display: none;
  }
}

.where_div:hover {
  .delImg {
    display: block;
  }
}

.or_div {
  width: 30px;
  text-align: center;
  line-height: 32px;
}

.addImg {
  cursor: pointer;
  position: relative;
  margin-left: 10px;
}

.andText {
  margin: 8px 16px;
}

.del_where_div {
  width: 36px;
  text-align: center;
  padding: 10px;
  background: rgba(48, 140, 255, 0.102);
  margin-top: -8px;
  cursor: pointer;
}

.border_div {
  border: 1px solid @border-color;
}

.show_field {
  padding: 5px 10px;
  border-right: 1px solid @border-color;
  display: flex;
  justify-content: center;
  align-items: center;
}

.show_span {
  padding: 5px 10px;
  background-color: #0a9fe5;
  margin: 5px;
  border-radius: 5px;
}

.showFieldSelect {
  background-color: #D9001B;
}
</style>

