<template>
  <a-spin :spinning="!dataInfo.id">
    <div class="risk_info_div" v-if="dataInfo.id">
      <div class="flex_top_div">
        <div class="btn_div">
          <a-button @click="showAddWhite" class="ant-btn-sm ax-btn-pre">
            <span class="soc ax-com-Record"></span>
            {{ tp('whitelist') }}
          </a-button>
          <a-button class="ant-btn-sm ax-btn-pre" @click="toThreatHunting(dataInfo, router, 'ml')">
            <span class="soc ax-com-Add"></span>
            {{ tp('threatHunting') }}
          </a-button>
          <!-- <a-button>
            <Icon icon="ant-design:plus-outlined" />
            {{ tp('investigation') }}
          </a-button> -->
          <div style="position: relative" v-if="hasTicketPermission()">
            <a-dropdown :trigger="['click']">
              <a-button class="ant-btn-sm ax-btn-pre">
                <span class="soc ax-com-Add"></span>
                {{ tp('ticket') }}
              </a-button>
              <template #overlay>
                <a-menu>
                  <EventApply :record="dataInfo" eventType="3" @applyOk="refReload" />
                </a-menu>
              </template>
            </a-dropdown>

            <div style="position: absolute; inset: 0; opacity: 0" v-if="dataInfo.ticketInstId">
              <Ticket :ticketInfo="dataInfo" />
            </div>
          </div>

          <a-dropdown :trigger="['click']">
            <div class="ax-icon-button ax-icon-small">
              <span class="soc ax-com-More ax-icon"></span>
            </div>
            <template #overlay>
              <a-menu>
                <!-- 有权限且没有分配人 -->
                <a-menu-item v-if="hasPermission('risk:assign_other') && dataInfo?.riskStatus !== 2">
                  <MlEventAssign :record="dataInfo" :reload="refReload" type="assign_other" />
                </a-menu-item>
                <a-menu-item v-else-if="hasPermission('risk:assign_self') && dataInfo?.riskStatus !== 2">
                  <MlEventAssign :record="dataInfo" :reload="refReload" type="assign_self" />
                </a-menu-item>

                <!-- 有权限且没有验证且分配给登录人 -->
                <a-menu-item v-if="hasPermission('risk:triage') && dataInfo?.riskStatus !== 2 && dataInfo?.owner == userStore.getUserInfo.id">
                  <MlEventTriage :record="dataInfo" :reload="refReload" />
                </a-menu-item>
                <!-- 有权限且已验证且分配给登录人且没有关闭 -->
                <a-menu-item
                  v-if="
                    hasPermission('risk:close') &&
                    dataInfo?.triageStatus !== 0 &&
                    dataInfo?.owner == userStore.getUserInfo.id &&
                    dataInfo?.riskStatus !== 2
                  "
                >
                  <MlEventClose :record="dataInfo" type="close" :reload="refReload" />
                </a-menu-item>
                <a-menu-item v-if="hasPermission('risk:close') && dataInfo?.owner == userStore.getUserInfo.id && dataInfo?.riskStatus === 2">
                  <MlEventClose :record="dataInfo" type="open" :reload="refReload" />
                </a-menu-item>
              </a-menu>
            </template>
          </a-dropdown>
        </div>
      </div>
      <div class="risk_content_div">
        <div class="ax-tab-card h-[100%]">
          <a-tabs type="card">
            <a-tab-pane key="1" :tab="tp('summary')">
              <Summary ref="summaryRef" :record="dataInfo" />
            </a-tab-pane>
            <a-tab-pane key="2" :tab="tp('detail')" class="detail-tab-pane">
              <RiskDetail source="ml" :record="handleParams()" v-if="dataInfo.ruleType == 1 || dataInfo.ruleType == 3" />
              <OrderDetail v-if="dataInfo.ruleType == 2" />
            </a-tab-pane>
            <a-tab-pane key="3" :tab="tp('rule')" class="detail-tab-pane">
              <RuleContentInfo :record="dataInfo" v-if="dataInfo.ruleType == 3" />
              <MlRuleViewModal pageSource="mlView" :mlId="dataInfo.ruleId" v-if="dataInfo.ruleType == 1" :record="dataInfo" />
              <RuleOrderInfo :record="dataInfo" source="event" v-if="dataInfo.ruleType == 2" />
            </a-tab-pane>
            <a-tab-pane key="4" :tab="tp('proposal')" v-if="ifShowProposal">
              <RiskProposal :record="proposalInfo" source="ml" />
            </a-tab-pane>
          </a-tabs>
        </div>
      </div>
    </div>
  </a-spin>
  <WhitelistVOModal @register="registerModal" type="2" />
</template>

<script lang="ts" setup>
  import { useI18n } from '/@/hooks/web/useI18n';
  import Summary from '/@/views/mlView/modules/cmts/summary/Summary.vue';
  import { useModal } from '/@/components/Modal';
  import WhitelistVOModal from '/@/views/whitelist/modules/WhitelistVOModal.vue';
  import { hasTicketPermission, toThreatHunting } from '/@/views/aggregationRiskEventView/RiskEventView.data';
  import { usePermission } from '/@/hooks/web/usePermission';
  import { useUserStore } from '/@/store/modules/user';
  import { Ticket } from '/@/views/aggregationRiskEventView/component/ticket/index';
  import EventApply from '/@/views/ticket/view/apply/eventApply.vue';
  import { ref } from 'vue';
  import RiskDetail from '/@/views/aggregationRiskEventView/modules/cmts/riskDetail/RiskDetail.vue';
  import RiskProposal from '/@/views/aggregationRiskEventView/modules/cmts/proposal/RiskProposal.vue';
  import { queryById } from '/@/views/mlView/MlEvent.api';
  import { useRouter } from 'vue-router';
  import MlEventAssign from '/@/views/aggregationRiskEventView/component/mlEvemt/MlEventAssign.vue';
  import MlEventTriage from '/@/views/aggregationRiskEventView/component/mlEvemt/MlEventTriage.vue';
  import MlEventClose from '/@/views/aggregationRiskEventView/component/mlEvemt/MlEventClose.vue';
  import RuleContentInfo from '/@/views/mlView/modules/cmts/rule/RuleContentInfo.vue';
  import MlRuleViewModal from '/@/views/mlRule/modules/MlRuleViewModal.vue';
  import RuleOrderInfo from '/@/views/mlView/modules/cmts/rule/RuleOrderInfo.vue';
  import OrderDetail from '/@/views/mlView/modules/cmts/detail/orderDetail.vue';
  import { list } from '/@/views/proposal/ProposalManagement.api';
  import { getTenantMode } from '/@/utils/auth';

  const router = useRouter();
  const { hasPermission } = usePermission();
  const userStore = useUserStore();
  const { t } = useI18n();
  const ifShowProposal = ref(false);
  const isTenantMode = getTenantMode();
  function tp(name) {
    return t('routes.riskEvent.' + name);
  }

  const props = defineProps({
    reload: Function,
    ruleType: Number,
  });
  console.log(props);
  const dataInfo = ref<any>({});

  loadInfo();

  function loadInfo() {
    const id = sessionStorage.getItem('MlEventModalId');
    queryById({ id: id }).then((data) => {
      dataInfo.value = data;
      console.log('dataInfo.value', dataInfo.value);
      loadProposalInfo(data);
    });
  }

  const proposalInfo = ref<any>({});
  function loadProposalInfo(data) {
    proposalInfo.value = {};
    let params: any = {
      mlRuleId: data.ruleId,
      socTenantId: isTenantMode ? data.socTenantId : undefined,
    };
    list(params).then((data) => {
      console.log(data);
      if (data && data.records && data.records.length > 0) {
        ifShowProposal.value = true;
        let obj = {};
        obj = data.records[0];
        proposalInfo.value = obj ? obj : {};
      } else {
        ifShowProposal.value = false;
      }
    });
  }

  const [registerModal, { openModal }] = useModal();

  function showAddWhite() {
    openModal(true, {
      isUpdate: false,
      showFooter: true,
    });
  }

  const summaryRef = ref();

  function refReload() {
    props.reload && props.reload();
    loadInfo();
  }

  function handleTime(time) {
    // 使用正则表达式去除时区信息
    const cleanTime = time.replace(/\[.*?\]/, '');
    // 创建 Date 对象
    const date = new Date(cleanTime);
    // 获取年月日时分秒
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0'); // 月份从0开始
    const day = String(date.getDate()).padStart(2, '0');
    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');
    const seconds = String(date.getSeconds()).padStart(2, '0');

    // 组合成年月日时分秒格式
    const dateTimeString = `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
    return dateTimeString;
  }

  function handleParams() {
    return {
      type: dataInfo.value.tableType,
      minDate: dataInfo.value.minAlarmTime,
      maxDate: dataInfo.value.maxAlarmTime,
      id: dataInfo.value.id,
      ruleType: dataInfo.value.ruleType,
    };
  }
</script>

<style lang="less" scoped>
  .risk_info_div {
    .flex_top_div {
      display: flex;
      gap: 16px;
      padding: 0 8px 0 56px;
      margin-bottom: 20px;
      margin-top: 8px;

      .btn_div {
        display: flex;
        gap: 8px;
        align-items: center;
      }
    }

    .risk_content_div {
      height: calc(100vh - 112px);
      padding-left: 40px;
      padding-right: 8px;
      flex: 1;
    }
  }

  .detail-tab-pane {
    height: calc(100vh - 146px);
    overflow-y: auto;
    padding: 0;
  }

  :deep(.ant-tabs-card > .ant-tabs-content-holder) {
    overflow: auto;
  }
</style>
