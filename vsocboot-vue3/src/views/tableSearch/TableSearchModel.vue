<template>
  <a-modal key="parent" v-model:visible="visible" :title="t('common.AdvancedQuery')" :maskClosable="false" width="800px" :destroyOnClose="true">
    <template #closeIcon>
      <div class="ax-icon-button ax-icon-large">
        <span class="soc ax-com-Close ax-icon"></span>
      </div>
    </template>
    <div class="p-16px">
      <!-- 系统内数据高级查询-->
      <div class="ax-border system-filter mb-16px" v-if="systemFilter">
        <div class="ax-border-right pt-16px px-8px">
          <a-switch v-model:checked="systemFilterChecked" />
        </div>
        <div class="flex flex-col gap-[8px] p-16px flex-1">
          <div class="flex flex-row gap-[8px]">
            <a-input v-model:value="advanceSystemData.data[0].label" disabled class="flex-1"></a-input>
            <a-select :options="judgeOption" v-model:value="advanceSystemData.data[0].rel" class="w-80px"></a-select>
            <a-select :options="systemValueOption" v-model:value="advanceSystemData.data[0].value" class="flex-1"></a-select>
          </div>
          <a-select :options="relOption" v-model:value="advanceSystemData.rel" class="w-120px" />
          <div class="flex flex-row gap-[8px]">
            <a-input v-model:value="advanceSystemData.data[1].label" disabled class="flex-1"></a-input>
            <a-select :options="judgeOption" v-model:value="advanceSystemData.data[1].rel" class="w-80px"></a-select>
            <a-select :options="systemValueOption" v-model:value="advanceSystemData.data[1].value" class="flex-1"></a-select>
          </div>
        </div>
      </div>
      <div class="table_search flex flex-col gap-8px" id="search_model">
        <template v-if="!isReport">
          <div style="margin-bottom: 12px;width:200px">
            <JSearchSelect
              :dict-options="templateOption"
              @change="templateChange"
              :allowClear="true"
              :optionsDel="true"
              v-model:value="templateId"
              :placeholder="t('common.template')"
              @delOption="delOption"
            />
          </div>
          <div
            class="search_flex border_bottom"
            v-if="source == 'correlation' || source == 'workHistory' || source == 'badmssp' || source == 'badActors' || source == 'badActorsHis'"
          >
            <div v-if="source == 'correlation'" class="search_flex_1" style="max-width: 105px; line-height: 32px">
              <span>
                {{ t('routes.CorrelationEvent.createTime') }}
              </span>
            </div>
            <div v-else-if="source == 'workHistory'" class="search_flex_1" style="max-width: 105px; line-height: 32px">
              <span>
                {{ t('routes.workflow.startTime') }}
              </span>
            </div>
            <div
              v-else-if="source == 'badmssp' || source == 'badActors' || source == 'badActorsHis'"
              class="search_flex_1"
              style="max-width: 135px; line-height: 32px"
            >
              <span>
                {{ t('routes.badActors.firstDetectionTime') }}
              </span>
            </div>
            <div class="search_flex_1">
              <a-date-picker
                show-time
                v-model:value="dateValue.startTime_begin"
                value-format="YYYY-MM-DD HH:mm:ss"
                :placeholder="t('common.startTimePlaceholder')"
                class="flex-1"
              />
              <a-date-picker
                show-time
                v-model:value="dateValue.startTime_end"
                value-format="YYYY-MM-DD HH:mm:ss"
                :placeholder="t('common.endTimePlaceholder')"
                class="flex-1"
              />
            </div>
            <div v-if="source == 'correlation'" class="search_flex_1" style="max-width: 105px; line-height: 32px">
              <span>
                {{ t('routes.CorrelationEvent.updateTime') }}
              </span>
            </div>
            <div v-else-if="source == 'workHistory'" class="search_flex_1" style="max-width: 105px; line-height: 32px">
              <span>
                {{ t('routes.workflow.endTime') }}
              </span>
            </div>
            <div
              v-else-if="source == 'badmssp' || source == 'badActors' || source == 'badActorsHis'"
              class="search_flex_1"
              style="max-width: 135px; line-height: 32px"
            >
              <span>
                {{ t('routes.badActors.lastDetectionTime') }}
              </span>
            </div>

            <div class="search_flex_1">
              <a-date-picker
                show-time
                v-model:value="dateValue.updateTime_begin"
                value-format="YYYY-MM-DD HH:mm:ss"
                :placeholder="t('common.startTimePlaceholder')"
                class="flex-1"
              />
              <a-date-picker
                show-time
                v-model:value="dateValue.updateTime_end"
                value-format="YYYY-MM-DD HH:mm:ss"
                :placeholder="t('common.endTimePlaceholder')"
                class="flex-1"
              />
            </div>
          </div>
          <div class="search_flex border_bottom" v-else-if="source != 'asset' && source != 'invest'">
            <div class="search_flex_1" style="max-width: 105px; line-height: 32px">
              <span v-if="source == 'ml'">
                {{ t('routes.MlEvent.alarmTime') }}
              </span>
              <span v-else-if="source == 'risk' || source == 'ueba'">
                {{ t('routes.RiskEventLogView.updateTime') }}
              </span>
            </div>
            <div class="search_flex_1">
              <a-date-picker
                show-time
                v-model:value="dateValue.updateTime_begin"
                value-format="YYYY-MM-DD HH:mm:ss"
                :placeholder="t('common.startTimePlaceholder')"
                class="flex-1"
              />
              <a-date-picker
                show-time
                v-model:value="dateValue.updateTime_end"
                value-format="YYYY-MM-DD HH:mm:ss"
                :placeholder="t('common.endTimePlaceholder')"
                class="flex-1"
              />
            </div>
          </div>
        </template>
        <!--查询group start-->
        <div class="flex flex-col gap-8px">
          <div class="div_or" v-for="(item, index) in whereModel.whereList" :key="index">
            <div v-if="index > 0" class="mb-8px fcolor1 font-600">OR</div>
            <div class="div_and flex flex-col gap-8px">
              <!--    删除按钮-->
              <div class="soc ax-com-Fault del_img" @click="delGroup(whereModel.whereList, index)"></div>
              <!--    查询内容-->
              <div v-for="(item2, index2) in item" :key="index + '_' + index2" class="flex flex-row gap-8px">
                <!--    删除按钮-->
                <div class="ax-icon-button is_del" @click="delField(item, index2)">
                  <span class="soc ax-com-Decrease ax-icon" ></span>
                </div>
                <div class="w-200px">
                  <a-select
                    v-model:value="item2.field"
                    :options="fieldList"
                    @blur="checkedElRuleRequired"
                    :id="'s_' + index + '_' + index2"
                    show-search
                    optionFilterProp="label"
                    @change="fieldChange('s_' + index + '_' + index2, item2)"
                    class="rule_required"
                  />
                </div>

                <div class="w-160px">
                  <a-select v-model:value="item2.rule" :options="getRuleOptions(item2)" @change="ruleChange(item2)" />
                </div>

                <div class="flex flex-row w-340px" v-if="item2.rule != 'not_empty'">
                  <div class="flex-1">
                    <div v-if="showDictSelect(item2)">
                      <template v-if="item2.field === 'owner'">
                        <JSearchSelect
                          :appendFirstData="ownerFirstOption"
                          :dict="getValOption(item2)"
                          v-model:options="dictOptionsMap[item2.field]"
                          className="rule_required"
                          @blur="checkedElRuleRequired"
                          :id="'val_' + index + '_' + index2"
                          @change="checkSelectChange('val_' + index + '_' + index2)"
                          v-model:value="item2.val"
                        />
                      </template>
                      <template v-else>
                        <JSearchSelect
                          :dict="getValOption(item2)"
                          v-model:options="dictOptionsMap[item2.field]"
                          className="rule_required"
                          @blur="checkedElRuleRequired"
                          :id="'val_' + index + '_' + index2"
                          @change="checkSelectChange('val_' + index + '_' + index2)"
                          v-model:value="item2.val"
                        />
                      </template>
                    </div>
                    <div v-else-if="showTreeSelect(item2)">
                      <a-tree-select
                        v-model:value="item2.val"
                        style="width: 100%"
                        :dropdown-style="{ maxHeight: '400px', overflow: 'auto' }"
                        :placeholder="t('common.PleaseSelect')"
                        allow-clear
                        showSearch
                        class="rule_required"
                        @blur="checkedElRuleRequired"
                        :id="'val_' + index + '_' + index2"
                        @change="checkSelectChange('val_' + index + '_' + index2)"
                        :tree-data="getValOption(item2)"
                        tree-node-filter-prop="label"
                      />
                    </div>
                    <div v-else-if="showSelect(item2)">
                      <JSearchSelect
                        :dict-options="getValOption(item2)"
                        className="rule_required"
                        @blur="checkedElRuleRequired"
                        :id="'val_' + index + '_' + index2"
                        @change="checkSelectChange('val_' + index + '_' + index2)"
                        v-model:value="item2.val"
                      />
                    </div>
                    <!-- showTimeType类型的数据存到另一个属性中，在查询的是后转换成数据库需要的数据 -->
                    <div v-else-if="showTimeType(item2)">
                      <a-input v-model:value="item2.timeVal" class="rule_number input-default" @input="checkedElRuleNumber($event)" />
                    </div>
                    <div v-else-if="fieldTypeMap[item2.field] == 'number'">
                      <a-input v-model:value="item2.val" class="rule_number input-default" @input="checkedElRuleNumber($event)" />
                    </div>
                    <div v-else>
                      <a-input v-model:value="item2.val" @input="checkedElRuleRequired($event)" />
                    </div>
                  </div>
                  <div style="max-width: 85px; min-width: 85px; padding-left: 5px" v-if="showTimeType(item2)">
                    <a-select
                      :options="getValOption(item2)"
                      @blur="checkedElRuleRequired"
                      :id="'time_type_' + index + '_' + index2"
                      @change="checkSelectChange('time_type_' + index + '_' + index2)"
                      class="rule_required"
                      show-search
                      optionFilterProp="label"
                      v-model:value="item2.timeValType"
                    />
                  </div>
                </div>
              </div>
              <!--    添加按钮-->
              <div>
                <a-button type="primary" ghost @click="add(item)" class="ant-btn-sm">
                  <span class="soc ax-com-Add"></span>
                  {{ t('common.add') }}
                </a-button>
              </div>
            </div>
          </div>
        </div>
        <!--查询group end-->
        <div>
          <a-button type="primary" ghost @click="addGroup" class="ant-btn-sm">
            <span class="soc ax-com-Add"></span>
            {{ t('common.AddGroup') }}
          </a-button>
        </div>
      </div>
    </div>

    <template #footer>
      <a-button @click="delAll">{{ t('common.deleteAll') }}</a-button>
      <a-button @click="saveSearch" v-if="!isReport">
        {{ t('common.saveText') }}
      </a-button>
      <a-button type="primary" @click="search">{{ t('common.Search') }}</a-button>
    </template>
  </a-modal>

  <TemplateName ref="templateNameRef" @success="refTemplateData" v-if="!isReport" />
</template>

<script setup lang="ts">
import {defineEmits, inject, Ref, ref} from 'vue';
  import { useI18n } from '/@/hooks/web/useI18n';
  import dayjs from 'dayjs';
  import { checkedElRuleNumber, checkedElRuleRequired, checkedRule, checkSelectChange } from '/@/utils/checkedRule';
  import JSearchSelect from '/@/components/Form/src/jeecg/components/JSearchSelect.vue';
  import {
    ASSET_LEVEL_SELECT,
    ASSET_LOG_SOURCE_SELECT,
    ASSET_MAIN_TYPE_SELECT,
    ASSET_SAFE_SELECT,
    ASSET_SOURCE_SELECT,
    ASSET_SOURCE_STATUS_SELECT,
    ASSET_STATE_SELECT,
    AUTHORITY_SELECT,
    CLOSE_STATUS_SELECT,
    CORRELATION_SEVERITY_SELECT,
    CORRELATION_STATUS_SELECT,
    ML_RULE_TYPE_SELECT,
    RISK_STATUS_SELECT,
    SEVERITY2_SELECT,
    SEVERITY_SELECT,
    TICKET_TYPE_SELECT,
    TIME_TYPE_SELECT,
    TRIAGE_STATUS_SELECT,
    WORKFLOW_STATUS_SELECT,
  } from '/@/utils/valueEnum';
  import TemplateName from '/@/views/tableSearch/TemplateName.vue';
  import { deleteOne, loadAssetGroup, searchTemplateList } from '/@/views/tableSearch/TableSearchData.api';
  import { E_ADVANCED_JUDGE, E_ADVANCED_QUERY, E_ADVANCED_REL, judgeOption, relOption, systemValueOption } from '/@/views/reportChart/ts/systemData';
  import { Modal } from 'ant-design-vue';
  import { getTenantMode, } from '/@/utils/auth';
import {EventSourceSelect} from "/@/views/aggregationRiskEventView/RiskEventView.data";

  const { t } = useI18n();
  const tenantId = inject<Ref<string>>('tenantId',ref(''));
  const ownerFirstOption = ref([{ text: t('common.Unassigned'), value: 'unassign' }]);
  const systemFilterChecked = ref(false);
  const props = defineProps({
    source: {
      type: String,
      default: '',
    },
    isReport: {
      type: Boolean,
      default: false,
    },
    systemFilter: {
      type: Boolean,
      default: false,
    },
  });
  const emits = defineEmits(['search']);
  const assetGroupTreeData = ref<any[]>([]);
  //记录每个字段下拉选的值
  const dictOptionsMap = ref<any>({});
  const advanceSystemData = ref({
    rel: E_ADVANCED_REL.AND,
    data: [
      {
        label: 'Source IP',
        field: E_ADVANCED_QUERY.SOURCE_IP,
        rel: E_ADVANCED_JUDGE.EQ,
        value: E_ADVANCED_QUERY.ASSET_IP,
      },
      {
        label: 'Destination IP',
        field: E_ADVANCED_QUERY.DESTINATION_IP,
        rel: E_ADVANCED_JUDGE.EQ,
        value: E_ADVANCED_QUERY.ASSET_IP,
      },
    ],
  });
  getAssetGroupData();
  const treeLabelMap = ref({
    'assetGroupId': {}
  })
  const sourceTypeMap = {
    risk: {
      showDictSelect: ['socTenantId', 'owner'],
      showOptionSelect: [ 'triageStatus', 'eventStatus','ruleType'],
      showTimeType: ['assignmentConsume', 'triageConsume', 'closeConsume', 'totalConsume'],
      getRuleOptions: ['socTenantId', 'owner', 'triageStatus', 'eventStatus','ruleType'],
      triageStatus: TRIAGE_STATUS_SELECT,
      eventStatus: RISK_STATUS_SELECT,
      assignmentConsume: TIME_TYPE_SELECT,
      triageConsume: TIME_TYPE_SELECT,
      closeConsume: TIME_TYPE_SELECT,
      totalConsume: TIME_TYPE_SELECT,
      ruleType: EventSourceSelect,
      socTenantId: 'tenantActiveDict',
      owner: 'sysUserActiveDict',
    },
    correlation: {
      showDictSelect: ['socTenantId', 'owner', 'eventTacticId', 'eventTechnicalIdStr', 'eventSubTechnicalIdStr'],
      showOptionSelect: ['eventSeverity', 'dismiss', 'eventStatus'],
      showTimeType: ['assignmentConsume', 'triageConsume', 'closeConsume', 'totalConsume'],
      getRuleOptions: ['socTenantId', 'owner', 'eventSeverity', 'dismiss', 'eventStatus'],
      eventSeverity: CORRELATION_SEVERITY_SELECT,
      dismiss: TRIAGE_STATUS_SELECT,
      eventStatus: CORRELATION_STATUS_SELECT,
      assignmentConsume: TIME_TYPE_SELECT,
      triageConsume: TIME_TYPE_SELECT,
      closeConsume: TIME_TYPE_SELECT,
      totalConsume: TIME_TYPE_SELECT,
      socTenantId: 'tenantActiveDict',
      owner: 'sysUserActiveDict',
      eventTacticId: 'tbl_tactics,name,id',
      eventTechnicalIdStr: 'tbl_technique,name,id,level=1',
      eventSubTechnicalIdStr: 'tbl_technique,name,id,level=2',
    },
    ml: {
      showDictSelect: ['socTenantId', 'owner'],
      showOptionSelect: ['ruleType', 'urgency', 'triageStatus', 'riskStatus'],
      showTimeType: ['assignmentConsume', 'triageConsume', 'closeConsume', 'totalConsume'],
      getRuleOptions: ['socTenantId', 'owner', 'ruleType', 'urgency', 'triageStatus', 'riskStatus'],
      socTenantId: 'tenantActiveDict',
      owner: 'sysUserActiveDict',
      ruleType: ML_RULE_TYPE_SELECT,
      urgency: SEVERITY2_SELECT,
      triageStatus: TRIAGE_STATUS_SELECT,
      riskStatus: RISK_STATUS_SELECT,
      assignmentConsume: TIME_TYPE_SELECT,
      triageConsume: TIME_TYPE_SELECT,
      closeConsume: TIME_TYPE_SELECT,
      totalConsume: TIME_TYPE_SELECT,
    },
    asset: {
      showDictSelect: ['socTenantId', 'tag'],
      showOptionSelect: ['assetState', 'assetSafe', 'assetLevel', 'assetType', 'assetSource', 'ifLogSource', 'logApiId', 'logSourceStatus'],
      showTreeSelect: ['assetGroupId'],
      getRuleOptions: [
        'socTenantId',
        'tag',
        'assetState',
        'assetSafe',
        'assetLevel',
        'assetType',
        'assetGroupId',
        'assetSource',
        'ifLogSource',
        'logApiId',
        'logSourceStatus',
      ],
      socTenantId: 'tenantActiveDict',
      tag: 'tbl_tag,name,id,tag_type=1',
      assetState: ASSET_STATE_SELECT,
      assetSafe: ASSET_SAFE_SELECT,
      assetLevel: ASSET_LEVEL_SELECT,
      assetType: ASSET_MAIN_TYPE_SELECT,
      assetSource: ASSET_SOURCE_SELECT,
      ifLogSource: ASSET_LOG_SOURCE_SELECT,
      logApiId: ASSET_LOG_SOURCE_SELECT,
      logSourceStatus: ASSET_SOURCE_STATUS_SELECT,
      assetGroupId: assetGroupTreeData,
    },
    workHistory: {
      showDictSelect: ['socTenantId', 'applyUser'],
      showOptionSelect: ['ticketType', 'workflowStatus'],
      showTimeType: ['duration'],
      getRuleOptions: ['socTenantId', 'applyUser', 'ticketType', 'workflowStatus'],
      socTenantId: 'tenantActiveDict',
      applyUser: 'sysUserActiveDict',
      ticketType: TICKET_TYPE_SELECT,
      workflowStatus: WORKFLOW_STATUS_SELECT,
      duration: TIME_TYPE_SELECT,
    },
    badmssp: {
      showDictSelect: ['socTenantIdStr'],
      showOptionSelect: ['severity'],
      showTimeType: ['durationSeconds'],
      getRuleOptions: ['socTenantIdStr', 'severity'],
      socTenantIdStr: 'tenantActiveDict',
      severity: SEVERITY_SELECT,
      durationSeconds: TIME_TYPE_SELECT,
    },
    badActors: {
      showDictSelect: ['socTenantId', 'owner'],
      showOptionSelect: ['severity', 'triageStatus', 'status'],
      showTimeType: ['durationSeconds', 'assignmentConsume', 'triageConsume', 'closeConsume', 'totalConsume'],
      getRuleOptions: ['socTenantId', 'severity', 'owner'],
      socTenantId: 'tenantActiveDict',
      owner: 'sysUserActiveDict',
      severity: SEVERITY_SELECT,
      triageStatus: TRIAGE_STATUS_SELECT,
      status: CLOSE_STATUS_SELECT,
      durationSeconds: TIME_TYPE_SELECT,
      assignmentConsume: TIME_TYPE_SELECT,
      triageConsume: TIME_TYPE_SELECT,
      closeConsume: TIME_TYPE_SELECT,
      totalConsume: TIME_TYPE_SELECT,
    },
    badActorsHis: {
      showDictSelect: ['socTenantId', 'owner'],
      showOptionSelect: ['severity', 'triageStatus', 'status'],
      showTimeType: ['durationSeconds', 'assignmentConsume', 'triageConsume', 'closeConsume', 'totalConsume'],
      getRuleOptions: ['socTenantId', 'severity', 'owner'],
      socTenantId: 'tenantActiveDict',
      owner: 'sysUserActiveDict',
      severity: SEVERITY_SELECT,
      triageStatus: TRIAGE_STATUS_SELECT,
      status: CLOSE_STATUS_SELECT,
      durationSeconds: TIME_TYPE_SELECT,
      assignmentConsume: TIME_TYPE_SELECT,
      triageConsume: TIME_TYPE_SELECT,
      closeConsume: TIME_TYPE_SELECT,
      totalConsume: TIME_TYPE_SELECT,
    },
    invest: {
      showDictSelect: ['socTenantId', 'templateId'],
      showOptionSelect: ['severity', 'state', 'authority'],
      getRuleOptions: ['socTenantId', 'templateId', 'severity', 'state', 'authority'],
      socTenantId: 'tenantActiveDict',
      templateId: 'investTemplateDict',
      severity: SEVERITY2_SELECT,
      state: RISK_STATUS_SELECT,
      authority: AUTHORITY_SELECT,
    },
    ueba: {
      showDictSelect: ['socTenantId', 'owner'],
      showOptionSelect: ['triageStatus', 'riskStatus'],
      showTimeType: ['assignmentConsume', 'triageConsume', 'closeConsume', 'totalConsume'],
      getRuleOptions: ['socTenantId', 'owner', 'triageStatus', 'riskStatus'],
      triageStatus: TRIAGE_STATUS_SELECT,
      riskStatus: RISK_STATUS_SELECT,
      assignmentConsume: TIME_TYPE_SELECT,
      triageConsume: TIME_TYPE_SELECT,
      closeConsume: TIME_TYPE_SELECT,
      totalConsume: TIME_TYPE_SELECT,
      socTenantId: 'tenantActiveDict',
      owner: 'sysUserActiveDict',
    },
  };

  async function getAssetGroupData() {
    await loadAssetGroup({socTenantId:tenantId.value}).then((data) => {
      let newData = [];
      handleTreeData(data, newData,'assetGroupId');
      assetGroupTreeData.value = newData;
      return newData;
    });
  }

  function handleTreeData(data, newData,type) {
    let ids: any = [];
    for (let i in data) {
      let children = data[i].children;
      let newChildren = [];
      let childrenIds = handleTreeData(children, newChildren,type);
      let value = data[i].id;
      if (childrenIds.length > 0) {
        value += ',' + childrenIds.join(',');
      }
      let d: any = {
        label: data[i].groupName,
        value: value,
      };
      ids.push(value);
      if (newChildren.length > 0) {
        d.children = newChildren;
      }
      treeLabelMap.value[type][value] = data[i].groupName;
      newData.push(d);
    }
    return ids;
  }

  /**
   * 时间数据对象
   */
  const dateValue = ref({
    startTime_begin: '',
    startTime_end: '',
    updateTime_begin: '',
    updateTime_end: '',
  });

  const whereModel = ref<any>({
    updateTimeStr: '',
    startTimeStr: '',
    whereList: [],
    advanceSystemData: {},
  });

  const visible = ref(false);
  const fieldList = ref<any[]>([]);
  const compareList = ref<any[]>([
    { label: t('common.compare.equal'), value: 'eq' },
    { label: t('common.compare.notEqual'), value: 'ne' },
    { label: t('common.compare.contains'), value: 'like' },
    { label: t('common.compare.notContains'), value: 'not_like' },
    { label: t('common.compare.isNotEmpty'), value: 'not_empty' },
  ]);

  const compareDictList = ref<any[]>([
    { label: t('common.compare.equal'), value: 'eq' },
    { label: t('common.compare.notEqual'), value: 'ne' },
  ]);

  const compareNumList = ref<any[]>([
    { label: t('common.compare.equal'), value: 'eq' },
    { label: t('common.compare.notEqual'), value: 'ne' },
    { label: t('common.compare.larger'), value: 'gt' },
    { label: t('common.compare.smaller'), value: 'lt' },
  ]);

  const ruleMap = {
    eq: ' == ',
    ne: ' != ',
    gt: ' > ',
    lt: ' < ',
    like: ' contain ',
    not_like: ' not contain ',
    not_empty: ' not empty ',
  };

  const tenantField = ['ticket_type'];
  const fieldTypeMap = ref({});

  function init(fields: any, defaultData: any) {
    dictOptionsMap.value = {};
    let startTime = dayjs().subtract(1, 'hour').format('YYYY-MM-DD HH:mm:ss');
    if (props.source !== 'asset' && props.source !== 'invest') {
      dateValue.value.updateTime_begin = startTime;
      dateValue.value.updateTime_end = '';
      dateValue.value.startTime_begin = '';
      dateValue.value.startTime_end = '';
    } else {
      dateValue.value.updateTime_begin = '';
      dateValue.value.updateTime_end = '';
      dateValue.value.startTime_begin = '';
      dateValue.value.startTime_end = '';
    }
    visible.value = true;
    fieldTypeMap.value = {};
    fieldList.value = [];
    for (let i in fields) {
      if (fields[i].fieldType == 'time') {
        continue;
      }
      //非租户模式下去除租户字段
      if (!getTenantMode() && tenantField.includes(fields[i].fieldValue)) {
        continue;
      }
      fieldTypeMap.value[fields[i].classFieldName] = fields[i].fieldType;
      fieldList.value.push({ label: fields[i].fieldName, value: fields[i].classFieldName });
    }
    whereModel.value.whereList = [];
    if (defaultData) {
      setDefaultData(defaultData);
    } else {
      whereModel.value.whereList.push([{ field: undefined, rule: 'eq', val: undefined, dbType: '' }]);
    }
  }

  function setDefaultData(defaultData) {
    whereModel.value = JSON.parse(defaultData);
    if (systemFilterChecked.value && whereModel.value.advanceSystemData && props.systemFilter) {
      advanceSystemData.value = whereModel.value.advanceSystemData;
    }
    if (whereModel.value.updateTimeStr) {
      const arr = whereModel.value.updateTimeStr.split(',');
      dateValue.value.updateTime_begin = arr[0] == 'null' ? '' : arr[0];
      dateValue.value.updateTime_end = arr[1] == 'null' ? '' : arr[1];
    }
    if (whereModel.value.startTimeStr) {
      const arr = whereModel.value.startTimeStr.split(',');
      dateValue.value.startTime_begin = arr[0] == 'null' ? '' : arr[0];
      dateValue.value.startTime_end = arr[1] == 'null' ? '' : arr[1];
    }
  }

  /**
   * 获取比较条件下拉选数据
   * @param item
   */
  function getRuleOptions(item) {
    const array = sourceTypeMap[props.source]['getRuleOptions'];
    if (array.indexOf(item.field) > -1) {
      //下拉选条件，只有等于和不等于
      return compareDictList.value;
    } else if (item.field && fieldTypeMap.value[item.field] == 'number') {
      //数字类型没有模糊查询
      return compareNumList.value;
    }
    return compareList.value;
  }

  function ruleChange(value) {
    console.log(value);
    if (value.rule == 'not_empty') {
      value.val = 'empty';
    }
  }

  /**
   * 判断是否为非查询数据库的下拉选值
   * @param item
   */
  function showSelect(item) {
    const array = sourceTypeMap[props.source]['showOptionSelect'] ?? [];
    if (array.indexOf(item.field) > -1) {
      return true;
    }
    return false;
  }

  /**
   * 是否为级联下拉选
   */
  function showTreeSelect(item) {
    const array = sourceTypeMap[props.source]['showTreeSelect'] ?? [];
    if (array.indexOf(item.field) > -1) {
      return true;
    }
    return false;
  }

  /**
   * 判断是否为查询数据库的下拉选值
   * @param item
   */
  function showDictSelect(item) {
    const array = sourceTypeMap[props.source]['showDictSelect'] ?? [];
    if (array.indexOf(item.field) > -1) {
      return true;
    }
    return false;
  }

  /**
   * 选择耗时时长字段
   * @param item
   */
  function showTimeType(item) {
    const timeType = sourceTypeMap[props.source]['showTimeType'] ?? [];
    if (timeType.indexOf(item.field) > -1) {
      return true;
    }
    return false;
  }

  /**
   * 根据字段获取值的下拉选数据
   * @param item
   */
  function getValOption(item: any) {
    if (sourceTypeMap[props.source]) {
      return sourceTypeMap[props.source][item.field] ?? [];
    } else {
      return [];
    }
  }

  function fieldChange(id, item) {
    item.val = '';
    item.timeVal = '';
    item.timeValType = '';
    item.dbType = '';
    item.rule = 'eq';
    if (fieldTypeMap.value[item.field] == 'number') {
      item.dbType = 'long';
    }
    if (document.getElementById(id)) {
      document.getElementById(id)?.blur();
    }
  }

  function handleDate(start: any, end: any) {
    return (start ?? '') + ',' + (end ?? '');
  }

  /**
   * 校验并处理数据
   */
  function handleData() {
    let flag = checkedRule('rule_required,rule_number');
    if (!flag) {
      return flag;
    }

    if (props.source === 'asset' || props.source === 'invest') {
      dateValue.value.updateTime_begin = '';
      dateValue.value.updateTime_end = '';
    }

    whereModel.value.startTimeStr = handleDate(dateValue.value.startTime_begin, dateValue.value.startTime_end);
    whereModel.value.updateTimeStr = handleDate(dateValue.value.updateTime_begin, dateValue.value.updateTime_end);

    let list = whereModel.value.whereList;
    for (let i in list) {
      let list2 = list[i];
      for (let j in list2) {
        if (list2[j].timeVal && list2[j].timeValType) {
          let timeVal = list2[j].timeVal;
          let timeValType = list2[j].timeValType;
          let field = list2[j].field;
          let num = 1000;
          if (props.source == 'workHistory') {
            num = 1;
          }
          if ((props.source == 'badmssp' || props.source == 'badActors' || props.source == 'badActorsHis') && field == 'durationSeconds') {
            num = 1;
          }
          let m = { 1: 60 * num, 2: 60 * 60 * num, 3: 60 * 60 * 24 * num };
          list2[j].val = timeVal * m[timeValType];
        }
      }
    }
    return true;
  }

  const templateNameRef = ref();
  const templateId = ref('');
  let templateName = '';

  /**
   * 保存高级查询条件
   */
  function saveSearch() {
    let flag = handleData();
    if (!flag) {
      return;
    }
    if (templateId.value) {
      templateName = templateDataMap[templateId.value].name;
    } else {
      templateName = '';
    }

    templateNameRef.value.init({
      id: templateId.value,
      templateName: templateName,
      dataJson: JSON.stringify(whereModel.value),
      source: props.source,
    });
  }

  /**
   * 查询
   */
  function search() {
    let flag = handleData();
    if (!flag) {
      return;
    }
    let data = JSON.parse(JSON.stringify(whereModel.value));
    if (
      !(dateValue.value.updateTime_begin || dateValue.value.updateTime_end) &&
      !(dateValue.value.startTime_begin || dateValue.value.startTime_end) &&
      (!data.whereList || data.whereList.length == 0)
    ) {
      if (systemFilterChecked.value && props.systemFilter) {
        data.advanceSystemData = JSON.parse(JSON.stringify(advanceSystemData.value));
        emits('search', data, '');
      } else {
        data.advanceSystemData = '';
        emits('search', '', '');
      }
    } else {
      const view = handleSearchView(data);
      if (systemFilterChecked.value && props.systemFilter) {
        data.advanceSystemData = JSON.parse(JSON.stringify(advanceSystemData.value));
      } else {
        data.advanceSystemData = '';
      }
      emits('search', data, view);
    }
    visible.value = false;
  }

  function handleSearchView(data) {
    let view: any = [];
    if (data && data?.whereList?.length > 0) {
      for (let i = 0; i < data?.whereList.length; i++) {
        let view2: any = [];
        const li = data?.whereList[i];
        for (let j = 0; j < li.length; j++) {
          let d = li[j];
          view2.push(handleSelectData(d));
        }
        const str = view2.join(' AND ');
        if (data?.whereList?.length > 1) {
          view.push('(' + str + ')');
        } else {
          view.push(str);
        }
      }
    }
    return view.join(' OR ');
  }

  function handleSelectData(d) {
    console.log('handleSelectData1111111-----------',d)
    const fieldData: any = fieldList.value.filter((item) => item.value === d.field);

    let value = d.val;
    if (sourceTypeMap[props.source].showDictSelect?.includes(d.field)) {
      value = dictOptionsMap.value[d.field].filter((item) => item.value === d.val)[0].label;
    } else if (sourceTypeMap[props.source].showOptionSelect?.includes(d.field)) {
      console.log(sourceTypeMap[props.source][d.field]);
      value = sourceTypeMap[props.source][d.field].filter((item) => item.value === d.val)[0].label;
    }else if (sourceTypeMap[props.source].showTreeSelect?.includes(d.field)) {
      console.log('showTreeSelect111111111',treeLabelMap);
      value = treeLabelMap.value[d.field][d.val];
    }
    if (d.rule == 'not_empty') {
      return (fieldData[0].label ?? d.field) + ruleMap[d.rule];
    }
    return (fieldData[0].label ?? d.field) + ruleMap[d.rule] + (!!value ? '"' + value + '"' : '""');
  }

  function addGroup() {
    if (!whereModel.value.whereList) {
      whereModel.value.whereList = [];
    }
    whereModel.value.whereList.push([{ field: undefined, rule: 'eq', val: undefined, dbType: '' }]);
  }

  function add(item) {
    item.push({ field: undefined, rule: 'eq', val: undefined, dbType: '' });
  }

  defineExpose({
    init,
  });

  /**
   * 删除查询字段
   * @param item
   * @param index
   */
  function delField(item, index) {
    item.splice(index, 1);
  }

  /**
   * 删除查询块
   * @param item
   * @param index
   */
  function delGroup(item, index) {
    item.splice(index, 1);
  }

  function delAll() {
    whereModel.value.whereList = [];
    // let startTime = dayjs().subtract(1, 'hour').format("YYYY-MM-DD HH:mm:ss");
    if (props.source !== 'asset') {
      dateValue.value.updateTime_begin = '';
      dateValue.value.updateTime_end = '';
      dateValue.value.startTime_begin = '';
      dateValue.value.startTime_end = '';
    } else {
      dateValue.value.updateTime_begin = '';
      dateValue.value.updateTime_end = '';
      dateValue.value.startTime_begin = '';
      dateValue.value.startTime_end = '';
    }
    templateId.value = '';
  }

  const templateOption = ref<any[]>([]);
  let templateDataMap = {};

  loadTemplateOption();

  /**
   * 加载高级查询模板
   */
  function loadTemplateOption() {
    searchTemplateList({ source: props.source }).then((data) => {
      console.log(data);
      templateDataMap = {};
      let list: any = [];
      for (let i in data) {
        list.push({
          label: data[i].name,
          text: data[i].name,
          value: data[i].id,
        });
        templateDataMap[data[i].id] = data[i];
      }
      templateOption.value = list;
    });
  }

  /**
   * 保存模板后重新加载数据
   */
  function refTemplateData() {
    loadTemplateOption();
  }

  /**
   * 切换查询模板
   * @param value
   */
  function templateChange(value) {
    console.log(value);
    templateId.value = value;
    if (!value) {
      return;
    }
    let dataJson = templateDataMap[value].dataJson;
    console.log(dataJson);
    if (dataJson) {
      setDefaultData(dataJson);
    }
  }

  /**
   * 删除
   * @param value
   */
  function delOption(value) {
    console.log(value);
    Modal.confirm({
      title: t('common.delConfirmText'),
      content: t('common.delContent'),
      okText: t('common.okText'),
      cancelText: t('common.cancelText'),
      onOk: () => {
        deleteOne({ id: value }, () => {
          templateId.value = '';
          refTemplateData();
        });
      },
    });
  }
</script>

<style scoped lang="less">
  /deep/.ant-picker {
    min-width: auto !important;
  }
  .table_search {
    /deep/.ant-input {
      padding: 8px 12px !important;
      line-height: 16px !important;
      border: 0 !important;
    }

    .border_bottom {
      border-bottom: 1px solid @border-color;
    }

    .search_flex {
      display: flex;
      padding-bottom: 16px;
      margin-bottom: 8px;
      gap: 8px;
      .search_flex_1 {
        flex: 1;
        display: flex;
        gap: 8px;
      }

      .search_flex_2 {
        flex: 2;
        display: flex;
      }
    }

    .div_and {
      padding: 8px 8px 8px 12px;
      border: 1px solid @border-color;
      border-left: 4px solid @m-color;
      position: relative;
      border-radius: 4px;

      .del_img {
        position: absolute;
        top: -8px;
        right: -8px;
        cursor: pointer;
        color: rgba(255, 255, 255, 0.8);
      }
    }

    .div_or {
      position: relative;
    }
  }
  .system-filter {
    border-radius: 4px;
    display: flex;
    flex-direction: row;
  }
</style>
