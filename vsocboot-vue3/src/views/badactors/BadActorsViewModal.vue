<template>
  <div class="padding16">
    <h2 style="position: relative">
      <span @click="back" style="cursor: pointer">
        <Icon icon="ant-design:left-outlined" style="margin-right: 5px; cursor: pointer" />
        {{ tp('badActor') }} {{ ip }}
      </span>
      <div style="position: absolute; right: 20px; font-size: 16px; top: 0px">
        <a-button
          @click="
            queryTime = '';
            refContent();
          "
          style="margin-right: 10px"
          v-if="queryTime"
        >
          {{ tp('all') }}
        </a-button>
        <!-- <a-dropdown
          :trigger="['click']" v-if="(hasPermission('investigation:add') ||
                            hasPermission('investigation:join')) && !noInvestigate">
          <a-button @click.prevent="loadInvestigation">
            {{t('common.Investigation')}}
          </a-button>
          <template #overlay>
            <a-menu>
              <a-menu-item key="0" @click="showAddInvestigation"
                           v-if="hasPermission('investigation:add')">
                <Icon icon="ant-design:plus-outlined"/>
                {{ t('routes.riskLogs.add') }}
              </a-menu-item>
              <a-menu-divider/>
              <a-menu-item v-for="item in investigationData" :key="item.id"
                           @click="addInvestigation(item)">
                <span>{{ item['investigation'] }}</span>
              </a-menu-item>
              <a-menu-divider/>
              <a-menu-item key="1" @click="showMoreInvestigation">
                <span>{{ t('routes.riskLogs.addMore') }}</span>
              </a-menu-item>
            </a-menu>
          </template>
        </a-dropdown> -->
      </div>
    </h2>

    <a-row>
      <a-col :span="4">
        <div style="height: calc(50vh - 57px); overflow: auto; overflow-x: hidden">
          <div ref="chartParent">
            <div ref="leftChart" style="height: 100%"></div>
          </div>
        </div>
        <div>
          <a-tabs>
            <a-tab-pane key="1_1" :tab="tp('LogSource')">
              <div style="padding: 0 15px 0 5px">
                <a-input v-model:value="searchVal" :placeholder="t('common.inputText')" />
                <!-- a-input-search -->
              </div>
              <div v-for="(item, index) in IpData1" :key="'tab1' + index" style="padding-right: 10px">
                <div
                  class="border2 chooseDiv"
                  style="margin: 5px; padding: 5px 10px"
                  v-show="item?.deviceName?.indexOf(searchVal) > -1 || item?.fromIp?.indexOf(searchVal) > -1"
                  @click="choose($event, 'fromIp', item)"
                >
                  <div class="content_div">{{ item['deviceName'] }}</div>
                  <div class="content_div">{{ item['fromIp'] }}</div>
                </div>
              </div>
            </a-tab-pane>
            <a-tab-pane key="1_2" :tab="tp('DestinationIP')">
              <div style="padding: 0 15px 0 5px">
                <a-input v-model:value="searchVal2" :placeholder="t('common.inputText')" />
                <!-- a-input-search -->
              </div>
              <div v-for="(item, index) in IpData2" :key="'tab1' + index" style="padding-right: 10px">
                <div
                  class="border2 chooseDiv"
                  style="margin: 5px; padding: 5px 10px"
                  v-show="item?.dstIp?.indexOf(searchVal2) > -1"
                  @click="choose($event, 'dstIp', item)"
                >
                  <div class="content_div">{{ item['dstIp'] }}</div>
                </div>
              </div>
            </a-tab-pane>
            <a-tab-pane key="1_3" :tab="tp('AttackName')">
              <div style="padding: 0 15px 0 5px">
                <a-input v-model:value="searchVal3" :placeholder="t('common.inputText')" />
                <!-- a-input-search -->
              </div>
              <div v-for="(item, index) in IpData3" :key="'tab1' + index" style="padding-right: 10px">
                <div
                  class="border2 chooseDiv"
                  style="margin: 5px; padding: 5px 10px"
                  v-show="item?.eventType?.indexOf(searchVal3) > -1"
                  @click="choose($event, 'eventType', item)"
                >
                  <div class="content_div">{{ item['eventType'] }}</div>
                </div>
              </div>
            </a-tab-pane>
          </a-tabs>
        </div>
      </a-col>
      <a-col :span="20" class="left_border" style="overflow: auto">
        <div style="height: 200px" ref="chart1Ref"></div>
        <div style="padding: 10px 20px; min-width: 1510px" class="border_div">
          <div>
            <h3 style="display: inline-block; width: 250px">{{ tp('badActor') }}</h3>
            <h3 style="display: inline-block; padding-left: 50px; width: 280px" v-if="isAdministrator()">{{ t('common.tenant') }}</h3>
            <h3 style="display: inline-block; padding-left: 50px; width: 280px">
              {{ tp('LogSource') }}
            </h3>
            <h3 style="display: inline-block; padding-left: 50px; width: 280px">
              {{ tp('DestinationIP') }}
            </h3>
            <h3 style="display: inline-block; padding-left: 50px; width: 360px">
              <div style="display: inline-block; width: 210px">{{ tp('AttackName') }}</div>
              <div style="display: inline-block; width: 100px">{{ tp('AttackCount') }}</div>
            </h3>
          </div>

          <div v-for="(srcIp, index) in IpData" :key="'ip' + index">
            <div class="parent_div">
              <div class="src_div border" @click="toRiskEvent({ srcIp: srcIp?.srcIp })">
                <div style="position: absolute">
                  <img src="../../assets/images/badActors/srcIp.png" width="24" />
                </div>
                <div style="margin-left: 30px">
                  <div class="content_div">{{ srcIp['srcIp'] }}</div>
                  <div class="content_div"
                    ><span class="severity_span">{{ severity }}</span></div
                  >
                  <div class="content_div">{{ tp('Score') }} : {{ threatScore }}</div>
                </div>
              </div>
              <div class="right_bottom_border" style="top: 36px"></div>
            </div>
            <div class="parent_div" style="width: 1200px">
              <div class="parent_div" v-for="(tenant, tenantIndex) in srcIp?.tenantList" :key="'tenant' + tenantIndex" :id="'tenant_' + tenantIndex">
                <div class="parent_div" v-if="isAdministrator()">
                  <div class="left_top_border" style="top: 36px">
                    <div class="div_end"></div>
                  </div>
                  <div
                    v-if="tenantIndex < srcIp?.tenantList.length - 1"
                    :id="'tenant_' + tenantIndex + '_l'"
                    :style="{ height: calcHeight('tenant_' + tenantIndex), top: '36px' }"
                    class="left_left_border"
                  >
                  </div>
                  <!--                  <div v-else-->
                  <!--                       :style="{ height: '32px',top:'20px'}"-->
                  <!--                       class="left_left_border">-->
                  <!--                  </div>-->
                  <div class="src_div border fromIp" style="width: 180px">
                    <!--                    <div style="position: absolute;">-->
                    <!--                      <img src="../../assets/images/badActors/fromIp.png" width="24"/>-->
                    <!--                    </div>-->
                    <div style="margin-left: 30px">
                      <div class="content_div">
                        {{ t('common.tenant') }}
                      </div>
                      <div class="content_div">
                        {{ tenant?.tenantName }}
                      </div>
                    </div>
                  </div>
                  <div class="right_bottom_border" v-if="tenant?.fromIpList.length > 0" style="top: 36px"></div>
                </div>
                <div class="fromIp_div">
                  <div class="parent_div" v-for="(fromIp, index2) in tenant?.fromIpList" :key="'fromIp' + index2" :id="'from_' + index2">
                    <div class="parent_div">
                      <div class="left_top_border" style="top: 36px">
                        <div class="div_end"></div>
                      </div>
                      <div
                        v-if="index2 < tenant?.fromIpList.length - 1"
                        :style="{ height: calcFromHeight('from_' + index2), top: isAdministrator() ? '36px' : '36px' }"
                        :id="'from_' + index2 + '_l'"
                        class="left_left_border"
                      >
                      </div>
                      <!--                      <div v-else :style="{height: '16px',top:isAdministrator()?'36px':'36px'}"-->
                      <!--                           class="left_left_border">-->
                      <!--                      </div>-->
                      <div class="src_div border fromIp" style="width: 180px" @click="toRiskEvent({ srcIp: srcIp?.srcIp, fromIp: fromIp?.fromIp })">
                        <div style="position: absolute">
                          <img src="../../assets/images/badActors/fromIp.png" width="24" />
                        </div>
                        <div style="margin-left: 30px">
                          <div class="content_div">{{ fromIp?.deviceName }}</div>
                          <div class="content_div">{{ fromIp?.fromIp }}</div>
                        </div>
                      </div>
                      <div class="right_bottom_border" v-if="fromIp?.dstIpList.length > 0" style="top: 36px"></div>
                    </div>
                    <div class="dst_div">
                      <div class="parent_div" v-for="(dstIp, index3) in fromIp?.dstIpList" :key="'dstIp' + index3">
                        <div class="parent_div">
                          <div class="left_top_border">
                            <div class="div_end"></div>
                          </div>
                          <div
                            v-if="index3 == 0 || index3 < fromIp?.dstIpList.length - 1"
                            :style="{ height: fromIp?.dstIpList.length == 1 ? '16px' : dstIp.height + 'px', top: '20px' }"
                            class="left_left_border"
                          ></div>
                          <div
                            class="src_div border dstIp"
                            style="margin-bottom: 10px; width: 180px"
                            @click="toRiskEvent({ srcIp: srcIp?.srcIp, fromIp: fromIp?.fromIp, dstIp: dstIp?.dstIp })"
                          >
                            <div style="position: absolute">
                              <img src="../../assets/images/badActors/dstIp.png" width="24" />
                            </div>
                            <div style="margin-left: 30px">
                              <div class="content_div">{{ dstIp?.dstIp }}</div>
                            </div>
                          </div>
                          <div class="right_bottom_border" v-if="dstIp?.eventTypeList.length > 0"></div>
                        </div>
                        <div class="eventType_div">
                          <div class="parent_div" v-for="(eventType, index4) in dstIp?.eventTypeList" :key="'eventType' + index4">
                            <div class="parent_div">
                              <div class="left_top_border">
                                <div class="div_end"></div>
                              </div>
                              <div v-if="index4 < dstIp?.eventTypeList.length - 1" :style="{ height: '100%' }" class="left_left_border"></div>
                              <div
                                class="src_div border eventType"
                                style="width: 310px"
                                @click="
                                  toRiskEvent({ srcIp: srcIp?.srcIp, fromIp: fromIp?.fromIp, dstIp: dstIp?.dstIp, eventType: eventType?.eventType })
                                "
                              >
                                <div style="display: flex">
                                  <div class="content_div" style="width: 200px">
                                    <span class="severity_span ant-table-cell-ellipsis" style="max-width: 200px; display: inline-block">
                                      {{ eventType?.eventType }}
                                    </span>
                                  </div>
                                  <div class="content_div left_border2" style="width: 100px; text-align: center">
                                    {{ eventType?.num }}
                                  </div>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </a-col>
    </a-row>
  </div>

  <InvestigationListModal ref="registerRiskLogsModal" :eventId="LogId" type="4" :socTenantId="inveTenantId" />
  <a-modal v-model:visible="inveVisible" :title="t('common.confirm')" @ok="saveToInve" :maskClosable="false">
    <a-row style="padding: 0 24px">
      <a-col :span="24">
        {{ t('routes.riskLogs.addInvestigationPrompt') }}
      </a-col>
      <a-col :span="24">
        <a-form class="antd-modal-form" autocomplete="off" :layout="formLayout">
          <a-form-item :label="t('routes.badActors.conclusion')">
            <a-textarea v-model:value="conclusion" />
          </a-form-item>
        </a-form>
      </a-col>
    </a-row>
  </a-modal>

  <BadactorEventPage ref="BadactorEventPageRef"/>
</template>

<script lang="ts" setup>
  import { useRouter } from 'vue-router';
  import { nextTick, onMounted, Ref, ref } from 'vue';
  import { useECharts } from '/@/hooks/web/useECharts';
  import { loadBadActorsEventDateCount, loadBarChart, loadEventSecurityByBadActorsIp } from '/@/views/badactors/BadActors.api';
  import { EChartsOption } from 'echarts';
  import dayjs from 'dayjs';
  import { loadInvestigationTop5 } from '/@/views/investigation/InvestigationVO.api';
  import { useUserStore } from '/@/store/modules/user';
  import { saveOrUpdate2 } from '/@/views/risk/InvestigationRiskEventlogs.api';
  import { formLayout } from '/@/settings/designSetting';
  import InvestigationListModal from '/@/views/investigation/modules/InvestigationListModal.vue';
  import { useI18n } from '/@/hooks/web/useI18n';
  import { isAdministrator } from '/@/utils/auth';
  import { usePermission } from '/@/hooks/web/usePermission';
  import BadactorEventPage from "/@/views/badactors/modules/BadactorEventPage.vue";

  const props = defineProps({
    closeDrawer: Function,
    noInvestigate: Boolean,
  });

  const { hasPermission } = usePermission();
  const router = useRouter();
  const { t } = useI18n();
  function tp(name) {
    return t('routes.badActors.' + name);
  }
  let dataJsonStr = sessionStorage.getItem('BadActorsData');
  console.log(dataJsonStr);
  let dataJson: any = {};
  const inveTenantId = ref('');
  if (dataJsonStr) {
    dataJson = JSON.parse(dataJsonStr);
    if (isAdministrator()) {
      let tenantList = dataJson.tenantList;
      let tenantIds: any = [];
      let ids: any = {};
      //多租户数据，需要把bad actor和租户id绑定，确定选择哪个租户的调查后，把对应租户的badactor存到对应调查里
      for (let i in tenantList) {
        tenantIds.push(tenantList[i].socTenantId);
        ids[tenantList[i].socTenantId] = tenantList[i].id;
      }
      inveTenantId.value = tenantIds.join(',');
    } else {
      inveTenantId.value = dataJson.socTenantId;
    }
  }
  const ip = ref(dataJson?.ip);
  const socTenantId = ref(dataJson?.socTenantId);
  const severity = ref<any>(dataJson?.severity);
  const threatScore = ref<any>(dataJson?.threatScore);
  const searchVal = ref('');
  const searchVal2 = ref('');
  const searchVal3 = ref('');
  const queryTime = ref('');
  onMounted(() => {
    init();
  });

  function init() {
    console.log(ip.value, socTenantId.value, severity.value, threatScore.value);
    loadEventSecurityByBadActorsIpData();
    loadEchart();
    loadLeftChart();
  }

  function refContent() {
    loadEventSecurityByBadActorsIpData();
    loadEchart();
  }

  function choose(event, type, value) {
    console.log(event, type, value);
    let element = event.target as HTMLElement;
    findElement(element, value);
    findAllElement(type, value);
  }

  function calcHeight(id) {
    nextTick(() => {
      let el = document.getElementById(id);
      let el2 = document.getElementById(id + '_l');
      if (el2) {
        el2.style.height = el?.clientHeight + 'px';
      }
    });
  }

  function calcFromHeight(id) {
    nextTick(() => {
      if (id == 'from_') {
      }
      let el = document.getElementById(id);
      let el2 = document.getElementById(id + '_l');
      if (el2) {
        el2.style.height = (el?.clientHeight ?? 0) + 'px';
      }
    });
  }

  function findAllElement(type, value) {
    let data2: any = [];
    let data = IpDataOld.value;
    for (let i in data) {
      data2[i] = {};
      data2[i].srcIp = data[i].srcIp;
      let tenantList = data[i].tenantList;
      let tenantList2: any = [];
      for (let i1 in tenantList) {
        let fromIpList2: any = [];
        let fromIpList = tenantList[i1].fromIpList;
        for (let i2 in fromIpList) {
          if (!IpData1.value[fromIpList[i2].fromIp].selected) {
            continue;
          }
          let dstIpList2: any = [];
          let dstIpList = fromIpList[i2].dstIpList;
          for (let i3 in dstIpList) {
            if (!IpData2.value[dstIpList[i3].dstIp].selected) {
              continue;
            }

            let eventTypeList2: any = [];
            let eventTypeList = dstIpList[i3].eventTypeList;
            for (let i4 in eventTypeList) {
              // eventType: "Deserialization"
              if (!IpData3.value[eventTypeList[i4].eventType].selected) {
                continue;
              }
              eventTypeList2.push(eventTypeList[i4]);
            }
            dstIpList2.push({
              dstIp: dstIpList[i3].dstIp,
              eventTypeList: eventTypeList2,
            });
          }
          fromIpList2.push({
            deviceName: fromIpList[i2].deviceName,
            fromIp: fromIpList[i2].fromIp,
            dstIpList: dstIpList2,
          });
        }
        tenantList2.push({
          socTenantId: tenantList[i1].socTenantId,
          tenantName: tenantList[i1].tenantName,
          fromIpList: fromIpList2,
        });
      }
      data2[i].tenantList = tenantList2;
    }
    for (let i in data2) {
      let tenantList = data2[i].tenantList;
      for (let i1 in tenantList) {
        let fromIpList = tenantList[i1].fromIpList;
        let tenantHeight = 0;
        for (let i2 in fromIpList) {
          let dstIpList = fromIpList[i2].dstIpList;
          fromIpList[i2].num = dstIpList.length;
          let height = 0;
          for (let i3 in dstIpList) {
            let eventTypeList = dstIpList[i3].eventTypeList;
            if (eventTypeList.length > 0) {
              dstIpList[i3].height = eventTypeList.length * 52;
              height += dstIpList[i3].height;
            } else {
              height += 62;
            }
          }
          fromIpList[i2].height = height + 16;
          tenantHeight += fromIpList[i2].height - 16;
          // //没有目的ip，只要fromid的高度
          // if (dstIpList.length == 0) {
          //   tenantHeight += 82 + 10
          // } else {
          //   tenantHeight += height + 30
          // }
        }
        if (fromIpList.length > 0) {
          tenantHeight += 30 * fromIpList.length;
        }
        tenantList[i1].height = tenantHeight;
      }
    }

    IpData.value = data2;
  }

  const BadactorEventPageRef = ref();

  function toRiskEvent(param) {
    console.log(param);
    param['updateTimeStr'] = dataJson.attackFirstTime + ',' + dataJson.attackLatestTime;

    BadactorEventPageRef.value.open(param);


    // router.push({
    //   path: '/aggregationRiskEventView/RiskEventViewList',
    //   // path: "/aggregationriskeventsecurity/AggregationRiskEventSecurityList",
    //   query: param,
    // });
  }

  function findElement(element: HTMLElement | null | undefined, value) {
    if (element) {
      let className = element.className;
      console.log(className);
      if (className == 'border2') {
        element.className = 'border2 chooseDiv';
        value.selected = true;
      } else if (className == 'border2 chooseDiv') {
        element.className = 'border2';
        value.selected = false;
      } else {
        findElement(element?.parentElement, value);
      }
    }
  }

  const chart1Ref = ref<HTMLDivElement | null>(null);
  const { setOptions: chart1Options } = useECharts(chart1Ref as Ref<HTMLDivElement>);

  function loadEchart() {
    let start = dataJson.attackFirstTime.substring(0, 10);
    let end = dataJson.attackLatestTime.substring(0, 10);
    let status = 1;
    if (queryTime.value) {
      start = queryTime.value + ' 00:00:00';
      end = queryTime.value + ' 23:59:59';
      status = 2;
    }
    loadBarChart({ ip: ip.value, socTenantId: socTenantId.value, startDate: start, endDate: end, status: status }).then((data) => {
      console.log(data);
      let xAxisData: any = [];
      let seriesData: any = [];
      for (let i in data) {
        xAxisData.push(data[i].eventDate);
        seriesData.push(data[i].eventNum);
      }
      let option: EChartsOption = {
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow',
          },
          confine: true,
        },
        grid: {
          top: '20',
          left: '30',
          right: '50',
          bottom: '60',
          containLabel: true,
        },
        yAxis: [
          {
            type: 'value',
          },
        ],
        xAxis: [
          {
            type: 'category',
            data: xAxisData,
            axisTick: {
              alignWithLabel: true,
            },
          },
        ],
        dataZoom: [
          {
            type: 'inside',
            start: 0,
            end: 100,
          },
          {
            start: 0,
            end: 100,
          },
        ],
        series: [
          {
            type: 'bar',
            barWidth: 20,
            data: seriesData,
          },
        ],
      };
      chart1Options(option);
    });
  }

  const IpDataOld = ref<any[]>([]);
  const IpData = ref<any[]>([]);
  const IpDataTenant = ref<any>({});
  const IpData1 = ref<any>({});
  const IpData2 = ref<any>();
  const IpData3 = ref<any>();

  function loadEventSecurityByBadActorsIpData() {
    let start = '';
    let end = '';
    let status = 1;
    if (queryTime.value) {
      start = queryTime.value + ' 00:00:00';
      end = queryTime.value + ' 23:59:59';
      status = 2;
    }

    loadEventSecurityByBadActorsIp({
      ip: ip.value,
      socTenantId: socTenantId.value,
      startDate: start,
      endDate: end,
      status: status,
    }).then((data) => {
      console.log(data);
      let map = {};
      let map1 = {};
      let map2 = {};
      let map3 = {};
      for (let i in data) {
        let tenantList = data[i].tenantList;
        for (let i1 in tenantList) {
          map[tenantList[i1].socTenantId] = {
            tenantName: tenantList[i1].tenantName,
            selected: true,
          };
          let fromIpList = tenantList[i1].fromIpList;
          let tenantHeight = 0;
          for (let i2 in fromIpList) {
            map1[fromIpList[i2].fromIp] = {
              fromIp: fromIpList[i2].fromIp,
              deviceName: fromIpList[i2].deviceName,
              selected: true,
            };
            let dstIpList = fromIpList[i2].dstIpList;
            fromIpList[i2].num = dstIpList.length;
            let height = 0;
            for (let i3 in dstIpList) {
              map2[dstIpList[i3].dstIp] = { dstIp: dstIpList[i3].dstIp, selected: true };
              let eventTypeList = dstIpList[i3].eventTypeList;
              dstIpList[i3].height = eventTypeList.length * 52;
              height += dstIpList[i3].height;
              for (let i4 in eventTypeList) {
                map3[eventTypeList[i4].eventType] = {
                  eventType: eventTypeList[i4].eventType,
                  selected: true,
                };
              }
            }
            fromIpList[i2].height = height + 16;
            tenantHeight += fromIpList[i2].height - 16;
          }
          tenantList[i1].height = tenantHeight;
        }
      }
      console.log(map1, map2, map3);
      IpData.value = data;
      console.log('data', data);
      IpDataOld.value = data;
      IpDataTenant.value = map;
      IpData1.value = map1;
      IpData2.value = map2;
      IpData3.value = map3;
    });
  }

  const chartParent = ref();
  const leftChart = ref<HTMLDivElement | null>(null);

  function loadLeftChart() {
    console.log(dataJson.attackFirstTime);
    console.log(dataJson.attackLatestTime);
    let start = dataJson.attackFirstTime.substring(0, 10);
    let end = dataJson.attackLatestTime.substring(0, 10);
    loadBadActorsEventDateCount({ srcIp: ip.value, socTenantId: socTenantId.value, startDate: start, endDate: end }).then((data) => {
      console.log(data);
      let seriesData: any = [];
      let min = 0;
      let max = 100;
      for (let i in data) {
        seriesData.push([data[i].eventDate, data[i].eventNum]);
        if (max < data[i].eventNum) {
          max = data[i].eventNum;
        }
      }
      let num = dayjs(end).diff(dayjs(start), 'day');
      num = Math.ceil(num / 7);
      chartParent.value.style.height = 42 * (num + 2) + 80 + 'px';
      const { setOptions: leftChartOptions, getInstance } = useECharts(leftChart as Ref<HTMLDivElement>);
      let option: any = {
        tooltip: {
          position: 'top',
          formatter: function (params) {
            return params.data[0] + ' : ' + params.data[1];
          },
        },
        visualMap: {
          min: min,
          max: max,
          calculable: true,
          orient: 'vertical',
          show: false,
          inRange: {
            color: ['rgba(247, 85, 85, 0.1)', 'rgba(247, 85, 85, 1)'],
          },
        },
        calendar: [
          {
            orient: 'vertical',
            top: 60,
            left: 40,
            right: 24,
            cellSize: [40, 40],
            range: [start, end],
            itemStyle: {
              color: '#06080C',
              borderWidth: 1,
              borderColor: '#333333',
            },
            splitLine: {
              lineStyle: {
                color: '#333333',
              },
            },
            monthLabel: {
              show: true,
              nameMap: 'EN',
            },
            dayLabel: {
              show: true,
              firstDay: 1,
              nameMap: ['Sun', 'Mon', 'Tue', 'Wen', 'Thu', 'Fri', 'Sat'],
            },
            yearLabel: { show: false },
          },
        ],

        series: [
          {
            type: 'heatmap',
            coordinateSystem: 'calendar',
            calendarIndex: 0,
            data: seriesData,
          },
        ],
      };
      leftChartOptions(option);
      getInstance()?.on('click', function (params: any) {
        let time = params.value[0];
        console.log(time);
        queryTime.value = time;
        refContent();
      });
    });
  }

  function back() {
    let json = sessionStorage.getItem('inRiskEvent_1');
    if (json) {
      sessionStorage.removeItem('inRiskEvent_1');
      sessionStorage.setItem('inRiskEvent_2', json);
      //获取详情页调查的id和名称
      let jsonStr = sessionStorage.getItem('investigationInfoJson');
      if (jsonStr) {
        let query = JSON.parse(jsonStr);
        router.push({
          path: '/situationPerception/situationNewPerception',
          query: query,
        });
      }
      return;
    }
    props.closeDrawer && props.closeDrawer();
    // router.push("/badactors/BadActorsList")
  }

  const investigationData = ref<any[]>([]);
  const LogId = ref<string>(''); //记录当前点击的日志id
  const loadInvestigation = () => {
    loadInvestigationTop5({ socTenantIds: inveTenantId.value.split(',') }).then((result) => {
      console.log(result);
      let list = result.records;
      investigationData.value = list;
    });
  };

  function addInvestigation(data): void {
    inveId = data.id;
    //todo 有问题，等新调查需求出来后在做处理
    if (isAdministrator()) {
      //运维管理员添加租户调查，需要根据选择的调查属于哪个租户的，吧对应租户的bad actor填进去
      let tenantList = dataJson.tenantList;
      for (let i in tenantList) {
        if (tenantList[i].socTenantId == data.socTenantId) {
          LogId.value = tenantList[i].id;
          break;
        }
      }
    } else {
      LogId.value = dataJson.id;
    }

    inveVisible.value = true;
  }

  const inveVisible = ref(false);
  let inveId = '';
  const conclusion = ref('');
  const userStore = useUserStore();
  const saveToInve = () => {
    if (addInveFlag) {
      addInveFlag = false;
      let param = {
        eventId: LogId.value,
        socTenantId: inveTenantId.value,
        conclusion: conclusion.value,
        conclusionBy: userStore.userInfo?.username,
        type: '4',
      };
      sessionStorage.setItem('addInvestigationParam', JSON.stringify(param));
      router.push({
        path: '/investigation/modules/InvestigationNewModal',
      });
      return;
    }

    saveOrUpdate2(
      {
        investigationId: inveId,
        eventId: LogId.value,
        conclusion: conclusion.value,
        conclusionBy: userStore.userInfo?.username,
        type: '4',
      },
      false
    ).then(() => {
      inveVisible.value = false;
      conclusion.value = '';
    });
  };
  const registerRiskLogsModal = ref();

  function showMoreInvestigation() {
    if (isAdministrator()) {
      let tenantList = dataJson.tenantList;
      let ids: any = {};
      //多租户数据，需要把bad actor和租户id绑定，确定选择哪个租户的调查后，把对应租户的badactor存到对应调查里
      for (let i in tenantList) {
        ids[tenantList[i].socTenantId] = tenantList[i].id;
      }
      LogId.value = JSON.stringify(ids);
    } else {
      LogId.value = dataJson.id;
    }

    registerRiskLogsModal.value.visible = true;
  }

  let addInveFlag = false;
  const showAddInvestigation = () => {
    if (isAdministrator()) {
      let tenantList = dataJson.tenantList;
      let ids: any = {};
      //多租户数据，需要把bad actor和租户id绑定，确定选择哪个租户的调查后，把对应租户的badactor存到对应调查里
      for (let i in tenantList) {
        ids[tenantList[i].socTenantId] = tenantList[i].id;
      }
      LogId.value = JSON.stringify(ids);
    } else {
      LogId.value = dataJson.id;
    }
    addInveFlag = true;
    inveVisible.value = true;
  };
</script>

<style lang="less" scoped>
  .parent_div {
    display: inline-block;
    vertical-align: top;
    position: relative;
  }

  .src_div {
    display: inline-block;
    width: 200px;
    padding: 5px 10px;
    line-height: 30px;
    margin-bottom: 10px;
    vertical-align: top;
    cursor: pointer;
  }

  .dst_div {
    width: 640px;
    display: inline-block;
    vertical-align: top;
  }

  .fromIp_div {
    width: 920px;
    display: inline-block;
    vertical-align: top;
  }

  .eventType_div {
    width: 360px;
    display: inline-block;
    vertical-align: top;
  }

  .border {
    border: 1px solid #555555;
  }

  .border2 {
    border: 1px solid @border-color;
  }

  .severity_span {
    background-color: rgba(@red-color, 0.3);
    color: @red-color;
    padding: 0 5px;
    border-radius: 3px;
  }

  .content_div {
    height: 30px;
    line-height: 30px;
  }

  .right_bottom_border {
    display: inline-block;
    width: 50px;
    border-bottom: 1px solid #555555;
    position: relative;
    vertical-align: top;
    top: 20px;
  }

  .left_top_border {
    display: inline-block;
    width: 40px;
    margin-right: 10px;
    border-top: 1px solid #555555;
    position: relative;
    top: 20px;
    height: 32px;
  }

  .left_left_border {
    display: inline-block;
    width: 0px;
    position: absolute;
    border-left: 1px solid #555555;
    height: 100%;
    left: 0;
    bottom: -20px;
  }

  .div_end {
    position: absolute;
    right: -10px;
    top: -5px;
    border-top: 5px solid transparent;
    border-bottom: 5px solid transparent;
    border-left: 10px solid #555555;
  }

  .border_div div {
    box-sizing: border-box;
  }

  .left_border {
    border-left: 1px solid @border-color;
  }

  .left_border2 {
    border-left: 1px solid #555555;
  }

  .chooseDiv {
    border-color: @m-text-color;
    border-width: 1px;
    position: relative;
  }

  .chooseDiv2 {
    border-color: @m-text-color;
    border-width: 1px;
    position: relative;
  }

  .chooseDiv::after {
    content: '';
    background-image: url('../../assets/images/badActors/choose.png');
    width: 20px;
    height: 20px;
    position: absolute;
    background-size: 100% 100%;
    right: -10px;
    top: -10px;
  }
</style>
