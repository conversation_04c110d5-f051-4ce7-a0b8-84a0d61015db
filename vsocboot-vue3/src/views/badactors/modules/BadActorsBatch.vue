<template>
  <a-modal v-model:visible="visible" footer="" :title="title" style="width: 900px;"
           :destroyOnClose="true" :maskClosable="false" @cancel="closeModal">
    <div style="padding-top: 12px;">

      <div style="width: 400px;margin: auto;">
        <a-steps :current="step">
          <a-step :title="t('routes.badActors.chooseTenant')"/>
          <a-step :title="stepTitle"/>
        </a-steps>
      </div>


      <div v-show="step == 1" style="padding-bottom: 16px;">
        <div style="padding: 12px 16px;text-align: right;">
          <a-button type="primary" @click="next" v-if="dataSource.length > 0">
            {{ t('common.nextText') }}
          </a-button>
        </div>
        <a-table :dataSource="dataSource" :columns="columns" class="tenant_table"
                 :pagination="false">
          <template #switchStatus="{ record }">
            <a-switch v-model:checked="record.check"
                      :loading="record?.loading" size="small"/>
          </template>
        </a-table>

      </div>
      <div v-show="step == 2">
        <div v-if="modelType == 1">
          <BasicTable @register="registerTable" :rowSelection="rowSelection as any">

            <template #form-formHeader>
              <a-input :allowClear="true" :placeholder="t('routes.sysUser.username')"
                       @change="doReload" preIcon="ant-design:search-outlined">
                <template #suffix>
                  <Icon icon="ant-design:search-outlined"/>
                </template>
              </a-input>
            </template>
            <template #form-formFooter>
              <div class="flex flex-row gap-8px  px-16px">
                <a-button @click="step = 1"  >
                  {{ t('common.prevText') }}
                </a-button>
                <a-button type="primary" @click="handleSubmit">
                  {{ t('common.okText') }}
                </a-button>
              </div>
            </template>
            <template #userInfo="{ record }">
              <UserName :record="record"/>
            </template>
          </BasicTable>
        </div>
        <div v-else-if="modelType == 2">
          <div class="flex flex-row gap-8px px-16px">
            <a-button @click="step = 1"  >
              {{ t('common.prevText') }}
            </a-button>
            <a-button type="primary" @click="handleSubmit2">
              {{ t('common.okText') }}
            </a-button>
          </div>
          <div style="width: 300px;margin: auto;padding-bottom: 16px;" class="triage_div_m">
            <a-form ref="triageFormRef" :model="triageModel" :layout="formLayout">
              <a-form-item
                label="" name="triageStatus"
                :rules="[{required: true,message: 'Please triage'}]">
                <a-radio-group v-model:value="triageModel.triageStatus" name="triageStatus">
                  <a-radio value="1" :style="radioStyle">
                    <div class="true_radio">
                      <i class="ax-com-Danger soc"></i>
                      {{ t('routes.RiskEventLogView.true') }}
                    </div>
                  </a-radio>
                  <a-radio value="3" :style="radioStyle">
                    <div class="other_radio">
                      <Icon icon="ant-design:question-circle-outlined"></Icon>
                      {{ t('routes.RiskEventLogView.other') }}
                    </div>
                  </a-radio>
                  <a-radio value="2" :style="radioStyle">
                    <div class="false_radio">
                      <i class="ax-com-Success soc"></i>
                      {{ t('routes.RiskEventLogView.false') }}
                    </div>
                  </a-radio>
                </a-radio-group>

              </a-form-item>
            </a-form>
          </div>

        </div>

      </div>
    </div>
  </a-modal>
</template>

<script lang="ts" setup>

import {defineEmits, reactive, ref, toRaw, unref} from "vue";
import {useI18n} from "/@/hooks/web/useI18n";
import {formLayout} from "/@/settings/designSetting";
import UserName from "/@/components/vsoc/UserName.vue";
import {BasicColumn, BasicTable, useTable} from "/@/components/Table";
import {listByTenant} from "/@/views/system/user/user.api";
import {userColumns} from "/@/views/system/role/role.data";
import {loadTenantBadList} from "/@/views/badactors/BadActors.api";

const emit = defineEmits(['assign', 'triage'])
const triageModel = ref({
  triageStatus: null,
})
const step = ref(1);
const title = ref("");
const stepTitle = ref("")
const triageFormRef = ref()
const {t} = useI18n();
const visible = ref(false)
const radioStyle = reactive({
  display: 'flex',
  height: '30px',
  lineHeight: '30px',
});
const modelType = ref(1)
const dataSource = ref<any[]>([])
const columns: BasicColumn[] = [
  {
    title: t('common.tenant'),
    dataIndex: 'socTenantName'
  },
  {
    title: t('routes.badActors.Score'),
    dataIndex: 'threatScore'
  },
  {
    title: t('common.Action'),
    dataIndex: 'switchStatus',
    slots: {customRender: 'switchStatus'}
  },
]

const AssignType = ref("");

function openModal(data, type, assignType?) {
  console.log(data)
  AssignType.value = assignType;
  modelType.value = type;
  if (type == 1) {
    title.value = t('routes.badActors.assign');
    stepTitle.value = t('routes.badActors.chooseAssigner');
  } else if (type == 2) {
    title.value = t('routes.badActors.triage');
    stepTitle.value = t('routes.badActors.chooseTriage');
  }
  loadDataSource(data, type);

  triageModel.value.triageStatus = null
  visible.value = true
}

function loadDataSource(data, type) {
  loadTenantBadList({ip: data.ip}).then(data => {
    console.log(data)

    let list: any = [];
    for (let i in data) {
      if (type == 2) {
        //未分配，不能验证
        if (data[i].owner == 'unassign' || !data[i].owner) {
          continue;
        }
      }
      data[i].check = true
      list.push(data[i])
    }
    dataSource.value = list;
  })
}


defineExpose({
  openModal
})


/**
 * 下一步
 */
let badActorIds: any = [];

function next() {
  step.value = 2;
  let list = dataSource.value;
  badActorIds = []
  for (let i in list) {
    if (list[i].check) {
      badActorIds.push(list[i].id)
    }
  }
  console.log(badActorIds)
}

/*用户列表*/

// 声明Emits
const checkedKeys = ref<Array<string | number>>([]);

//注册table数据
const [registerTable, {reload}] = useTable({
  api: listByTenant,
  rowKey: 'id',
  columns: userColumns,
  striped: true,
  useSearchForm: true,
  showTableSetting: false,
  bordered: true,
  showIndexColumn: false,
  canResize: false
});
/**
 * 选择列配置
 */
const rowSelection = {
  type: 'radio',
  columnWidth: 50,
  selectedRowKeys: checkedKeys,
  selectable: true,
  onChange: onSelectChange,
};

/**
 * 选择事件
 */
function onSelectChange(selectedRowKeys: (string | number)[]) {
  checkedKeys.value = selectedRowKeys;
}

//提交事件
function handleSubmit() {
  //刷新列表
  const data = {
    users: toRaw(unref(checkedKeys)),
    ids: badActorIds
  }
  emit('assign', data, AssignType.value);
  closeModal()
}

function handleSubmit2() {
  const data = {
    triageStatus: triageModel.value.triageStatus,
    ids: badActorIds
  }
  emit('triage', data);
  closeModal()
}

function doReload(e) {
  let value = e.target.value || '';
  if (value) {
    value = "*" + value + "*";
  }
  reload({searchInfo: {username: value}});
}


function closeModal() {
  visible.value = false;
  step.value = 1;
  dataSource.value = [];
}

</script>

<style scoped lang="less">
.tenant_table {
  :deep(.ant-table .ant-table-container .ant-table-content thead > tr > th) {
    border-bottom: 1px solid @border-color !important;
  }
}

.triage_div_m {
  :deep(.ant-radio) {
    top: 0 !important;
  }

  .true_radio {
    color: @color-red;
  }

  .false_radio {
    color: @color-green;
  }

  .other_radio {
    color: @color-yellow;
  }
}

</style>
