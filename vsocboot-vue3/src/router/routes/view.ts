import {AppRouteRecordRaw} from "/@/router/types";
import {LAYOUT, PAGE_TAB_NAME} from "/@/router/constant";

/**
 * 查看，编辑等不是菜单的路由跳转写在这里，不在菜单里面配置了
 */
export const ALL_VIEW_ROUTE: AppRouteRecordRaw = {

  path: '/allViews',
  name: PAGE_TAB_NAME,
  component: LAYOUT,
  meta: {
    title: 'asset',
    hideBreadcrumb: true,
    hideMenu: true,
    ignoreKeepAlive: false,
  },
  children: [
    {
      path: '/aggregationRiskEventView/modules/RiskEventViewModal',
      name: 'aggregationRiskEventView/modules/RiskEventViewModal',
      component: () => import('/@/views/aggregationRiskEventView/modules/RiskEventViewModal.vue'),
      meta: {
        title: 'Risk Event View',
        hideBreadcrumb: true,
        hideMenu: true,
        ignoreKeepAlive: false,
      }
    }, {
      path: '/suspiciousProcesses/SuspiciousProcessesList',
      name: 'suspiciousProcesses/SuspiciousProcessesList/modules/RiskEventViewModal',
      component: () => import('/@/views/suspiciousProcesses/SuspiciousProcessesList.vue'),
      meta: {
        title: 'Suspicious Processes View',
        hideBreadcrumb: true,
        hideMenu: true,
        ignoreKeepAlive: false,
      }
    }, {
      path: '/correlationevent/CorrelationEventView',
      name: 'correlationevent/CorrelationEventView',
      component: () => import('/@/views/correlationevent/CorrelationEventView.vue'),
      meta: {
        title: 'Correlation Event View',
        hideBreadcrumb: true,
        hideMenu: true,
        ignoreKeepAlive: false,
      }
    }, {
      path: '/badactors/BadActorsViewModal',
      name: 'badactors/BadActorsViewModal',
      component: () => import('/@/views/badactors/BadActorsViewModal.vue'),
      meta: {
        title: 'Bad Actors View',
        hideBreadcrumb: true,
        hideMenu: true,
        ignoreKeepAlive: false,
      }
    }, {
      path: '/mlView/modules/MlEventModal',
      name: 'mlView/modules/MlEventModal',
      component: () => import('/@/views/mlView/modules/MlEventModal.vue'),
      meta: {
        title: 'ML View',
        hideBreadcrumb: true,
        hideMenu: true,
        ignoreKeepAlive: false,
      }
    }, {
      path: '/investigation/modules/InvestigationNewModal',
      name: 'investigation/modules/InvestigationNewModal',
      component: () => import('/@/views/investigation/modules/InvestigationNewModal.vue'),
      meta: {
        title: 'Investigation Edit',
        hideBreadcrumb: true,
        hideMenu: true,
        ignoreKeepAlive: false,
      }
    }, {
      path: '/situationPerception/situationNewPerception',
      name: 'situationPerception/situationNewPerception',
      component: () => import('/@/views/situationPerception/situationNewPerception.vue'),
      meta: {
        title: 'Investigation View',
        hideBreadcrumb: true,
        hideMenu: true,
        ignoreKeepAlive: false,
      }
    }, {
      path: '/threatHunting/ThreatHuntingModal',
      name: 'threatHunting/ThreatHuntingModal',
      component: () => import('/@/views/threatHunting/ThreatHuntingModal.vue'),
      meta: {
        title: 'Threat Hunting View',
        hideBreadcrumb: true,
        hideMenu: true,
        ignoreKeepAlive: false,
      }
    },
    {
      path: '/asset/assetDetail/assetDetail',
      name: '/asset/assetDetail/assetDetail',
      component: () => import('/@/views/asset/assetDetail/assetDetail.vue'),
      meta: {
        title: 'AssetDetail',
        hideBreadcrumb: true,
        hideMenu: true,
        ignoreKeepAlive: false,
      },
      children: [
        {
          path: '/asset/modules/RiskEventList',
          name: 'AssetDetailRiskEventList',
          component: () => import('/@/views/asset/modules/RiskEventList.vue'),
          meta: {
            title: 'RiskEventList',
            hideBreadcrumb: true,
            hideMenu: true,
            ignoreKeepAlive: false,
          }
        },
        {
          path: '/asset/modules/MlEventList',
          name: 'AssetDetailMlEventList',
          component: () => import('/@/views/asset/modules/MlEventList.vue'),
          meta: {
            title: 'MlEventList',
            hideBreadcrumb: true,
            hideMenu: true,
            ignoreKeepAlive: false,
          },
        },
        {
          path: '/asset/suspiciousProcesses/SuspiciousProcessesList',
          name: 'AssetDetailSuspiciousProcessesList',
          component: () => import('/@/views/suspiciousProcesses/SuspiciousProcessesList.vue'),
          meta: {
            title: 'SuspiciousProcessesList',
            hideBreadcrumb: true,
            hideMenu: true,
            ignoreKeepAlive: false,
          },
        }
      ]
    },
    {
      path: '/asset/AssetView',
      name: 'AssetView',
      component: () => import('/@/views/asset/AssetView.vue'),
      redirect: '/asset/modules/Overview',
      meta: {
        title: 'AssetView',
        hideBreadcrumb: true,
        hideMenu: true,
        ignoreKeepAlive: false,
      },
      children: [
        {
          path: '/asset/modules/Overview',
          name: 'Overview',
          component: () => import('/@/views/asset/modules/Overview.vue'),
          meta: {
            title: 'Overview',
            hideBreadcrumb: true,
            hideMenu: true,
            ignoreKeepAlive: false,
          },
        },
        {
          path: '/asset/modules/Alert',
          name: 'AlertView',
          component: () => import('/@/views/asset/modules/AlertView.vue'),
          redirect: '/asset/modules/RiskEventList',
          meta: {
            title: 'AlertView',
            hideBreadcrumb: true,
            hideMenu: true,
            ignoreKeepAlive: false,
          },
          children: [
            {
              path: '/asset/modules/RiskEventList',
              name: 'AssetRiskEventList',
              component: () => import('/@/views/asset/modules/RiskEventList.vue'),
              meta: {
                title: 'RiskEventList',
                hideBreadcrumb: true,
                hideMenu: true,
                ignoreKeepAlive: false,
              }
            },
            {
              path: '/asset/modules/MlEventList',
              name: 'AssetMlEventList',
              component: () => import('/@/views/asset/modules/MlEventList.vue'),
              meta: {
                title: 'MlEventList',
                hideBreadcrumb: true,
                hideMenu: true,
                ignoreKeepAlive: false,
              },
            },
            {
              path: '/asset/suspiciousProcesses/SuspiciousProcessesList',
              name: 'SuspiciousProcessesList',
              component: () => import('/@/views/suspiciousProcesses/SuspiciousProcessesList.vue'),
              meta: {
                title: 'SuspiciousProcessesList',
                hideBreadcrumb: true,
                hideMenu: true,
                ignoreKeepAlive: false,
              },
            }
          ]
        },
        {
          path: '/asset/modules/Vulnerability',
          name: 'Vulnerability',
          component: () => import('/@/views/asset/modules/Vulnerability.vue'),
          meta: {
            title: 'Vulnerability',
            hideBreadcrumb: true,
            hideMenu: true,
            ignoreKeepAlive: false,
          },
        },
        {
          path: '/asset/agentHost/AgentHost',
          name: 'AgentHost',
          component: () => import('/@/views/asset/agentHost/AgentHost.vue'),
          meta: {
            title: 'AgentHost',
            hideBreadcrumb: true,
            hideMenu: true,
          },
        },
      ]
    }, {
      path: '/asset/AssetBaseEdit',
      name: 'asset/AssetBaseEdit',
      component: () => import('/@/views/asset/AssetBaseEdit.vue'),
      meta: {
        title: 'Asset Base Edit',
        hideBreadcrumb: true,
        hideMenu: true,
        ignoreKeepAlive: false,
      }
    }, {
      path: '/workflow/setting/WorkflowSettingEdit',
      name: 'workflow/setting/WorkflowSettingEdit',
      component: () => import('/@/views/workflow/setting/WorkflowSettingEdit.vue'),
      meta: {
        title: 'Workflow Setting Edit',
        hideBreadcrumb: true,
        hideMenu: true,
        ignoreKeepAlive: false,
      }
    },
    {
      path: '/ticket/edit/index',
      name: '/ticket/edit/index',
      component: () => import('/@/views/ticket/edit/index.vue'),
      meta: {
        title: 'Ticket Setting Edit',
        hideBreadcrumb: true,
        hideMenu: true,
        ignoreKeepAlive: false,
      }
    },{
      path: '/workflow/view/ApplyTenant',
      name: 'workflow/view/ApplyTenant',
      component: () => import('/@/views/workflow/view/ApplyTenant.vue'),
      meta: {
        title: 'Apply',
        hideBreadcrumb: true,
        hideMenu: true,
        ignoreKeepAlive: false,
      }
    }, {
      path: '/workflow/view/DisposeTenant',
      name: 'workflow/view/DisposeTenant',
      component: () => import('/@/views/workflow/view/DisposeTenant.vue'),
      meta: {
        title: 'Dispose',
        hideBreadcrumb: true,
        hideMenu: true,
        ignoreKeepAlive: false,
      }
    }, {
      path: '/mlRule/modules/MlRuleVOModal',
      name: 'mlRule/modules/MlRuleVOModal',
      component: () => import('/@/views/mlRule/modules/MlRuleVOModal.vue'),
      meta: {
        title: 'Ml Rule Edit',
        hideBreadcrumb: true,
        hideMenu: true,
        ignoreKeepAlive: false,
      }
    }, {
      path: '/mlRule/modules/MlRuleViewModal',
      name: 'mlRule/modules/MlRuleViewModal',
      component: () => import('/@/views/mlRule/modules/MlRuleViewModal.vue'),
      meta: {
        title: 'Ml Rule View',
        hideBreadcrumb: true,
        hideMenu: true,
        ignoreKeepAlive: false,
      }
    }, {
      path: '/parserule/ParseRuleView',
      name: 'parserule/ParseRuleView',
      component: () => import('/@/views/parseRule/modules/ParseRuleView.vue'),
      meta: {
        title: 'Parse Rule VIew',
        hideBreadcrumb: true,
        hideMenu: true,
        ignoreKeepAlive: false,
      }
    }, {
      path: '/parserule/ParseRuleEdit',
      name: 'parserule/ParseRuleEdit',
      component: () => import('/@/views/parseRule/ParseRuleEdit.vue'),
      meta: {
        title: 'Parse Rule Edit',
        hideBreadcrumb: true,
        hideMenu: true,
        ignoreKeepAlive: false,
      }
    }, {
      path: '/configure/unparsedLog/UnparsedLogList',
      name: 'configure/unparsedLog/UnparsedLogList',
      component: () => import('/@/views/configure/unparsedLog/UnparsedLogList.vue'),
      meta: {
        title: 'Unparsed Logs',
        hideBreadcrumb: true,
        hideMenu: true,
        ignoreKeepAlive: false,
      }
    }, {
      path: '/system/user/Passwordl',
      name: 'system/user/Passwordl',
      component: () => import('/@/views/system/user/Passwordl.vue'),
      meta: {
        title: 'Passwordl',
        hideBreadcrumb: true,
        hideMenu: true,
        ignoreKeepAlive: false,
      }
    }, {
      path: '/flowmlRule/modules/FlowmlRuleModal',
      name: 'flowmlRule/modules/FlowmlRuleModal',
      component: () => import('/@/views/flowmlRule/modules/FlowmlRuleModal.vue'),
      meta: {
        title: 'FlowmlRuleEdit',
        hideBreadcrumb: true,
        hideMenu: true,
        ignoreKeepAlive: false,
      }
    }, {
      path: '/flowmlRule/modules/FlowmlRuleViewModal',
      name: 'flowmlRule/modules/FlowmlRuleViewModal',
      component: () => import('/@/views/flowmlRule/modules/FlowmlRuleViewModal.vue'),
      meta: {
        title: 'FlowmlRuleView',
        hideBreadcrumb: true,
        hideMenu: true,
        ignoreKeepAlive: false,
      }
    }, {
      path: '/contentMlRule/modules/ContentMlRuleModal',
      name: 'contentMlRule/modules/ContentMlRuleModal',
      component: () => import('/@/views/contentMlRule/modules/ContentMlRuleModal.vue'),
      meta: {
        title: 'ContentMlRuleModal',
        hideBreadcrumb: true,
        hideMenu: true,
        ignoreKeepAlive: false,
      }
    }, {
      path: '/contentMlRule/modules/ContentMlRuleViewModal',
      name: 'contentMlRule/modules/ContentMlRuleViewModal',
      component: () => import('/@/views/contentMlRule/modules/ContentMlRuleViewModal.vue'),
      meta: {
        title: 'ContentMlRuleViewModal',
        hideBreadcrumb: true,
        hideMenu: true,
        ignoreKeepAlive: false,
      }
    }, {
      path: '/flowmlResult/FlowmlResultView',
      name: 'flowmlResult/FlowmlResultView',
      component: () => import('/@/views/flowmlResult/FlowmlResultView.vue'),
      meta: {
        title: 'FlowmlResultView',
        hideBreadcrumb: true,
        hideMenu: true,
        ignoreKeepAlive: false,
      }
    }, {
      path: '/mlView/modules/ContentEventModal',
      name: 'mlView/modules/ContentEventModal',
      component: () => import('/@/views/mlView/modules/ContentEventModal.vue'),
      meta: {
        title: 'ContentEventModal',
        hideBreadcrumb: true,
        hideMenu: true,
        ignoreKeepAlive: false,
      }
    }, {
      path: '/soar/workflow/WorkflowIndex',
      name: 'soar/workflow/WorkflowIndex',
      component: () => import('/@/views/soar/workflow/WorkflowIndex.vue'),
      meta: {
        title: 'WorkflowIndex',
        hideBreadcrumb: true,
        hideMenu: true,
        ignoreKeepAlive: false,
      }
    },
    {
      path: '/reports/ReportEdit',
      name: 'reports/ReportEdit',
      component: () => import('/@/views/reports/ReportEdit.vue'),
      meta: {
        title: 'ReportEdit',
        hideBreadcrumb: true,
        hideMenu: true,
        ignoreKeepAlive: false,
      }
    },
    {
      path: '/reports/ReportView',
      name: 'reports/ReportView',
      component: () => import('/@/views/reports/ReportView.vue'),
      meta: {
        title: 'ReportEdit',
        hideBreadcrumb: true,
        hideMenu: true,
        ignoreKeepAlive: false,
      }
    },
    {
      path: '/proposal/modules/ProposalAdd',
      name: '/proposal/modules/ProposalAdd',
      component: () => import('/@/views/proposal/modules/ProposalAdd.vue'),
      meta: {
        title: 'ProposalEdit',
        hideBreadcrumb: true,
        hideMenu: true,
        ignoreKeepAlive: false,
      }
    },
    {
      path: '/investigationTemplate/modules/AddTemplate',
      name: '/investigationTemplate/modules/AddTemplate',
      component: () => import('/@/views/investigationTemplate/modules/AddTemplate.vue'),
      meta: {
        title: 'InvestigationTemplateEdit',
        hideBreadcrumb: true,
        hideMenu: true,
        ignoreKeepAlive: false,
      }
    },
    {
      path: '/invest/modules/InvestView',
      name: '/invest/modules/InvestView',
      component: () => import('/@/views/invest/modules/InvestView.vue'),
      meta: {
        title: 'InvestView',
        hideBreadcrumb: true,
        hideMenu: true,
        ignoreKeepAlive: false,
      }
    },






  ]
};


/**
 * 弹出页面，不显示菜单栏
 */
export const ALONE_VIEW_ROUTE: AppRouteRecordRaw = {

  path: '/aloneViews',
  name: 'aloneViews',
  // component: LAYOUT,
  meta: {
    title: 'alone',
    hideBreadcrumb: true,
    hideMenu: true,
    ignoreKeepAlive: false,
  },
  children: [
    {
      path: '/largeScreen',
      name: 'largeScreen',
      component: () => import('/@/views/posture/index.vue'),
      meta: {
        title: 'LargeScreen',
        hideBreadcrumb: true,
        hideMenu: true,
        ignoreKeepAlive: false,
      }
    }
  ]
};

