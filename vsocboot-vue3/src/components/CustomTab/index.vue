<template>
  <div class="custom-tab">
    <template v-for="item in tabOption" :key="item[attrId]">
      <div
        class="custom-tab-item" v-show="!item.hidden"
        :class="{ 'active': item[attrId] == currentTab }" @click="changeTab(item[attrId])">
        <template v-if="showTitle">{{ item.name }}</template>
        <slot name="title" :item="item"></slot>
      </div>
    </template>
  </div>
</template>

<script setup lang="ts" name="Drawer">
  import { ref, watchEffect } from 'vue';

  interface Tab {
    name: string;
    value: number | string;
    id?: string;
  }

  const emit = defineEmits(['change', 'update:active']);

  const props = defineProps({
    attrId: {
      type: String,
      default: 'value',
    },
    active: [String, Number],
    tabs: Array as () => Tab[],
    showTitle: {
      type: Boolean,
      default: true,
    },
  });
  const currentTab = ref();
  const tabOption = ref<Tab[]>([]);
  watchEffect(() => {
    currentTab.value = props.active;
    tabOption.value = props.tabs ?? [];
  });

  function changeTab(value) {
    currentTab.value = value;
    emit('change', value);
    emit('update:active', value);
  }
</script>

<style lang="less" scoped>
  /*------------------------自定义tab start-------------------------------------*/
  .custom-tab {
    display: flex;
    flex-direction: row;
    gap: 24px;
    font-size: 14px;
    font-weight: normal;
    line-height: 24px;
    color: @font-color-default;

    .custom-tab-item {
      cursor: pointer;

      &.active {
        background: @font-active-color;
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        text-fill-color: transparent;
        z-index: 0;
        font-size: 16px;
        font-weight: 600;
      }
    }
  }

  /*------------------------自定义tab end-------------------------------------*/
</style>
