<template>
  <div style="position: relative">
    <vue-json-pretty :path="'res'" :deep="3" :showLength="true" :data="data">
      <template #renderNodeKey="{ node }"> "{{ node.key }}" </template>
      <template #renderNodeValue="{ node }">
        <a-dropdown :trigger="['contextmenu']">
          <span @mouseup="getSelectText">
            {{ getContent(node.content) }}
          </span>
          <template #overlay>
            <a-menu>
              <a-menu-item key="1" @click="decode">{{ t('common.decode') }}</a-menu-item>
              <a-menu-item key="1" @click="Base64decode">{{ t('common.Base64decode') }}</a-menu-item>
              <a-menu-item key="1" @click="UTF8decode">{{ t('common.UTF8decode') }}</a-menu-item>
            </a-menu>
          </template>
        </a-dropdown>
      </template>
    </vue-json-pretty>
    <a-button class="copyBtn" type="primary" @click="copyJson" v-if="copy">
      {{ t('common.copyText') }}
    </a-button>
  </div>
</template>

<script lang="ts" setup>
  import VueJsonPretty from 'vue-json-pretty';
  import 'vue-json-pretty/lib/styles.css';
  import { useI18n } from '/@/hooks/web/useI18n';
  import { copyPageUrl } from '/@/utils/copyUtil';
  import { getContent } from '/@/views/soar/utils/Workflow';
  import { Modal } from 'ant-design-vue';

  const { t } = useI18n();
  const props = defineProps({
    data: Object,
    copy: {
      type: Boolean,
      default: true,
    },
  });

  function copyJson() {
    copyPageUrl(JSON.stringify(props.data));
  }

  let selectText: any = '';

  function getSelectText() {
    selectText = window?.getSelection()?.toString();
    console.log(selectText); // 打印选中的文本
  }

  function base64Decode(str) {
    try {
      // 尝试解码Base64字符串
      const decoded = window.atob(str);
      // 检查解码后的字符串是否为有效的UTF-8
      const buffer = new Uint8Array(new ArrayBuffer(decoded.length));
      for (let i = 0; i < decoded.length; i++) {
        buffer[i] = decoded.charCodeAt(i);
      }
      const isUtf8 = new TextDecoder().decode(buffer) === decoded;

      return isUtf8 ? decoded : str;
    } catch (e) {
      // 如果解码失败，返回原字符串
      return str;
    }
  }

  function decodeUtf8(bytes) {
    let string = '';
    let i = 0;

    while (i < bytes.length) {
      let charCode = bytes[i];

      if (charCode < 0x80) {
        string += String.fromCharCode(charCode);
        i += 1;
      } else if (charCode < 0xe0) {
        string += String.fromCharCode(((charCode & 0x1f) << 6) | (bytes[i + 1] & 0x3f));
        i += 2;
      } else if (charCode < 0xf0) {
        string += String.fromCharCode(((charCode & 0x0f) << 12) | ((bytes[i + 1] & 0x3f) << 6) | (bytes[i + 2] & 0x3f));
        i += 3;
      } else {
        const charCode2 = bytes[i + 1];
        string += String.fromCharCode((0xd840 + ((charCode & 0x07) << 8)) | (charCode2 & 0x3f));
        string += String.fromCharCode((0xdc00 + ((bytes[i + 2] & 0x3f) << 6)) | (bytes[i + 3] & 0x3f));
        i += 4;
      }
    }

    return string;
  }

  function decode() {
    if (selectText) {
      const text2: any = decodeURIComponent(selectText);
      Modal.success({
        title: t('common.decode'),
        content: text2,
      });
    }
  }
  function Base64decode() {
    if (selectText) {
      const text2: any = base64Decode(selectText);
      Modal.success({
        title: t('common.Base64decode'),
        content: text2,
      });
    }
  }
  function UTF8decode() {
    if (selectText) {
      const bytes = selectText.split('').map((char) => char.charCodeAt(0));
      const text2 = decodeUtf8(bytes);
      Modal.success({
        title: t('common.UTF8decode'),
        content: text2,
      });
    }
  }
</script>
<style>
  .vjs-tree-node:hover {
    background-color: rgba(255, 255, 255, 0.2) !important;
  }
</style>
<style scoped>
  :deep(.vjs-tree-node:hover) {
    background-color: rgba(255, 255, 255, 0.2) !important;
  }

  .copyBtn {
    position: absolute;
    top: 0;
    right: 0;
  }

  :deep(.vjs-value) {
    white-space: break-spaces;
  }
</style>
