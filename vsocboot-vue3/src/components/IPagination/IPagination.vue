<template>
  <div style="display: flex; justify-content: flex-end">
    <Pagination
      size="small"
      v-model:current="current"
      :defaultPageSize="defaultPageSize"
      :total="total"
      :show-total="(total) => `Total of ${total}`"
      show-size-changer
      show-quick-jumper
      :pageSizeOptions="pageSizeOptions"
      @change="onChange"
    />
  </div>
</template>

<script lang="ts" setup>
  import { ref } from 'vue';
  import { Pagination } from 'ant-design-vue';
  const emit = defineEmits(['handlePageChange']);
  const props = defineProps({
    total: {
      type: Number,
      default: 50,
    },
    defaultPageSize: {
      type: Number,
      default: 10,
    },
    pageSizeOptions: {
      type: Array as any,
      default: () => ['10', '20', '30', '40', '50'],
    },
  });
  const current = ref(1);
  function onChange(page, pageSize) {
    current.value = page;
    emit('handlePageChange', page, pageSize);
  }
  defineExpose({
    onChange,
  });
</script>

<style lang="less" scoped></style>
