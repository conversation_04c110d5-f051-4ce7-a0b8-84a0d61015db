<template>
  <div class="ax-step-wrapper">
    <!-- 导航 start-->
    <div class="ax-step_top">
      <!-- 返回按钮 start-->
      <div class="ax-step-back" >
        <div class="ax-icon-button" @click="emits('back')">
          <span class="soc ax-com-Arrow-left ax-icon"></span>
        </div>
        <div class="font16 fcolor">
          <slot name="backBtnName"></slot>
        </div>

      </div>
      <!-- 返回按钮 end-->

      <!-- 步骤名称  start-->
      <div class="ax-step-container">
        <!-- 步骤导航条 start
        ========================================-->
        <div class="steps">
          <div  v-for="(item, index) in stepName" :key="'step-' + index" :class="['step', { 'step-ok' :index <= current } ]">
            <div class="step-number ">{{ index + 1 }}</div>
            <div class="step-text">{{ item }}</div>
            <div class="step-split" v-if="index < stepName.length - 1"></div>
          </div>
        </div>
        <!-- 步骤导航条 end -->
      </div>
      <!-- 步骤名称  end-->

      <!-- 操作项  start-->
      <div class="ax-step-action">
        <slot name="actions"></slot>
      </div>
      <!-- 操作项  end-->
    </div>
    <!--  导航 end-->

    <!-- 内容 start-->
    <div class="ax-step_content">
      <slot name="content"></slot>
    </div>
    <!-- 内容 end-->
  </div>



</template>
<script setup lang="ts">
import {defineProps, inject, ref} from 'vue';
const emits = defineEmits(['back','btnClick']);
const current = inject('current', ref(0));
  defineProps({
    stepName: {
      type: Array,
      default: null,
    },
  });
</script>
<style scoped lang="less">

.ax-step-wrapper {
  display: flex;
  flex-direction: column;
  height: 100%;
  width: 100%;
  position: relative;
  overflow: hidden;
  .ax-step_top {
    position: relative;
    width: 100%;
    display: flex;
    flex-direction: row;
    justify-content: center;
    border-bottom: 1px solid @border-color-01;
    align-items: center;
    .ax-step-back {
      position: absolute;
      left: 16px;
      display:flex;
      flex-direction: row;
      gap: 8px;
      align-items: center;
    }

    .ax-step-container {
      display: flex;
      align-items: center;
      padding: 14px 20px;
      .steps{
        display: flex;
        align-items: center;
        flex-direction: row;
        gap: 8px;
        .step {
          display: flex;
          align-items: center;
          gap: 8px;
          flex-direction: row;
          font-size: 13px;
          line-height: 20px;
          .step-number {
            display: inline-flex;
            justify-content: center;
            align-items: center;
            width: 20px;
            height: 20px;
            border-radius: 50%;
            background: rgba(64, 148, 255, 0.2);
            color: rgba(255, 255, 255, 0.6);
          }
          &.step-ok {
            .step-number {
              color: #FFFFFF;
              background: @primary-color;
            }
            .step-text{
              font-weight: 600;
            }
          }
          .step-split{
            width: 20px;
            border-top: 1px solid rgba(255, 255, 255, 0.2);
            height: 0;
          }
        }
      }

    }

    .ax-step-action {
      position: absolute;
      right: 16px;
      top: 0;
      display: flex;
      flex-direction: row;
      justify-content: flex-end;
      align-items: center;
      gap: 8px;
      height: 100%;
    }
  }

  .ax-step_content {
    width: 100%;
    flex: 1;
    overflow-y: auto;
  }
}





</style>
