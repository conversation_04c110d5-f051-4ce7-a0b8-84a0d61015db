<template>
  <div class="flex flex-col gap-8px">
    <div class="mx-16px ax-border-bottom pb-12px">
      <a-input v-model:value="tenantFilterStr" :placeholder="t('routes.posture.searchTenant')" allow-clear>
        <template #suffix v-if="!tenantFilterStr">
          <span class="soc ax-com-Search"></span>
        </template>
      </a-input>
    </div>
    <div class="flex flex-col px-4px" :class="activeClass + '_wrapper'">
      <template v-for="(item, index) in tenantList" :key="index">
        <div
          class="tenant-item"
          @click="checkTenant(item)"
          :class="[tenantId == item.id ? props.activeClass : '']"
          v-if="tenantFilterStr == '' || -1 != item.name.indexOf(tenantFilterStr)">
          {{item.name}}
        </div>
      </template>
    </div>
  </div>

</template>

<script setup lang="ts" name="posture-tenant">

import {queryList} from "/@/views/system/tenant/tenant.api";

import {onMounted, ref} from "vue";
import {useI18n} from "/@/hooks/web/useI18n";
const emit = defineEmits(['change']);
const {t} = useI18n();
interface Tenant {
  id: string;
  name: string;
}
 const props = defineProps({
  activeClass:{
    type:String,
    default:'ax-bg-active'
  }
})

//租户list
const tenantList = ref<Tenant[]>([]);
const tenantId = ref('');
// 租户 filter 字符串
const tenantFilterStr = ref("");

onMounted(()=>{
  queryTenant();
})
//背景配置

/**
 * 查询租户数据
 */
function queryTenant() {
  queryList({status: 1}).then(data => {
    tenantList.value = data;
    if(data.length > 0){
      checkTenant(data[0]);
    }
  })
}
/**
 * 选择租户
 * @param item
 */
function checkTenant(item:Tenant){
  tenantId.value = item.id;
  emit('change',item.id);
}
</script>

<style lang="less" scoped>

.tenant-item {
  padding: 8px 12px;
  color: @font-color-default;
  cursor: pointer;
  &:hover {
    border-radius: 4px;
    background:@menu-hover-bg;
  }
}
.ax-bg-default_wrapper{
  .tenant-item {
    &:hover {
      border-radius: 4px;
      background:@border-color-01;
    }
  }
}

</style>
