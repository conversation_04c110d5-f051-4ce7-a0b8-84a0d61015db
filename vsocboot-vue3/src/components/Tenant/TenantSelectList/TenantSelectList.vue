<template>
  <div class="pr-16px pb-8px">
    <a-input :allowClear="true" v-model:value="searchValue" :placeholder="t('common.tenantName')" @change="search" class="input-default-width">
      <template #suffix>
        <span class="soc ax-com-Search" ></span>
      </template>
    </a-input>
  </div>
  <div class="ant-table ant-table-fixed-header">
    <div class="ant-table-container">
      <div class="ant-table-header" style="overflow: hidden; margin: 0 -16px">
        <table style="table-layout: fixed">
          <colgroup>
            <col style="width: calc(100% - 140px)" />
            <col style="width: 130px" />
            <col style="width: 7px" />
          </colgroup>
          <thead class="ant-table-thead">
            <tr>
              <th class="ant-table-cell" colstart="0" colend="0">{{ t('common.tenantName') }}</th>
              <th class="ant-table-cell" colstart="1" colend="1">{{ t('common.operation') }}</th>
              <th class="ant-table-cell ant-table-cell-scrollbar" colstart="2" colend="2"> </th>
            </tr>
          </thead>
        </table>
      </div>
      <div class="ant-table-body" :style="{ 'overflow-y': 'scroll', 'margin-left': '-16px', 'margin-right': '-16px', 'max-height': scrollY + 'px' }">
        <table style="table-layout: fixed">
          <colgroup>
            <col />
            <col style="width: 130px" />
          </colgroup>
          <tbody class="ant-table-tbody">
            <tr aria-hidden="true" class="ant-table-measure-row" style="height: 0px; font-size: 0px">
              <td style="padding: 0px; border: 0px; height: 0px">
                <div style="height: 0px; overflow: hidden">&nbsp;</div>
              </td>
              <td style="padding: 0px; border: 0px; height: 0px">
                <div style="height: 0px; overflow: hidden">&nbsp;</div>
              </td>
            </tr>
            <template v-for="(item, index) in dataSource" :key="'tr_' + index">
              <tr class="ant-table-row ant-table-row-level-0" v-show="item.show != false">
                <td class="ant-table-cell">
                  {{ item['name'] }}
                </td>
                <td class="ant-table-cell">
                  <a-switch v-model:checked="item.checked" size="small" :disabled="disabled" @change="switchChange" />
                </td>
              </tr>
            </template>
          </tbody>
        </table>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
  import { ref, defineProps, onMounted, defineEmits, watch } from 'vue';
  import { list } from '/@/components/Tenant/Tenant.api';
  import { useI18n } from '/@/hooks/web/useI18n';

  const { t } = useI18n();
  const emit = defineEmits(['update:value','change']);
  const props = defineProps({
    scrollY: {
      type: Number,
      default: 300,
    },
    value: {
      type: String,
      default: '',
    },
    disabled: {
      type: Boolean,
      default: false,
    },
    tenantMap: {
      type: Object,
      default: () => {
        return {};
      },
    },
  });

  // watch(
  //   () => props.value,
  //   () => {
  //     initData();
  //   }
  // );

  const dataSource = ref<any[]>([]);
  const searchValue = ref('');
  let tenantList: any = [];
  onMounted(() => {
    list({}).then((data) => {
      tenantList = data;
      initData();
    });
  });

  function initData() {
    // dataSource.value = []
    if (tenantList.length == 0) {
      list({}).then((data) => {
        console.log(data);
        tenantList = data;
        let map: any = {};
        let value = props.value;
        if (value) {
          let array = value.split(',');
          for (let i = 0; i < array.length; i++) {
            let array2 = array[i].split(':');
            map[array2[0]] = array2[1];
          }
        }
        for (let i = 0; i < data.length; i++) {
          //默认为true
          data[i].checked = true;
          //1启用，2停用
          if (map[data[i].id] == 2) {
            data[i].checked = false;
          }
          props.tenantMap[data[i].id] = data[i];
        }
        dataSource.value = data;
        setValue();
      });
    } else {
      let map: any = {};
      let value = props.value;
      if (value) {
        let array = value.split(',');
        for (let i = 0; i < array.length; i++) {
          let array2 = array[i].split(':');
          map[array2[0]] = array2[1];
        }
      }
      console.log(map);
      for (let i = 0; i < tenantList.length; i++) {
        //默认为true
        tenantList[i].checked = true;
        //1启用，2停用
        if (map[tenantList[i].id] == 2) {
          tenantList[i].checked = false;
        }
        props.tenantMap[tenantList[i].id] = tenantList[i];
      }
      dataSource.value = tenantList;
      setValue();
    }
  }

  function setValue() {
    let list = dataSource.value;
    let array: any = [];
    for (let i = 0; i < list.length; i++) {
      array.push(list[i].id + ':' + (list[i].checked ? 1 : 2));
    }
    let value = array.join(',');
    emit('update:value', value);
    emit('change', value);
  }

  function switchChange() {
    setValue();
  }

  /**
   * 搜索
   */
  function search() {
    for (let i = 0; i < dataSource.value.length; i++) {
      if (!searchValue.value) {
        dataSource.value[i].show = true;
        continue;
      }
      dataSource.value[i].show = !!dataSource.value[i].name.includes(searchValue.value);
    }
  }
</script>

<style scoped></style>
