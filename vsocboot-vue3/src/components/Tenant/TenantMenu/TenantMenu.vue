<template>

  <div class="tenant_div">
    <div class="tenant_search">
      <a-input-search @search="onSearch"/>
    </div>
    <div class="tenant_list">
      <template v-for="(tenant,index) in tenantList" :key="index">
        <div class="tenant_name" v-if="tenant.show">
          <div :class="{'active':tenant.id == socTenantId}">
            <img v-if="tenant['companyLogo']"
                 :src="render.renderUploadImageSrc(tenant['companyLogo'])" class="img"/>
            <img v-else src="../../../assets/images/no_graph.png" class="img"/>
            {{ tenant.name }}
            <Icon icon="ant-design:fullscreen-outlined" size="24" class="icon"
                  v-if="tenant.id != socTenantId"
                  @click="toPage(tenant.id)"/>
            <Icon icon="ant-design:fullscreen-exit-outlined" size="24" class="icon"
                  v-if="tenant.id == socTenantId"
                  @click="toPage('')"/>
          </div>
        </div>
      </template>
    </div>
  </div>
</template>

<script lang="ts" setup>
import {render} from "/@/utils/common/renderUtils";
import {ref, defineEmits} from 'vue'
import {list} from "/@/components/Tenant/Tenant.api";

const emits = defineEmits(['ok'])
defineProps({
  socTenantId: {
    type: String
  }
})

const tenantList = ref<any[]>([])
list({}).then((data) => {
  for (let i in data) {
    data[i].show = true
  }
  tenantList.value = data;
})

function onSearch(value) {
  let list = tenantList.value
  for (let i in list) {
    if (value) {
      if (list[i].name.indexOf(value) > -1) {
        list[i].show = true
      } else {
        list[i].show = false
      }
    } else {
      list[i].show = true
    }
  }
  tenantList.value = list;
}

function toPage(id) {
  if (!id) {
    emits('ok')
    return
  }
  console.log('socTenantId', id)
  emits('ok', id)
}
</script>

<style scoped lang="less">
.tenant_div {
  position: absolute;
  background-color: @dark-bg2;
  z-index: 99;
  padding: 10px;
  margin-top: 5px;

  .tenant_search {
    margin-bottom: 6px;
  }

  .tenant_list {
    max-height: 60vh;
    overflow: auto;
  }

  .tenant_name > div {
    padding: 6px 10px;
    cursor: pointer;
  }

  .icon {
    float: right;
    margin-left: 10px;
  }

  .active {
    background-color: @dark-bg3;
    border-radius: 4px;
  }

  .img {
    width: 24px;
    height: 24px;
    border-radius: 12px;
  }
}
</style>
