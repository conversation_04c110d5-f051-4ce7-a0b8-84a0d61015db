<template>
  <template v-if="type === 'log' && value">
    <span class="severity1" v-if="value?.toLowerCase() == 'critical'">
      {{ t('common.Critical') }}
    </span>
    <span class="severity2" v-else-if="value?.toLowerCase() == 'high'">
      {{ t('common.High') }}
    </span>
    <span class="severity3" v-else-if="value?.toLowerCase() == 'medium'">
      {{ t('common.Medium') }}
    </span>
    <span class="severity4" v-else-if="value?.toLowerCase() == 'low'">
       {{ t('common.Low') }}
    </span>
    <span class="severity5"
          v-else-if="value?.toLowerCase() == 'info' || value?.toLowerCase() == 'information'">
      {{ t('common.Information') }}
    </span>
    <span class="severity4" v-else-if="value">
      {{ value }}
    </span>
  </template>
  <template v-else-if="type === 'correlation' && value">
    <span class="severity1" v-if="value == '4'">
      {{ t('common.Critical') }}
    </span>
    <span class="severity2" v-else-if="value == '3'">
      {{ t('common.High') }}
    </span>
    <span class="severity3" v-else-if="value == '2'">
      {{ t('common.Medium') }}
    </span>
    <span class="severity4" v-else-if="value == '1'">
      {{ t('common.Low') }}
    </span>
    <span class="severity5" v-else-if="value">
      {{ t('common.Information') }}
    </span>
  </template>
  <template v-else-if="type === 'number' && value">
    <span class="severity1" v-if="value == '1'">
      {{ t('common.Critical') }}
    </span>
    <span class="severity2" v-else-if="value == '2'">
      {{ t('common.High') }}
    </span>
    <span class="severity3" v-else-if="value == '3'">
      {{ t('common.Medium') }}
    </span>
    <span class="severity4" v-else-if="value == '4'">
      {{ t('common.Low') }}
    </span>
    <span class="severity5" v-else-if="value">
      {{ t('common.Information') }}
    </span>
  </template>
  <template v-else-if="value">
    <span class="severity1" v-if="value?.toLowerCase() == 'critical'">
      {{ t('common.Critical') }}
    </span>
    <span class="severity2" v-else-if="value?.toLowerCase() == 'high'">
      {{ t('common.High') }}
    </span>
    <span class="severity3" v-else-if="value?.toLowerCase() == 'medium'">
      {{ t('common.Medium') }}
    </span>
    <span class="severity4" v-else-if="value?.toLowerCase() == 'low'">
      {{ t('common.Low') }}
    </span>
    <span class="severity5"
          v-else-if="value?.toLowerCase() == 'info' || value?.toLowerCase() == 'information'">
      {{ t('common.Information') }}
    </span>
    <span class="severity5" v-else-if="value">
      {{ value }}
    </span>
  </template>


</template>

<script lang="ts" setup>
import {useI18n} from "/@/hooks/web/useI18n";

const {t} = useI18n();
defineProps({
  value: {
    type: String,
    default: ''
  },
  type: {
    type: String,
    default: ''
  }
})
</script>

<style scoped>

</style>
