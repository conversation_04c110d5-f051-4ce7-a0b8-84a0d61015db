<template>
  <template v-if="type === 'number' && value">
    <span class="severity1_span" v-if="value == '1'">
      <span class="soc ax-com-Danger"></span>
      <span>{{ t('common.Critical') }}</span>
    </span>
    <span class="severity2_span" v-else-if="value == '2'">
      <span class="soc ax-com-Danger"></span>
      <span>{{ t('common.High') }}</span>
    </span>
    <span class="severity3_span" v-else-if="value == '3'">
      <span class="soc ax-com-Danger"></span>
      <span>{{ t('common.Medium') }}</span>
    </span>
    <span class="severity4_span" v-else-if="value == '4'">
      <span class="soc ax-com-Danger"></span>
      <span>{{ t('common.Low') }}</span>
    </span>
    <span class="severity5_span" v-else-if="value == '5'">
      <span class="soc ax-com-Danger"></span>
      <template v-if="source == 'invest'">
         <span>{{ t('common.Safe') }}</span>
      </template>
      <template v-else>
        <span>{{ t('common.Information') }}</span>
      </template>
    </span>
    <span class="severity5_span" v-else-if="value">
      <span class="soc ax-com-Danger"></span>
      <span>{{ value }}</span>
    </span>
  </template>
  <template v-else-if="value">
    <span class="severity1_span" v-if="value?.toLowerCase() == 'critical'">
      <span class="soc ax-com-Danger"></span>
      <span>{{  t('common.Critical') }}</span>
    </span>
    <span class="severity2_span" v-else-if="value?.toLowerCase() == 'high'">
      <span class="soc ax-com-Danger"></span>
      <span>{{ t('common.High') }}</span>
    </span>
    <span class="severity3_span" v-else-if="value?.toLowerCase() == 'medium'">
      <span class="soc ax-com-Danger"></span>
      <span>{{ t('common.Medium') }}</span>
    </span>
    <span class="severity4_span" v-else-if="value?.toLowerCase() == 'low'">
      <span class="soc ax-com-Danger"></span>
      <span>{{ t('common.Low') }}</span>
    </span>
    <span
      class="severity5_span"
      v-else-if="value?.toLowerCase() == 'info' || value?.toLowerCase() == 'information'">
      <span class="soc ax-com-Danger"></span>
      <span>{{value.charAt(0).toUpperCase() + value.slice(1) }}</span>
    </span>
    <span class="severity5_span" v-else-if="value">
      <span class="soc ax-com-Danger"></span>
      <span>{{ value }}</span>
    </span>
  </template>

  <template v-else-if="source == 'invest'">
    <span class="severity_span">
      <span class="soc ax-com-Danger"></span>
      <span>{{ t('common.Untriaged') }}</span>
    </span>

  </template>
</template>

<script lang="ts" setup>
import {useI18n} from "/@/hooks/web/useI18n";

const {t} = useI18n();
defineProps({
  value: {
    type: String,
    default: ''
  },
  type: {
    type: String,
    default: ''
  },
  source: {
    type: String,
    default: ''
  }
})
</script>

<style scoped lang="less">
.severity1_span {
  color: @critical-color;

}

.severity2_span {
  color: @high-color;

}

.severity3_span {
  color: @medium-color;

}

.severity4_span {
  color: @low-color;

}

.severity5_span {
  color: @information-color;

}

.severity_span {

}
.soc{
  margin-right: 4px;
}
</style>
