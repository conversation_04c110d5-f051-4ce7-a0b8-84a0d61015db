<template>
  <div ref="chartRef" :style="{ height, width }"></div>
</template>
<script lang="ts">
import {defineComponent, PropType, ref, Ref, reactive, watchEffect} from 'vue';
import {useECharts} from '/@/hooks/web/useECharts';
import {cloneDeep} from 'lodash-es';

export default defineComponent({
  props: {
    chartData: {
      type: Array,
      default: () => [],
    },
    option: {
      type: Object,
      default: () => ({}),
    },
    width: {
      type: String as PropType<string>,
      default: '100%',
    },
    height: {
      type: String as PropType<string>,
      default: '50px',
    },
  },
  setup(props) {
    const chartRef = ref<HTMLDivElement | null>(null);
    const {setOptions} = useECharts(chartRef as Ref<HTMLDivElement>);
    const option: any = reactive({
      tooltip: {
        trigger: 'axis',
        confine: true
      },
      grid: {
        top: '5',
        left: '5',
        right: '5',
        bottom: '5'
      },
      xAxis: {
        show: false,
        type: 'category',
        data: [],
      },
      yAxis: {
        show: false,
        type: 'value',
      },
      series: [
        {
          type: 'line',
          smooth: true,
          data: [],
        },
      ],
    });

    watchEffect(() => {
      props.chartData && initCharts();
    });

    function initCharts() {
      if (props.option) {
        Object.assign(option, cloneDeep(props.option));
      }
      let seriesData: any = props.chartData.map((item: any) => {
        return item.value;
      });
      let xAxisData: any = props.chartData.map((item: any) => {
        return item.name;
      });
      option.series[0].data = seriesData;
      option.xAxis.data = xAxisData;
      setOptions(option);
    }

    return {chartRef};
  },
});
</script>
