import { mixLighten, mixDarken, tinycolor } from '@rys-fe/vite-plugin-theme/es/colorUtils';
import {useRootSetting} from "/@/hooks/setting/useRootSetting";
export const axisLabelColor = 'rgba(255,255,255,0.4)';
//主轴线颜色
export const axisMainLineColor = 'rgba(255,255,255,0.2)';
//图表超出范围默认颜色
export const outRangeDefaultColor = '#F75555';

export const axisLabelColorBlack = 'rgba(0,0,0,0.8)';
export const axisLineColor = 'rgba(255,255,255,0.08)';
export const axisPointerBg = 'rgba(255,255,255,0.08)';
export const riskColor = ['#F75555','#FB7E52','#F8A556','#F6C84D','#F6C84D'];
export const barColor = ['#308cff', '#E2CD00', '#F8A556'];
export const axisLabel = {
  fontFamily: 'PingFang SC',
  fontSize : 12,
  lineHeight : 16,
  // backgroundColor:'#1A1B1F',
  // padding: [10, 0, 5, 0],
  color: axisLabelColor
}
//图上数字颜色
export const labelNumColor = '#fff';
const { getDarkMode } = useRootSetting();

export function generateColor(color: string,length: number): any {
  console.log('getDarkMode.value:',getDarkMode.value)
   if(getDarkMode.value === 'dark'){
     return generateDarkColor(color,length);
   }else{
     return generateLightColor(color,length);
   }
}
export function generateDarkColor(color: string,length: number): any {
  const arr = new Array(length).fill(0);
  let num:number = length > 5 ? length : 5;
  const lightens = arr.map((_t, i) => {
    return mixDarken(color, i / num);
  });
  return lightens;
}

export function generateLightColor(color: string,length: number): any {
  const arr = new Array(length).fill(0);
  let num:number = length > 5 ? length : 5;
  const lightens = arr.map((_t, i) => {
    return mixLighten(color, i / num);
  });
  return lightens;
}



