<template>
  <div ref="chartRef" :style="{ height, width }"></div>
</template>
<script lang="ts">
import {defineComponent, PropType, ref, Ref, reactive, watchEffect} from 'vue';
import {useECharts} from '/@/hooks/web/useECharts';
import {cloneDeep} from 'lodash-es';
import {primaryColor} from '../../../build/config/themeConfig';
import {axisLabelColor, axisLineColor, axisPointerBg} from "./ChartColor"

export default defineComponent({
  name: 'Column',
  props: {

    chartData: {
      type: Array,
      default: () => [],
    },
    option: {
      type: Object,
      default: () => ({}),
    },
    width: {
      type: String as PropType<string>,
      default: '100%',
    },
    height: {
      type: String as PropType<string>,
      default: 'calc(100vh - 78px)',
    },
  },
  emits: ['click'],
  setup(props, { emit }) {
    const chartRef = ref<HTMLDivElement | null>(null);
    const {setOptions, echarts,getInstance} = useECharts(chartRef as Ref<HTMLDivElement>);
    const chartColor = ref([]);
    chartColor.value.push(primaryColor);

    const option = reactive({
      color: chartColor.value,
      tooltip: {
        trigger: 'axis',
        formatter: '{b}<br/>Count:{c}',
        axisPointer: {
          type: 'shadow',
          label: {
            show: true,
            backgroundColor: axisPointerBg,
          },
        },
      },
      grid: {
        top: 10,
        bottom: 20,
        left: 100,
        right: 10,
      },
      yAxis: {
        type: 'value',
        axisLine: {
          show: false
        },
        splitLine: {
          show: false
        },
        axisTick: {
          show: false
        },
        axisLabel: {
          color: axisLabelColor
        },
      },
      xAxis: {
        type: 'category',
        data: [],
        axisLine: {
          show: true,
          lineStyle: {
            color: axisLineColor
          }
        },
        splitLine: {
          show: false
        },
        axisTick: {
          show: false
        },
        axisLabel: {
          show: true,
          color: axisLabelColor
        },
      },

      series: [
        {
          name: 'bar',
          type: 'bar',
          barMaxWidth: '12',
          data: [],
        },
      ],
    });

    watchEffect(() => {
      props.chartData && initCharts();
    });

    function initCharts() {

      if (props.option) {
        Object.assign(option, cloneDeep(props.option));
      }

      let seriesData = props.chartData.map((item) => {
        return item.value;
      });
      let xAxisData = props.chartData.map((item) => {
        return item.name;
      });
      option.series[0].data = seriesData;
      option.xAxis.data = xAxisData;
      setOptions(option);

      getInstance()?.off('click', onClick);
      getInstance()?.on('click', onClick);
    }

    function onClick(params) {
      emit('click', params);
    }

    return {chartRef};
  },
});
</script>
