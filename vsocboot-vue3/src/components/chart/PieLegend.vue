<template>
  <div style="display: flex;">
    <div v-if="chartData?.length > 0" ref="chartRef" :style="{ height, width }"></div>
    <div v-if="rightDiv" class="pie_right_div"
         :style="{ height, 'width':'calc(100% - '+width+')' }">
      <div :class="['legend_wraper',{'flex-start' : chartData?.length > 4 ? true : false}]"
           style="overflow-y: auto;overflow-x: clip;">
        <template v-for="(item,index) in chartData" :key="index">
          <div class="right-div">
            <div class="right-div__b font20"
                 :style="{'border-left': '2px solid '+colors[index] ,'color' : colors[index]}">
              <span v-if="total > 0">{{ (item.value * 100 / total).toFixed(0) }}%</span>
              <span v-else>{{ 0 }}%</span>

            </div>
            <div class="right-info">

              <div class="right-div__n2">
                <div class="font13 fcolor1" :title="item.name" :style="{
                      'text-overflow': 'ellipsis',
                      'overflow': 'hidden',
                      'word-break': 'break-all',
                      'white-space': 'nowrap',
                      'width': textWidth
                    }">
                  {{ item.name }}
                </div>
                <div class="font13">
                  {{ item.value }}
                </div>
              </div>
            </div>
          </div>
        </template>
      </div>
    </div>
  </div>
</template>
<script lang="ts">
import {defineComponent, PropType, reactive, Ref, ref, watch, watchEffect} from 'vue';
import {useECharts} from '/@/hooks/web/useECharts';
import {cloneDeep} from 'lodash-es';
import {generateColor} from "/@/components/chart/ChartColor";

export default defineComponent({
  name: 'PieLegend',
  props: {
    chartData: {
      type: Array,
      default: () => [],
    },
    size: {
      type: Object,
      default: () => {
      },
    },
    option: {
      type: Object,
      default: () => ({}),
    },
    width: {
      type: String as PropType<string>,
      default: '100%',
    },
    height: {
      type: String as PropType<string>,
      default: 'calc(100vh - 78px)',
    },
    rightDiv: {
      type: Boolean as PropType<boolean>,
      default: false,
    },
  },
  emits: ['click'],
  setup(props, {emit}) {
    const chartRef = ref<HTMLDivElement | null>(null);
    const {setOptions, getInstance, resize} = useECharts(chartRef as Ref<HTMLDivElement>);
    const total = ref<any>(0)
    const colors = ref([]);
    const textWidth = ref('100%');
    const option: any = reactive({

      tooltip: {
        formatter: '{b} ({c})',
      },
      series: [
        {
          type: 'pie',
          radius: '72%',
          center: ['50%', '50%'],
          data: [],
          labelLine: {show: true},
          label: {
            show: false,
            formatter: '{b} \n ({d}%)',
            color: 'rgba(255,255,255,0.8)',
          },
        },
      ],
    });


    watchEffect(() => {
      props.chartData && initCharts();
    });
    /**
     * 监听拖拽大小变化
     */
    watch(
      () => props.size,
      () => {
        resize();
      },
      {
        immediate: true,
      }
    );

    function initCharts() {

      let right_div = document.getElementsByClassName("right-div");
      if (right_div.length > 0) {
        let width = window.getComputedStyle(right_div[0]).width.replace("px", "");
        let left1_width = window.getComputedStyle(document.getElementsByClassName("right-div__b")[0]).width.replace("px", "");
        textWidth.value = (parseInt(width) - parseInt(left1_width) - 24 - 10) + "px";
      }
      if (props.option) {
        Object.assign(option, cloneDeep(props.option));
      }
      total.value = 0
      for (let i in props.chartData) {
        total.value += props.chartData[i].value
      }
      option.series[0].data = props.chartData;
      console.log(option.color)
      if (option.color && option.color.length > 0) {
        colors.value = option.color
      } else {
        option.color = generateColor('#F75555', props.chartData.length)
        colors.value = generateColor('#F75555', props.chartData.length)
      }

      setOptions(option);
      resize();
      getInstance()?.off('click', onClick);
      getInstance()?.on('click', onClick);
    }

    function onClick(params) {
      emit('click', params);
    }

    return {chartRef, total, colors, textWidth};
  },
});
</script>

<style scoped lang="less">


.pie_right_div {
  position: relative;

  .legend_wraper {
    position: absolute;
    width: 220px;
    height: calc(100%);
    top: 0;
    overflow: auto;
    right: 20px;
    display: flex;
    /*垂直排列*/
    flex-direction: column;
    align-items: center; /*由于flex-direction: column，因此align-items代表的是水平方向*/
    justify-content: center; /*由于flex-direction: column，因此justify-content代表的是垂直方向*/
  }

  .flex-start {
    justify-content: flex-start;
  }

  .right-div {
    width: 100%;
    height: 48px;
    background-color: @bg-color;
    display: flex;
    margin-bottom: 10px;
    align-items: center;

  }

  .right-div__b {
    height: calc(100% - 16px);
    margin: 8px 0px;
    text-indent: 8px;

    font-size: 20px;
    font-weight: 600;
    line-height: 32px;
    width: 55px;

  }

  .right-info {
    flex: 1;
    display: flex;
    margin-left: 24px;
  }
}
</style>
