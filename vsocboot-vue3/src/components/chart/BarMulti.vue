<template>
  <div ref="chartRef" :style="{ height, width }"></div>
</template>
<script lang="ts">
import {defineComponent, PropType, ref, Ref, reactive, watchEffect} from 'vue';
import {useECharts} from '/@/hooks/web/useECharts';
import {cloneDeep} from 'lodash-es';

export default defineComponent({
  name: 'Bar<PERSON>ult<PERSON>',
  props: {
    chartData: {
      type: Array,
      default: () => [],
      required: true,
    },
    markLine: {
      type: Array,
      default: () => [],
      required: false,
    },
    option: {
      type: Object,
      default: () => ({}),
    },
    type: {
      type: String as PropType<string>,
      default: 'bar',
    },
    width: {
      type: String as PropType<string>,
      default: '100%',
    },
    height: {
      type: String as PropType<string>,
      default: 'calc(100vh - 78px)',
    },
  },
  emits: ['click'],
  setup(props, {emit}) {
    const chartRef = ref<HTMLDivElement | null>(null);
    const {setOptions, getInstance} = useECharts(chartRef as Ref<HTMLDivElement>);
    const option: any = reactive({
      tooltip: {
        trigger: 'axis',
        formatter: (params) => {
          let map: any = {}
          for (let i in params) {
            let name = params[i].seriesName;
            if (!map[name]) {
              map[name] = 0
            }
            map[name] += params[i].value * 1
          }
          let html = ''
          for (let key in map) {
            if (html) {
              html += "<br>"
            }
            html += key + ' : ' + map[key]
          }
          return html
        },
        axisPointer: {
          type: 'shadow',
          label: {
            show: true,
            backgroundColor: '#333',
          },
        },
      },
      legend: {
        top: 0,
        // orient: 'vertical'
      },
      grid: {
        top: 50,
        left: 40,
        right: 40,
        bottom: 10,
        containLabel: true
      },
      xAxis: {
        type: 'category',
        data: [],
      },
      yAxis: {
        type: 'value',
      },
      series: [],
      animation: false
    });

    watchEffect(() => {
      props.chartData && initCharts();
    });

    function initCharts() {
      if (props.option) {
        Object.assign(option, cloneDeep(props.option));
      }
      //图例类型
      let typeArr = Array.from(new Set(props.chartData.map((item) => item.type)));
      //轴数据
      let xAxisData = Array.from(new Set(props.chartData.map((item) => item.name)));
      let seriesData: any = [];
      let index = 0
      typeArr.forEach((type) => {
        let obj: any = {name: type, type: props.type, barMaxWidth: 50, stack: type};
        let map: any = {}
        if (index == 0) {
          obj['markLine'] = {data: []}
          for (let i = 0; i < props.markLine?.length; i++) {
            let num = props.markLine[i]?.value as number
            obj.markLine.data.push({
              name: 'line',
              yAxis: num,
              lineStyle: {
                color: "rgba(227, 19, 19, 1)"
              }
            })
            if (num > 0) {
              let compare = props.markLine[i]?.compare
              map[compare] = num
            }
          }
        }
        // update-begin-author:liusq date:2023-7-12 for: [issues/613] LineMulti 在数据不对齐时，横坐标计算错误
        let data: any = [];
        xAxisData.forEach((x) => {
          let dataArr: any = props.chartData.filter((item) => type === item.type && item.name == x);
          if (dataArr && dataArr.length > 0) {
            data.push(dataArr[0].value);
          } else {
            data.push(null);
          }
        });
        // update-end-author:liusq date:2023-7-12 for: [issues/613] LineMulti 在数据不对齐时，横坐标计算错误
        //data数据
        obj['data'] = data;
        seriesData.push(obj)
        let obj2: any = {
          name: type, type: props.type, barMaxWidth: 50, stack: type, itemStyle: {
            color: "rgba(213, 102, 102, 1)"
          }
        };
        for (let key in map) {
          for (let i in data) {
            if (key == "gt") {
              if (data[i] > map[key]) {
                if (!obj2.data) {
                  obj2.data = []
                }
                obj2.data.push(data[i] - map[key])
                data[i] = map[key]
              }
            }
          }
        }
        if (obj2.data && obj2.data.length > 0) {
          seriesData.push(obj2)
        }
        console.log(seriesData)

        // seriesData.push(obj);
        index++
      });
      option.series = seriesData;
      option.xAxis.data = xAxisData;
      setOptions(option);
      getInstance()?.off('click', onClick);
      getInstance()?.on('click', onClick);
    }

    function onClick(params) {
      emit('click', params);
    }

    return {chartRef};
  },
});
</script>
