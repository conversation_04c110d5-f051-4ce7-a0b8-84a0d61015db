<template>
  <div v-if="chartData?.length > 0" ref="chartRef" :style="{ height, width }"></div>
  <div v-if="rightDiv"
       :class="['legend_wraper',{'flex-start' : chartData?.length > 4 ? true : false}]">

    <template v-for="(item,index) in chartData" :key="index">
      <div class="right-div font12">
        <div :style="{'color' : colors[index]}" class="legendName">{{ item.name }}</div>
        <div>
          <span style="color:#B4B4B6">Count:</span>
          <span style="margin:0px 10px">{{ item.value }}</span>
          <span>{{ (item.value * 100 / total).toFixed(0) == 'NaN' ? 0 : (item.value * 100 /
            total).toFixed(0)}}%</span>
        </div>
      </div>
    </template>

  </div>
</template>
<script lang="ts">
import {defineComponent, PropType, ref, Ref, watchEffect, reactive, watch} from 'vue';
import {useECharts} from '/@/hooks/web/useECharts';
import {cloneDeep} from 'lodash-es';
import {generateColor} from "/@/components/chart/ChartColor";

export default defineComponent({
  name: 'PieLegend',
  props: {
    color: {
      type: Array,
      default: () => [],
    },
    chartData: {
      type: Array,
      default: () => [],
    },
    size: {
      type: Object,
      default: () => {
      },
    },
    option: {
      type: Object,
      default: () => ({}),
    },
    width: {
      type: String as PropType<string>,
      default: '100%',
    },
    height: {
      type: String as PropType<string>,
      default: 'calc(100vh - 78px)',
    },
    rightDiv: {
      type: Boolean as PropType<boolean>,
      default: false,
    },
  },
  emits: ['click'],
  setup(props, {emit}) {
    const chartRef = ref<HTMLDivElement | null>(null);
    const {setOptions, getInstance, resize} = useECharts(chartRef as Ref<HTMLDivElement>);

    const option: any = reactive({

      tooltip: {
        formatter: '{b} ({c})',
      },
      series: [
        {
          type: 'pie',
          radius: '72%',
          center: ['50%', '50%'],
          data: [],
          labelLine: {show: true},
          label: {
            show: false,
            formatter: '{b} \n ({d}%)',
            color: 'rgba(255,255,255,0.8)',
          },
        },
      ],
    });
    const total = ref<number>(0)
    const colors = ref<any>([]);

    watchEffect(() => {
      props.chartData && initCharts();
    });
    /**
     * 监听拖拽大小变化
     */
    watch(
      () => props.size,
      () => {
        resize();
      },
      {
        immediate: true,
      }
    );

    function initCharts() {
      total.value = 0
      if (props.option) {
        Object.assign(option, cloneDeep(props.option));
      }
      for (let i in props?.chartData) {
        const data: any = props.chartData[i]
        console.log(data.value)
        total.value += data.value ?? 0
      }
      console.log(total)
      option.series[0].data = props.chartData;

      if (props.color) {
        option.color = props.color;
        colors.value = props.color;
      } else {
        option.color = generateColor('#F75555', props.chartData.length)
        colors.value = generateColor('#F75555', props.chartData.length)
      }
      setOptions(option);
      resize();
      getInstance()?.off('click', onClick);
      getInstance()?.on('click', onClick);
    }

    function onClick(params) {
      emit('click', params);
    }

    return {chartRef, total, colors};
  },
});
</script>

<style scoped lang="less">
.legend_wraper {
  position: absolute;
  width: 220px;
  height: calc(100%);
  top: 0;
  overflow: auto;
  right: 0px;
  display: flex;
  /*垂直排列*/
  flex-direction: column;
  align-items: center; /*由于flex-direction: column，因此align-items代表的是水平方向*/
  justify-content: center; /*由于flex-direction: column，因此justify-content代表的是垂直方向*/

  .legendName {
    min-width: 80px;
    font-weight: 600;
  }

}

.flex-start {
  justify-content: flex-start;
}

.right-div {
  width: 100%;
  display: flex;
  margin-bottom: 10px;
  align-items: center;

}

.right-div__b {
  height: calc(100% - 16px);
  border-width: 2px;
  margin: 8px 0px;
  text-indent: 8px;

  font-size: 20px;
  font-weight: 600;
  line-height: 32px;
  width: 55px;

}

.right-info {
  flex: 1;
  display: flex;
  margin-left: 24px;
}

</style>
