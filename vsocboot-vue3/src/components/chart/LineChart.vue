<template>
  <div ref="chartRef" :style="{ height, width }"></div>
</template>
<script lang="ts">
  import { defineComponent, PropType, ref, Ref, reactive, watchEffect } from 'vue';
  import { useECharts } from '/@/hooks/web/useECharts';
  import { cloneDeep } from 'lodash-es';
  import {
    axisLabelColor,
    axisLabelColorBlack,
    axisLineColor,
    axisPointerBg
  } from "/@/components/chart/ChartColor";

  export default defineComponent({
    name: 'LineChart',
    props: {
      color: {
        type: Array,
        default: () => [],
      },
      chartData: {
        type: Array,
        default: () => [],
        required: true,
      },
      option: {
        type: Object,
        default: () => ({}),
      },
      type: {
        type: String as PropType<string>,
        default: 'line',
      },
      width: {
        type: String as PropType<string>,
        default: '100%',
      },
      height: {
        type: String as PropType<string>,
        default: 'calc(100vh - 78px)',
      },
      reversalColor: {
        type: Boolean,
        default: false,
      },
    },
    emits: ['click'],
    setup(props, { emit }) {
      const chartRef = ref<HTMLDivElement | null>(null);
      const { setOptions, getInstance } = useECharts(chartRef as Ref<HTMLDivElement>);
      const option = reactive({
        tooltip: {
          trigger: 'axis',
          formatter: function(val){
            //console.log(val);
            var text = val[0].axisValue;
            for(let i=0;i<val.length;i++){
              text += "<br/>"+val[i].seriesName+":"+val[i].data;
            }
            return text;
          },
          axisPointer: {
            type: 'shadow',
            label: {
              show: true,
              backgroundColor: axisPointerBg,
            },
          },
        },
        legend: {
          show:true,
          orient: 'horizontal',
          align :  'right',
          right: 0,
          top: 0,
          formatter: function (name) {
            return name;
          },
          tooltip: {
            show: true
          }
        },
        grid: {
          top:50,
          bottom:20,
          left:50,
          right:10,
        },
        xAxis: {
          type: 'category',
          data: [],

          splitLine : {
            show : false
          },
          axisTick: {
            show : false
          },
          axisLabel: {
            color: props.reversalColor?axisLabelColorBlack:axisLabelColor
          },
          axisLine: {
            show: true,
            lineStyle: {
              color: props.reversalColor?axisLabelColorBlack:axisLineColor
            }
          },
        },
        yAxis: {
          type: 'value',
          splitLine : {
            show : false
          },
          axisTick: {
            show : false
          },
          axisLabel: {
            color: props.reversalColor?axisLabelColorBlack:axisLabelColor
          },
          axisLine: {
            show: true,
            lineStyle: {
              color: props.reversalColor?axisLabelColorBlack:axisLineColor
            }
          },
        },
        series: [],
      });

      watchEffect(() => {
        props.chartData && initCharts();
      });

      function initCharts() {
        if (props.option) {
          Object.assign(option, cloneDeep(props.option));
        }
        //图例类型
        let seriesData = [];
        if(props.chartData){
          let typeArr = props.chartData.legend || [];
          typeArr.forEach((type) => {
            let obj: any = { name: type, type: 'line',data:props.chartData[type]};
            seriesData.push(obj);
          });
          //轴数据
          option.xAxis.data = props.chartData.xAxis;
        }

        option.series = seriesData;

        if(props.color){
          option.color = props.color;
        }
        setOptions(option);
        getInstance()?.off('click', onClick);
        getInstance()?.on('click', onClick);
      }

      function onClick(params) {
        emit('click', params);
      }

      return { chartRef };
    },
  });
</script>
