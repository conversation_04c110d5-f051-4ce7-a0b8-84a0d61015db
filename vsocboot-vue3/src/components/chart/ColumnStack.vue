<template>
  <div ref="chartRef" :style="{ height, width }"></div>
</template>
<script lang="ts">
  import { defineComponent, PropType, ref, Ref, reactive, watchEffect } from 'vue';
  import { useECharts } from '/@/hooks/web/useECharts';
  import { cloneDeep } from 'lodash-es';
  import {axisLabelColor,axisLineColor,axisPointerBg,generateColor} from "./ChartColor"
  import {primaryColor} from "../../../build/config/themeConfig";
  export default defineComponent({
    name: 'columnStack',
    props: {
      chartData: {
        type: Object,
        default: () => {},
      },
      option: {
        type: Object,
        default: () => ({}),
      },
      width: {
        type: String as PropType<string>,
        default: '100%',
      },
      height: {
        type: String as PropType<string>,
        default: 'calc(100vh - 78px)',
      },
    },
    setup(props) {

      const chartRef = ref<HTMLDivElement | null>(null);
      const { setOptions, echarts } = useECharts(chartRef as Ref<HTMLDivElement>);
      const chartColor = ref([]);
      chartColor.value.push('#F75555');
      chartColor.value.push(primaryColor);
      const option = reactive({
        color: chartColor.value,
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow',
            label: {
              show: true,
              backgroundColor: axisPointerBg,
            },
          },
        },
        grid: {
          top:15,
          bottom:20,
          left:50,
          right:10,
        },
        xAxis: {
          type: 'category',
          data: [],
          axisLine : {
            show : true,
            lineStyle : {
              color : axisLineColor,
            }
          },
          axisLabel : {
            color : axisLabelColor
          },
          axisTick: {
            show : false
          }
        },
        yAxis: {
          type: 'value',
          axisLabel : {
            color : axisLabelColor
          },
          axisLine : {
            show : true,
            lineStyle : {
              color : axisLineColor,
            }
          },
          splitLine : {
            show : false
          },
          axisTick: {
            show : false
          }
        },
        series: [

        ],
      });

      watchEffect(() => {
        props.chartData.stack && initCharts();
      });

      function initCharts() {
        if (props.option) {
          Object.assign(option, cloneDeep(props.option));
        }
        let seriesData = [];
        if(props.chartData && props.chartData.stack){
          let typeArr = props.chartData.stack;
          typeArr.forEach((type) => {
            let obj: any = { name: type, type: 'bar', stack: 'total',data:props.chartData[type],barMaxWidth:'32px'};
            seriesData.push(obj);
          });
          option.xAxis.data = props.chartData.xAxis || [];
        }
        seriesData['barMaxWidth'] = 32;
        option.series = seriesData;
        setOptions(option);
      }
      return { chartRef };
    },
  });
</script>
