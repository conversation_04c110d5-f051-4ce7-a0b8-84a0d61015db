<template>
  <div ref="chartRef" :style="{ height, width }"></div>
</template>
<script lang="ts">
  import { defineComponent, PropType, ref, Ref, reactive, watchEffect } from 'vue';
  import { useECharts } from '/@/hooks/web/useECharts';
  import { cloneDeep } from 'lodash-es';
  import { primaryColor } from '../../../build/config/themeConfig';
  import {axisPointerBg} from "./ChartColor"

  export default defineComponent({
    name: 'BarBg',
    props: {
      color: {
        type: Array,
        default: () => [],
      },
      chartData: {
        type: Array,
        default: () => [],
      },
      option: {
        type: Object,
        default: () => ({}),
      },
      width: {
        type: String as PropType<string>,
        default: '100%',
      },
      height: {
        type: String as PropType<string>,
        default: 'calc(100vh - 78px)',
      },
      barColor :{
        type: Boolean as PropType<Boolean>,
        default:false
      },
    },
    emits: ['click'],
    setup(props, { emit }) {
      const chartRef = ref<HTMLDivElement | null>(null);
      const { setOptions, getInstance } = useECharts(chartRef as Ref<HTMLDivElement>);
      const chartColor = ref<any>([]);
      chartColor.value.push(primaryColor);

      const option = reactive<any>({
        tooltip: {
          // trigger: 'axis',
          formatter: '{b}: {c}',
          axisPointer: {
            type: 'shadow',
            label: {
              show: false,
              backgroundColor: axisPointerBg,
            },
          },
        },
        grid: {
          top:10,
          bottom:20,
          left:100,
          right:10,
        },
        xAxis: {
          type: 'value',
          axisLine : {
            show : false
          },
          splitLine : {
            show : false
          },
          axisTick: {
            show : false
          }
        },
        yAxis: {
          type: 'category',
          data: [],
          axisLine : {
            show : false
          },
          splitLine : {
            show : false
          },
          axisTick: {
            show : false
          },
          axisLabel: {
            formatter: function (value) {
              var label1 = value.split(" ")[0];
              var label2 = value.split(" ")[1] || '';
              let text = '{a|'+ label1 + '}'
              if(label2){
                text += '\n{b|' + label2 + '}'
              }
              return text;
            },
            rich: {
              a: {
                color: 'rgba(255, 255, 255, 0.8)',
                fontSize: 13,
                lineHeight: 20,
                align: 'right'
              },

              b: {
                color: primaryColor,
                fontSize: 13,
                fontWeight: 'bold',
                lineHeight: 20
              },

            }
          }
        },
        series: [
          {
            name: 'bar',
            type: 'bar',
            barMaxWidth:'48',
            // showBackground: true,
            // backgroundStyle: {
            //   color: 'rgba(255, 255, 255, 0.08)'
            // },
            data: [],
          },
        ],
      });

      watchEffect(() => {
        props.chartData && initCharts();
      });

      function initCharts() {
        if (props.option) {
          Object.assign(option, cloneDeep(props.option));
        }

        let seriesData = props.chartData.map((item:any) => {
          return item.value;
        });
        let xAxisData = props.chartData.map((item:any) => {
          return item.name;
        });
        option.color = chartColor.value
        option.series[0].data = seriesData;

        option.yAxis.data = xAxisData;
        let colorSeries:any = chartColor.value;
        if(props.color){
          option.color = props.color;
          colorSeries = props.color;
        }

        if(props.barColor){
          option.series[0].itemStyle = {
            color: function(p){
              return colorSeries[p.dataIndex];
            }
          }
        }
        setOptions(option);
        getInstance()?.off('click', onClick);
        getInstance()?.on('click', onClick);
      }

      function onClick(params) {
        emit('click', params);
      }


      return { chartRef };
    },
  });
</script>
