<template>


  <div class="ax-label ax-label-red" v-if="status == 1">
    <span class="soc ax-com-Danger ax-icon"></span>
    <div>{{ t('routes.RiskEventLogView.true') }}</div>
  </div>

  <div class="ax-label ax-label-cyan" v-else-if="status == 2">
    <span class="soc ax-com-Success ax-icon"></span>
    <div>{{ t('routes.RiskEventLogView.false') }}</div>
  </div>

  <div class="ax-label ax-label-yellow" v-else-if="status == 3">
    <span class="soc ax-com-Wait ax-icon"></span>
    <div>{{ t('routes.RiskEventLogView.other') }}</div>
  </div>

  <div class="ax-label ax-label-yellow" v-else>
    <span class="soc ax-com-Help ax-icon"></span>
    <div> {{ t('common.Untriaged') }}</div>
  </div>


</template>

<script setup lang="ts">
import {defineProps} from "vue";
import {useI18n} from "/@/hooks/web/useI18n";

const {t} = useI18n();
defineProps({
  status: Number
});
</script>

<style scoped lang="less">
 

</style>
