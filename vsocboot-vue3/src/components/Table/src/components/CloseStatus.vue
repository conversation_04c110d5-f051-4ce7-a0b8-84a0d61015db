<template>

  <div class="unclose_status_div" v-if="status === 1">
    <Icon icon="ant-design:warning-filled"/>
    <span>
        {{ t('common.Unclosed') }}
      </span>
  </div>

  <div class="close_status_div" v-if="status === 2">
    <Icon icon="ant-design:check-circle-filled"/>
    <span>
        {{ t('common.Closed') }}
      </span>
  </div>

</template>

<script setup lang="ts">
import {defineProps} from "vue";
import {useI18n} from "/@/hooks/web/useI18n";

const {t} = useI18n();
defineProps({
  status: Number
});
</script>

<style scoped lang="less">
.close_status_div {
  display: flex;
  gap: 4px;
  color: #2ECF99;
  align-items: center;
}

.unclose_status_div {
  display: flex;
  gap: 4px;
  color: #F75555;
  align-items: center;
}
</style>
