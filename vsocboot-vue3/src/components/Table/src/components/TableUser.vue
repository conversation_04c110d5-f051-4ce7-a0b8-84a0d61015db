<template>


  <div class="table_user_div f-color-1" v-if="value">
    <div class="user_top ft13-bold">
      {{ value.substring(0, 1).toUpperCase() }}
    </div>
    <div class="ft12">
      {{ value }}
    </div>
  </div>


</template>

<script setup lang="ts">
import {defineProps} from "vue";

defineProps({
  value: String
});
</script>

<style scoped lang="less">
.table_user_div {
  display: flex;
  gap: 8px;
  align-items: center;

  .user_top {
    width: 24px;
    height: 24px;
    border-radius: 12px;
    background: @m-color;
    display: flex;
    align-items: center;
    justify-content: center;
  }
}


</style>
