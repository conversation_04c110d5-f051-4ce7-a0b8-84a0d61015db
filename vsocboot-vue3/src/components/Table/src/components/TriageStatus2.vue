<template>


  <div class="triage_status2_div1" v-if="status == 1">
    {{ t('common.TurePositive') }}
  </div>

  <div class="triage_status2_div2" v-else-if="status == 2">
    {{ t('common.FalsePositive') }}
  </div>

  <div class="triage_status2_div0" v-else>
    {{ t('common.Untriaged') }}
  </div>


</template>

<script setup lang="ts">
import {defineProps} from "vue";
import {useI18n} from "/@/hooks/web/useI18n";

const {t} = useI18n();
defineProps({
  status: Number
});
</script>

<style scoped lang="less">
.triage_status2_div0 {
  display: inline-block;
  color: #F6C84D;
  background: rgba(246, 200, 77, 0.2);
  padding: 4px 8px;
  border-radius: 4px;
}

.triage_status2_div1 {
  display: inline-block;
  color: #308CFF;
  background: rgba(48, 140, 255, 0.2);
  padding: 4px 8px;
  border-radius: 4px;
}

.triage_status2_div2 {
  display: inline-block;
  color: #F75555;
  background: rgba(247, 85, 85, 0.2);
  padding: 4px 8px;
  border-radius: 4px;
}


</style>
