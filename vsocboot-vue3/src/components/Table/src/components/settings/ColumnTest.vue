<template>
  <!-- <Teleport to="#filter-button"> -->
  <Tooltip placement="topRight" v-bind="getBindProps">
    <template #title>
      <span>{{ t('component.table.settingColumn') }}</span>
    </template>
    <Popover
      v-model:visible="popoverVisible"
      placement="bottomLeft"
      trigger="click"
      @visible-change="handleVisibleChange"
      :overlayClassName="`${prefixCls}__cloumn-list`"
      :getPopupContainer="getPopupContainer"
    >
      <template #content>
        <ScrollContainer ref="scroll">
          <!-- show item -->
          <!-- hide item -->
          <div class="w-[100%]" ref="columnListRef">
            <!-- 搜索框 -->
            <div class="pl-14px pr-8px mb-16px">
              <a-input
                v-model:value="fieldValue"
                :placeholder="t('common.column.fieldSearchPlaceholder')"
                style="width: 200px"
                @change="onSearch(fieldValue)"
                allow-clear
              >
                <template #suffix>
                  <span class="soc ax-com-Search"/>
                </template>

              </a-input>
            </div>
            <div class="w-[calc(100%-8px)] h-1px bg-[#28282C]" style="margin: 12px auto"></div>
            <!-- end -->
            <div class="mb-16px pl-16px">{{ t('common.showitem') }}</div>
            <div ref="visibleDrap1" id="visibleDrap1" class="px-4px">
              <div
                v-for="i in showOptions"
                :key="i.value"
                class="visibleItem flex items-center pt-8px pb-8px pl-16px pr-16px"
                :style="i.isSearch ? 'display:none' : ''"
                :class="{ 'ignore-elements': showOptions.length === 1 && !isSearch }"
              >
                <div class="flex items-center mr-12px"> <MoreOutlined class="w-0.5em" /> <MoreOutlined class="w-0.5em" /></div>
                <div>{{ i.label }}</div>
              </div>
            </div>
            <div class="w-[calc(100%-8px)] h-1px bg-[#28282C]" style="margin: 12px auto"></div>
            <div class="mb-16px pl-16px">{{ t('common.hideitem') }}</div>
            <div ref="invisibleDrap1" id="invisibleDrap1" class="px-4px">
              <div
                v-for="i in hideOptions"
                :key="i.value"
                :style="i.isSearch ? 'display:none' : ''"
                class="invisibleItem flex items-center pt-8px pb-8px pl-16px pr-16px"
              >
                <div class="flex items-center mr-12px"> <MoreOutlined class="w-0.5em" /> <MoreOutlined class="w-0.5em" /></div>
                <div>{{ i.label }}</div>
              </div>
            </div>
          </div>
        </ScrollContainer>
      </template>
      <MenuOutlined />
    </Popover>
  </Tooltip>
</template>
<script lang="ts">
  import type { BasicColumn, ColumnChangeParam } from '../../types/table';
  import { computed, defineComponent, nextTick, reactive, ref, toRefs, unref, watchEffect, inject } from 'vue';
  import { Checkbox, Divider, message, Popover, Tooltip } from 'ant-design-vue';
  import { DragOutlined, MenuOutlined, MoreOutlined, SettingOutlined } from '@ant-design/icons-vue';
  import { Icon } from '/@/components/Icon';
  import { ScrollContainer } from '/@/components/Container';
  import { useI18n } from '/@/hooks/web/useI18n';
  import { useTableContext } from '../../hooks/useTableContext';
  import { useColumnsCache } from '../../hooks/useColumnsCache';
  import { useDesign } from '/@/hooks/web/useDesign';
  // import { useSortable } from '/@/hooks/web/useSortable';
  import { isFunction, isNullAndUnDef } from '/@/utils/is';
  import { getPopupContainer as getParentContainer } from '/@/utils';
  import { omit, cloneDeep } from 'lodash-es';
  import Sortablejs from 'sortablejs';
  import { createLocalStorage } from '/@/utils/cache';

  interface State {
    checkAll: boolean;
    isInit?: boolean;
    checkedList: string[];
    defaultCheckList: string[];
    sortedList: string[];
  }

  interface Options {
    label: string;
    value: string;
    fixed?: boolean | 'left' | 'right';
  }

  export default defineComponent({
    name: 'ColumnSetting',
    props: {
      isMobile: Boolean,
    },
    components: {
      SettingOutlined,
      Popover,
      Tooltip,
      Checkbox,
      CheckboxGroup: Checkbox.Group,
      DragOutlined,
      ScrollContainer,
      Divider,
      Icon,
      MoreOutlined,
      MenuOutlined,
    },
    emits: ['columns-change','save-columns'],

    setup(props, { emit, attrs }) {
      const { t } = useI18n();
      const table = useTableContext();
      const popoverVisible = ref(true);
      const fieldValue = ref('');
      // update-begin--author:sunjianlei---date:20221101---for: 修复第一次进入时列表配置不能拖拽
      nextTick(() => (popoverVisible.value = false));
      // update-end--author:sunjianlei---date:20221101---for: 修复第一次进入时列表配置不能拖拽
      const defaultRowSelection = omit(table.getRowSelection(), 'selectedRowKeys');
      let inited = false;
      const plainOptions = ref<any[] | any>([]);

      const plainSortOptions = ref<any[]>([]);

      const columnListRef = ref<ComponentRef>(null);
      const showOptions = ref<Options[] | any>([]);
      const hideOptions = ref<Options[] | any>([]);
      const originOptions = ref<any>({
        show: [],
        hide: [],
      });
      const isSearch = ref(false);
      const visibleDrap1 = ref();
      const invisibleDrap1 = ref();

      const state = reactive<State>({
        checkAll: true,
        checkedList: [],
        defaultCheckList: [],
        sortedList: [],
      });
      const showSearch = ref(false);
      const scroll = ref();
      const checkIndex = ref(false);
      const checkSelect = ref(false);

      const { prefixCls } = useDesign('basic-column-setting');

      const getValues = computed(() => {
        return unref(table?.getBindValues) || {};
      });

      const getBindProps = computed(() => {
        let obj = {};
        if (props.isMobile) {
          obj['visible'] = false;
        }
        return obj;
      });

      const sortableOrder = ref<string[]>();
      // 列表字段配置缓存
      const { saveSetting, resetSetting, cacheKey } = useColumnsCache(
        {
          state,
          popoverVisible,
          plainOptions,
          plainSortOptions,
          sortableOrder,
          checkIndex,
        },
        setColumns,
        handleColumnFixed
      );

      watchEffect(() => {
        setTimeout(() => {
          const columns = table.getColumns();
          if (columns.length && !state.isInit) {
            init();
          }
        }, 0);
      });

      watchEffect(() => {
        const values = unref(getValues);
        checkIndex.value = !!values.showIndexColumn;
        checkSelect.value = !!values.rowSelection;
      });

      function getColumns() {
        const ret: Options[] = [];
        const all = table.getColumns({ignoreIndex: true, ignoreAction: true});
        for (let i = 0; i < all.length; i++) {
          const item = all[i];
          if(item.ifShow != false){
            ret.push({
              label: (item.title as string) || (item.customTitle as string),
              value: (item.dataIndex || item.title) as string,
              ...item,
            });
          }
        }
        return ret;
      }

      function init() {
        const columns = getColumns();
        const $ls = createLocalStorage();
        let columnCache = $ls.get(cacheKey.value);
        if (columnCache && columnCache.sortedList) {
          columns.sort((prev, next) => {
            return columnCache.sortedList.indexOf(prev.value) - columnCache.sortedList.indexOf(next.value);
          });
          state.sortedList = columnCache.sortedList;
        } else {
          state.sortedList = columns.map((item) => item?.value ?? '');
        }

        const checkList = table
          .getColumns({ ignoreAction: true })
          .map((item) => {
            if(item.ifShow != false){
              if (item.defaultHidden ) {
                return '';
              }
              return item.dataIndex || item.title;
            }
          })
          .filter(Boolean) as string[];
        if (!plainOptions.value.length) {
          plainOptions.value = columns;
          plainSortOptions.value = columns;
        } else {
          unref(plainOptions).forEach((item: BasicColumn) => {
            const findItem = columns.find((col: BasicColumn) => col.dataIndex === item.dataIndex);
            if (findItem) {
              item.fixed = findItem.fixed;
            }
          });
        }
        unref(plainOptions).forEach((item: any) => {
          item.isShow = checkList.some((col) => col === item.value);
          item.isSearch = false;
          if (item.isShow) {
            unref(showOptions).push(item);
            const i: any = cloneDeep(item);
            unref(originOptions).show.push(i);
          } else {
            unref(hideOptions).push(item);
            const i: any = cloneDeep(item);
            unref(originOptions).hide.push(i);
          }
        });
        state.isInit = true;
        state.checkedList = checkList;
      }

      // 新的拖拽逻辑
      function handleVisibleChange(flag: boolean) {
        if (flag && inited) {
          fieldValue.value = '';
          onSearch('');
          scroll.value?.setScrollTop();
        }
        if (inited) return;
        nextTick(() => {
          const columnListEl = unref(columnListRef);
          const showEL = visibleDrap1.value as HTMLElement;
          const invisibleEL = invisibleDrap1.value as HTMLElement;
          if (!columnListEl) return;
          // const el = columnListEl.$el as any;
          // if (!el) return;

          const sortable1 = new Sortablejs(showEL, {
            animation: 500,
            delay: 400,
            scroll: true,
            handle: '.visibleItem',
            draggable: '.visibleItem',
            delayOnTouchOnly: true,
            filter: '.ignore-elements',
            group: 'shared',
            onFilter: function () {
              if (showOptions.value.length === 1) {
                message.error(t('component.table.lastElTips'), 1);
                return;
              }
            },
            onEnd: function (evt) {
              if (isNullAndUnDef(evt.oldIndex) || isNullAndUnDef(evt.newIndex)) {
                return;
              }
              if (evt.to.id === evt.from.id && evt.oldIndex === evt.newIndex) {
                return;
              }
              if (evt.to.id === 'invisibleDrap1') {
                let itemToAdd: null | object = null;
                if (evt.oldIndex !== null) {
                  itemToAdd = showOptions.value.splice(evt.oldIndex, 1)[0];
                }
                if (evt.newIndex !== null && itemToAdd) {
                  const itemNew = { ...itemToAdd, isShow: false, defaultHidden: true };
                  hideOptions.value.splice(evt.newIndex, 0, itemNew);
                }
              } else {
                let itemToAdd = null;
                if (evt.oldIndex !== null) {
                  itemToAdd = showOptions.value.splice(evt.oldIndex, 1)[0];
                }
                if (evt.newIndex !== null && itemToAdd) {
                  showOptions.value.splice(evt.newIndex, 0, itemToAdd);
                }
              }
              setColumns([...hideOptions.value, ...showOptions.value]);
              state.checkedList = showOptions.value.map((i) => i.value);
              state.sortedList = showOptions.value.map((i) => i.value);
              state.sortedList.push(...hideOptions.value.map((i) => i.value));
              saveSetting();
              emit('save-columns');
            },
          });
          const sortable2 = new Sortablejs(invisibleEL, {
            animation: 500,
            handle: '.invisibleItem',
            group: 'shared',
            onEnd: function (evt) {
              if (isNullAndUnDef(evt.oldIndex) || isNullAndUnDef(evt.newIndex)) {
                return;
              }
              if (evt.to.id === evt.from.id && evt.oldIndex === evt.newIndex) {
                return;
              }
              if (evt.to.id === 'visibleDrap1') {
                let itemToAdd: null | object = null;
                if (evt.oldIndex !== null) {
                  itemToAdd = hideOptions.value.splice(evt.oldIndex, 1)[0];
                }
                if (evt.newIndex !== null && itemToAdd) {
                  const itemNew = { ...itemToAdd, isShow: true, defaultHidden: false };
                  showOptions.value.splice(evt.newIndex, 0, itemNew);
                }
              } else {
                let itemToAdd = null;
                if (evt.oldIndex !== null) {
                  itemToAdd = hideOptions.value.splice(evt.oldIndex, 1)[0];
                }
                if (evt.newIndex !== null && itemToAdd) {
                  hideOptions.value.splice(evt.newIndex, 0, itemToAdd);
                }
              }

              setColumns([...hideOptions.value, ...showOptions.value]);
              state.checkedList = showOptions.value.map((i) => i.value);
              state.sortedList = showOptions.value.map((i) => i.value);
              state.sortedList.push(...hideOptions.value.map((i) => i.value));
              saveSetting();
              emit('save-columns');
            },
          });
          inited = true;
        });
      }

      function handleColumnFixed(item: BasicColumn, fixed?: 'left' | 'right') {
        if (!state.checkedList.includes(item.dataIndex as string)) return;

        const columns = getColumns() as BasicColumn[];
        const isFixed = item.fixed === fixed ? false : fixed;
        const index = columns.findIndex((col) => col.dataIndex === item.dataIndex);
        if (index !== -1) {
          columns[index].fixed = isFixed;
        }
        item.fixed = isFixed;

        if (isFixed && !item.width) {
          item.width = 100;
        }
        table.setCacheColumnsByField?.(item.dataIndex as string, { fixed: isFixed });
        setColumns(columns);
      }

      function setColumns(columns: BasicColumn[] | string[]) {
        table.setColumns(columns);
        const data: ColumnChangeParam[] = unref(plainSortOptions).map((col) => {
          const visible =
            columns.findIndex((c: BasicColumn | string) => c === col.value || (typeof c !== 'string' && c.dataIndex === col.value)) !== -1;
          return { dataIndex: col.value, fixed: col.fixed, visible };
        });
        emit('columns-change', data);
      }

      function getPopupContainer() {
        return isFunction(attrs.getPopupContainer) ? attrs.getPopupContainer() : getParentContainer();
      }
      function onSearch(value: string) {
        console.log('onSearch',value)
        scroll.value?.setScrollTop();
        // if (!value) {
        //   showOptions.value = showOptions.value.map((item) => ({
        //     ...item,
        //     isSearch: false,
        //   }));

        //   hideOptions.value = hideOptions.value.map((item) => ({
        //     ...item,
        //     isSearch: false,
        //   }));
        //   isSearch.value = false;
        //   return;
        // }
        // isSearch.value = true;
        // showOptions.value = showOptions.value.map((item) => {
        //   const isSearch = item.label.toLowerCase().indexOf(value.toLowerCase()) !== -1;

        //   return {
        //     ...item,
        //     isSearch: !isSearch,
        //   };
        // });

        // hideOptions.value = hideOptions.value.map((item) => {
        //   const isSearch = item.label.toLowerCase().indexOf(value.toLowerCase()) !== -1;

        //   return {
        //     ...item,
        //     isSearch: !isSearch,
        //   };
        // });
        // 优化
        const updateSearchStatus = (options, value) => {
          console.log('options',options)
          return options.map((item) => ({
            ...item,
            isSearch: value ? item?.label.toLowerCase().indexOf(value.toLowerCase()) === -1 : false,
          }));
        };

        if (!value) {
          showOptions.value = updateSearchStatus(showOptions.value, '');
          hideOptions.value = updateSearchStatus(hideOptions.value, '');
          isSearch.value = false;
        } else {
          showOptions.value = updateSearchStatus(showOptions.value, value);
          hideOptions.value = updateSearchStatus(hideOptions.value, value);
          isSearch.value = true;
        }
      }

      return {
        fieldValue,
        onSearch,
        getBindProps,
        t,
        ...toRefs(state),
        popoverVisible,
        plainOptions,
        saveSetting,
        prefixCls,
        columnListRef,
        visibleDrap1,
        invisibleDrap1,
        handleVisibleChange,
        checkIndex,
        checkSelect,
        defaultRowSelection,
        handleColumnFixed,
        getPopupContainer,
        showSearch,
        showOptions,
        hideOptions,
        isSearch,
        originOptions,
        scroll,
      };
    },
  });
</script>
<style lang="less">
  @prefix-cls: ~'@{namespace}-basic-column-setting';

  .table-column-drag-icon {
    // margin: 0 5px;
    cursor: move;
  }

  .@{prefix-cls} {
    &__popover-title {
      position: relative;
      display: flex;
      align-items: center;
      justify-content: space-between;
    }

    /* 卡片底部样式 */

    &__popover-footer {
      position: relative;
      top: 7px;
      text-align: right;
      padding: 4px 0 0;
      border-top: 1px solid @border-color;

      .ant-btn {
        margin-right: 6px;
      }
    }

    &__check-item {
      display: flex;
      align-items: center;
      min-width: 100%;
      padding: 4px 16px 8px 0;

      .ant-checkbox-wrapper {
        width: 100%;

        &:hover {
          color: @primary-color;
        }
      }
    }

    &__fixed-left,
    &__fixed-right {
      color: rgba(0, 0, 0, 0.45);
      cursor: pointer;

      &.active,
      &:hover {
        color: @primary-color;
      }

      &.disabled {
        color: @disabled-color;
        cursor: not-allowed;
      }
    }

    &__fixed-right {
      transform: rotate(180deg);
    }

    &__cloumn-list {
      svg {
        width: 1em !important;
        height: 1em !important;
      }

      .ant-popover-inner-content {
        // max-height: 360px;
        padding-right: 0;
        padding-left: 0;
        // overflow: auto;
      }

      .ant-checkbox-group {
        width: 100%;
        min-width: 260px;
        // flex-wrap: wrap;
      }

      .scrollbar {
        // min-height: 220px;
        max-height: 60vh;
        min-height: 200px;
        overflow-y: auto!important;
      }
    }
  }
</style>

<style lang="less" scoped>
  .visibleItem,
  .invisibleItem {
    &:hover {
      @apply siem-gradient-bg;
      cursor: pointer;
      border-radius: 8px;
    }
    &:active {
      cursor: pointer;
      @apply siem-gradient-bg;
    }
  }
  .invisibleItem {
    color: rgba(255, 255, 255, 0.2);
  }
</style>
