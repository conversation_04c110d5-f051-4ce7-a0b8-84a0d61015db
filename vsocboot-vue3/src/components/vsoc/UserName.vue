<template>
  <div class="user_wraper" :style="{ width: column.width ? column.width + 'px' : '' }">
    <template v-if="record?.avatar">
      <img :src="render.renderUploadImageSrc(record?.avatar)" />
    </template>
    <div class="avatar_wrapper_img" v-else>
      <div class="img_title">{{ (record?.username ?? record?.userName).substring(0, 1).toUpperCase() }}</div>
    </div>

    <div class="user_detail" v-show="!unShowUsername">
      <div class="user_detail_name" :title="(record?.username ?? record?.userName) || ''">{{ (record?.username ?? record?.userName) || '' }}</div>
      <div class="user_detail_info" :title="record?.email || ''">{{ record?.email || '' }}</div>
    </div>
  </div>
</template>

<script lang="ts" name="UserName" setup>
  import { defineProps } from 'vue';
  import { render } from '/@/utils/common/renderUtils';

  const props = defineProps({
    record: {
      type: Object,
      default: () => {},
    },
    unShowUsername: {
      type: Boolean,
      default: false,
    },
    column: {
      type: Object as any,
      default: () => ({}),
    },
  });
</script>

<style lang="less" scoped>
  .user_wraper {
    display: flex;
    align-items: center;
    justify-content: left;

    img {
      width: 24px;
      height: 24px;
      border-radius: 50%;
    }

    .user_detail {
      flex: 1;
      text-align: left;
      margin-left: 5px;
      font-size: 12px;
      line-height: 16px;
      min-width: 0;
      .user_detail_info {
        color: rgba(255, 255, 255, 0.4);
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
      }
      .user_detail_name {
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
      }
    }
  }

  .avatar_wrapper_img {
    width: 24px;
    height: 24px;
    border-radius: 50%;
    background: @primary-color;
    display: flex;
    align-items: center;
    justify-content: center;

    .img_title {
      font-size: 12px;
    }

    .ant-upload-list {
      display: none;
    }
  }
</style>
