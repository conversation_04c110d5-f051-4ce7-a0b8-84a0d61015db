<template>
  <div class="user_wraper">
    <template  v-if="userData.avatar">
      <img :src="render.renderUploadImageSrc(userData.avatar)"/>
    </template>

    <div class="avatar_wrapper_img" v-else>
      <div class="img_title">{{userData.username.substring(0,1).toUpperCase()}}</div>
    </div>
    <div class="user_detail" v-if="props.detail">
      <div class="user_detail_name">{{userData.username  || ''}}</div>
      <div class="user_detail_info">{{userData.email || ''}}</div>
    </div>
  </div>
</template>

<script lang="ts" name="table-user" setup>
import {defHttp} from "/@/utils/http/axios";
import {onMounted, defineProps, reactive} from "vue";
import {render} from "/@/utils/common/renderUtils";
const props = defineProps({
  value: String,
  detail: {
    type: Boolean,
    default: true
  }
});
const userData = reactive({
  username: '',
  email: '' ,
  avatar: ''
});
onMounted(() => {
  setTimeout(()=>{
    props.value && getUserInfo({userNames:props.value})
  },100)
});

async function getUserInfo(params){
  const data =  await defHttp.get({url: '/sys/user/queryByNames', params});
  if(data){
    userData.avatar = data[0].avatar || '';
    userData.email = data[0].email;
    userData.username = data[0].username;
  }

}
</script>

<style lang="less" scoped>
.avatar_wrapper_img{
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background: @primary-color;
  display: flex;
  align-items: center;
  justify-content: center;
  .img_title{
    font-size:12px
  }
  .ant-upload-list{
    display: none
  }
}
.user_wraper{
  display: flex;
  align-items: center;
  justify-content: left;
  img{
    width: 24px;
    height: 24px;
    border-radius: 50%;
  }
  .user_detail{
    flex: 1;
    text-align: left;
    margin-left: 5px;
    font-size: 12px;
    line-height: 16px;

    .user_detail_info{
      color: rgba(255, 255, 255,0.4);
    }
  }
}
</style>
