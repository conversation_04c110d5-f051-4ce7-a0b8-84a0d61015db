<template>
  <div class="user_wraper">
    <template v-if="userData.avatar">
      <img :src="render.renderUploadImageSrc(userData.avatar)" />
    </template>

    <div class="avatar_wrapper_img" v-else>
      <div class="img_title" :title="userData.username">
        {{ userData.username && userData.username.substring(0, 1).toUpperCase() }}
      </div>
    </div>
    <div class="user_detail" v-if="props.detail">
      <div class="user_detail_name">{{ userData.username || '' }}</div>
      <div class="user_detail_info" :title="userData.email || ''">{{ userData.email || '' }}</div>
    </div>
  </div>
</template>

<script lang="ts" name="table-user" setup>
  import { defineProps, reactive, watch } from 'vue';
  import { render } from '/@/utils/common/renderUtils';

  const props = defineProps({
    value: {
      type: Object as any,
      default: () => {
        return {
          username: '',
          email: '',
          avatar: '',
        };
      },
    },
    detail: {
      type: Boolean,
      default: true,
    },
  });
  let userData = reactive<any>({});
  watch(
    () => props.value,
    () => {
      userData = { ...props.value };
    },
    { deep: true, immediate: true }
  );
</script>

<style lang="less" scoped>
  .avatar_wrapper_img {
    width: 24px;
    height: 24px;
    border-radius: 50%;
    background: @primary-color;
    display: flex;
    align-items: center;
    justify-content: center;

    .img_title {
      font-size: 12px;
    }

    .ant-upload-list {
      display: none;
    }
  }

  .user_wraper {
    display: flex;
    align-items: center;
    justify-content: left;
    gap: 8px;
    img {
      width: 24px;
      height: 24px;
      border-radius: 50%;
    }

    .user_detail {
      flex: 1;
      text-align: left;
      font-size: 12px;
      line-height: 16px;
      min-width: 0;

      .user_detail_info {
        color: rgba(255, 255, 255, 0.4);
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
      }

      .user_detail_name {
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
      }
    }
  }
</style>
