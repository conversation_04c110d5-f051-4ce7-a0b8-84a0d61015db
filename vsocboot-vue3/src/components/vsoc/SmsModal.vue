<template>
  <BasicModal
    @register="registerModal" :title="t('sys.login.sendSms')"
    @ok="handleSubmit" width="600px" :minHeight="50">
    <div style="padding: 16px;">
      <a-form :model="formState" ref="formRef">
        <a-form-item label="" name="smsCode" required>
          <div style="display: flex;gap: 10px;align-items: center;">
            <a-input v-model:value="formState.smsCode" :placeholder="t('sys.login.smsCode')"/>
            <a-button size="small" @click="send" type="primary" :disabled="sendFlag">
              {{ t('sys.login.sendSms') }} {{ timeText }}
            </a-button>
          </div>
        </a-form-item>
      </a-form>
    </div>
  </BasicModal>
</template>

<script setup lang="ts">
import {useI18n} from "/@/hooks/web/useI18n";
import {BasicModal, useModalInner} from "/@/components/Modal";
import {ref, unref} from "vue";
import {sendSmsByLogin} from "/@/api/sys/user";

const emits = defineEmits(["ok"]);
const {t} = useI18n();
const formRef = ref();
const formState = ref<any>({smsCode: ''});
const sendFlag = ref(false);
const timeText = ref("");
let total = 60;
let timeId: any;
const [registerModal, {closeModal, setModalProps}] = useModalInner(() => {
  formState.value.smsCode = "";
});


/**
 * 发送短信
 */
function send() {
  setModalProps({loading: true});
  total = 60;
  try {
    sendSmsByLogin().then(data => {
      console.log(data)
      sendFlag.value = true;
      timeText.value = " (60s)";
      timeId = setInterval(() => {
        total--;
        if (total <= 0) {
          clearInterval(timeId);
          sendFlag.value = false;
          timeText.value = "";
          return;
        }
        timeText.value = " (" + total + "s)";
      }, 1000)
      setModalProps({loading: false});
    })
  } catch (error) {
    console.log(error)
  } finally {
    if (timeId) {
      clearInterval(timeId);
    }
    sendFlag.value = false;
    setModalProps({loading: false});
  }
}

async function handleSubmit() {

  await formRef.value.validate();

  emits("ok", unref(formState).smsCode);
  //关闭弹窗
  closeModal();
}
</script>


<style scoped lang="less">

</style>
