<template>
  <div class="tag-container" ref="tagContainer" @click.stop>
    <template v-for="(tag, index) in visibleTags" :key="index">
      <div class="ax-label">{{ tag }}</div>
    </template>
    <!-- 如果没有标签，则不显示任何内容 -->
    <a-dropdown v-if="hiddenTags.length > 0" trigger="click">
      <!-- 如果有隐藏的标签，显示下拉菜单和数字指示器 -->
      <div @click.stop class="ax-label ax-icon-small cursor-pointer"> <span class="soc ax-com-Add" style="font-size: 14px;"></span>{{ hiddenTags.length }}</div>
      <template #overlay>
        <a-menu>
          <a-menu-item :key="index" v-for="(tag, index) in hiddenTags">
            {{ tag }}
          </a-menu-item>
        </a-menu>
      </template>
    </a-dropdown>
  </div>
</template>

<script setup>
  import { ref, onMounted, watch, nextTick } from 'vue';

  const props = defineProps({
    tags: {
      type: Array,
      default: () => [],
    },
    maxHeight: {
      type: Number,
      default: 56,
    },
  });

  const tagContainer = ref(null);
  const visibleTags = ref([]);
  const hiddenTags = ref([]);

  const updateTags = async () => {
    if (!tagContainer.value) return;

    // 先重置
    visibleTags.value = [];
    hiddenTags.value = [];

    // 先将所有标签添加到visibleTags中
    visibleTags.value = [...props.tags];

    // 等待DOM更新后再测量
    await nextTick();
    if (!tagContainer.value) return;

    // 检查容器高度是否超出最大高度
    if (tagContainer.value.offsetHeight <= props.maxHeight) {
      // 如果没超出，所有标签都可见
      return;
    }

    // 如果超出了最大高度，需要隐藏部分标签
    visibleTags.value = [];
    let testTags = [];

    // 逐个添加标签并测试高度
    for (let i = 0; i < props.tags.length; i++) {
      testTags.push(props.tags[i]);
      visibleTags.value = [...testTags];

      // 等待DOM更新
      await nextTick();
      console.log('tagContainer.value.offsetHeight', tagContainer.value.offsetHeight);

      // 检查高度
      if (tagContainer.value && tagContainer.value.offsetHeight > props.maxHeight) {
        // 移除最后添加的标签
        testTags.pop();
        visibleTags.value = [...testTags];
        if (tagContainer.value && tagContainer.value.offsetHeight > props.maxHeight) {
          // 如果还是超出最大高度，再移除一个标签以留出空间给"更多"按钮
          testTags.pop();
          visibleTags.value = [...testTags];
        }
        // 剩余的标签放入hiddenTags
        hiddenTags.value = props.tags.slice(testTags.length);
        break;
      }
    }
  };

 

  watch(
    () => props.tags,
    async () => {
      await nextTick();
      updateTags();
    }
  );
</script>

<style scoped>
  .tag-container {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    gap: 8px;
  }
</style>
