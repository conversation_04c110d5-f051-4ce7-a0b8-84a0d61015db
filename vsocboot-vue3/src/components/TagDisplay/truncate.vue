<template>
  <div class="tag-container" :class="className"  ref="tagContainer">
    <slot name="content" :data="visibleTags"></slot>

    <!-- 如果没有标签，则不显示任何内容 -->
    <a-dropdown v-if="hiddenTags.length > 0" trigger="click" >
      <!-- 如果有隐藏的标签，显示下拉菜单和数字指示器 -->
      <div @click.stop class="ax-icon-button" ref="hideRef">
        <span class="soc ax-com-Arrow-down ax-icon"></span>
      </div>
      <template #overlay>
        <slot name="menu" :menuData="hiddenTags"></slot>
      </template>
    </a-dropdown>
  </div>
</template>

<script setup>
import {ref, onMounted, watch, nextTick, onUnmounted, watchEffect} from 'vue';

const props = defineProps({
  tags: {
    type: Array,
    default: () => [],
  },
  maxWidth: {
    type: Number,
    default: 1000, // 默认不限制宽度
  },
  className: {
    type: String,
    default: '',
  },
});

const tagContainer = ref(null);
const visibleTags = ref([]);
const hiddenTags = ref([]);


const updateTags = async () => {
  if (!tagContainer.value) return;

  // 先重置
  visibleTags.value = [];
  hiddenTags.value = [...props.tags];

  // 计算可用宽度
  let availableWidth = props.maxWidth;

  // 如果容器宽度超过最大宽度，预留下拉菜单的空间
  if (tagContainer.value.offsetWidth > availableWidth) {
    availableWidth = availableWidth - 40; // 为下拉菜单预留空间
  }

  // 先清空可见标签，然后逐个添加并检查宽度
  for (let i = 0; i < props.tags.length; i++) {
    visibleTags.value.push(props.tags[i]);

    await nextTick(); // 等待DOM更新

    console.log('tagContainer.value -i=',i + ',width=' + tagContainer.value.offsetWidth)
    // 如果添加当前标签后超出可用宽度，则移除该标签并将剩余标签放入隐藏数组
    if (tagContainer.value.offsetWidth >= availableWidth) {
      visibleTags.value.pop();
      // 去重处理（根据标签的唯一标识，这里假设是`id`或`label`）
      // visibleTags.value = [...new Set(visibleTags.value.map(tag => tag.id || tag))]
      //   .map(id => props.tags.find(tag => tag.id === id || tag === id));
      hiddenTags.value = props.tags.slice(i);
      break;
    } else {
      hiddenTags.value.shift(); // 从隐藏数组中移除当前标签
    }
  }
};


defineExpose({
  updateTags
})

</script>

<style scoped>
.tag-container {
  display: flex;
  flex-wrap: nowrap; /* 防止换行 */
  align-items: center;
  overflow: hidden; /* 隐藏溢出内容 */
  white-space: nowrap; /* 防止文本换行 */
}
</style>
