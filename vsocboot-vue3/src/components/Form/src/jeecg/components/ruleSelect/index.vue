<template>
  <a-form-item-rest>
    <a-row :gutter="10" v-for="(item,i) in defaultData" :key="i">
      <a-col :span="6">
        <a-select v-model:value="item.fieldId" :options="options"  :disabled="isShow" @change="doChange"/>
      </a-col>
      <a-col :span="18">

        <a-row :gutter="10"  class="conditions">
          <div class="condition-wraper"  v-for="(data,j) in item.conditions" :key="j">
            <a-select :options="RULE_FILTER" v-model:value="data.rel" class="condition-wraper_rel" :disabled="isShow" @change="doChange"/>
            <a-input v-model:value="data.value" class="condition-wraper_value" :disabled="isShow" @change="doChange"/>
            <div class="condition-wraper_del" v-if="!isShow">
              <a-button  @click="delCol(i,j)">x</a-button>
            </div>

          </div>
          <div class="condition-opertate" v-if="!isShow">
            <a-button shape="circle" @click="addCol(i)">+</a-button>
            <a-button shape="circle" @click="delRow(i)">-</a-button>
          </div>
        </a-row>



      </a-col>


    </a-row>
    <a-row v-if="!isShow">
      <a-button  @click="addRule">+ {{ t('common.add') }}</a-button>
    </a-row>

  </a-form-item-rest>
</template>
<script lang="ts" name="RuleSelect" setup>
import {
  defineComponent,
  watchEffect,
  watch,
  reactive,
  nextTick,
  ref,
  toRaw,
  unref,
  onMounted
} from 'vue';
import Rule from './component/Rule.vue'
import {RULE_FILTER} from './rule'
import {isEmpty} from "/@/utils/is";
import {useI18n} from "/@/hooks/web/useI18n";
import {Form} from "ant-design-vue";
const { t } = useI18n();
const emit = defineEmits(['change', 'update:value']);
const formItemContext = Form.useInjectFormItemContext();
const props = defineProps({
  isShow : Boolean,
  value: String,
  options: Array
});
let defValue = {fieldId: '', conditions: [{"rel": 1, "value": ""}]};
const defaultData = ref([]);

onMounted(()=>{
  let innerValue = [];
  setTimeout(()=>{
    if (props && props.value) {
      innerValue = JSON.parse(props.value);
    }
    // ============默认有选项时，展开下面 start==================
    // else{
    //   innerValue.push(defValue);
    // }
    // ======================默认有选项时展开 end==============
    defaultData.value = [...innerValue];
  },100);
})


/**
 * 添加规则
 */
function addRule() {
  console.log('addRule started');
  console.log(defValue)
  defaultData.value.push(JSON.parse(JSON.stringify(defValue)));
  console.log(defaultData.value)


}
function handleChangeBegin(e) {
  let arr = e.target.value;

  emit('change', arr);
  emit('update:value', arr);
  formItemContext.onFieldChange();
}
function addCol(row) {
  defaultData.value[row].conditions.push({"rel": 1, "value": ""});

}

function delCol(row,j) {
  defaultData.value[row].conditions.splice(j, 1);

}
function delRow(row) {
  defaultData.value.splice(row, 1);
}
function doChange(){
  let data = isEmpty(defaultData.value) ? '' : JSON.stringify(defaultData.value);
  console.log('change values rule',data)
  emit('update:value', data);
  emit('change', data);
  formItemContext.onFieldChange();
}



</script>


<style  lang="less" scoped>

.conditions{
  display:flex;

  .condition-wraper{
    display: flex;
    width:auto;
    border-radius: 6px;
    border:1px solid rgba(255,255,255,0.08);
    margin-right:10px;
    margin-bottom: 10px;

     /deep/ .ant-select-disabled .ant-select-arrow{
      display: none!important;
    }
    /deep/.ant-input{
      border-radius: 0!important;
    }
    /deep/ .ant-select-selector{
      border: 0!important;
      border-radius: 0!important;
      &:focus{
        border-radius: 6px!important;
      }
    }

    .condition-wraper_rel{
      width: 70px;
      border:0;
      &.ant-select-disabled{
        width: 30px!important;
      }

    }
    .condition-wraper_value{
      width: 120px;
      border:0
    }
    .condition-wraper_del{
      width: 40px;
      button{
        display: none;
      }
      &:hover{
        button{
          display: block;
        }
      }
    }
    & > {
      input:focus{
        border:0px;
        box-shadow: none;
      }
    }

  }
  .condition-opertate{
    max-width:120px
    button{
      margin-right: 5px;
    }
  }

}

</style>
