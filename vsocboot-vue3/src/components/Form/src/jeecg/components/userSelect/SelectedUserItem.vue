<template>
  <div class="user-selected-item">
    <div :class="{'user-div':isShowName,'user-div2':!isShowName}"
         class="user-selected-item-div"
    >
      <span :class="{'user-span':isShowName,'user-span2':!isShowName}">
        <a-avatar v-if="info.avatar" :src="render.renderUploadImageSrc(info.avatar)" :size="24"></a-avatar>
        
        <a-avatar v-else-if="info.selectType == 'sys_role'" :size="24"
                  style="background-color: rgb(255, 173, 0);">
          <template #icon>
            <team-outlined style="font-size: 16px"/>
          </template>
        </a-avatar>
        <a-avatar v-else-if="info.selectType == 'sys_position'" :size="24"
                  style="background-color: rgb(245, 34, 45);">
          <template #icon>
            <TagsOutlined style="font-size: 16px"/>
          </template>
        </a-avatar>
        
        <a-avatar :size="24" v-else>
          <template #icon>
<!--            <UserOutlined/>-->
            {{info.username && info.username.substring(0,1).toUpperCase()}}
          </template>
        </a-avatar>
      </span>

      <div style="height: 24px; line-height: 24px" class="ellipsis" v-if="isShowName">
        {{ info.realname || info.name }}
      </div>

      <div v-if="showClose" class="icon-close">
        <CloseOutlined @click="removeSelect"/>
      </div>
    </div>

    <div v-if="!showClose" class="icon-remove">
      <MinusCircleFilled @click="removeSelect"/>
    </div>
  </div>
</template>

<script>
  import {
    UserOutlined,
    CloseOutlined,
    MinusCircleFilled,
    TagsOutlined,
    TeamOutlined
  } from '@ant-design/icons-vue';
  import {computed} from 'vue'
  import {getFileAccessHttpUrl} from '/@/utils/common/compUtils';
  import {render} from "/@/utils/common/renderUtils";

  export default {
    name: 'SelectedUserItem',
    components: {
      UserOutlined,
      MinusCircleFilled,
      CloseOutlined,
      TagsOutlined,
      TeamOutlined
    },
    props: {
      info: {
        type: Object,
        default: () => {
        },
      },
      // 是否作为查询条件
      query: {
        type: Boolean,
        default: false,
      },
      showName: {
        type: Boolean,
        default: true,
      }

    },
    emits: ['unSelect'],
    setup(props, {emit}) {
      function removeSelect(e) {
        e.preventDefault();
        e.stopPropagation();
        emit('unSelect', props.info.id);
      }

      const showClose = computed(() => {
        if (props.query === true) {
          return true;
        } else {
          return false;
        }
      });

      const isShowName = computed(() => {
        if (props.showName === false) {
          return false;
        } else {
          return true;
        }
      });

      return {
        showClose,
        removeSelect,
        isShowName,
        render
      };
    },
  };
</script>

<style lang="less">
  .user-selected-item {
    position: relative;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    margin-right: 8px;
    height: 30px;
    border-radius: 12px;
    line-height: 30px;
    vertical-align: middle;

    .ellipsis {
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }

    .icon-remove {
      position: absolute;
      top: -10px;
      right: -4px;
      font-size: 18px;
      width: 15px;
      height: 15px;
      cursor: pointer;
      display: none;
    }

    .icon-close {
      height: 22px;
      line-height: 24px;
      font-size: 10px;
      font-weight: bold;
      margin-left: 7px;

      &:hover {
        color: #0a8fe9;
      }
    }

    &:hover {
      .icon-remove {
        display: block;
      }
    }
  }

  .user-selected-item-div {
    background-color: #aaa;
  }

  .user-span {
    width: 24px;
    height: 24px;
    line-height: 20px;
    margin-right: 3px;
    display: inline-block
  }

  .user-span2 {
    width: 24px;
    height: 24px;
    line-height: 20px;
    display: inline-block;

    .ant-avatar {
      background:  @primary-color;
    }
  }

  .user-div {
    display: flex;
    flex-direction: row;
    height: 24px;
    border-radius: 12px;
    padding-right: 10px;
    vertical-align: middle;
  }

  .user-div2 {
    display: flex;
    flex-direction: row;
    height: 24px;
    border-radius: 12px;
    vertical-align: middle;
  }
</style>
