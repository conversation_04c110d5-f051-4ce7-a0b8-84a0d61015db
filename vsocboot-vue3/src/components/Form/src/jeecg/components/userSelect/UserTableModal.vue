<template>
  <BasicModal v-bind="$attrs" @register="sModal" :title="title" width="1000px" destroyOnClose
              @ok="handleSubmit">

    <BasicTable @register="registerTable" :rowSelection="rowSelection as any">
      <template #userInfo="{ record }">
        <UserName :record="record"/>
      </template>
    </BasicTable>

  </BasicModal>
</template>
<script lang="ts" setup>
import {ref, toRaw, unref} from 'vue';
import {BasicModal, useModalInner} from '/@/components/Modal';
import {BasicTable} from '/@/components/Table';
import {searchUserFormSchema, userColumns} from '/@/views/system/role/role.data';
import {listByTenant} from '/@/views/system/user/user.api';
import {useI18n} from "/@/hooks/web/useI18n";
import UserName from "/@/components/vsoc/UserName.vue";
import {useListPage} from "/@/hooks/system/useListPage";
import {TABLE_CACHE_KEY} from "/@/utils/valueEnum";

const {t} = useI18n();
const title = t('routes.sysRole.userSelectList');
const props = defineProps({
  rowSelectionType: {
    type: String as PropType<'checkbox' | 'radio'>,
    default: 'radio'
  }
});
const socTenantId = ref("");
const notIds = ref("")

// 声明Emits
const emit = defineEmits(['select', 'register']);
const checkedKeys = ref<Array<string | number>>([]);
const checkedNames = ref<Array<string | number>>([]);
const [sModal, {setModalProps, closeModal}] = useModalInner(async (data) => {
  console.log(data)
  socTenantId.value = data.record.socTenantId;
  notIds.value = data.record.notIds;
  console.log(toRaw(socTenantId.value))
});
//注册table数据

const {  tableContext  } = useListPage({
  tableProps: {
    api: listByTenant,
    columns: userColumns,
    formConfig: {
      schemas: searchUserFormSchema,
    },
    tableSetting: {
      cacheKey: TABLE_CACHE_KEY.userSelect,
    },
    beforeFetch: (param) => {
      param.socTenantId = socTenantId.value;
      param.notIds = notIds.value;
    },
    //自定义默认排序
    defSort: {
      column: 'id',
      order: 'desc',
    },
  },

});
const [registerTable, { reload }] = tableContext;
/**
 * 选择列配置
 */
const rowSelection = {
  type: props.rowSelectionType,
  columnWidth: 50,
  selectedRowKeys: checkedKeys,
  selectable: true,
  onChange: onSelectChange,
};

/**
 * 选择事件
 */
function onSelectChange(selectedRowKeys: (string | number)[], selectedRows) {
  checkedKeys.value = selectedRowKeys;
  checkedNames.value = [];
  console.log(selectedRows)
  selectedRows.forEach(item => {
    checkedNames.value.push(item.username);
  })
}

//提交事件
function handleSubmit() {
  setModalProps({confirmLoading: true});
  //关闭弹窗
  closeModal();
  //刷新列表
  emit('select', toRaw(unref(checkedKeys)), unref(checkedNames));
  setModalProps({confirmLoading: false});
}

function doReload(e) {
  let value = e.target.value || '';
  if (value) {
    value = "*" + value + "*";
  }
  reload({searchInfo: {username: value}});
}

</script>
<style scoped lang="less">

</style>
