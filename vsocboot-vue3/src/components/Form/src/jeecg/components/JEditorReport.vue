<template>
  <Tinymce v-bind="bindProps" :key="key" @change="onChange" />
</template>

<script lang="ts">
  import { computed, defineComponent, ref } from 'vue';

  import { Tinymce } from '/@/components/Tinymce/report';
  import { propTypes } from '/@/utils/propTypes';

  export default defineComponent({
    name: 'JEditorReport',
    // 不将 attrs 的属性绑定到 html 标签上
    inheritAttrs: false,
    components: { Tinymce },
    props: {
      value: propTypes.string.def(''),
      disabled: propTypes.bool.def(false),
    },
    emits: ['change', 'update:value'],
    setup(props, { emit, attrs }) {
      // 合并 props 和 attrs
      const bindProps = computed(() => Object.assign({}, props, attrs));
      const key = ref((Math.random()*10000000000000).toFixed(0));

      // value change 事件
      function onChange(value) {
        emit('change', value);
        emit('update:value', value);
      }

      return {
        bindProps,
        key,
        onChange,
      };
    },
  });
</script>

<style lang="less" scoped>

 .tox-menubar{
  display: none!important;
  background: transparent!important;
  background-color: transparent!important;
}
 .tox-anchorbar{
  display: none!important;
}
</style>
