<template>
  <!--异步字典下拉搜素-->
  <a-select
    v-if="async"
    v-bind="attrs"
    v-model:value="selectedAsyncValue"
    showSearch
    labelInValue
    allowClear
    :mode="multiple ? 'multiple' : undefined"
    :getPopupContainer="getParentContainer"
    :placeholder="placeholder"
    :filterOption="false"
    :notFoundContent="loading ? undefined : null"
    @search="loadData"
    @change="handleAsyncChange"
  >
    <template #notFoundContent>
      <a-spin size="small" />
    </template>
    <a-select-option v-for="d in options" :key="d.value" :value="d.value">{{ d.text }} </a-select-option>
  </a-select>
  <!--字典下拉搜素-->
  <a-select
    v-else
    v-model:value="selectedValue"
    v-bind="attrs"
    showSearch
    :mode="multiple ? 'multiple' : undefined"
    :getPopupContainer="getParentContainer"
    :placeholder="placeholder"
    :class="className"
    :filterOption="filterOption"
    :notFoundContent="loading ? undefined : null"
    :dropdownAlign="{ overflow: { adjustY: adjustY } }"
    @change="handleChange"
    :disabled="ifDisable"
  >
    <template #notFoundContent>
      <a-spin v-if="loading" size="small" />
    </template>
    <template v-if="optionsDel">
      <a-select-option v-for="d in options" :key="d.value" :value="d.value">
        <div style="display: flex; justify-content: space-between">
          <span>{{ d.text }}</span>
          <Icon icon="ant-design:delete-outlined" class="del-option" v-if="optionsDel" @click.stop="delOption(d.value)"></Icon>
        </div>
      </a-select-option>
    </template>
    <template v-else>
      <a-select-option v-for="d in options" :key="d.value" :value="d.value">{{ d.text }} </a-select-option>
    </template>
  </a-select>
</template>

<script lang="ts">
  import { useDebounceFn } from '@vueuse/core';
  import { defineComponent, ref, unref, watch, watchEffect } from 'vue';
  import { propTypes } from '/@/utils/propTypes';
  import { useAttrs } from '/@/hooks/core/useAttrs';
  import { initDictOptions } from '/@/utils/dict/index';
  import { defHttp } from '/@/utils/http/axios';

  export default defineComponent({
    name: 'JSearchSelect',
    inheritAttrs: false,
    props: {
      value: propTypes.oneOfType([propTypes.string, propTypes.number, propTypes.array]),
      dict: propTypes.string,
      dictOptions: {
        type: Array,
        default: () => [],
      },
      async: propTypes.bool.def(false),
      placeholder: propTypes.string,
      popContainer: propTypes.string,
      pageSize: propTypes.number.def(10),
      getPopupContainer: {
        type: Function,
        default: (node) => node.parentNode,
      },
      className: propTypes.string,
      //默认开启Y轴溢出位置调整，因此在可视空间不足时下拉框位置会自动上移，导致Select的输入框被遮挡。需要注意的是，默认情况是是可视空间，而不是所拥有的空间
      //update-begin-author:liusq date:2023-04-04 for:[issue/286]下拉搜索框遮挡问题
      adjustY: propTypes.bool.def(true),
      //update-end-author:liusq date:2023-04-04 for:[issue/286]下拉搜索框遮挡问题
      //是否在有值后立即触发change
      immediateChange: propTypes.bool.def(false),
      //update-begin-author:taoyan date:2022-8-15 for: VUEN-1971 【online 专项测试】关联记录和他表字段 1
      //支持传入查询参数，如排序信息
      params: {
        type: Object,
        default: () => {},
      },
      //update-end-author:taoyan date:2022-8-15 for: VUEN-1971 【online 专项测试】关联记录和他表字段 1

      //下拉选额外添加数据，加载最前边
      appendFirstData: {
        type: Array,
        default: () => [],
      },
      ifDisable: propTypes.bool.def(false),
      optionsDel: {
        //删除下拉选项
        type: Boolean,
        default: false,
      },
      multiple: {
        //是否支持多选
        type: Boolean,
        default: false,
      },
    },
    emits: ['change', 'update:value', 'update:options', 'delOption'],
    setup(props, { emit }) {
      const options = ref<any[]>([]);
      const loading = ref(false);
      const attrs = useAttrs();
      const selectedValue = ref<any>();
      const selectedAsyncValue = ref<any>();
      const lastLoad = ref(0);
      // 是否根据value加载text
      const loadSelectText = ref(true);
      /**
       * 监听字典code
       */
      watchEffect(() => {
        props.dict && initDictData();
      });
      /**
       * 监听value
       */
      watch(
        () => props.value,
        (val) => {
          if (val || val === 0) {
            initSelectValue();
          } else {
            selectedValue.value = [];
            selectedAsyncValue.value = [];
          }
        },
        { immediate: true }
      );
      /**
       * 监听dictOptions
       */
      watch(
        () => props.dictOptions,
        (val) => {
          if (val && val.length > 0) {
            options.value = [...val];
          } else {
            options.value = [];
          }
        },
        { immediate: true }
      );

      /**
       * 异步查询数据
       */
      async function loadData(value) {
        lastLoad.value += 1;
        const currentLoad = unref(lastLoad);
        options.value = [];
        loading.value = true;
        let keywordInfo = getKeywordParam(value);
        // 字典code格式：table,text,code
        defHttp
          .get({
            url: `/sys/dict/loadDict/${props.dict}`,
            params: { keyword: keywordInfo, pageSize: props.pageSize },
          })
          .then((res) => {
            loading.value = false;
            if (res && res.length > 0) {
              if (currentLoad != unref(lastLoad)) {
                return;
              }
              options.value = res;
            }
          });
      }

      /**
       * 初始化value
       */
      function initSelectValue() {
        //update-begin-author:taoyan date:2022-4-24 for: 下拉搜索组件每次选中值会触发value的监听事件，触发此方法，但是实际不需要
        if (loadSelectText.value === false) {
          loadSelectText.value = true;
          return;
        }
        //update-end-author:taoyan date:2022-4-24 for: 下拉搜索组件每次选中值会触发value的监听事件，触发此方法，但是实际不需要
        let { async, value, dict, multiple } = props;

        if (async) {
          if (multiple) {
            // 多选异步模式处理
            if (Array.isArray(value) && value.length > 0) {
              const keys = typeof value[0] === 'object' ? value.map((v) => v.key) : value;
              defHttp.get({ url: `/sys/dict/loadDictItem/${dict}`, params: { key: keys.join(',') } }).then((res) => {
                if (res && res.length > 0) {
                  selectedAsyncValue.value = value;
                  if (props.immediateChange == true) {
                    emit('change', value);
                  }
                }
              });
            } else {
              selectedAsyncValue.value = [];
            }
          } else {
            // 单选异步模式处理
            if (!selectedAsyncValue.value || !selectedAsyncValue.value.key || selectedAsyncValue.value.key !== value) {
              defHttp.get({ url: `/sys/dict/loadDictItem/${dict}`, params: { key: value } }).then((res) => {
                if (res && res.length > 0) {
                  let obj = {
                    key: value,
                    label: res,
                  };
                  selectedAsyncValue.value = { ...obj };
                  //update-begin-author:taoyan date:2022-8-11 for: 值改变触发change事件--用于online关联记录配置页面
                  if (props.immediateChange == true) {
                    emit('change', value);
                  }
                  //update-end-author:taoyan date:2022-8-11 for: 值改变触发change事件--用于online关联记录配置页面
                }
              });
            }
          }
        } else {
          // 处理同步模式的值
          if (multiple) {
            selectedValue.value = Array.isArray(value) ? value : value ? [value?.toString()] : [];
          } else {
            selectedValue.value = value?.toString();
          }
          //update-begin-author:taoyan date:2022-8-11 for: 值改变触发change事件--用于online他表字段配置界面
          if (props.immediateChange == true) {
            emit('change', selectedValue.value);
          }
          //update-end-author:taoyan date:2022-8-11 for: 值改变触发change事件--用于online他表字段配置界面
        }
      }

      /**
       * 初始化字典下拉数据
       */
      async function initDictData() {
        let { dict, async, dictOptions, pageSize, appendFirstData } = props;
        if (!async) {
          //如果字典项集合有数据
          if (dictOptions && dictOptions.length > 0) {
            options.value = dictOptions;
          } else {
            //根据字典Code, 初始化字典数组
            let dictStr = '';
            if (dict) {
              let arr = dict.split(',');
              if (arr[0].indexOf('where') > 0) {
                let tbInfo = arr[0].split('where');
                dictStr = tbInfo[0].trim() + ',' + arr[1] + ',' + arr[2] + ',' + encodeURIComponent(tbInfo[1]);
              } else {
                dictStr = dict;
              }
              //根据字典Code, 初始化字典数组
              const dictData: any = await initDictOptions(dictStr);
              if (appendFirstData && appendFirstData.length > 0) {
                for (let i = appendFirstData.length - 1; i >= 0; i--) {
                  dictData.unshift(appendFirstData[i]);
                }
              }
              options.value = dictData;
              //2024-09-23 给使用组件返回查询出来的下拉选值
              emit('update:options', dictData);
              //2024-09-23 给使用组件返回查询出来的下拉选值
            }
          }
        } else {
          if (!dict) {
            console.error('搜索组件未配置字典项');
          } else {
            //异步一开始也加载一点数据
            loading.value = true;
            let keywordInfo = getKeywordParam('');
            defHttp
              .get({
                url: `/sys/dict/loadDict/${dict}`,
                params: { pageSize: pageSize, keyword: keywordInfo },
              })
              .then((res) => {
                loading.value = false;
                if (res && res.length > 0) {
                  options.value = res;
                  //2024-09-23 给使用组件返回查询出来的下拉选值
                  emit('update:options', res);
                  //2024-09-23 给使用组件返回查询出来的下拉选值
                }
              });
          }
        }
      }

      /**
       * 同步改变事件
       * */
      function handleChange(value) {
        selectedValue.value = value;
        callback();
      }

      /**
       * 异步改变事件
       * */
      function handleAsyncChange(selectedObj) {
        if (selectedObj) {
          selectedAsyncValue.value = selectedObj;
          selectedValue.value = selectedObj.key;
        } else {
          selectedAsyncValue.value = null;
          selectedValue.value = null;
          options.value = [];
          loadData('');
        }
        callback();
      }

      /**
       *回调方法
       * */
      function callback() {
        loadSelectText.value = false;
        emit('change', unref(selectedValue));
        emit('update:value', unref(selectedValue));
      }

      /**
       * 过滤选中option
       */
      function filterOption(input, option) {
        //update-begin-author:taoyan date:2022-11-8 for: issues/218 所有功能表单的下拉搜索框搜索无效
        let label: any = '';
        if (option?.text) {
          label = option?.text;
        }
        try {
          if (option?.children) {//表单下拉选有这个属性
            label = option.children()[0]?.children;
            if (label[0] && label[0]?.children) {
              //下拉选带有删除操作按钮是这种格式
              label = label[0].children;
            }
          }
        } catch (e) {
          console.error(e);
        }
        //update-end-author:taoyan date:2022-11-8 for: issues/218 所有功能表单的下拉搜索框搜索无效
        
        let str = input.toLowerCase();
        // return value.toLowerCase().indexOf(str) >= 0 || label.toLowerCase().indexOf(str) >= 0;
        //2024-03-21 15:02修改，改为只过滤label
        return label.toLowerCase().indexOf(str) >= 0;
        //update-end-author:taoyan date:2022-11-8 for: issues/218 所有功能表单的下拉搜索框搜索无效
      }

      function getParentContainer(node) {
        // update-begin-author:taoyan date:20220407 for: getPopupContainer一直有值 导致popContainer的逻辑永远走不进去，把它挪到前面判断
        if (props.popContainer) {
          return document.querySelector(props.popContainer);
        } else {
          if (typeof props.getPopupContainer === 'function') {
            return props.getPopupContainer(node);
          } else {
            return node.parentNode;
          }
        }
        // update-end-author:taoyan date:20220407 for: getPopupContainer一直有值 导致popContainer的逻辑永远走不进去，把它挪到前面判断
      }

      //update-begin-author:taoyan date:2022-8-15 for: VUEN-1971 【online 专项测试】关联记录和他表字段 1
      //获取关键词参数 支持设置排序信息
      function getKeywordParam(text) {
        // 如果设定了排序信息，需要写入排序信息，在关键词后加 [orderby:create_time,desc]
        if (props.params && props.params.column && props.params.order) {
          let temp = text || '';

          //update-begin-author:taoyan date:2023-5-22 for: /issues/4905 表单生成器字段配置时，选择关联字段，在进行高级配置时，无法加载数据库列表，提示 Sgin签名校验错误！ #4905
          temp = temp + '[orderby:' + props.params.column + ',' + props.params.order + ']';
          return encodeURI(temp);
          //update-end-author:taoyan date:2023-5-22 for: /issues/4905 表单生成器字段配置时，选择关联字段，在进行高级配置时，无法加载数据库列表，提示 Sgin签名校验错误！ #4905
        } else {
          return text;
        }
      }

      //update-end-author:taoyan date:2022-8-15 for: VUEN-1971 【online 专项测试】关联记录和他表字段 1

      function delOption(value) {
        emit('delOption', value);
      }

      return {
        attrs,
        options,
        loading,
        selectedValue,
        selectedAsyncValue,
        loadData: useDebounceFn(loadData, 800),
        getParentContainer,
        filterOption,
        handleChange,
        handleAsyncChange,
        delOption,
      };
    },
  });
</script>

<style scoped>
  .del-option {
    display: none !important;
  }

  :deep(.ant-select-item-option-content .del-option) {
    display: block !important;
  }
</style>
