<template>
  <a-range-picker
    v-model:value="rangeValue"
    :ranges="ranges"
    @change="handleChange"
    :allowClear="allowClear" :show-time="datetime"
    :placeholder="placeholderName"
    :valueFormat="valueFormat"
    :disabled-date="disabledDate"
    @openChange="openChange"/>
</template>

<script>
import {defineComponent, ref, watch, computed} from 'vue';
import {propTypes} from '/@/utils/propTypes';
import {Form} from 'ant-design-vue';
import {useI18n} from "/@/hooks/web/useI18n";
import dayjs from "dayjs";

const {t} = useI18n();


/**
 * 用于范围查询
 */
export default defineComponent({
  name: "JRangeDate",
  props: {
    value: propTypes.string.def(''),
    datetime: propTypes.bool.def(false),
    placeholder: propTypes.string.def(''),
    allowClear: propTypes.bool.def(true),
    disabledDate: Function
  },
  emits: ['change', 'update:value'],
  setup(props, {emit}) {
    const rangeValue = ref([])
    const intervalTime = ref('');
    const formItemContext = Form.useInjectFormItemContext();
    const placeholderName = ref("");
    if (props?.placeholder) {
      placeholderName.value = props?.placeholder;
    } else {
      if (props.datetime) {
        placeholderName.value = [t('common.startTimePlaceholder'), t('common.endTimePlaceholder')]
      } else {
        placeholderName.value = [t('common.startDatePlaceholder'), t('common.endDatePlaceholder')]
      }
    }

    watch(() => props.value, (val) => {
      if (val) {
        rangeValue.value = val.split(',')
      } else {
        rangeValue.value = []
      }
    }, {immediate: true});

    const valueFormat = computed(() => {
      if (props.datetime === true) {
        return 'YYYY-MM-DD HH:mm:ss'
      } else {
        return 'YYYY-MM-DD'
      }
    });

    function handleChange(arr) {
      let str = ''
      if (arr && arr.length > 0) {
        if (arr[1] && arr[0]) {
          str = arr.join(',')
        }
      }
      emit('change', str);
      emit('update:value', str);
      formItemContext.onFieldChange();
    }

    function getKeys() {
      const keys = [
        [t('common.Last') + '15' + t('common.min'), [dayjs().subtract(15, 'minute'), dayjs()]],
        [t('common.Last') + '60' + t('common.min'), [dayjs().subtract(60, 'minute'), dayjs()]],
        [t('common.Last') + '12' + t('common.hours'), [dayjs().subtract(12, 'hour'), dayjs()]],
        [t('common.Last') + '1' + t('common.day'), [dayjs().subtract(1, 'day'), dayjs()]],
        [t('common.Last') + '1' + t('common.week'), [dayjs().subtract(1, 'week'), dayjs()]],
        [t('common.Last') + '1' + t('common.month'), [dayjs().subtract(1, 'month'), dayjs()]]
      ];
      let map = {};
      for (let i in keys) {
        map[keys[i][0]] = keys[i][1]
      }
      return map;
    }


    let ranges = ref(getKeys());

    /**
     * 打开日历，更新range时间范围
     * @param status
     */
    function openChange(status) {
      if (status === true) {
        intervalTime.value = window.setInterval(() => {
          ranges.value = getKeys();
        }, 1000);
      } else {
        clearInterval(intervalTime.value);
      }
    }

    return {
      ranges,
      rangeValue,
      placeholderName,
      valueFormat,
      handleChange,
      openChange
    }
  }
});
</script>

<style scoped>

</style>
