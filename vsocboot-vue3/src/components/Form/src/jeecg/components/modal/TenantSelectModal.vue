<!--用户选择框-->
<template>
  <div>
    <BasicModal
      v-bind="$attrs"
      @register="register"
      :title="modalTitle"
      width="900px"
      wrapClassName="j-user-select-modal"
      @ok="handleOk"
      destroyOnClose
      @visible-change="visibleChange"
    >
      <a-row>
        <a-col :span="showSelected ? 18 : 24">
          <BasicTable
            ref="tableRef"
            :columns="columns"
            :scroll="tableScroll"
            v-bind="getBindValue"
            :useSearchForm="false"
            :formConfig="formConfig"
            :api="getTenantList"
            :searchInfo="searchInfo"
            :rowSelection="rowSelection"
            :showIndexColumn="false"
          >
            <template #form-formFooter>
              <div class="search-left-wrapper">
                <div class="search-left">
                  <a-input :allowClear="true" placeholder="Tenant name" @change="doReload">
                    <template #prefix>
                      <Icon icon="ant-design:search-outlined"></Icon>
                    </template>
                  </a-input>
                </div>
              </div>
            </template>
            <template #userInfo="{ text }">
              <img :src="render.renderUploadImageSrc(text)" class="tenantImage" />
            </template>
          </BasicTable>
        </a-col>
        <a-col :span="showSelected ? 6 : 0">
          <BasicTable
            v-bind="selectedTable"
            :dataSource="selectRows"
            :useSearchForm="false"
            :formConfig="{ showActionButtonGroup: false, baseRowStyle: { minHeight: '40px' } }"
          >
            <!--操作栏-->
            <template #action="{ record }">
              <a href="javascript:void(0)" @click="handleDeleteSelected(record)"><Icon icon="ant-design:delete-outlined"></Icon></a>
            </template>
          </BasicTable>
        </a-col>
      </a-row>
    </BasicModal>
  </div>
</template>
<script lang="ts">
  import { defineComponent, unref, ref } from 'vue';
  import { BasicModal, useModalInner } from '/@/components/Modal';
  import { getTenantList, getUserList } from '/@/api/common/api';
  import { createAsyncComponent } from '/@/utils/factory/createAsyncComponent';
  import { useSelectBiz } from '/@/components/Form/src/jeecg/hooks/useSelectBiz';
  import { useAttrs } from '/@/hooks/core/useAttrs';
  import { selectProps } from '/@/components/Form/src/jeecg/props/props';
  import { useI18n } from '/@/hooks/web/useI18n';
  import { useTableForm } from '/@/components/Table/src/hooks/useTableForm';
  import UserName from '/@/components/vsoc/UserName.vue';
  import { render } from '/@/utils/common/renderUtils';
  const { t } = useI18n();

  export default defineComponent({
    name: 'TenantSelectModal',
    computed: {
      render() {
        return render;
      },
    },
    components: {
      //此处需要异步加载BasicTable
      BasicModal,
      BasicTable: createAsyncComponent(() => import('/@/components/Table/src/BasicTable.vue'), {
        loading: true,
      }),
    },
    props: {
      ...selectProps,
      //选择框标题
      modalTitle: {
        type: String,
        default: 'Choose Tenant',
      },
    },
    emits: ['register', 'getSelectResult'],
    setup(props, { emit, refs }) {
      // update-begin-author:taoyan date:2022-5-24 for: VUEN-1086 【移动端】用户选择 查询按钮 效果不好 列表展示没有滚动条
      const tableScroll = ref<any>({ x: false });
      const tableRef = ref();
      const { t } = useI18n();

      //注册弹框
      const [register, { closeModal }] = useModalInner(() => {
        if (window.innerWidth < 900) {
          tableScroll.value = { x: 900 };
        } else {
          tableScroll.value = { x: false };
        }
        //update-begin-author:taoyan date:2022-6-2 for: VUEN-1112 一对多 用户选择 未显示选择条数，及清空
        setTimeout(() => {
          if (tableRef.value) {
            tableRef.value.setSelectedRowKeys(selectValues['value'] || []);
          }
        }, 800);
        //update-end-author:taoyan date:2022-6-2 for: VUEN-1112 一对多 用户选择 未显示选择条数，及清空
      });
      // update-end-author:taoyan date:2022-5-24 for: VUEN-1086 【移动端】用户选择 查询按钮 效果不好 列表展示没有滚动条
      const attrs = useAttrs();
      //表格配置
      const config = {
        canResize: false,
        bordered: true,
        size: 'small',
      };
      const getBindValue = Object.assign({}, unref(props), unref(attrs), config);
      const [{ rowSelection, visibleChange, selectValues, getSelectResult, handleDeleteSelected, selectRows }] = useSelectBiz(
        getTenantList,
        getBindValue
      );
      const searchInfo = ref(!props.params || Object.keys(props.params).length == 0 ? { delFlag: 0 } : props.params);
      //查询form
      const formConfig = {
        baseColProps: {
          xs: 24,
          sm: 8,
          md: 6,
          lg: 8,
          xl: 6,
          xxl: 6,
        },
        //update-begin-author:taoyan date:2022-5-24 for: VUEN-1086 【移动端】用户选择 查询按钮 效果不好 列表展示没有滚动条---查询表单按钮的栅格布局和表单的保持一致
        actionColOptions: {
          xs: 24,
          sm: 8,
          md: 8,
          lg: 8,
          xl: 8,
          xxl: 8,
        },
        //update-end-author:taoyan date:2022-5-24 for: VUEN-1086 【移动端】用户选择 查询按钮 效果不好 列表展示没有滚动条---查询表单按钮的栅格布局和表单的保持一致
        schemas: [
          {
            label: 'Tenant name',
            field: 'name',
            component: 'JInput',
          },
        ],
      };
      //定义表格列
      const columns = [
        {
          title: 'Tenant name',
          dataIndex: 'name',
          align: 'left',
        },
        {
          title: 'code',
          dataIndex: 'code',
        },
        {
          title: 'Organization Logo',
          dataIndex: 'companyLogo',
          slots: { customRender: 'userInfo' },
        },
        {
          title: 'Status',
          dataIndex: 'status',
          customRender: ({ text }) => {
            if (text == 0) {
              return 'Freeze';
            }
            return 'Normal';
          },
        },
        // {
        //   dataIndex: 'createBy',
        //   title: 'Creator',
        // },
        {
          title: 'CreateTime',
          dataIndex: 'createTime',
        },
      ];
      //已选择的table信息
      const selectedTable = {
        pagination: false,
        showIndexColumn: false,
        scroll: { y: 390 },
        size: 'small',
        canResize: false,
        bordered: true,
        rowKey: 'id',
        columns: [
          {
            title: 'Action',
            dataIndex: 'action',
            align: 'center',
            width: 40,
            slots: { customRender: 'action' },
          },
        ],
      };
      /**
       * 确定选择
       */
      function handleOk() {
        getSelectResult((options, values) => {
          //回传选项和已选择的值
          emit('getSelectResult', options, values);
          //关闭弹窗
          closeModal();

          // 为了与资产上传功能配合，发送postMessage消息
          if (values && values.length > 0) {
            window.postMessage(
              {
                type: 'tenant-selected',
                tenantId: values[0],
                options,
              },
              '*'
            );
          }
        });
      }
      function doReload(e) {
        let value = e.target.value || '';
        if (value) {
          value = '*' + value + '*';
        }
        tableRef.value.reload({ searchInfo: { username: value, delFlag: 0 } });
      }
      return {
        //config,
        handleOk,
        searchInfo,
        register,
        visibleChange,
        getBindValue,
        getTenantList,
        formConfig,
        columns,
        rowSelection,
        selectRows,
        selectedTable,
        handleDeleteSelected,
        tableScroll,
        tableRef,
        doReload,
        t,
      };
    },
  });
</script>
<style scoped lang="less">
  .search-left-wrapper {
    position: relative;
    left: 0px;
    width: 100%;
    .search-left {
      position: relative;
      width: 200px;
    }
  }
  .tenantImage {
    width: 25px;
    height: 25px;
    border-radius: 6px;
    image-rendering: -moz-crisp-edges;
    image-rendering: -o-crisp-edges;
    image-rendering: -webkit-optimize-contrast;
    image-rendering: crisp-edges;
    -ms-interpolation-mode: nearest-neighbor;
  }
</style>
