<template>
  <div class="ax-text-ellipsis">
    <a-tooltip :placement="position">
      <template #title>
        <span>{{ value }}</span>
      </template>
      <template v-if="!slot">{{ value }}</template>
      <template v-else>
        <slot name="text"></slot>
      </template>
    </a-tooltip>
  </div>
</template>
<script lang="ts" setup>
  defineProps({
    value: String,
    width: {
      type: Number,
      default: 100,
    },
    slot: {
      type: Boolean,
      default: false,
    },
    position: {
      type: String,
      default: 'topLeft',
    },
  });
  //显示的文本
</script>
