@import 'vxe.const';
@import 'vxe.dark';

.@{prefix-cls} {
  // 编辑按钮样式
  .vxe-cell--edit-icon {
    border-color: #606266;
  }

  .sort--active {
    border-color: @primary-color;
  }

  // toolbar 样式
  &-toolbar {
    &-collapsed {
      [data-collapse] {
        display: none;
      }
    }

    &-button.div .ant-btn {
      margin-right: 8px;
    }
  }

  // 分页器
  .j-vxe-pagination {
    padding-top: 8px;
    .ant-pagination{
      font-size: 12px;
      height: 32px;
      padding: 4px 16px;
      align-items: center;
      display: flex;
      justify-content: right;
      text-align: left;
      gap: 0;

      li {
        font-size: 12px!important;
        line-height: 16px!important;
      }


      .ant-pagination-total-text{
        padding: 4px 16px;
        margin: 0;
        height: 24px;
      }
      .ant-pagination-prev,
      .ant-pagination-next {
        font-size: 12px;
        color: @text-color-base;
        border: 1px solid;
      }
      .ant-pagination-prev:hover,
      .ant-pagination-next:hover,
      .ant-pagination-item:focus,
      .ant-pagination-item:hover {
        a {
          color: @primary-color;
        }
      }
      .ant-pagination-item,.ant-pagination-next{
        margin-left: 4px!important;
      }
      .ant-pagination-prev,
      .ant-pagination-next,
      .ant-pagination-item,.ant-pagination-item-link {
        margin:0;
        border: 0!important;
        border-radius: 0 !important;
        height: 24px!important;
        min-width: 16px!important;
        display:flex;
        align-items: center;
        a {
          margin-top: 0;
          color: @font-color-default!important;
          padding: 4px 8px;
        }

        &:last-child {
          margin-right: 0 !important;
        }
      }
      .ant-pagination-item-active {
        border-radius: 4px !important;
        background: rgba(255,255,255,0.1);
        a {

          color: @white !important;
        }
      }
      /*省略号*/
      .ant-pagination-jump-next-custom-icon,.ant-pagination-jump-prev-custom-icon{
        display: flex;
        align-items: center;
        padding: 0 8px;
        margin: 0;
        justify-content: center;
        .ant-pagination-item-container{
          height: 24px;
          display: flex;
          align-items: center;
          justify-content: center;
          .ant-pagination-item-ellipsis{
            top: 5px!important;
            min-width: 22px;
            text-align: center;
          }
        }
      }
      /*跳转页码*/
      .ant-pagination-options-quick-jumper {

        margin-left: 16px!important;
        input {
          height: 24px;
          width: 48px!important;
          margin-left: 16px;
          background: rgba(255, 255, 255, 0.1);
          border-radius: 4px;
          border: 0!important;
          padding: 4px 12px!important;
        }
      }
      /*每页显示数*/
      .ant-pagination-options {
        margin-left: 16px!important;
        display:flex;
        flex-direction: row;
        align-items: center;
        .ant-select-selector{
          width: 120px;
          font-size: 12px!important;
          line-height: 16px!important;
          padding: 0 12px;
          height: 24px;

          & > span,input{
            line-height: 16px;
            font-size: 12px!important;
            height: 24px;
          }
          .ant-select-selection-search{
            left: 0!important;
          }
          .ant-select-selection-item{
            line-height: 16px;
            display: flex;
            align-items: center;
            padding: 0;
          }
        }
        .ant-select-arrow{
          top: 6px!important;
          margin: 0!important;
        }
        .ant-select-selection-search-input {
          height: 24px;
          line-height: 16px;
          font-size: 12px!important;
        }
    }


    }



  }

  // 更改 header 底色
  .vxe-table.border--default .vxe-table--header-wrapper,
  .vxe-table.border--full .vxe-table--header-wrapper,
  .vxe-table.border--outer .vxe-table--header-wrapper {
    //background-color: #FFFFFF;
  }

  // 更改 tooltip 校验失败的颜色
  .vxe-table--tooltip-wrapper.vxe-table--valid-error {
    background-color: #f5222d !important;
  }

  // 更改 输入框 校验失败的颜色
  .col--valid-error > .vxe-cell > .ant-input,
  .col--valid-error > .vxe-cell > .ant-select .ant-input,
  .col--valid-error > .vxe-cell > .ant-select .ant-select-selection,
  .col--valid-error > .vxe-cell > .ant-input-number,
  .col--valid-error > .vxe-cell > .ant-cascader-picker .ant-cascader-input,
  .col--valid-error > .vxe-cell > .ant-calendar-picker .ant-calendar-picker-input,
  .col--valid-error > .vxe-tree-cell > .ant-input,
  .col--valid-error > .vxe-tree-cell > .ant-select .ant-input,
  .col--valid-error > .vxe-tree-cell > .ant-select .ant-select-selection,
  .col--valid-error > .vxe-tree-cell > .ant-input-number,
  .col--valid-error > .vxe-tree-cell > .ant-cascader-picker .ant-cascader-input,
  .col--valid-error > .vxe-tree-cell > .ant-calendar-picker .ant-calendar-picker-input {
    border-color: #f5222d !important;
  }

  .vxe-body--row.sortable-ghost,
  .vxe-body--row.sortable-chosen {
    background-color: rgba(255,255,255,0.1);
  }

  // ----------- 【VUEN-1691】默认隐藏滚动条，鼠标放上去才显示 -------------------------------------------
  .vxe-table {
    //.vxe-table--footer-wrapper.body--wrapper,
    .vxe-table--body-wrapper.body--wrapper {
      overflow-x: hidden;
    }
    
    &:hover {
      //.vxe-table--footer-wrapper.body--wrapper,
      .vxe-table--body-wrapper.body--wrapper {
        overflow-x: auto;
      }
    }
  }
  // ----------- 【VUEN-1691】默认隐藏滚动条，鼠标放上去才显示 -------------------------------------------

  // 调整展开/收起图标样式
  .vxe-table--render-default .vxe-table--expanded .vxe-table--expand-btn {
    width: 17px;
    height: 17px;
  }
  /*【美化表单】行编辑table的title字体改小一号*/
  .vxe-header--column.col--ellipsis>.vxe-cell .vxe-cell--title{
    font-size: 13px;
  }

}
