<template>
  <svg :class="[prefixCls, $attrs.class, spin && 'svg-icon-spin']" :style="getStyle" aria-hidden="true">
    <defs v-if="gradient">
      <linearGradient :id="gradientId" x1="0%" y1="0%" x2="100%" y2="0%">
        <stop offset="0%" :stop-color="startColor" />
        <stop offset="100%" :stop-color="endColor" />
      </linearGradient>
    </defs>
    <use :xlink:href="symbolId" :fill="gradient ? `url(#${gradientId})` : 'currentColor'" />
    <!-- <rect x="0" y="0" width="20" height="20" :fill="gradient ? `url(#${gradientId})` : 'currentColor'" /> -->
  </svg>
</template>
<script lang="ts">
  import type { CSSProperties } from 'vue';
  import { defineComponent, computed } from 'vue';
  import { useDesign } from '/@/hooks/web/useDesign';

  export default defineComponent({
    name: 'SvgIcon',
    props: {
      prefix: {
        type: String,
        default: 'icon',
      },
      name: {
        type: String,
        required: true,
      },
      size: {
        type: [Number, String],
        default: 16,
      },
      spin: {
        type: Boolean,
        default: false,
      },
      gradient: {
        type: Boolean,
        default: true,
      },
      startColor: { type: String, default: 'red' },
      endColor: { type: String, default: 'white' },
    },
    setup(props) {
      const { prefixCls } = useDesign('svg-icon');
      const symbolId = computed(() => `#${props.prefix}-${props.name}`);
      const gradientId = computed(() => `gradient-${Math.random().toString(36).slice(2, 11)}`);

      const getStyle = computed((): CSSProperties => {
        const { size } = props;
        let s = `${size}`;
        s = `${s.replace('px', '')}px`;
        return {
          width: s,
          height: s,
        };
      });
      return { symbolId, prefixCls, getStyle, gradientId };
    },
  });
</script>
<style lang="less" scoped>
  @prefix-cls: ~'@{namespace}-svg-icon';

  .@{prefix-cls} {
    display: inline-block;
    overflow: hidden;
    vertical-align: -0.15em;
  }

  .svg-icon-spin {
    animation: loadingCircle 1s infinite linear;
  }
</style>
