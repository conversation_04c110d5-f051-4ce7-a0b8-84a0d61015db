Stack trace:
Frame         Function      Args
0007FFFF9FA0  00021005FE8E (000210285F68, 00021026AB6E, 000000000000, 0007FFFF8EA0) msys-2.0.dll+0x1FE8E
0007FFFF9FA0  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFFA278) msys-2.0.dll+0x67F9
0007FFFF9FA0  000210046832 (000210286019, 0007FFFF9E58, 000000000000, 000000000000) msys-2.0.dll+0x6832
0007FFFF9FA0  000210068CF6 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28CF6
0007FFFF9FA0  000210068E24 (0007FFFF9FB0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28E24
0007FFFFA280  00021006A225 (0007FFFF9FB0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A225
End of stack trace
Loaded modules:
000100400000 bash.exe
7FF816720000 ntdll.dll
7FF8146E0000 KERNEL32.DLL
7FF813E60000 KERNELBASE.dll
7FF8147D0000 USER32.dll
7FF814430000 win32u.dll
7FF815BA0000 GDI32.dll
7FF813AF0000 gdi32full.dll
7FF813DB0000 msvcp_win.dll
7FF813900000 ucrtbase.dll
000210040000 msys-2.0.dll
7FF814610000 advapi32.dll
7FF816630000 msvcrt.dll
7FF8149A0000 sechost.dll
7FF814B30000 RPCRT4.dll
7FF812EF0000 CRYPTBASE.DLL
7FF813A50000 bcryptPrimitives.dll
7FF814520000 IMM32.DLL
