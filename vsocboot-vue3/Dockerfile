FROM registry.axsiem.local:5000/axsoc/axsiem-node:latest as builder

# 复制package.json和pnpm-lock.yaml（如果存在）
COPY package.json pnpm-lock.yaml* ./

# 安装依赖
RUN pnpm install --no-frozen-lockfile

# 单独安装esbuild
# RUN pnpm add esbuild@latest
RUN pnpm add esbuild@0.11.23 --ignore-scripts
RUN node node_modules/esbuild/install.js

# 复制源代码
COPY . .

# 构建项目
RUN pnpm run build-en

FROM registry.axsiem.local:5000/axsoc/axsiem-nginx:1.28.0_0.3

# Remove default nginx static assets
RUN rm -rf /usr/local/nginx/html/*

# 复制构建产物
COPY --from=builder /app/html /usr/local/nginx/html/html


# Create log directory
RUN mkdir -p /var/log/nginx 

# Copy nginx configuration
COPY nginx/nginx.conf /usr/local/nginx/conf/nginx.conf
COPY nginx/conf.d/default.conf /usr/local/nginx/conf.d/default.conf

# Copy SSL certificates
COPY nginx/certs/server.crt /usr/local/nginx/certs/
COPY nginx/certs/server.key /usr/local/nginx/certs/

# Set proper permissions for SSL certificates and handle HTML content
# USER root
RUN chmod 600 /usr/local/nginx/certs/server.key \
    && chmod 600 /usr/local/nginx/certs/server.crt

# Copy and extract the HTML content
#COPY html/html.tar.gz /tmp/
#RUN tar -xzf /tmp/html.tar.gz -C /usr/local/nginx/html/ \
#    && rm /tmp/html.tar.gz

# Switch back to nginx user
# USER nginx

EXPOSE 80
EXPOSE 443

CMD ["nginx", "-g", "daemon off;"] 
