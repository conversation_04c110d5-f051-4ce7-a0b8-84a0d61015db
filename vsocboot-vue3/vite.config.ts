import type {ConfigEnv, UserConfig} from 'vite';
import {loadEnv} from 'vite';
import pkg from './package.json';
import dayjs from 'dayjs';
import {resolve} from 'path';
import {generateModifyVars} from './build/generate/generateModifyVars';
import {createProxy} from './build/vite/proxy';
import {wrapperEnv} from './build/utils';
import {createVitePlugins} from './build/vite/plugin';
import {OUTPUT_DIR} from './build/constant';

function pathResolve(dir: string) {
  return resolve(process.cwd(), '.', dir);
}

const { dependencies, devDependencies, name, version } = pkg;
const __APP_INFO__ = {
  pkg: { dependencies, devDependencies, name, version },
  lastBuildTime: dayjs().format('YYYY-MM-DD HH:mm:ss'),
};

export default ({ command, mode }: ConfigEnv): UserConfig => {
  const root = process.cwd();

  const env = loadEnv(mode, root);

  // The boolean type read by loadEnv is a string. This function can be converted to boolean type
  const viteEnv = wrapperEnv(env);

  const { VITE_PORT, VITE_PUBLIC_PATH, VITE_PROXY, VITE_DROP_CONSOLE } = viteEnv;

  const isBuild = command === 'build';

  return {
    base: VITE_PUBLIC_PATH,
    root,
    resolve: {
      alias: [
        {
          find: 'vue-i18n',
          replacement: 'vue-i18n/dist/vue-i18n.cjs.js',
        },
        // /@/xxxx => src/xxxx
        {
          find: /\/@\//,
          replacement: pathResolve('src') + '/',
        },
        // /#/xxxx => types/xxxx
        {
          find: /\/#\//,
          replacement: pathResolve('types') + '/',
        },
      ],
    },
    server: {
      // Listening on all local IPs
      cors: true, //允许跨域
      host: true,
      https: false,
      port: VITE_PORT,
      // Load proxy configuration from .env
      proxy: createProxy(VITE_PROXY),
    },
    build: {
      minify: 'esbuild',
      target: 'es2015',
      cssTarget: 'chrome80',
      outDir: OUTPUT_DIR,
      terserOptions: {
        compress: {
          keep_infinity: true,
          // Used to delete console in production environment
          drop_console: VITE_DROP_CONSOLE,
          drop_debugger: true,
        },
      },
      // Turning off brotliSize display can slightly reduce packaging time
      reportCompressedSize: false,
      chunkSizeWarningLimit: 2000,
      // rollupOptions: {
      //   output: {
      //     manualChunks(id) {
      //       // 按功能模块分包
      //       if (id.includes('src/views')) {
      //         return id.split('src/views/')[1].split('/')[0];
      //       } else if (id.includes('node_modules')) {
      //         const array = id.split('node_modules/');
      //         const pkgName = array[array.length - 1].split('/')[0].replace('@', '');
      //         switch (pkgName) {
      //           case 'vue': // Vue 核心框架保留主包
      //             return 'vendor-vue';
      //           case 'ant-design-vue': // Ant Design Vue 组件库，也保留主包
      //             return 'vendor-antd';
      //           case 'lodash': // Lodash 工具库可考虑放在主包或单独分包
      //             return 'vendor-lodash';
      //           case 'echarts': // ECharts 图表库
      //             return 'vendor-echarts';
      //           case 'monaco-editor': // Monaco 编辑器
      //             return 'vendor-monaco';
      //           default:
      //             return 'vendor'; // 其他依赖统一放在 vendor 包
      //         }
      //       }
      //     }
      //   }
      // }
    },
    define: {
      // setting vue-i18-next
      // Suppress warning
      __INTLIFY_PROD_DEVTOOLS__: false,
      __APP_INFO__: JSON.stringify(__APP_INFO__),
    },
    css: {
      preprocessorOptions: {
        less: {
          modifyVars: generateModifyVars(),
          javascriptEnabled: true,
        },
      },
      devSourcemap: true,
    },

    // The vite plugin used by the project. The quantity is large, so it is separately extracted and managed
    plugins: createVitePlugins(viteEnv, isBuild),

    optimizeDeps: {
      esbuildOptions: {
        target: 'es2020',
      },
      // @iconify/iconify: The dependency is dynamically and virtually loaded by @purge-icons/generated, so it needs to be specified explicitly
      include: ['@vue/runtime-core', '@vue/shared', '@iconify/iconify', 'ant-design-vue/es/locale/zh_CN', 'ant-design-vue/es/locale/en_US'],
    },
  };
};
